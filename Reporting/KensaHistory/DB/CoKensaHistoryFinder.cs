﻿using Domain.Models.HpInf;
using Domain.Models.KensaIrai;
using Entity.Tenant;
using Helper.Constants;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Reporting.KensaHistory.Models;
using System.Linq.Dynamic.Core;
using static Domain.Models.KensaIrai.ListKensaInfDetailModel;

namespace Reporting.KensaHistory.DB
{
    public class CoKensaHistoryFinder : RepositoryBase, ICoKensaHistoryFinder
    {
        public CoKensaHistoryFinder(ITenantProvider tenantProvider) : base(tenantProvider)
        {
        }

        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        public HpInfModel GetHpInf(int hpId, int sinDate)
        {
            var hpInf = NoTrackingDataContext.HpInfs.Where(item => item.HpId == hpId && item.StartDate < sinDate).OrderBy(x => x.StartDate).First();
            return hpInf != null ? new HpInfModel(hpId,
                                                    hpInf.StartDate,
                                                    hpInf.HpCd ?? string.Empty,
                                                    hpInf.RousaiHpCd ?? string.Empty,
                                                    hpInf.HpName ?? string.Empty,
                                                    hpInf.ReceHpName ?? string.Empty,
                                                    hpInf.KaisetuName ?? string.Empty,
                                                    hpInf.PostCd ?? string.Empty,
                                                    hpInf.PrefNo,
                                                    hpInf.Address1 ?? string.Empty,
                                                    hpInf.Address2 ?? string.Empty,
                                                    hpInf.Tel ?? string.Empty,
                                                    hpInf.FaxNo ?? string.Empty,
                                                    hpInf.OtherContacts ?? string.Empty
                                                ) : new HpInfModel();
        }

        public PtInf GetPtInf(int hpId, long ptId)
        {
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId);
            return ptInf;
        }

        private static (string, string) GetValueLowHigSdt(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return (string.Empty, string.Empty);
            }
            else
            {
                string[] values = input.Split("-");

                if (values.Length == 2)
                {
                    return (values[0], values[1]);
                }
                else
                {
                    return (string.Empty, string.Empty);
                }
            }
        }

        public (List<CoKensaResultMultiModel>, List<long>, List<string>) GetListKensaInfDetail(int hpId, int userId, long ptId, int setId, int startDate, int endDate, bool showAbnormalKbn, List<string> kensaItemCdList)
        {
            var userConfDict = NoTrackingDataContext.UserConfs
                .Where(x => x.UserId == userId && x.HpId == hpId && x.GrpCd == 1002)
                .ToDictionary(x => new { x.GrpItemCd, x.GrpItemEdaNo }, x => x.Val);

            var kensaSetDetailDict = NoTrackingDataContext.KensaSetDetails
                .Where(x => x.SetId == setId && x.HpId == hpId && x.IsDeleted == DeleteTypes.None)
                .GroupBy(item => item.KensaItemCd)
                .ToDictionary(
                    group => group.Key,
                    group => group.Min(item => item.SortNo)
                );

            var kensaMstDict = NoTrackingDataContext.KensaMsts
                .Where(x => x.HpId == hpId && x.IsDelete == DeleteTypes.None)
                .GroupBy(x => x.KensaItemCd)
                .ToDictionary(
                    group => group.Key,
                    group => group.OrderBy(x => x.KensaItemSeqNo).First()
                );

            var kensaCmtMstDict = NoTrackingDataContext.KensaCmtMsts
                .Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None)
                .GroupBy(x => x.CmtCd)
                .ToDictionary(
                    group => group.Key,
                    group => group.OrderBy(x => x.CmtSeqNo).First()
                );

            var commonCenterKensaMstDict = NoTrackingDataContext.CommonCenterKensaMst
                .ToDictionary(x => new { x.CenterCd, x.KensaItemCd }, x => x);

            var commonKensaCenterMstDict = NoTrackingDataContext.CommonKensaCenterMst
                .ToDictionary(x => x.CenterCd, x => x);

            var commonCenterStdMstDict = NoTrackingDataContext.CommonCenterStdMst
                .ToDictionary(x => new { x.CenterCd, x.KensaItemCd }, x => x);

            bool sortIraiDateAsc = userConfDict.GetValueOrDefault(new { GrpItemCd = 0, GrpItemEdaNo = 0 }) != 1;

            IQueryable<KensaInfDetail> baseKensaQuery = NoTrackingDataContext.KensaInfDetails
                .Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None);

            if (setId != 0)
            {
                var kensaItemCodes = kensaSetDetailDict.Keys.ToList();
                baseKensaQuery = baseKensaQuery.Where(x => kensaItemCodes.Contains(x.KensaItemCd));
            }


            var combinedData = (from kensaDetail in baseKensaQuery
                                join kensaInf in NoTrackingDataContext.KensaInfs
                                    on new { kensaDetail.HpId, kensaDetail.PtId, kensaDetail.IraiCd }
                                    equals new { kensaInf.HpId, kensaInf.PtId, kensaInf.IraiCd }
                                join ptInfo in NoTrackingDataContext.PtInfs
                                    on new { kensaDetail.PtId, kensaDetail.HpId }
                                    equals new { ptInfo.PtId, ptInfo.HpId }
                                where kensaInf.IsDeleted == DeleteTypes.None &&
                                      ptInfo.IsDelete == DeleteTypes.None &&
                                      (!kensaItemCdList.Any() || kensaItemCdList.Contains(kensaDetail.KensaItemCd + "-" + kensaInf.CenterCd))
                                select new
                                {
                                    KensaDetail = kensaDetail,
                                    KensaInf = kensaInf,
                                    PtInf = ptInfo
                                }).ToList();

            var processedData = new List<ListKensaInfDetailItemModel>();
            var existingKensaItemCodes = new HashSet<string>();

            foreach (var kensaItemCdFull in kensaItemCdList)
            {
                var parts = kensaItemCdFull.Split('-');
                if (parts.Length != 2) continue;

                var kensaItemCd = parts[0];
                var centerCd = parts[1];

                existingKensaItemCodes.Add(kensaItemCdFull);

                var matched = combinedData
                    .Where(x => x.KensaDetail.KensaItemCd == kensaItemCd && x.KensaInf.CenterCd == centerCd)
                    .ToList();

                if (matched.Any())
                {
                    foreach (var item in matched)
                    {
                        var kensaDetail = item.KensaDetail;
                        var kensaInf = item.KensaInf;
                        var isInternal = kensaInf.CenterCd == CommonConstants.InHospitalCenterCd;

                        ListKensaInfDetailItemModel processedItem;

                        if (isInternal)
                        {
                            var kensaMst = kensaMstDict.GetValueOrDefault(kensaDetail.KensaItemCd);
                            var cmtMst1 = kensaCmtMstDict.GetValueOrDefault(kensaDetail.CmtCd1);
                            var cmtMst2 = kensaCmtMstDict.GetValueOrDefault(kensaDetail.CmtCd2);

                            var cmt1 = (cmtMst1 == null || kensaInf.CenterCd == cmtMst1.CenterCd || string.IsNullOrEmpty(cmtMst1.CenterCd))
                                ? (cmtMst1?.CMT ?? string.Empty) : "不明";
                            var cmt2 = (cmtMst2 == null || kensaInf.CenterCd == cmtMst2.CenterCd || string.IsNullOrEmpty(cmtMst2.CenterCd))
                                ? (cmtMst2?.CMT ?? string.Empty) : "不明";

                            processedItem = new ListKensaInfDetailItemModel(
                                kensaDetail.PtId,
                                kensaDetail.IraiCd,
                                kensaDetail.RaiinNo,
                                kensaInf.IraiDate,
                                kensaDetail.SeqNo,
                                kensaDetail.SeqParentNo,
                                kensaMst?.KensaName ?? string.Empty,
                                kensaMst?.KensaKana ?? string.Empty,
                                kensaMst?.SortNo ?? 0,
                                kensaDetail.KensaItemCd ?? string.Empty,
                                kensaDetail.ResultVal ?? string.Empty,
                                kensaDetail.ResultType ?? string.Empty,
                                kensaDetail.AbnormalKbn ?? string.Empty,
                                kensaDetail.CmtCd1 ?? string.Empty,
                                kensaDetail.CmtCd2 ?? string.Empty,
                                cmt1,
                                cmt2,
                                kensaMst?.MaleStd ?? string.Empty,
                                kensaMst?.FemaleStd ?? string.Empty,
                                kensaMst?.MaleStdLow ?? string.Empty,
                                kensaMst?.FemaleStdLow ?? string.Empty,
                                kensaMst?.MaleStdHigh ?? string.Empty,
                                kensaMst?.FemaleStdHigh ?? string.Empty,
                                kensaMst?.Unit ?? string.Empty,
                                kensaInf.Nyubi ?? string.Empty,
                                kensaInf.Yoketu ?? string.Empty,
                                kensaInf.Bilirubin ?? string.Empty,
                                kensaInf.SikyuKbn,
                                kensaInf.TosekiKbn,
                                kensaInf.InoutKbn,
                                kensaInf.Status,
                                DeleteTypes.None,
                                kensaDetail.SeqGroupNo,
                                string.Empty,
                                kensaInf.KensaTime,
                                kensaInf.CenterCd ?? string.Empty
                            );
                        }
                        else
                        {
                            var centerKensaMst = commonCenterKensaMstDict.GetValueOrDefault(
                                new { CenterCd = kensaInf.CenterCd, KensaItemCd = kensaDetail.KensaItemCd });
                            var itemCenterStdMst = commonCenterStdMstDict.GetValueOrDefault(
                                new { CenterCd = kensaInf.CenterCd, KensaItemCd = kensaDetail.KensaItemCd });

                            processedItem = new ListKensaInfDetailItemModel(
                                kensaDetail.PtId,
                                kensaDetail.IraiCd,
                                kensaDetail.RaiinNo,
                                kensaInf.IraiDate,
                                kensaDetail.SeqNo,
                                kensaDetail.SeqParentNo,
                                centerKensaMst?.KensaName ?? string.Empty,
                                centerKensaMst?.KensaKana ?? string.Empty,
                                centerKensaMst?.SortNo ?? 0,
                                kensaDetail.KensaItemCd ?? string.Empty,
                                kensaDetail.ResultVal ?? string.Empty,
                                kensaDetail.ResultType ?? string.Empty,
                                kensaDetail.AbnormalKbn ?? string.Empty,
                                kensaDetail.CmtCd1 ?? string.Empty,
                                kensaDetail.CmtCd2 ?? string.Empty,
                                string.Empty,
                                string.Empty,
                                itemCenterStdMst?.MealStd ?? string.Empty,
                                itemCenterStdMst?.FemelStd ?? string.Empty,
                                itemCenterStdMst?.MealStdLow ?? string.Empty,
                                itemCenterStdMst?.FemelStdLow ?? string.Empty,
                                itemCenterStdMst?.MealStdHigh ?? string.Empty,
                                itemCenterStdMst?.FemelStdHigh ?? string.Empty,
                                centerKensaMst?.Unit ?? string.Empty,
                                kensaInf.Nyubi ?? string.Empty,
                                kensaInf.Yoketu ?? string.Empty,
                                kensaInf.Bilirubin ?? string.Empty,
                                kensaInf.SikyuKbn,
                                kensaInf.TosekiKbn,
                                kensaInf.InoutKbn,
                                kensaInf.Status,
                                DeleteTypes.None,
                                kensaDetail.SeqGroupNo,
                                string.Empty,
                                kensaInf.KensaTime,
                                kensaInf.CenterCd ?? string.Empty
                            );
                        }

                        processedData.Add(processedItem);
                    }
                }

                var kensaMstTemp = kensaMstDict.GetValueOrDefault(kensaItemCd);
                var centerKensa = commonCenterKensaMstDict.GetValueOrDefault(new { CenterCd = centerCd, KensaItemCd = kensaItemCd });
                var centerStdMst = commonCenterStdMstDict.GetValueOrDefault(new { CenterCd = centerCd, KensaItemCd = kensaItemCd });

                if (kensaMstTemp != null || centerKensa != null)
                {
                    var masterItem = new ListKensaInfDetailItemModel(
                        ptId,
                        0,
                        0,
                        0,
                        0,
                        0,
                        kensaMstTemp?.KensaName ?? centerKensa?.KensaName ?? string.Empty,
                        kensaMstTemp?.KensaKana ?? centerKensa?.KensaKana ?? string.Empty,
                        kensaMstTemp?.SortNo ?? centerKensa?.SortNo ?? 0,
                        kensaItemCd,
                        string.Empty, string.Empty, string.Empty, string.Empty, string.Empty,
                        string.Empty, string.Empty,
                        kensaMstTemp?.MaleStd ?? centerStdMst?.MealStd ?? string.Empty,
                        kensaMstTemp?.FemaleStd ?? centerStdMst?.FemelStd ?? string.Empty,
                        kensaMstTemp?.MaleStdLow ?? centerStdMst?.MealStdLow ?? string.Empty,
                        kensaMstTemp?.FemaleStdLow ?? centerStdMst?.FemelStdLow ?? string.Empty,
                        kensaMstTemp?.MaleStdHigh ?? centerStdMst?.MealStdHigh ?? string.Empty,
                        kensaMstTemp?.FemaleStdHigh ?? centerStdMst?.FemelStdHigh ?? string.Empty,
                        kensaMstTemp?.Unit ?? centerKensa?.Unit ?? string.Empty,
                        string.Empty, string.Empty, string.Empty,
                        0, 0, 0, 0,
                        DeleteTypes.None,
                        0,
                        string.Empty,
                        string.Empty,
                        centerCd
                    );

                    processedData.Add(masterItem);
                }
            }

            var sortedData = sortIraiDateAsc
                ? processedData.OrderBy(x => x.IraiDate.ToString() + x.KensaTime)
                : processedData.OrderByDescending(x => x.IraiDate.ToString() + x.KensaTime);

            var kensaInfDetailCol = sortedData
                .Where(x => x.IraiDate != 0 && x.IraiDate >= startDate && x.IraiDate <= endDate)
                .GroupBy(x => new { x.IraiCd, x.IraiDate, x.Nyubi, x.Yoketu, x.Bilirubin, x.SikyuKbn, x.TosekiKbn, x.KensaTime, x.CenterCd })
                .Select((group, index) => new KensaInfDetailColModel(
                    group.Key.IraiCd,
                    group.Key.IraiDate,
                    group.Key.Nyubi,
                    group.Key.Yoketu,
                    group.Key.Bilirubin,
                    group.Key.SikyuKbn,
                    group.Key.TosekiKbn,
                    index,
                    group.Key.KensaTime,
                    group.Key.CenterCd
                ))
                .DistinctBy(x => (x.KensaTime, x.IraiCd, x.IraiDate))
                .ToList();

            var kensaIraiCdSet = new HashSet<long>(kensaInfDetailCol.Select(item => item.IraiCd));

            IEnumerable<ListKensaInfDetailItemModel> data = sortedData;

            var kensaItemDuplicate = data.GroupBy(x => new { x.KensaItemCd, x.KensaName, x.Unit, x.MaleStd, x.FemaleStd, x.IraiCd }).SelectMany(group => group.Skip(1)).Select(x => x);

            var seqNos = new HashSet<long>(kensaItemDuplicate.Select(item => item.SeqNo));

            var kensaItemWithOutDuplicate = data.Where(x => seqNos.Contains(x.SeqNo)).GroupBy(x => new { x.KensaItemCd, x.SeqGroupNo })
                .Select(group =>
                {
                    var newItem = group.First();
                    newItem.SetRowSeqId(string.Join("-", group.Select(x => x.SeqNo)));
                    return newItem;
                })
                .ToList();

            var kensaItemCds = data.GroupBy(x => new { x.KensaItemCd, x.KensaName, x.Unit, x.MaleStd, x.FemaleStd, x.SeqNo, x.SeqGroupNo, x.CenterCd }).Select(x => new { x.Key.KensaItemCd, x.Key.KensaName, x.Key.Unit, x.Key.MaleStd, x.Key.FemaleStd, x.Key.SeqNo, x.Key.SeqGroupNo, x.Key.CenterCd });

            var kensaInfDetailData = new List<KensaInfDetailDataModel>();

            foreach (var kensaMstItem in kensaItemCds)
            {
                var dynamicArray = new List<ListKensaInfDetailItemModel>();

                foreach (var item in kensaInfDetailCol)
                {
                    var dynamicDataItem = data.Where(x => x.SeqGroupNo == kensaMstItem.SeqGroupNo && x.IraiCd == item.IraiCd && x.KensaItemCd == kensaMstItem.KensaItemCd && kensaMstItem.CenterCd == x.CenterCd).FirstOrDefault();

                    if (dynamicDataItem == null)
                    {
                        dynamicArray.Add(new ListKensaInfDetailItemModel(
                            ptId,
                            item.IraiCd
                        ));
                    }
                    else
                    {
                        dynamicArray.Add(dynamicDataItem);
                    }
                }

                var rowData = new KensaInfDetailDataModel(
                    kensaMstItem.KensaItemCd,
                    kensaMstItem.KensaName,
                    kensaMstItem.Unit,
                    kensaMstItem.MaleStd,
                    kensaMstItem.FemaleStd,
                    kensaMstItem.SeqNo,
                    dynamicArray,
                    kensaMstItem.CenterCd
                );

                kensaInfDetailData.Add(rowData);
            }

            var kensaInfDetailRows = new List<KensaInfDetailDataModel>();

            var groupRowDataItem = data
                .GroupBy(x => new { x.KensaItemCd, x.SeqGroupNo, x.CenterCd })
                .ToDictionary(
                    group =>
                    {
                        var newItem = group.First();
                        newItem.SetRowSeqId(string.Join("-", group.Select(x => x.SeqNo)));
                        return newItem;
                    },
                    group => group.ToList());

            foreach (var item in groupRowDataItem)
            {
                KensaInfDetailDataModel kensaInfDetailDataItem = null;

                if (item.Key.SeqNo != 0)
                {
                    kensaInfDetailDataItem = kensaInfDetailData.FirstOrDefault(x => x.SeqNo == item.Key.SeqNo);
                }
                else
                {
                    kensaInfDetailDataItem = kensaInfDetailData.FirstOrDefault(x =>
                        x.KensaItemCd == item.Key.KensaItemCd &&
                        x.CenterCd == item.Key.CenterCd);
                }

                var dspCenterName = item.Key.CenterCd == CommonConstants.InHospitalCenterCd
                    ? "院内"
                    : commonKensaCenterMstDict.GetValueOrDefault(item.Key.CenterCd)?.DspCenterName ?? string.Empty;

                var rowData = new KensaInfDetailDataModel(
                    kensaInfDetailDataItem?.KensaItemCd ?? item.Key.KensaItemCd ?? string.Empty,
                    kensaInfDetailDataItem?.KensaName ?? item.Key.KensaName ?? string.Empty,
                    kensaInfDetailDataItem?.Unit ?? string.Empty,
                    kensaInfDetailDataItem?.MaleStd ?? string.Empty,
                    kensaInfDetailDataItem?.FemaleStd ?? string.Empty,
                    item.Key.IraiDate,
                    item.Key.KensaKana,
                    item.Key.SortNo,
                    item.Key.SeqNo,
                    item.Key.SeqParentNo,
                    item.Key.RowSeqId,
                    kensaInfDetailDataItem?.DynamicArray ?? new(),
                    item.Key.KensaTime,
                    item.Key.CenterCd,
                    dspCenterName
                );
                kensaInfDetailRows.Add(rowData);
            }

            kensaInfDetailRows = kensaInfDetailRows
                .GroupBy(x => new { x.KensaItemCd, x.CenterCd })
                .Select(g => g.OrderByDescending(x => x.IraiDate).First())
                .OrderBy(x => x.KensaName)
                .ToList();

            var kensaInfDetailRowItem = new List<KensaInfDetailDataModel>();

            if (setId == 0)
            {
                var sortColumn = userConfDict.GetValueOrDefault(new { GrpItemCd = 1, GrpItemEdaNo = 0 });
                var sortType = userConfDict.GetValueOrDefault(new { GrpItemCd = 1, GrpItemEdaNo = 1 });

                Func<List<KensaInfDetailDataModel>, List<KensaInfDetailDataModel>> sortFunc = sortColumn switch
                {
                    SortKensaMstColumn.KensaItemCd => data => sortType == 1
                        ? data.OrderByDescending(x => x.KensaItemCd).ToList()
                        : data.OrderBy(x => x.KensaItemCd).ToList(),
                    SortKensaMstColumn.KensaKana => data => sortType == 1
                        ? data.OrderByDescending(x => x.KensaKana).ToList()
                        : data.OrderBy(x => x.KensaKana).ToList(),
                    _ => data => sortType == 1
                        ? data.OrderByDescending(x => x.SortNo).ToList()
                        : data.OrderBy(x => x.SortNo).ToList()
                };

                // Get parent items and sort them
                var parentItems = kensaInfDetailRows.Where(x => x.SeqParentNo == 0).ToList();
                kensaInfDetailRowItem = sortFunc(parentItems);

                // Handle children items
                var childrenItems = kensaInfDetailRows.Where(x => x.SeqParentNo > 0).ToList();
                var childrenLookup = childrenItems
                    .GroupBy(x => x.SeqParentNo)
                    .ToDictionary(g => g.Key, g => g.ToList());

                var finalRows = new List<KensaInfDetailDataModel>();
                foreach (var item in kensaInfDetailRowItem)
                {
                    finalRows.Add(item);

                    var itemSeqNos = item.RowSeqId.Split('-').Where(x => !string.IsNullOrEmpty(x)).Select(long.Parse).ToList();
                    var relevantChildren = new List<KensaInfDetailDataModel>();

                    foreach (var seqNo in itemSeqNos)
                    {
                        if (childrenLookup.TryGetValue(seqNo, out var children))
                        {
                            relevantChildren.AddRange(children);
                        }
                    }

                    if (relevantChildren.Any())
                    {
                        if (relevantChildren.Count > 1)
                        {
                            relevantChildren = sortFunc(relevantChildren);
                        }
                        finalRows.AddRange(relevantChildren);
                    }
                }

                kensaInfDetailRowItem = finalRows;
            }
            else
            {
                var kensasetDetail = NoTrackingDataContext.KensaSetDetails
                    .Where(x => x.HpId == hpId && x.SetId == setId && x.IsDeleted == DeleteTypes.None)
                    .OrderBy(x => x.SortNo)
                    .ToList();

                var kensaDataLookup = kensaInfDetailRows
                    .GroupBy(x => x.KensaItemCd)
                    .ToDictionary(g => g.Key, g => g.ToList());

                foreach (var currentItemSet in kensasetDetail)
                {
                    var lastItemSet = kensasetDetail.LastOrDefault(x => x.KensaItemCd == currentItemSet.KensaItemCd);

                    if (currentItemSet == lastItemSet)
                    {
                        if (kensaDataLookup.TryGetValue(currentItemSet.KensaItemCd, out var listRow) && listRow.Count > 0)
                        {
                            kensaInfDetailRowItem.AddRange(listRow);
                        }
                        else
                        {
                            var duplicateRow = kensaInfDetailRowItem.LastOrDefault(x => x.KensaItemCd == currentItemSet.KensaItemCd);
                            if (duplicateRow != null)
                            {
                                kensaInfDetailRowItem.Add(duplicateRow);
                            }
                        }
                    }
                    else
                    {
                        if (kensaDataLookup.TryGetValue(currentItemSet.KensaItemCd, out var rows) && rows.Count > 0)
                        {
                            var row = rows.First();
                            kensaInfDetailRowItem.Add(row);
                            rows.Remove(row);
                        }
                        else
                        {
                            var duplicateRow = kensaInfDetailRowItem.LastOrDefault(x => x.KensaItemCd == currentItemSet.KensaItemCd);
                            if (duplicateRow != null)
                            {
                                kensaInfDetailRowItem.Add(duplicateRow);
                            }
                        }
                    }
                }
            }

            //print Report
            List<string> itemName = kensaInfDetailRowItem.Select(x => x.KensaName).ToList();

            List<CoKensaResultMultiModel> result = new();
            List<KensaResultMultiItem> kensaResultMultiItems = new();
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId);

            foreach (var item in kensaInfDetailRowItem)
            {
                switch (ptInf?.Sex)
                {
                    case 1: result.Add(new CoKensaResultMultiModel(item.IraiDate, item.KensaName, item.Unit, item.MaleStd, new(), new(), item.SeqParentNo, item.RowSeqId, item.KensaTime, null, item.DspCenterName)); break;
                    case 2: result.Add(new CoKensaResultMultiModel(item.IraiDate, item.KensaName, item.Unit, item.FemaleStd, new(), new(), item.SeqParentNo, item.RowSeqId, item.KensaTime, null, item.DspCenterName)); break;
                }

            }

            foreach (var item in kensaInfDetailRowItem)
            {
                foreach (var kensaResultMultiItem in item.DynamicArray)
                {
                    kensaResultMultiItems.Add(new KensaResultMultiItem(kensaResultMultiItem.IraiDate, kensaResultMultiItem.ResultVal, kensaResultMultiItem.AbnormalKbn, kensaResultMultiItem.ResultType, kensaResultMultiItem.KensaTime));
                }
            }

            List<long> date = new();
            List<string> dateTime = new();

            if (itemName.Count > 0 && kensaInfDetailCol.Count > 0)
            {
                int columnCount = kensaInfDetailCol.Count;
                int j = 0;

                for (int i = 0; i < result.Count; i++)
                {
                    for (int k = 0; k < columnCount; k++)
                    {
                        if (j < kensaResultMultiItems.Count)
                        {
                            result[i].KensaResultMultiItems.Add(kensaResultMultiItems[j++]);
                        }
                    }
                }
            }

            date.AddRange(kensaInfDetailCol.Select(x => x.IraiDate).Where(x => x != 0).ToList());
            dateTime.AddRange(kensaInfDetailCol.Select(x => x.IraiDate.ToString() + x.KensaTime).ToList());

            bool sortDateAsc = userConfDict.GetValueOrDefault(new { GrpItemCd = 0, GrpItemEdaNo = 0 }) == 0 ||
                              userConfDict.GetValueOrDefault(new { GrpItemCd = 0, GrpItemEdaNo = 0 }) == null;

            if (sortDateAsc)
            {
                date = date.OrderBy(x => x).ToList();
                dateTime = dateTime.OrderBy(x => x).ToList();
            }
            else
            {
                date = date.OrderByDescending(x => x).ToList();
                dateTime = dateTime.OrderByDescending(x => x).ToList();
            }

            result.Add(new CoKensaResultMultiModel(0, "", "", "", new(), date, 0, "", "", dateTime));

            return (result, date, dateTime);
        }

        public ListKensaInfDetailModel GetListKensaInf(int hpId, int userId, long ptId, int setId, int iraiCdStart, bool getGetPrevious, bool showAbnormalKbn, List<string> kensaItemCdList, int startDate, int iraiCd = 0)
        {
            var userConfDict = NoTrackingDataContext.UserConfs
                .Where(x => x.UserId == userId && x.HpId == hpId && x.GrpCd == 1002)
                .ToDictionary(x => new { x.GrpItemCd, x.GrpItemEdaNo }, x => x.Val);

            var kensaSetDetailDict = NoTrackingDataContext.KensaSetDetails
                .Where(x => x.SetId == setId && x.HpId == hpId && x.IsDeleted == DeleteTypes.None)
                .GroupBy(item => item.KensaItemCd)
                .ToDictionary(
                    group => group.Key,
                    group => group.Min(item => item.SortNo)
                );

            var kensaMstDict = NoTrackingDataContext.KensaMsts
                .Where(x => x.HpId == hpId && x.IsDelete == DeleteTypes.None)
                .ToDictionary(x => new { x.CenterCd, x.KensaItemCd }, x => x);

            var kensaCmtMstDict = NoTrackingDataContext.KensaCmtMsts
                .Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None)
                .ToDictionary(x => x.CmtCd, x => x);

            var commonCenterKensaMstDict = NoTrackingDataContext.CommonCenterKensaMst
                .ToDictionary(x => new { x.CenterCd, x.KensaItemCd }, x => x);

            var commonKensaCenterMstDict = NoTrackingDataContext.CommonKensaCenterMst
                .ToDictionary(x => x.CenterCd, x => x);

            var commonCenterStdMstDict = NoTrackingDataContext.CommonCenterStdMst
                .ToDictionary(x => new { x.CenterCd, x.KensaItemCd }, x => x);

            bool sortIraiDateAsc = userConfDict.GetValueOrDefault(new { GrpItemCd = 0, GrpItemEdaNo = 0 }) != 1;

            IQueryable<KensaInfDetail> baseKensaQuery = NoTrackingDataContext.KensaInfDetails
                .Where(x => x.HpId == hpId &&
                            x.PtId == ptId &&
                            x.IsDeleted == DeleteTypes.None &&
                            !string.IsNullOrEmpty(x.KensaItemCd));

            if (startDate > 0)
            {
                baseKensaQuery = baseKensaQuery.Where(x => x.IraiDate >= startDate);
            }

            if (setId == 0)
            {
                baseKensaQuery = baseKensaQuery.Where(x => x.IraiCd == iraiCd);

                if (kensaItemCdList.Any())
                {
                    baseKensaQuery = baseKensaQuery.Where(x => kensaItemCdList.Contains(x.KensaItemCd));
                }
            }
            else
            {
                // Flter data with KensaSet
                var kensaItemCodes = kensaSetDetailDict.Keys.ToList();
                baseKensaQuery = baseKensaQuery.Where(x => kensaItemCodes.Contains(x.KensaItemCd));

                if (kensaItemCdList.Any())
                {
                    baseKensaQuery = baseKensaQuery.Where(x => kensaItemCdList.Contains(x.KensaItemCd));
                }

                baseKensaQuery = baseKensaQuery.Where(x => x.IraiCd == iraiCd);
            }

            var combinedData = (from kensaDetail in baseKensaQuery
                                join kensaInf in NoTrackingDataContext.KensaInfs
                                    on new { kensaDetail.HpId, kensaDetail.PtId, kensaDetail.IraiCd }
                                    equals new { kensaInf.HpId, kensaInf.PtId, kensaInf.IraiCd }
                                join ptInf in NoTrackingDataContext.PtInfs
                                    on new { kensaDetail.PtId, kensaDetail.HpId }
                                    equals new { ptInf.PtId, ptInf.HpId }
                                where kensaInf.IsDeleted == DeleteTypes.None &&
                                      ptInf.IsDelete == DeleteTypes.None
                                select new
                                {
                                    KensaDetail = kensaDetail,
                                    KensaInf = kensaInf,
                                    PtInf = ptInf
                                }).ToList();

            var processedData = new List<ListKensaInfDetailItemModel>();

            foreach (var item in combinedData)
            {
                var kensaDetail = item.KensaDetail;
                var kensaInf = item.KensaInf;
                var isInternal = kensaInf.CenterCd == CommonConstants.InHospitalCenterCd;

                ListKensaInfDetailItemModel processedItem;

                if (isInternal)
                {
                    var kensaMst = kensaMstDict.GetValueOrDefault(new { CenterCd = kensaInf.CenterCd, KensaItemCd = kensaDetail.KensaItemCd });
                    var cmtMst1 = kensaCmtMstDict.GetValueOrDefault(kensaDetail.CmtCd1);
                    var cmtMst2 = kensaCmtMstDict.GetValueOrDefault(kensaDetail.CmtCd2);

                    var cmt1 = (cmtMst1 == null || kensaInf.CenterCd == cmtMst1.CenterCd || string.IsNullOrEmpty(cmtMst1.CenterCd))
                        ? (cmtMst1?.CMT ?? string.Empty) : "不明";
                    var cmt2 = (cmtMst2 == null || kensaInf.CenterCd == cmtMst2.CenterCd || string.IsNullOrEmpty(cmtMst2.CenterCd))
                        ? (cmtMst2?.CMT ?? string.Empty) : "不明";

                    processedItem = new ListKensaInfDetailItemModel(
                        kensaDetail.PtId,
                        kensaDetail.IraiCd,
                        kensaDetail.RaiinNo,
                        kensaInf.IraiDate,
                        kensaDetail.SeqNo,
                        kensaDetail.SeqParentNo,
                        kensaMst?.KensaName ?? string.Empty,
                        kensaMst?.KensaKana ?? string.Empty,
                        kensaMst?.SortNo ?? 0,
                        kensaDetail.KensaItemCd ?? string.Empty,
                        kensaDetail.ResultVal ?? string.Empty,
                        kensaDetail.ResultType ?? string.Empty,
                        kensaDetail.AbnormalKbn ?? string.Empty,
                        kensaDetail.CmtCd1 ?? string.Empty,
                        kensaDetail.CmtCd2 ?? string.Empty,
                        cmt1,
                        cmt2,
                        kensaMst?.MaleStd ?? string.Empty,
                        kensaMst?.FemaleStd ?? string.Empty,
                        kensaMst?.MaleStdLow ?? string.Empty,
                        kensaMst?.FemaleStdLow ?? string.Empty,
                        kensaMst?.MaleStdHigh ?? string.Empty,
                        kensaMst?.FemaleStdHigh ?? string.Empty,
                        kensaMst?.Unit ?? string.Empty,
                        kensaInf.Nyubi ?? string.Empty,
                        kensaInf.Yoketu ?? string.Empty,
                        kensaInf.Bilirubin ?? string.Empty,
                        kensaInf.SikyuKbn,
                        kensaInf.TosekiKbn,
                        kensaInf.InoutKbn,
                        kensaInf.Status,
                        DeleteTypes.None,
                        kensaDetail.SeqGroupNo,
                        string.Empty,
                        kensaInf.KensaTime
                    );
                }
                else
                {
                    var centerKensaMst = commonCenterKensaMstDict.GetValueOrDefault(
                        new { CenterCd = kensaInf.CenterCd, KensaItemCd = kensaDetail.KensaItemCd });
                    var centerStdMst = commonCenterStdMstDict.GetValueOrDefault(
                        new { CenterCd = kensaInf.CenterCd, KensaItemCd = kensaDetail.KensaItemCd });

                    processedItem = new ListKensaInfDetailItemModel(
                        kensaDetail.PtId,
                        kensaDetail.IraiCd,
                        kensaDetail.RaiinNo,
                        kensaInf.IraiDate,
                        kensaDetail.SeqNo,
                        kensaDetail.SeqParentNo,
                        centerKensaMst?.KensaName ?? string.Empty,
                        centerKensaMst?.KensaKana ?? string.Empty,
                        centerKensaMst?.SortNo ?? 0,
                        kensaDetail.KensaItemCd ?? string.Empty,
                        kensaDetail.ResultVal ?? string.Empty,
                        kensaDetail.ResultType ?? string.Empty,
                        kensaDetail.AbnormalKbn ?? string.Empty,
                        kensaDetail.CmtCd1 ?? string.Empty,
                        kensaDetail.CmtCd2 ?? string.Empty,
                        string.Empty,
                        string.Empty,
                        centerStdMst?.MealStd ?? string.Empty,
                        centerStdMst?.FemelStd ?? string.Empty,
                        centerStdMst?.MealStdLow ?? string.Empty,
                        centerStdMst?.FemelStdLow ?? string.Empty,
                        centerStdMst?.MealStdHigh ?? string.Empty,
                        centerStdMst?.FemelStdHigh ?? string.Empty,
                        centerKensaMst?.Unit ?? string.Empty,
                        kensaInf.Nyubi ?? string.Empty,
                        kensaInf.Yoketu ?? string.Empty,
                        kensaInf.Bilirubin ?? string.Empty,
                        kensaInf.SikyuKbn,
                        kensaInf.TosekiKbn,
                        kensaInf.InoutKbn,
                        kensaInf.Status,
                        DeleteTypes.None,
                        kensaDetail.SeqGroupNo,
                        string.Empty,
                        kensaInf.KensaTime
                    );
                }

                // Combine the results
                processedData.Add(processedItem);
            }

            #region Get Col dynamic

            // Sort col by IraiDate
            var sortedData = sortIraiDateAsc
                ? processedData.OrderBy(x => x.IraiDate)
                : processedData.OrderByDescending(x => x.IraiDate);

            var kensaInfDetailCol = sortedData
                .GroupBy(x => new { x.IraiCd, x.IraiDate, x.Nyubi, x.Yoketu, x.Bilirubin, x.SikyuKbn, x.TosekiKbn, x.KensaTime })
                .Select((group, index) => new KensaInfDetailColModel(
                    group.Key.IraiCd,
                    group.Key.IraiDate,
                    group.Key.Nyubi,
                    group.Key.Yoketu,
                    group.Key.Bilirubin,
                    group.Key.SikyuKbn,
                    group.Key.TosekiKbn,
                    index,
                    group.Key.KensaTime
                ))
                .ToList();

            var totalCol = kensaInfDetailCol.Count();

            #endregion

            #region Get Row dynamic
            // Filter data with col
            var kensaIraiCdSet = new HashSet<long>(kensaInfDetailCol.Select(item => item.IraiCd));
            var filteredData = processedData.Where(x => kensaIraiCdSet.Contains(x.IraiCd)).ToList();

            var groupRowData = filteredData
                .GroupBy(x => new { x.KensaItemCd, x.SeqGroupNo })
                .Select(group => {
                    var firstItem = group.First();
                    var rowSeqId = string.Join("-", group.Select(x => x.SeqNo));
                    firstItem.SetRowSeqId(rowSeqId);
                    return new
                    {
                        Key = firstItem,
                        Values = group.ToList()
                    };
                })
                .ToList();

            var kensaInfDetailData = groupRowData
                .Select(item => new KensaInfDetailDataModel(
                    item.Key.KensaItemCd,
                    item.Key.KensaName,
                    item.Key.Unit,
                    item.Key.MaleStd,
                    item.Key.FemaleStd,
                    item.Key.KensaKana,
                    item.Key.SortNo,
                    item.Key.SeqNo,
                    item.Key.SeqParentNo,
                    item.Key.RowSeqId,
                    item.Values
                ))
                .ToList();

            // Sort row by user config
            List<KensaInfDetailDataModel> kensaInfDetailRows = new List<KensaInfDetailDataModel>();

            if (setId == 0)
            {
                var sortColumn = userConfDict.GetValueOrDefault(new { GrpItemCd = 1, GrpItemEdaNo = 0 });
                var sortType = userConfDict.GetValueOrDefault(new { GrpItemCd = 1, GrpItemEdaNo = 1 });

                Func<List<KensaInfDetailDataModel>, List<KensaInfDetailDataModel>> sortFunc = sortColumn switch
                {
                    SortKensaMstColumn.KensaItemCd => data => sortType == 1
                        ? data.OrderByDescending(x => x.KensaItemCd).ToList()
                        : data.OrderBy(x => x.KensaItemCd).ToList(),
                    SortKensaMstColumn.KensaKana => data => sortType == 1
                        ? data.OrderByDescending(x => x.KensaKana).ToList()
                        : data.OrderBy(x => x.KensaKana).ToList(),
                    _ => data => sortType == 1
                        ? data.OrderByDescending(x => x.SortNo).ToList()
                        : data.OrderBy(x => x.SortNo).ToList()
                };

                // Get all parent item
                var parentItems = kensaInfDetailData.Where(x => x.SeqParentNo == 0).ToList();
                kensaInfDetailRows = sortFunc(parentItems);

                // Children item
                var childrenItems = kensaInfDetailData.Where(x => x.SeqParentNo > 0).ToList();
                var childrenLookup = childrenItems
                    .GroupBy(x => x.SeqParentNo)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // Append childrends
                var finalRows = new List<KensaInfDetailDataModel>();
                foreach (var item in kensaInfDetailRows)
                {
                    finalRows.Add(item);

                    var itemSeqNos = item.RowSeqId.Split('-').Select(int.Parse).ToList();
                    var relevantChildren = new List<KensaInfDetailDataModel>();

                    foreach (var seqNo in itemSeqNos)
                    {
                        if (childrenLookup.TryGetValue(seqNo, out var children))
                        {
                            relevantChildren.AddRange(children);
                        }
                    }

                    if (relevantChildren.Any())
                    {
                        if (relevantChildren.Count > 1)
                        {
                            relevantChildren = sortFunc(relevantChildren);
                        }
                        finalRows.AddRange(relevantChildren);
                    }
                }

                kensaInfDetailRows = finalRows;
            }
            // Filter row by KensaSet
            else
            {
                var kensasetDetailList = NoTrackingDataContext.KensaSetDetails
                    .Where(x => x.HpId == hpId && x.SetId == setId && x.IsDeleted == DeleteTypes.None)
                    .OrderBy(x => x.SortNo)
                    .ToList();

                var kensaDataLookup = kensaInfDetailData
                    .GroupBy(x => x.KensaItemCd)
                    .ToDictionary(g => g.Key, g => g.ToList());

                foreach (var currentItemSet in kensasetDetailList)
                {
                    var lastItemSet = kensasetDetailList.LastOrDefault(x => x.KensaItemCd == currentItemSet.KensaItemCd);

                    if (currentItemSet == lastItemSet)
                    {
                        if (kensaDataLookup.TryGetValue(currentItemSet.KensaItemCd, out var listRow) && listRow.Count > 0)
                        {
                            kensaInfDetailRows.AddRange(listRow);
                        }
                        else
                        {
                            var duplicateRow = kensaInfDetailRows.LastOrDefault(x => x.KensaItemCd == currentItemSet.KensaItemCd);
                            if (duplicateRow != null)
                            {
                                kensaInfDetailRows.Add(duplicateRow);
                            }
                        }
                    }
                    else
                    {
                        if (kensaDataLookup.TryGetValue(currentItemSet.KensaItemCd, out var rows) && rows.Count > 0)
                        {
                            var row = rows.First();
                            kensaInfDetailRows.Add(row);
                            rows.Remove(row);
                        }
                        else
                        {
                            var duplicateRow = kensaInfDetailRows.LastOrDefault(x => x.KensaItemCd == currentItemSet.KensaItemCd);
                            if (duplicateRow != null)
                            {
                                kensaInfDetailRows.Add(duplicateRow);
                            }
                        }
                    }
                }
            }
            #endregion

            var result = new ListKensaInfDetailModel(kensaInfDetailCol.ToList(), kensaInfDetailRows.OrderBy(x => x.KensaName).ToList(), totalCol);

            return result;
        }
    }
}
