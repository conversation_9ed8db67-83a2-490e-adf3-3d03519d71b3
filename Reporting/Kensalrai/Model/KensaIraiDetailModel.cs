﻿using Entity.Tenant;

namespace Reporting.Kensalrai.Model
{
    public class KensaIraiDetailModel
    {
        public KensaMst KensaMst { get; } = null;

        public TenMst TenMst { get; } = null;

        public CommonCenterKensaMst? CommonCenterKensaMst { get; } = null;

        public KensaIraiDetailModel(bool isSelected, long rpNo, long rpEdaNo, int rowNo, int seqNo, KensaMst kensaMst)
        {
            IsSelected = isSelected;
            RpNo = rpNo;
            RpEdaNo = rpEdaNo;
            RowNo = rowNo;
            SeqNo = seqNo;
            KensaMst = kensaMst;
            CenterItemCd = string.Empty;
        }

        public KensaIraiDetailModel(bool isSelected, long rpNo, long rpEdaNo, int rowNo, int seqNo, CommonCenterKensaMst commonCenterKensaMst, string centerCd)
        {
            IsSelected = isSelected;
            RpNo = rpNo;
            RpEdaNo = rpEdaNo;
            RowNo = rowNo;
            SeqNo = seqNo;
            CommonCenterKensaMst = commonCenterKensaMst;
            CenterItemCd = string.Empty;
            CenterCdKensaIrai = centerCd;
        }

        public KensaIraiDetailModel(bool isSelected, long rpNo, long rpEdaNo, int rowNo, int seqNo, KensaMst kensaMst, string centerItemCd, string centerKey, string itemName)
        {
            IsSelected = isSelected;
            RpNo = rpNo;
            RpEdaNo = rpEdaNo;
            RowNo = rowNo;
            SeqNo = seqNo;
            KensaMst = kensaMst;
            CenterItemCd = centerItemCd;
            CenterKey = centerKey;
            OdrInfDetailItemName = itemName;
        }

        public KensaIraiDetailModel(bool isSelected, long rpNo, long rpEdaNo, int rowNo, int seqNo, TenMst tenMst, KensaMst kensaMst)
        {
            IsSelected = isSelected;
            RpNo = rpNo;
            RpEdaNo = rpEdaNo;
            RowNo = rowNo;
            SeqNo = seqNo;
            TenMst = tenMst;
            KensaMst = kensaMst == null ? new KensaMst() : kensaMst;
            CenterItemCd = string.Empty;
        }

        public string CenterKey { get; private set; }

        public string TenKensaItemCd
        {
            get => TenMst?.KensaItemCd;
        }

        public string ItemCd
        {
            get => TenMst.ItemCd;
        }

        public string ItemName
        {
            get => TenMst.Name;
        }

        public string KanaName1
        {
            get => TenMst.KanaName1;
        }

        public string CenterCd
        {
            get => KensaMst != null ? KensaMst.CenterCd : (CommonCenterKensaMst != null ? CommonCenterKensaMst.CenterCd : string.Empty);
        }

        public string KensaItemCd
        {
            get { return KensaMst != null ? KensaMst.KensaItemCd : (CommonCenterKensaMst != null ? CommonCenterKensaMst.KensaItemCd : string.Empty); }
        }

        public string CenterItemCd { get; private set; }

        public string KensaKana
        {
            get { return KensaMst != null ? KensaMst.KensaKana : (CommonCenterKensaMst != null ? CommonCenterKensaMst.KensaKana : string.Empty); }
        }
        public string KensaName
        {
            get { return KensaMst != null ? KensaMst.KensaName : (CommonCenterKensaMst != null ? CommonCenterKensaMst.KensaName : string.Empty); ; }
        }
        public long ContainerCd
        {
            get { return KensaMst != null ? KensaMst.ContainerCd : 0; }
        }
        /// <summary>
        /// 容器名
        /// </summary>
        public string ContainerName { get; set; } = "";
        /// <summary>
        /// 選択されているかどうか
        /// </summary>
        public bool IsSelected { get; set; } = true;
        /// <summary>
        /// RpNo（ODR_INF_DETAILとのリレーション用）
        /// </summary>
        public long RpNo { get; set; } = 0;
        /// <summary>
        /// RpEdaNo（ODR_INF_DETAILとのリレーション用）
        /// </summary>
        public long RpEdaNo { get; set; } = 0;
        /// <summary>
        /// RowNo（ODR_INF_DETAILとのリレーション用）
        /// </summary>
        public int RowNo { get; set; } = 0;
        /// <summary>
        /// シーケンス。ソートに使用する番号
        /// </summary>
        public int SeqNo { get; set; } = 0;

        public string CenterCdKensaIrai { get; set; }

        public bool CheckDefaultValue()
        {
            return string.IsNullOrEmpty(ItemCd) && string.IsNullOrEmpty(ItemName) && string.IsNullOrEmpty(KanaName1);
        }

        public string OdrInfDetailItemName {  get; private set; }
    }
}
