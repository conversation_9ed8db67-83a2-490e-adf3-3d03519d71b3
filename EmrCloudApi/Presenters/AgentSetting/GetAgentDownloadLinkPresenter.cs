using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.AgentSetting;
using UseCase.AgentSetting.GetAgentDownloadLink;

namespace EmrCloudApi.Presenters.AgentSetting
{
    public class GetAgentDownloadLinkPresenter
    {
        public Response<GetAgentDownloadLinkResponse> Result { get; private set; } = new();

        public void Complete(GetAgentDownloadLinkOutputData output)
        {
            Result.Data = new GetAgentDownloadLinkResponse(output.DownloadUrl, output.FileName);
            Result.Message = GetMessage(output.Status);
            Result.Status = (int)output.Status;
        }

        private string GetMessage(GetAgentDownloadLinkStatus status) => status switch
        {
            GetAgentDownloadLinkStatus.Success => ResponseMessage.Success,
            GetAgentDownloadLinkStatus.Failed => ResponseMessage.Failed,
            GetAgentDownloadLinkStatus.InvalidDeviceType => "Invalid device type. Must be 'win' or 'mac'.",
            GetAgentDownloadLinkStatus.FileNotFound => "Agent file not found in S3.",
            GetAgentDownloadLinkStatus.NotImplemented => "Device type not implemented yet.",
            _ => string.Empty
        };
    }
}
