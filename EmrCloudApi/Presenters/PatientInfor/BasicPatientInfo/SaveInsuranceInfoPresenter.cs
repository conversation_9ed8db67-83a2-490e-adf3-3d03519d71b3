﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.PatientInfor.BasicPatientInfo;
using UseCase.PatientInfor.Save;
using UseCase.PatientInfor.SaveBasicInfo.SaveInsuranceInfo;

namespace EmrCloudApi.Presenters.PatientInfor.BasicPatientInfo
{
    public class SaveInsuranceInfoPresenter : ISaveInsuranceInfoOutputPort
    {
        public Response<SaveInsuranceInfoResponse> Result { get; private set; } = new Response<SaveInsuranceInfoResponse>();

        private string GetMessage(SavePatientInfoStatus status) => status switch
        {
            SavePatientInfoStatus.Successful => ResponseMessage.Success,
            SavePatientInfoStatus.Failed => ResponseMessage.Failed,
            _ => string.Empty
        };

        public void Complete(SaveInsuranceInfoOutputData outputData)
        {
            Result.Data = new SaveInsuranceInfoResponse(outputData.ValidateDetails, outputData.Status, outputData.PtID, outputData.HokenId, outputData.OnlineConfirmationHisId, outputData.PatientInforModel);
            Result.Status = (int)outputData.Status;
            Result.Message = GetMessage(outputData.Status);
        }
    }
}
