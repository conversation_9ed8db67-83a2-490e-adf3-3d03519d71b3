﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.Reception;
using UseCase.Reception.SaveCombineBill;

namespace EmrCloudApi.Presenters.Reception
{
    public class SaveCombineSplitBillPresenter : ISaveCombineBillOutputPort
    {
        public Response<CombineSplitBillResponse> Result { get; private set; } = default!;

        public void Complete(SaveCombineBillOutputData outputData)
        {
            Result = new Response<CombineSplitBillResponse>()
            {
                Data = new CombineSplitBillResponse(outputData.Status == SaveCombineBillStatus.Success),
                Status = (byte)outputData.Status
            };
            switch (outputData.Status)
            {
                case SaveCombineBillStatus.Success:
                    Result.Message = ResponseMessage.Success;
                    break;
                case SaveCombineBillStatus.ErrorDeleted:
                    Result.Message = ResponseMessage.ErrorDeleted;
                    break;
                case SaveCombineBillStatus.ErrStatus:
                    Result.Message = ResponseMessage.ErrStatus;
                    break;
                default:
                    Result.Message = ResponseMessage.SaveCombineFail;
                    break;
            }
        }
    }
}
