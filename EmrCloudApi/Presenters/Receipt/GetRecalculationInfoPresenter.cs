﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.Receipt;
using UseCase.Receipt.CancelProccessCaculation;
using UseCase.ReceiptCheck.GetRecalculationInfo;

namespace EmrCloudApi.Presenters.Receipt
{
    public class GetRecalculationInfoPresenter : IGetRecalculationInfoOutputPort
    {
        public Response<GetRecalculationInfoResponse> Result { get; private set; } = new();
        public void Complete(GetRecalculationInfoOutputData outputData)
        {
            Result.Data = new GetRecalculationInfoResponse(outputData.ProcessInfo);
            Result.Message = GetMessage(outputData.Status);
            Result.Status = (int)outputData.Status;
        }

        private string GetMessage(GetRecalculationInfoStatus status) => status switch
        {
            GetRecalculationInfoStatus.Successed => ResponseMessage.Success,
            GetRecalculationInfoStatus.NotFound => ResponseMessage.NotFound,
            GetRecalculationInfoStatus.Failed => ResponseMessage.Failed,
            _ => string.Empty
        };
    }
}
