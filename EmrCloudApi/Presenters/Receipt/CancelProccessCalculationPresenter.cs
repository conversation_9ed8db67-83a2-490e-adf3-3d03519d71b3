﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.Receipt;
using UseCase.Receipt.CancelProccessCaculation;

namespace EmrCloudApi.Presenters.Receipt
{
    public class CancelProccessCalculationPresenter : ICancelProccessCalculationOutputPort
    {
        public Response<CancelProccessCalculationResponse> Result { get; private set; } = new();
        public void Complete(CancelProccessCalculationOutputData outputData)
        {
            Result.Message = GetMessage(outputData.Status);
            Result.Status = (int)outputData.Status;
        }

        private string GetMessage(CancelProccessCalculationStatus status) => status switch
        {
            CancelProccessCalculationStatus.Successed => ResponseMessage.Success,
            CancelProccessCalculationStatus.NotFound => ResponseMessage.NotFound,
            CancelProccessCalculationStatus.Failed => ResponseMessage.Failed,
            _ => string.Empty
        };
    }
}
