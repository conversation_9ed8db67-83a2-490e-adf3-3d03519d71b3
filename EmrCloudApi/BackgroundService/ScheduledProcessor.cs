﻿using NCrontab;
namespace EmrCloudApi.BackgroundService
{
    public abstract class ScheduledProcessor : ScopedProcessor
    {
        private CrontabSchedule _schedule;
        private DateTime _nextRun;
        protected abstract string Schedule { get; }
        public ScheduledProcessor(IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
        {
        }

        private void InitializeSchedule()
        {
            if (_schedule == null)
            {
                _schedule = CrontabSchedule.Parse(Schedule);
                _nextRun = _schedule.GetNextOccurrence(DateTime.Now);
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            InitializeSchedule();

            do
            {
                var now = DateTime.Now;
                if (now > _nextRun)
                {
                    await Process();
                    _nextRun = _schedule.GetNextOccurrence(DateTime.Now);
                }
                await Task.Delay(5000, stoppingToken); // 5 seconds delay
            } while (!stoppingToken.IsCancellationRequested);
        }
    }
}