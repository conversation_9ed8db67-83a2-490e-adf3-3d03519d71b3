﻿namespace Domain.Models.Recalculation;
public class RecalculationRequest
{
    public bool IsRecalculationCheckBox { get; set; }

    public bool IsReceiptAggregationCheckBox { get; set; }

    public bool IsCheckErrorCheckBox { get; set; }

    public int SinYm { get; set; }

    public List<PtItem> PtIdList { get; set; } = new();

    public int IsOperator { get; set; }

    public string OperatorName { get; set; } = string.Empty;

    public string ProccessKey { get; set; } = string.Empty;
}
