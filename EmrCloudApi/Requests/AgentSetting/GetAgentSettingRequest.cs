﻿namespace EmrCloudApi.Requests.AgentSetting
{
    public class GetAgentSettingRequest
    {
        public string Host { get; set; } = string.Empty;

        /// <summary>
        /// Default is true. If not specified, the API will return the settings information.
        /// </summary>
        public bool IncludeSetting { get; set; } = true;

        /// <summary>
        /// Default is false. If not specified, the API will not return the version information.
        /// </summary>
        public bool IncludeVersion { get; set; } = false;

        public string FileNameApp { get; set; } = string.Empty;
    }
}
