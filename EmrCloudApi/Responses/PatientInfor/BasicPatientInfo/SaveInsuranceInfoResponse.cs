﻿using Domain.Models.PatientInfor;
using UseCase.PatientInfor.Save;

namespace EmrCloudApi.Responses.PatientInfor.BasicPatientInfo
{
    public class SaveInsuranceInfoResponse
    {
        public SaveInsuranceInfoResponse(IEnumerable<SavePatientInfoValidationResult> validateDetails, SavePatientInfoStatus state, long ptID, int hokenId, long onlineConfirmationHisId, PatientInforModel patientInforModel)
        {
            ValidateDetails = validateDetails;
            State = state;
            PtID = ptID;
            HokenId = hokenId;
            OnlineConfirmationHisId = onlineConfirmationHisId;
            PatientInforModel = patientInforModel;
        }

        public IEnumerable<SavePatientInfoValidationResult> ValidateDetails { get; private set; }

        public SavePatientInfoStatus State { get; private set; }

        public long PtID { get; private set; }

        public int HokenId { get; private set; }

        public long OnlineConfirmationHisId { get; private set; }

        public PatientInforModel PatientInforModel { get; private set; }
    }
}
