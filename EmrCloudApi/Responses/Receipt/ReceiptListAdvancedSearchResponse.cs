using UseCase.Receipt.ReceiptListAdvancedSearch;

namespace EmrCloudApi.Responses.Receipt;

public class ReceiptListAdvancedSearchResponse
{
    public ReceiptListAdvancedSearchResponse(ReceiptListAdvancedSearchOutputData outputData)
    {
        TotalCount = outputData.TotalCount;
        DisplayTensu = outputData.DisplayTensu;
        ReceiptList = outputData.ReceiptList;
    }

    public int TotalCount { get; private set; }
    public int DisplayTensu { get; private set; }
    public List<ReceiptListAdvancedSearchItem> ReceiptList { get; private set; }
}
