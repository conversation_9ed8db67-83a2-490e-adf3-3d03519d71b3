﻿using EmrCloudApi.Constants;
using EmrCloudApi.Presenters.Family;
using EmrCloudApi.Requests.Family;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.Family;
using Helper.Responses;
using Microsoft.AspNetCore.Mvc;
using UseCase.Core.Sync;
using UseCase.Family.GetRaiinInfList;

namespace EmrCloudApi.Controller;

[Route("api/[controller]")]
public class FamilyController : BaseParamControllerBase
{
    private readonly UseCaseBus _bus;
    public FamilyController(UseCaseBus bus, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
    {
        _bus = bus;
    }

    [HttpGet(ApiPath.GetRaiinInfList)]
    public ActionResult<Response<GetRaiinInfListResponse>> GetListRaiinInf([FromQuery] GetRaiinInfListRequest request)
    {
        var input = new GetRaiinInfListInputData(HpId, request.PtId);
        var output = _bus.Handle(input);

        var presenter = new GetRaiinInfListPresenter();
        presenter.Complete(output);
     
        var okStatus = new[] { GetRaiinInfListStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<GetRaiinInfListResponse>>(presenter.Result);
    }
}
