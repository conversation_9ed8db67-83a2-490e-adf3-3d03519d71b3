﻿using Domain.Models.ConfirmOnline;
using EmrCloudApi.Constants;
using EmrCloudApi.Messages;
using EmrCloudApi.Presenters.MappingMember;
using EmrCloudApi.Presenters.Reception;
using EmrCloudApi.Presenters.VisitingList;
using EmrCloudApi.Realtime;
using EmrCloudApi.Realtime.Subscription;
using EmrCloudApi.Realtime.HasuraClient;
using EmrCloudApi.Requests.MappingMember;
using EmrCloudApi.Realtime.Subscription;
using EmrCloudApi.Requests.Reception;
using EmrCloudApi.Requests.ReceptionVisiting;
using EmrCloudApi.Requests.VisitingList;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.MappingMember;
using EmrCloudApi.Responses.Reception;
using EmrCloudApi.Responses.ReceptionVisiting;
using EmrCloudApi.Responses.VisitingList;
using Helper.Responses;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using UseCase.Core.Sync;
using UseCase.Online.SubmitConfirmation;
using UseCase.Reception.GetList;
using UseCase.Reception.GetPagingList;
using UseCase.Reception.GetSettings;
using UseCase.Reception.MappingMember;
using UseCase.Reception.MappingMember.Update;
using UseCase.Reception.UpdateDynamicCell;
using UseCase.Reception.UpdateStaticCell;
using UseCase.ReceptionVisiting.Get;
using UseCase.VisitingList.ReceptionLock;
using UseCase.VisitingList.SaveSettings;

namespace EmrCloudApi.Controller;

[Route("api/[controller]")]
public class VisitingController : BaseParamControllerBase
{
    private readonly UseCaseBus _bus;
    private readonly IWebSocketService _webSocketService;
    private readonly ISubscriptionService _subscriptionService;

    public VisitingController(UseCaseBus bus, IWebSocketService webSocketService, IHttpContextAccessor httpContextAccessor, ISubscriptionService subscriptionService) : base(httpContextAccessor)
    {
        _bus = bus;
        _webSocketService = webSocketService;
        _subscriptionService = subscriptionService;
    }

    [HttpGet(ApiPath.Get + "ReceptionLock")]
    public ActionResult<Response<GetReceptionLockRespone>> GetList([FromQuery] GetReceptionLockRequest request)
    {
        var input = new GetReceptionLockInputData(HpId, request.SinDate, request.PtId, request.RaiinNo, request.FunctionCd);
        var output = _bus.Handle(input);
        var presenter = new GetReceptionLockPresenter();
        presenter.Complete(output);

        var okStatus = new[] { GetReceptionLockStatus.Success };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return Ok(presenter.Result);
    }

    [HttpGet(ApiPath.GetList)]
    public ActionResult<Response<GetReceptionListForViewResponse>> GetList([FromQuery] GetReceptionListRequest request)
    {
        var input = new GetReceptionListInputData(HpId, request.SinDate);
        var output = _bus.Handle(input);
        var presenter = new GetReceptionListPresenter();
        presenter.Complete(output);

        var okStatus = new[] { GetReceptionListStatus.Success };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return Ok(presenter.Result);
    }

    [HttpGet("GetPagingList")]
    public ActionResult<Response<GetReceptionListResponse>> GetPagingList([FromQuery] GetReceptionPagingListRequest request)
    {
        var input = new GetReceptionPagingListInputData(HpId, request.SinDate, request.RaiinNo, request.PtId, request.IsGetFamily, request.IsDeleted, request.SearchSameVisit, request.Limit, request.Offset);
        var output = _bus.Handle(input);
        var presenter = new GetReceptionPagingListPresenter();
        presenter.Complete(output);

        var okStatus = new[] { GetReceptionPagingListStatus.Success };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return Ok(presenter.Result);
    }

    [HttpGet(ApiPath.Get + "ReceptionInfo")]
    public ActionResult<Response<GetReceptionVisitingResponse>> GetList([FromQuery] GetReceptionVisitingRequest request)
    {
        var input = new GetReceptionVisitingInputData(HpId, request.RaiinNo);
        var output = _bus.Handle(input);
        var presenter = new GetReceptionVisitingPresenter();
        presenter.Complete(output);

        var okStatus = new[] { GetReceptionVisitingStatus.Success };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return Ok(presenter.Result);
    }

    [HttpGet(ApiPath.Get + "Settings")]
    public ActionResult<Response<GetReceptionSettingsResponse>> GetSettings([FromQuery] GetReceptionSettingsRequest req)
    {
        var input = new GetReceptionSettingsInputData(UserId, HpId);
        var output = _bus.Handle(input);
        var presenter = new GetReceptionSettingsPresenter();
        presenter.Complete(output);

        // NOTE: Success only
        var okStatus = new[] { GetReceptionSettingsStatus.Success };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return Ok(presenter.Result);
    }

    [HttpPost("SaveSettings")]
    public ActionResult<Response<SaveVisitingListSettingsResponse>> SaveSettings([FromBody] SaveVisitingListSettingsRequest req)
    {
        var input = new SaveVisitingListSettingsInputData(req.Settings, HpId, UserId);
        var output = _bus.Handle(input);
        var presenter = new SaveVisitingListSettingsPresenter();
        presenter.Complete(output);

        // NOTE: Success only
        var okStatus = new[] { SaveVisitingListSettingsStatus.Success };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return Ok(presenter.Result);
    }

    [HttpPut(ApiPath.Update + "StaticCell")]
    public async Task<ActionResult<Response<UpdateReceptionStaticCellResponse>>> UpdateStaticCellAsync([FromBody] UpdateReceptionStaticCellRequest req)
    {
        var input = new UpdateReceptionStaticCellInputData(
            req.HpId ?? HpId, req.SinDate, req.RaiinNo, req.PtId, req.CellName, req.CellValue, UserId, req.GrpIds ?? new());
        var output = _bus.Handle(input);
        switch (output.Status)
        {
            case UpdateReceptionStaticCellStatus.RaiinInfUpdated:
            case UpdateReceptionStaticCellStatus.LabelUpdated:
            case UpdateReceptionStaticCellStatus.RaiinCmtUpdated:
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionUpdateMessage(output.ReceptionUpdateInfos, output.SameVisitList, output.RaiinStatusCountList));
                break;
            case UpdateReceptionStaticCellStatus.PatientCmtUpdated:
                _subscriptionService.ExecuteSubscription(FunctionCodes.PatientInfChanged, new PatientInforMessage(output.PatientInforModel));
                break;
        }

        var presenter = new UpdateReceptionStaticCellPresenter();
        presenter.Complete(output);

        var okStatus = new[]
        {
            UpdateReceptionStaticCellStatus.RaiinInfUpdated,
            UpdateReceptionStaticCellStatus.RaiinCmtUpdated,
            UpdateReceptionStaticCellStatus.PatientCmtUpdated,
            UpdateReceptionStaticCellStatus.LabelUpdated,
            UpdateReceptionStaticCellStatus.InvalidStatus
        };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return Ok(presenter.Result);
    }

    [HttpPut(ApiPath.Update + "DynamicCell")]
    public async Task<ActionResult<Response<UpdateReceptionDynamicCellResponse>>> UpdateDynamicCellAsync([FromBody] UpdateReceptionDynamicCellRequest req)
    {
        var input = new UpdateReceptionDynamicCellInputData(HpId, req.SinDate, req.RaiinNo, req.PtId, req.GrpId, req.KbnCd, UserId);
        var output = _bus.Handle(input);
        if (output.Status == UpdateReceptionDynamicCellStatus.Success)
        {
            await _webSocketService.SendMessageAsync(FunctionCodes.ReceptionChanged, new ReceptionChangedMessage(output.ReceptionInfos, output.SameVisitList));
        }

        var presenter = new UpdateReceptionDynamicCellPresenter();
        presenter.Complete(output);

        var okStatus = new[] { UpdateReceptionDynamicCellStatus.Success };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return Ok(presenter.Result);
    }

    [HttpGet(ApiPath.GetList + "MappingMember")]
    public ActionResult<Response<GetMappingModelResponse>> GetListMappingMember([FromQuery] GetMappingMemberRequest request)
    {
        var input = new GetMappingMemberInputData
            (
                HpId,
                request.PtId,
                request.PtNum,
                request.Status,
                request.PortalCustomerId,
                request.Birthday,
                request.Sex,
                request.Name,
                request.KanaName,
                request.SinDate,
                request.ModelNum
            );

        var output = _bus.Handle(input);
        var presenter = new GetMappingMemberPresenter();
        presenter.Complete(output);
        var okStatus = new[] { GetMappingMemberStatus.Success };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return Ok(presenter.Result);
    }

    [HttpPut(ApiPath.SubmitConfirmation)]
    public async Task<ActionResult<Response<UpdateReceptionDynamicCellResponse>>> SubmitConfirmation([FromBody] SubmitConfirmationRequest req)
    {
        var input = new SubmitConfirmationInputData(HpId, req.PtId, req.OnlineConfirmationHisId, req.Raiino, UserId);
        var output = _bus.Handle(input);
        switch (output.Status)
        {
            case SubmitConfirmationStatus.Successed:
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionUpdateMessage(output.ReceptionUpdateInfos, req.OnlineConfirmationHisId));
                break;
        }

        var presenter = new SubmitConfirmationPresenter();
        var okStatus = new[] { SubmitConfirmationStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        presenter.Complete(output);
        return Ok(presenter.Result);
    }

    [HttpPost(ApiPath.Update + "MappingMember")]
    public async Task<ActionResult<Response<UpdateMappingMemberResponse>>> LinkMappingMemberRecords([FromBody] UpdateMappingMemberRequest request)
    {
        var input = new UpdateMappingMemberInputData
        (
           request.hospitalArrivalStatus,
           new UpdateConfirmOnlineModel
           (
               HpId,
               request.AiChartPtId,
               request.PortalPtId,
               request.OnlineConfirmHisId,
               UserId
           ),
           request.SinDate ?? 0
        );

        var output = _bus.Handle(input);
        if (output.UpdateMappingMemberStatus == UpdateMappingMemberStatus.Success)
        {
            _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionUpdateMessage(output.ReceptionForViewDtos, output.DeletedOnlineConfirmationId));
        }
        var presenter = new UpdateMappingMemberPresenter();
        presenter.Complete(output);

        var okStatus = new[] { UpdateMappingMemberStatus.Success };
        if (!okStatus.Contains(output.UpdateMappingMemberStatus))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return Ok(presenter.Result);
    }
}
