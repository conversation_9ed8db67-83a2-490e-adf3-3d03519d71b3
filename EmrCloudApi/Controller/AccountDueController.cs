﻿using EmrCloudApi.Constants;
using EmrCloudApi.Messages;
using EmrCloudApi.Presenters.AccountDue;
using EmrCloudApi.Realtime.Subscription;
using EmrCloudApi.Requests.AccountDue;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.AccountDue;
using Helper.Responses;
using Microsoft.AspNetCore.Mvc;
using UseCase.AccountDue.GetAccountDueList;
using UseCase.AccountDue.IsNyukinExisted;
using UseCase.AccountDue.SaveAccountDueList;
using UseCase.Core.Sync;

namespace EmrCloudApi.Controller;

[Route("api/[controller]")]
public class AccountDueController : BaseParamControllerBase
{
    private readonly UseCaseBus _bus;
    private readonly ISubscriptionService _subscriptionService;
    public AccountDueController(UseCaseBus bus, IHttpContextAccessor httpContextAccessor, ISubscriptionService subscriptionService) : base(httpContextAccessor)
    {
        _bus = bus;
        _subscriptionService = subscriptionService;
    }

    [HttpGet(ApiPath.GetList)]
    public ActionResult<Response<GetAccountDueListResponse>> GetList([FromQuery] GetAccountDueListRequest request)
    {
        var input = new GetAccountDueListInputData(HpId, request.PtId, request.SinDate, request.IsUnpaidChecked);
        var output = _bus.Handle(input);

        var presenter = new GetAccountDueListPresenter();
        presenter.Complete(output);

        var okStatus = new[] { GetAccountDueListStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }
        
        return new ActionResult<Response<GetAccountDueListResponse>>(presenter.Result);
    }

    [HttpGet(ApiPath.IsNyukinExisted)]
    public ActionResult<Response<IsNyukinExistedResponse>> IsNyukinExisted([FromQuery] IsNyukinExistedRequest request)
    {
        var input = new IsNyukinExistedInputData(HpId, request.PtId, request.RaiinNo, request.SinDate);
        var output = _bus.Handle(input);

        var presenter = new IsNyukinExistedPresenter();
        presenter.Complete(output);

        // NOTE: Success only
        var okStatus = new[] { IsNyukinExistedStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<IsNyukinExistedResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.SaveList)]
    public ActionResult<Response<SaveAccountDueListResponse>> SaveList([FromBody] SaveAccountDueListRequest request)
    {
        var input = new SaveAccountDueListInputData(HpId, request.UserId, request.PtId, request.SinDate, request.KaikeiTime, ConvertToListSyunoNyukinInputItem(request), request.IsBulk);
        var output = _bus.Handle(input);

        if (output.Status == SaveAccountDueListStatus.Successed)
        {
            _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionUpdateMessage(output.ReceptionInfos, output.SameVisitList,new()));
        }

        var presenter = new SaveAccountDueListPresenter();
        presenter.Complete(output);

        var okStatus = new[] { SaveAccountDueListStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<SaveAccountDueListResponse>>(presenter.Result);
    }

    private List<SyunoNyukinInputItem> ConvertToListSyunoNyukinInputItem(SaveAccountDueListRequest request)
    {
        return request.ListAccountDues.Select(item => new SyunoNyukinInputItem(
                                                item.NyukinKbn,
                                                item.RaiinNo,
                                                item.SortNo,
                                                item.AdjustFutan,
                                                item.NyukinGaku,
                                                item.PaymentMethodCd,
                                                item.NyukinDate,
                                                item.UketukeSbt,
                                                item.NyukinCmt,
                                                item.SeikyuGaku,
                                                item.SeikyuTensu,
                                                item.SeikyuDetail,
                                                item.IsUpdated,
                                                item.SeqNo,
                                                item.RaiinInfStatus,
                                                item.SeikyuAdjustFutan,
                                                item.SeikyuSinDate,
                                                item.IsDelete
                                            )).ToList();
    }
}
