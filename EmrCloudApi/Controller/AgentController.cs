﻿using Domain.Models.Eps;
using Domain.Models.EventProcessor;
using Domain.Models.Reception;
using EmrCloudApi.Constants;
using EmrCloudApi.Messages;
using EmrCloudApi.Presenters.AgentSetting;
using EmrCloudApi.Presenters.Eps;
using EmrCloudApi.Presenters.Logger;
using EmrCloudApi.Presenters.Online;
using EmrCloudApi.Presenters.RenKei;
using EmrCloudApi.Presenters.SytemConf;
using EmrCloudApi.Realtime.Subscription;
using EmrCloudApi.Requests;
using EmrCloudApi.Requests.AgentSetting;
using EmrCloudApi.Requests.Auth;
using EmrCloudApi.Requests.Eps;
using EmrCloudApi.Requests.Logging;
using EmrCloudApi.Requests.Online;
using EmrCloudApi.Requests.Renkei;
using EmrCloudApi.Requests.SystemConf;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.AgentSetting;
using EmrCloudApi.Responses.Auth;
using EmrCloudApi.Responses.Eps;
using EmrCloudApi.Responses.Logger;
using EmrCloudApi.Responses.Online;
using EmrCloudApi.Responses.Renkei;
using EmrCloudApi.Responses.SystemConf;
using EmrCloudApi.Security;
using Helper.Constants;
using Helper.Mapping;
using Helper.Responses;
using Infrastructure.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using UseCase.AgentSetting.GetAgentSetting;
using UseCase.AgentSetting.GetIraiFileDataDummy;
using UseCase.AgentSetting.UpdateAgentSetting;
using UseCase.Core.Sync;
using UseCase.Eps.GetDispensingInfList;
using UseCase.Eps.GetPreRegistrationData;
using UseCase.Eps.GetPrescriptionIdList;
using UseCase.Eps.SavePrescriptionInfo;
using UseCase.Eps.UpdatePrescriptionStatusByIds;
using UseCase.Eps.UpsertEpsRegisterData;
using UseCase.Logger.WriteListLog;
using UseCase.Online.CreateOnlineConfirmationByXml;
using UseCase.Renkei.Get;
using UseCase.Renkei.SaveToFileProcess;
using UseCase.SmartKartePort.GetPort;
using UseCase.SmartKartePort.UpdatePort;
using UseCase.SystemConf.GetSystemConfList;
using UseCase.SystemConf.GetXmlPath;
using UseCase.User.GetByLoginId;
using UseCase.Eps.UpdateEpsDispensings;
using Domain.Models.EpsDispensing;
using UseCase.Eps.GetDispensingInfFromCsv;
using System.Text;
using EmrCloudApi.Presenters.CustomButtonConf;
using EmrCloudApi.Responses.CustomButtonConf;
using UseCase.CustomButtonConf;
using UseCase.AgentSetting.GetAgentDownloadLink;

namespace EmrCloudApi.Controller
{
    [Route("api/[controller]")]
    public class AgentController : BaseParamControllerBase
    {
        private readonly UseCaseBus _bus;
        private readonly ISubscriptionService _subscriptionService;

        public AgentController(UseCaseBus bus, IHttpContextAccessor httpContextAccessor, ISubscriptionService subscriptionService) : base(httpContextAccessor)
        {
            _bus = bus;
            _subscriptionService = subscriptionService;
        }

        [Authorize]
        [HttpGet(ApiPath.GetSystemConfListXmlPath)]
        public ActionResult<Response<GetSystemConfListXmlPathResponse>> GetSystemConfListXmlPath([FromQuery] GetSystemConfListXmlPathRequest request)
        {
            var input = new GetSystemConfListXmlPathInputData(HpId, request.GrpCd, request.Machine, request.IsKensaIrai);
            var output = _bus.Handle(input);

            var presenter = new GetSystemConfListXmlPathPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetSystemConfListXmlPathStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetSystemConfListXmlPathResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpGet("Authentication")]
        public ActionResult ValidateToken()
        {
            return Ok("Token is valid");
        }

        [HttpPost("AppToken"), Produces("application/json")]
        public ActionResult<Response<AppTokenResponse>> AppToken([FromBody] AppTokenRequest req)
        {
            var getUserInput = new GetUserByLoginIdInputData(req.LoginId, req.Password);
            var getUserOutput = _bus.Handle(getUserInput);
            var user = getUserOutput.User;
            if (user is null)
            {
                var errorResult = GetErrorResult("The loginId is invalid.");
                return BadRequest(errorResult);
            }

            // The claims that will be persisted in the tokens.
            var claims = new Claim[]
            {
            new(ParamConstant.UserId, user.UserId.ToString()),
            new(ParamConstant.HpId, user.HpId.ToString()),
            new(ParamConstant.DepartmentId, user.KaId.ToString()),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            string token = AuthProvider.GenerateAppToken(claims).token;

            if (!string.IsNullOrEmpty(token))
            {
                var successResult = GetSuccessResult(token, user.UserId, user.LoginId);
                return Ok(successResult);
            }
            else
            {
                var errorResult = GetErrorResult("An error occurred while verification token");
                return BadRequest(errorResult);
            }

            #region Helper methods

            Response<AppTokenResponse> GetErrorResult(string errorMessage)
            {
                return new Response<AppTokenResponse>
                {
                    Data = new AppTokenResponse(string.Empty, 0, string.Empty),
                    Status = 0,
                    Message = errorMessage
                };
            }

            Response<AppTokenResponse> GetSuccessResult(string token, int userId, string loginId)
            {
                return new Response<AppTokenResponse>
                {
                    Data = new AppTokenResponse(token, userId, loginId),
                    Status = 1,
                    Message = ResponseMessage.Success
                };
            }

            #endregion Helper methods
        }

        [Authorize]
        [HttpPost(ApiPath.InsertOnlineConfirmation + "ByXml")]
        public async Task<ActionResult<Response<InsertOnlineConfirmationByXmlResponse>>> InsertOnlineConfirmationByXml([FromBody] CreatOnlineConfirmationByXmlRequest request)
        {
            var input = new CreateOnlineConfirmationByXmlInputData(HpId, request.XmlFileInfo, UserId, request.PmhStatus ?? 0, request.PmhResult);
            var output = _bus.Handle(input);

            var presenter = new CreateConfirmationOnlineByXml();
            presenter.Complete(output);
            if (output.Status == CreateOnlineConfirmationByXmlStatus.Successed)
            {
                if (output.ListReceptionForViewDtos.Any())
                {
                    _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionUpdateMessage(output.ListReceptionForViewDtos, new List<SameVisitModel>(), new()));
                }
                else if (output.ListReceptionRowModels.Any())
                {
                    _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionChangedMessage(output.ListReceptionRowModels, new List<SameVisitModel>()));
                }
            }

            var okStatus = new[] { CreateOnlineConfirmationByXmlStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<InsertOnlineConfirmationByXmlResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpPost(ApiPath.WriteListLog)]
        public ActionResult<Response<WriteListLogResponse>> WriteListLogForAgent([FromBody] WriteListLogRequest request)
        {
            var auditLogModelList = request.WriteListLogRequests.Select(item => new AuditLogModel(
                                                                                item.EventCd,
                                                                                item.PtId,
                                                                                item.SinDay,
                                                                                item.RaiinNo,
                                                                                item.Path,
                                                                                item.RequestInfo,
                                                                                item.Description,
                                                                                item.LogType
                                                                )).ToList();
            var input = new WriteListLogInputData(auditLogModelList);
            var output = _bus.Handle(input);

            var presenter = new WriteListLogPresenter();
            presenter.Complete(output);

            var okStatus = new[] { WriteListLogStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<WriteListLogResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpPost("UpdateAgentSetting")]
        public ActionResult<Response<UpdateAgentSettingResponse>> UpdateAgentSetting([FromBody] UpdateAgentSettingRequest request)
        {
            var input = Mapper.Map(request, new UpdateAgentSettingInputData());
            input.HpId = HpId;
            var output = _bus.Handle(input);

            var presenter = new UpdateAgentSettingPresenter();
            presenter.Complete(output);

            var okStatus = new[] { UpdateAgentSettingStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<UpdateAgentSettingResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpGet("GetAgentSetting")]
        public ActionResult<Response<GetAgentSettingResponse>> GetAgentSetting([FromQuery] GetAgentSettingRequest request)
        {
            var input = Mapper.Map(request, new GetAgentSettingInputData());
            input.HpId = HpId;

            var output = _bus.Handle(input);
            var presenter = new GetAgentSettingPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetAgentSettingStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetAgentSettingResponse>>(presenter.Result);
        }

        #region [KensaIrai - Renkei]

        [Authorize]
        [HttpPost(ApiPath.ProcessKensaIrai)]
        public ActionResult<Response<ProcessKensaIraiResponse>> ProcessKensaIrai([FromBody] ProcessKensaIraiRequest request)
        {
            var input = new ProcessKensaIraiInputData(HpId, request.PtId, request.SinDate, request.RaiinNo, UserId, request.MachineName, request.EventCd);
            var output = _bus.Handle(input);
            var presenter = new ProcessKensaIraiPresenter();
            presenter.Complete(output);

            var okStatus = new[] { ProcessKensaIraiStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<ProcessKensaIraiResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpPost(ApiPath.GetListRenkei)]
        public ActionResult<Response<GetListRenkeiResponse>> GetListRenkei([FromBody] GetListRenkeiRequest request)
        {
            var input = new GetListRenKeiInputData(HpId, request.MachineName, request.EventCd);
            var output = _bus.Handle(input);

            var presenter = new GetListRenkeiPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetRenkeiStatus.Success };
            if (!okStatus.Contains(output.RenkeiStatus))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetListRenkeiResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpPost(ApiPath.ProcessRenkei)]
        public ActionResult<Response<ProcessRenkeiResponse>> ProcessRenkei([FromBody] ProcessRenkeiRequest request)
        {
            var arg = new ArgumentModel(
                eventCd: request.EventCd,
                ptId: request.PtId,
                sinDate: request.SinDate,
                raiinNo: request.RaiinNo,
                misyu: request.Misyu,
                nyukinDate: request.NyukinDate,
                nyukin: request.Nyukin,
                nyukinSortNo: request.NyukinSortNo,
                userId: UserId
            );
            var input = new ProcessRenkeiInputData(HpId, arg, request.MachineName ?? string.Empty);
            var output = _bus.Handle(input);

            var presenter = new ProcessRenkeiPresenter();
            presenter.Complete(output);

            var okStatus = new[] { ProcessRenkeiStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }
            return new ActionResult<Response<ProcessRenkeiResponse>>(presenter.Result);
        }

        #endregion [KensaIrai - Renkei]       

        #region Eps
        [Authorize]
        [HttpPost(ApiPath.GetPrescriptionIdList)]
        public ActionResult<Response<GetPrescriptionIdListResponse>> GetPrescriptionIdList([FromBody] GetPrescriptionIdListRequest request)
        {
            var input = new GetPrescriptionIdListInputData();
            input = Mapper.Map(request, input);

            input.HpId = HpId;
            input.UserId = UserId;
            var output = _bus.Handle(input);
            var presenter = new GetPrescriptionIdListPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetPrescriptionIdListStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetPrescriptionIdListResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpPost(ApiPath.UpdatePrescriptionStatusByIds)]
        public ActionResult<Response<UpdatePrescriptionStatusByIdsResponse>> UpdatePrescriptionStatusByIds([FromBody] UpdatePrescriptionStatusByIdsRequest byIdsRequest)
        {
            var input = new UpdatePrescriptionStatusByIdsInputData(HpId, byIdsRequest.PtId, byIdsRequest.RaiinNo, byIdsRequest.SinDate, Mapper.Map<PrescriptionStatusItem, UpdatePrescriptionStatusModel>(byIdsRequest.PrescriptionStatusItem));
            var output = _bus.Handle(input);

            var presenter = new UpdatePrescriptionStatusByIdsPresenter();
            presenter.Complete(output);

            var okStatus = new[] { UpdatePrescriptionStatusByIdsStatus.Successed };
            if (!okStatus.Contains(output.StatusByIds))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }
            return new ActionResult<Response<UpdatePrescriptionStatusByIdsResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpPost(ApiPath.UpsertEpsRegister)]
        public ActionResult<Response<UpsertEpsRegisterResponse>> UpsertEpsRegister([FromBody] UpsertEpsRegisterRequest request)
        {
            var input = new UpsertEpsRegisterInputData(HpId, request.ReqDate, request.DateSeqNo, request.ArbitraryFileIdentifier, request.PtId, request.SinDate, request.RaiinNo, request.PrescriptionId, request.DispensingResultId, request.ReqType, request.Status, request.ResultCode, request.ResultMessage, request.Result, UserId);
            var output = _bus.Handle(input);
            var presenter = new UpsertEpsRegisterPresenter();
            presenter.Complete(output);
            var okStatus = new[] { UpsertEpsRegisterStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }
            return new ActionResult<Response<UpsertEpsRegisterResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpPost(ApiPath.GetPreRegistrationData)]
        public ActionResult<Response<GetPreRegistrationResponseData>> GetPreRegistrationData([FromBody] GetPreRegistrationDataRequest request)
        {
            var input = new GetPreRegistrationInputData(HpId, request.PtId, request.RaiinNo, request.SinDate, request.StatusList, request.OdrInfs);
            var output = _bus.Handle(input);
            var presenter = new GetPreRegistrationDataPresenter();
            presenter.Complete(output);
            var okStatus = new[] { GetPreRegistrationStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }
            return new ActionResult<Response<GetPreRegistrationResponseData>>(presenter.Result);
        }

        [Authorize]
        [HttpPost(ApiPath.SavePrescriptionInfo)]
        public ActionResult<Response<SavePrescriptionInfoResponse>> SavePrescriptionInfo([FromBody] SavePrescriptionInfoRequest request)
        {
            var input = new SavePrescriptionInfoInputData(HpId, UserId, Mapper.Map<SavePrescriptionInfoRequest.PrescriptionInfoRequest, SaveEpsPrescriptionInfoModel>(request.PrescriptionInfos));
            var output = _bus.Handle(input);

            var presenter = new SavePrescriptionInfoPresenter();
            presenter.Complete(output);

            var okStatus = new[] { SavePrescriptionInfoStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }
            return new ActionResult<Response<SavePrescriptionInfoResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpPost(ApiPath.UpdateEpsDispensings)]
        public ActionResult<Response<UpdateEpsDispensingsResponse>> UpdateEpsDispensings([FromBody] UpdateEpsDispensingsRequest request)
        {
            var input = new UpdateEpsDispensingsInputData(HpId, Mapper.Map<UpdateEpsDispensingItems, EpsDispensingModel>(request.UpdateEpsDispensingItems), request.NotAcceptedAtPharmacy, request.PreparedAtPharmacy, request.CollectedOrDispensedByPharmacy, request.CancelledPrescription, UserId);
            var output = _bus.Handle(input);

            var presenter = new UpdateEpsDispensingsPresenter();
            presenter.Complete(output);

            var okStatus = new[] { UpdateEpsDispensingsStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }
            return new ActionResult<Response<UpdateEpsDispensingsResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpPost(ApiPath.GetDispensingInfFromCsvData)]
        public ActionResult<Response<GetDispensingFromCsvResponse>> GetDispensingInfFromCsvData([FromBody] GetDispensingInfFromCsvRequest request)
        {
            byte[] csvBytes = Convert.FromBase64String(request.DispensingCsvBase64Data);
            var input = new GetDispensingInfFromCsvInputData(HpId, Encoding.UTF8.GetString(csvBytes));
            var output = _bus.Handle(input);
            var presenter = new GetDispensingInfFromCsvPresenter();
            presenter.Complete(output);
            var okStatus = new[] { GetDispensingInfFromCsvStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }
            return new ActionResult<Response<GetDispensingFromCsvResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpGet(ApiPath.GetEpsDispensings)]
        public ActionResult<Response<GetDispensingInfListResponse>> GetEpsDispensings([FromQuery] GetDispensingInfListRequest request)
        {
            var input = new GetDispensingInfListInputData(HpId, request.PtId);
            var output = _bus.Handle(input);
            var presenter = new GetDispensingInfListPresenter();
            presenter.Complete(output);
            var okStatus = new[] { GetDispensingInfListStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }
            return new ActionResult<Response<GetDispensingInfListResponse>>(presenter.Result);
        }

        [Authorize]
        [HttpGet(ApiPath.GetList)]
        public ActionResult<Response<GetSystemConfListResponse>> GetSystemConfList()
        {
            var input = new GetSystemConfListInputData(HpId);
            var output = _bus.Handle(input);

            var presenter = new GetSystemConfListPresenter();
            presenter.Complete(output);

            // NOTE: Success only
            var okStatus = new[] { GetSystemConfListStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetSystemConfListResponse>>(presenter.Result);
        }
        #endregion

        #region RunCustomButton
        [Authorize]
        [HttpGet(ApiPath.GetDetailCustomButtonConf)]
        public ActionResult<Response<GetDetailCustomButtonConfResponse>> GetDetailCustomButtonConf(int id, int ptId, long raiinNo)
        {
            var input = new GetDetailCustomButtonConfInputData(id, HpId, ptId, raiinNo);
            var output = _bus.Handle(input);

            var presenter = new GetDetailCustomButtonConfPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetDetailCustomButtonConfStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetDetailCustomButtonConfResponse>>(presenter.Result);
        }
        #endregion

        [HttpGet(ApiPath.GetAgentDownloadLink)]
        public ActionResult<Response<GetAgentDownloadLinkResponse>> GetAgentDownloadLink([FromQuery] GetAgentDownloadLinkRequest request)
        {
            var input = new GetAgentDownloadLinkInputData(HpId, request.DeviceType);
            var output = _bus.Handle(input);

            var presenter = new GetAgentDownloadLinkPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetAgentDownloadLinkStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetAgentDownloadLinkResponse>>(presenter.Result);
        }
    }
}
