﻿using Newtonsoft.Json;
using ErrorCodeGenerator.Definitions;
using Helper.Responses;
using Infrastructure.Interfaces;
using Infrastructure.Clients;
using EmrCloudApi.Constants;
using Domain.Models.DenkaruApi;
using System.Text;

public class AuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IConfiguration _configuration;

    public AuthenticationMiddleware(RequestDelegate next, IConfiguration configuration)
    {
        _next = next;
        _configuration = configuration;
    }

    public async Task Invoke(HttpContext context)
    {
        try
        {
            var headers = context.Request.Headers;

            // Internalからのリクエストは認証をスキップ
            if (headers.TryGetValue(DenkaruConstants.InternalAPIKeyHeader, out var internalApiKey))
            {
                var apiKey = _configuration["DenkaruServer:InternalApiKey"] ?? string.Empty;

                if (internalApiKey == apiKey)
                {
                    await _next(context);
                    return;
                }
            }

            // APIキー認証
            string[] authorizationPath = { "/fco/unaccountedTransactions", "/fco/deposits" };
            // APIリクエストで受け取ったURL
            string requestUrl = context.Request.Path;
            // チェック処理
            if (authorizationPath.Contains(requestUrl))
            {
                await _next(context);
                return;
            }

            if (context.Request.Headers.TryGetValue("X-Type", out var typeHeaderValue))
            {
                if (typeHeaderValue == "SmartKarteApp")
                {
                    await _next(context);
                    return;
                }
            }

            var authEndpoint = _configuration["Auth:Path"];
            using var client = new HttpClient();

            foreach (var header in context.Request.Headers)
            {
                client.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value.ToArray());
            }

            HttpResponseMessage response;
            switch (context.Request.Method)
            {
                case "GET":
                    response = await client.GetAsync(authEndpoint);
                    break;

                case "POST":
                    response = await client.PostAsync(authEndpoint, null);
                    break;

                case "PUT":
                    response = await client.PutAsync(authEndpoint, null);
                    break;

                case "DELETE":
                    response = await client.DeleteAsync(authEndpoint);
                    break;

                default:
                    response = await client.GetAsync(authEndpoint);
                    break;
            }

            var responseString = await response.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<InternalAccessCheckResponse>(responseString);
            if (result == null)
            {
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                var errorResponse = new DenkaruErrorResponse(DenkaruCodes.INTERNAL_SERVER_ERROR);
                await context.Response.WriteAsJsonAsync(errorResponse);
                return;
            }

            if (response.IsSuccessStatusCode)
            {
                if (response.Headers.TryGetValues("Set-Cookie", out var cookieValues))
                {
                    foreach (var cookieValue in cookieValues)
                    {
                        context.Response.Headers.Append("Set-Cookie", cookieValue);
                    }
                }
                context.Request.Headers["HpId"] = result.HpId.ToString();
                context.Request.Headers["UserId"] = result.UserId.ToString();
                await _next(context);
            }
            else
            {
                //t-azegami hasura cannot convert 5XX errors.
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                var errorResponse = new DenkaruErrorResponse(result.Code, result.userMessage);
                await context.Response.WriteAsJsonAsync(errorResponse);
                return;
            }

        }
        catch
        {
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            var errorResponse = new DenkaruErrorResponse(DenkaruCodes.STATUS401_UNAUTHORIZED);
            await context.Response.WriteAsJsonAsync(errorResponse);
            return;
        }
    }

    internal class InternalAccessCheckResponse
    {
        public InternalAccessCheckResponse(int hpId, int userId, bool ok)
        {
            HpId = hpId;
            UserId = userId;
            Ok = ok;
        }

        [JsonProperty("hp_id")]
        public int HpId { get; private set; }

        [JsonProperty("user_id")]
        public int UserId { get; private set; }

        [JsonProperty("ok")]
        public bool Ok { get; private set; }

        [JsonProperty("code")]
        public string Code { get; private set; } = string.Empty;

        [JsonProperty("userMessage")]
        public string userMessage { get; private set; } = string.Empty;
    }
}
