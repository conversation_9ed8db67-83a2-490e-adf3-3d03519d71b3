﻿using Domain.Models.Recalculation;
using EmrCloudApi.Constants;
using EmrCloudApi.Presenters.Receipt;
using EmrCloudApi.Realtime.Subscription;
using EmrCloudApi.Requests.ReceiptCheck;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.Receipt;
using EmrCloudApi.Responses.Receipt.Dto;
using Helper.Constants;
using Helper.Messaging;
using Helper.Messaging.Data;
using Helper.Redis;
using Helper.Responses;
using Infrastructure.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR.Client;
using StackExchange.Redis;
using System.Text;
using System.Text.Json;
using UseCase.Core.Sync;
using UseCase.Receipt.CancelProccessCaculation;
using UseCase.Receipt.Recalculation;
using UseCase.ReceiptCheck.GetRecalculationInfo;
using UseCase.ReceiptCheck.Recalculation;
using UseCase.ReceiptCheck.ReceiptInfEdit;

namespace EmrCloudApi.Controller;

[Route("api/[controller]")]
[ApiController]
public class RecalculationController : BaseParamControllerBase
{
    private readonly UseCaseBus _bus;
    private CancellationToken? _cancellationToken;
    private readonly ITenantProvider _tenantProvider;
    private readonly IConfiguration _configuration;
    private readonly IMessenger _messenger;
    private HubConnection _connection;
    private string uniqueKey;
    private bool stopCalculate = false;
    private bool allowNextStep = false;
    private readonly ISubscriptionService _subscriptionService;
    private readonly IDatabase _cache;
    private string proccessKey = string.Empty;
    private ProccessStatus? proccessStatus;
    public RecalculationController(UseCaseBus bus, IConfiguration configuration, ITenantProvider tenantProvider, IMessenger messenger, IHttpContextAccessor httpContextAccessor, ISubscriptionService subscriptionService) : base(httpContextAccessor)
    {
        _bus = bus;
        _tenantProvider = tenantProvider;
        _configuration = configuration;
        uniqueKey = string.Empty;
        _messenger = messenger;
        if (_connection == null)
        {
            string domain = _tenantProvider.GetDomainFromHeader();
            string socketUrl = _configuration.GetSection("CalculateApi")["WssPath"]! + domain;
            _connection = new HubConnectionBuilder()
             .WithUrl(socketUrl)
             .Build();
        }
        _subscriptionService = subscriptionService;
        _cache = RedisConnectorHelper.Connection.GetDatabase();

    }

    [HttpPost]
    public async void Recalculation([FromBody] RecalculationRequest request, CancellationToken cancellationToken)
    {
        var finalKey = $"Recalculation{HpId.ToString()}";
        _cancellationToken = cancellationToken;
        try
        {
            if (!_cache.KeyExists(finalKey))
            {
                _subscriptionService.ExecuteSubscription(string.Format(FunctionCodes.ProccessStart, request.ProccessKey), new RecalculationMessage
                {
                    HpId = HpId,
                    UserId = UserId,
                    IsRecalculationCheckBox = request.IsRecalculationCheckBox,
                    IsReceiptAggregationCheckBox = request.IsReceiptAggregationCheckBox,
                    IsCheckErrorCheckBox = request.IsCheckErrorCheckBox,
                    SinYm = request.SinYm,
                    PtIdList = request.PtIdList,
                    IsOperator = request.IsOperator,
                    OperatorName = request.OperatorName,
                    ProccessKey = request.ProccessKey
                });
            }
            proccessKey = request.ProccessKey;
            _messenger.Register<RecalculationStatus>(this, UpdateRecalculationStatus);
            _messenger.Register<StopCalcStatus>(this, StopCalculation);
            _messenger.Register<AllowNextStepStatus>(this, CheckAllowNextStepAction);
            _messenger.Register<CancelProccessStatus>(this, CancelProccessAction);

            HttpContext.Response.ContentType = "application/json";

            uniqueKey = Guid.NewGuid().ToString();
            var input = new RecalculationInputData(HpId, UserId, request.SinYm, request.PtIdList, request.IsRecalculationCheckBox, request.IsReceiptAggregationCheckBox, request.IsCheckErrorCheckBox, uniqueKey, cancellationToken, _messenger, request.IsOperator, request.OperatorName, request.ProccessKey);
            _bus.Handle(input);
        }
        catch (Exception ex)
        {
            allowNextStep = true;
            stopCalculate = true;
            Console.WriteLine("Exception Cloud:" + ex.Message);
            await SendMessage(new RecalculationStatus(true, CalculateStatusConstant.None, 0, 0, "再計算にエラーが発生しました。\n\rしばらくしてからもう一度お試しください。", string.Empty));
        }
        finally
        {
            allowNextStep = true;
            stopCalculate = true;
            _messenger.Deregister<RecalculationStatus>(this, UpdateRecalculationStatus);
            _messenger.Deregister<StopCalcStatus>(this, StopCalculation);
            _messenger.Deregister<AllowNextStepStatus>(this, CheckAllowNextStepAction);
            _messenger.Deregister<CancelProccessStatus>(this, CancelProccessAction);
            HttpContext.Response.Body.Close();
            _tenantProvider.DisposeDataContext();
        }
    }

    private void CancelProccessAction(CancelProccessStatus status)
    {
        var finalKey = $"Recalculation{status.HpId.ToString()}";
        if (_cache.KeyExists(finalKey))
        {
            var stringValue = _cache.StringGet(finalKey).ToString();
            RecalculationMessage inputOld = JsonSerializer.Deserialize<RecalculationMessage>(stringValue)!;
            _cache.KeyDelete(finalKey);
            if (_cancellationToken!.Value.IsCancellationRequested)
                proccessStatus = ProccessStatus.ProccessCancelled;
            Thread.Sleep(1000);
            _subscriptionService.ExecuteSubscription(string.Format(FunctionCodes.ProccessEnd, inputOld.ProccessKey), new RecalculationMessage
            {
                HpId = status.HpId,
                UserId = status.UserId,
                IsRecalculationCheckBox = inputOld.IsRecalculationCheckBox,
                IsReceiptAggregationCheckBox = inputOld.IsReceiptAggregationCheckBox,
                IsCheckErrorCheckBox = inputOld.IsCheckErrorCheckBox,
                SinYm = inputOld.SinYm,
                PtIdList = inputOld.PtIdList,
                IsOperator = inputOld.IsOperator,
                OperatorName = inputOld.OperatorName,
                ProccessKey = inputOld.ProccessKey,
                IsSuccessRunning = proccessStatus == null ? status.IsSuccessRunning : (int)proccessStatus
            });
            Thread.Sleep(1000);
            _subscriptionService.DeleteSubscriptionHistory(string.Format(FunctionCodes.ProccessStart, inputOld.ProccessKey));
            _subscriptionService.DeleteSubscriptionHistory(string.Format(FunctionCodes.ProcessRunning, inputOld.ProccessKey));
            _subscriptionService.DeleteSubscriptionHistory(string.Format(FunctionCodes.ProccessEnd, inputOld.ProccessKey));
        }
    }

    private void StopCalculation(StopCalcStatus stopCalcStatus)
    {
        if (stopCalculate)
        {
            stopCalcStatus.CallFailCallback(stopCalculate);
        }
        else if (!_cancellationToken.HasValue)
        {
            stopCalcStatus.CallFailCallback(false);
        }
        else
        {
            stopCalcStatus.CallSuccessCallback(_cancellationToken!.Value.IsCancellationRequested);
        }
    }

    private void CheckAllowNextStepAction(AllowNextStepStatus status)
    {
        if (allowNextStep)
        {
            status.CallSuccessCallback(allowNextStep);
            allowNextStep = false;
        }
        else
        {
            status.CallFailCallback(allowNextStep);
        }
    }

    private async void UpdateRecalculationStatus(RecalculationStatus status)
    {
        if (status.Message.Equals("StartCalculateMonth") || status.Message.Equals("StartFutanCalculateMain"))
        {
            allowNextStep = false;
            stopCalculate = false;
        }

        if (status.Type == 1 || status.Type == 2)
        {
            if (status != null && status.UniqueKey.Equals(uniqueKey))
            {
                await SendMessage(status);
                if (status.Type == CalculateStatusConstant.Invalid)
                {
                    stopCalculate = true;
                    allowNextStep = true;
                }
                if (status.Done)
                {
                    allowNextStep = true;
                }
            }
        }
        else
        {
            await SendMessage(status);
            if (status.Type == CalculateStatusConstant.Invalid)
            {
                stopCalculate = true;
                allowNextStep = true;
            }
            if (status.Done)
            {
                allowNextStep = true;
            }

        }
    }

    private async Task SendMessage(RecalculationStatus status)
    {
        var dto = new RecalculationDto(status);
        string result = "\n" + JsonSerializer.Serialize(dto);

        if (!string.IsNullOrEmpty(dto.Message))
        {
            proccessStatus = ProccessStatus.ProccessFailed;
            if (!HttpContext.Response.HasStarted)
            {
                HttpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
                HttpContext.Response.ContentType = "application/json";

                var errorResponse = new
                {
                    code = "INVALID_PARAMETER",
                    message = dto.Message
                };

                var json = JsonSerializer.Serialize(errorResponse);
                var bytes = Encoding.UTF8.GetBytes(json);

                await HttpContext.Response.Body.WriteAsync(bytes, 0, bytes.Length);
                await HttpContext.Response.Body.FlushAsync();
                return;
            }
        }

        var resultForFrontEnd = Encoding.UTF8.GetBytes(result.ToString());
        await HttpContext.Response.Body.WriteAsync(resultForFrontEnd, 0, resultForFrontEnd.Length);
        await HttpContext.Response.Body.FlushAsync();
        var finalKey = $"Recalculation{HpId.ToString()}";
        if (_cache.KeyExists(finalKey))
        {
            _subscriptionService.ExecuteSubscription(string.Format(FunctionCodes.ProcessRunning, proccessKey), new RecalculationRunningMessage(HpId, UserId, dto.Done, dto.Type, dto.Length, dto.SuccessCount, dto.Message));
        }
    }

    [HttpPost(ApiPath.ReceiptCheck)]
    public async void ReceiptCheckRecalculation([FromBody] ReceiptCheckRecalculationRequest request)
    {
        try
        {
            _messenger.Register<RecalculationStatus>(this, UpdateRecalculationStatus);
            _messenger.Register<StopCalcStatus>(this, StopCalculation);

            HttpContext.Response.ContentType = "application/json";

            var input = new ReceiptCheckRecalculationInputData(HpId, UserId, request.PtIds, request.SeikyuYm, request.ReceStatus, _messenger);
            _bus.Handle(input);
        }
        catch (Exception)
        {
            await SendMessage(new RecalculationStatus(true, CalculateStatusConstant.None, 0, 0, "再計算にエラーが発生しました。\n\rしばらくしてからもう一度お試しください。", string.Empty));
            throw;
        }
        finally
        {
            _messenger.Deregister<RecalculationStatus>(this, UpdateRecalculationStatus);
            _messenger.Deregister<StopCalcStatus>(this, StopCalculation);
            await HttpContext.Response.CompleteAsync();
            HttpContext.Response.Body.Close();
            _tenantProvider.DisposeDataContext();
        }
    }

    [HttpGet(ApiPath.DeleteReceiptInfEdit)]
    public ActionResult<Response<DeleteReceiptInfResponse>> DeleteReceiptInfEdit([FromQuery] DeleteReceiptInfEditRequest request)
    {
        var input = new DeleteReceiptInfEditInputData(HpId, UserId, request.PtId, request.SeikyuYm, request.SinYm, request.HokenId);
        var output = _bus.Handle(input);

        var presenter = new DeleteReceiptInfPresenter();
        presenter.Complete(output);
        _tenantProvider.DisposeDataContext();

        var okStatus = new[] { DeleteReceiptInfStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<DeleteReceiptInfResponse>>(presenter.Result);
    }

    [HttpDelete(ApiPath.CancelProccessCalculation)]
    public ActionResult<Response<CancelProccessCalculationResponse>> CancelProccessCalculation()
    {
        var input = new CancelProccessCalculationInputData(HpId, UserId, _messenger);
        var output = _bus.Handle(input);
        var presenter = new CancelProccessCalculationPresenter();
        presenter.Complete(output);
        _tenantProvider.DisposeDataContext();

        var okStatus = new[] { CancelProccessCalculationStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        _subscriptionService.ExecuteSubscription(string.Format(FunctionCodes.ProccessEnd, output.Message!.ProccessKey), new RecalculationMessage
        {
            HpId = HpId,
            UserId = UserId,
            IsRecalculationCheckBox = output.Message.IsRecalculationCheckBox,
            IsReceiptAggregationCheckBox = output.Message.IsReceiptAggregationCheckBox,
            IsCheckErrorCheckBox = output.Message.IsCheckErrorCheckBox,
            SinYm = output.Message.SinYm,
            PtIdList = output.Message.PtIdList,
            IsOperator = output.Message.IsOperator,
            OperatorName = output.Message.OperatorName,
            ProccessKey = output.Message.ProccessKey,
            IsSuccessRunning = (int)ProccessStatus.ProccessCancelled
        });

        Thread.Sleep(2000);
        _subscriptionService.DeleteSubscriptionHistory(string.Format(FunctionCodes.ProccessStart, output.Message!.ProccessKey));
        _subscriptionService.DeleteSubscriptionHistory(string.Format(FunctionCodes.ProcessRunning, output.Message!.ProccessKey));
        _subscriptionService.DeleteSubscriptionHistory(string.Format(FunctionCodes.ProccessEnd, output.Message!.ProccessKey));
        return new ActionResult<Response<CancelProccessCalculationResponse>>(presenter.Result);
    }

    [HttpGet(ApiPath.GetReCalculationInfo)]
    public ActionResult<Response<GetRecalculationInfoResponse>> GetRelCaculationInfo()
    {
        var input = new GetRecalculationInfoInputData(HpId, UserId);
        var output = _bus.Handle(input);
        var presenter = new GetRecalculationInfoPresenter();
        presenter.Complete(output);
        if (output.Status == GetRecalculationInfoStatus.Failed)
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }
        _tenantProvider.DisposeDataContext();
        return new ActionResult<Response<GetRecalculationInfoResponse>>(presenter.Result);
    }
}
