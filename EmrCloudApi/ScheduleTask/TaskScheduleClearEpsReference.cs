using EmrCloudApi.BackgroundService;
using EmrCloudApi.Services;

namespace EmrCloudApi.ScheduleTask;

public class TaskScheduleClearEpsReference : ScheduledProcessor
{
    public TaskScheduleClearEpsReference(IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
    }
    
    protected override string Schedule => "0 9 * * 1-6"; //9AM Mon-Sat (server time) 

    public override Task ProcessInScope(IServiceProvider scopeServiceProvider)
    {
        IClearEpsReferenceService clearEpsReferenceService = scopeServiceProvider.GetRequiredService<IClearEpsReferenceService>();

        clearEpsReferenceService.ClearEpsReference();

        return Task.CompletedTask;
    }
}