﻿using EmrCloudApi.BackgroundService;
using EmrCloudApi.Services;

namespace EmrCloudApi.ScheduleTask;

public class TaskScheduleKarteAutoApprove : ScheduledProcessor
{
    private readonly IConfiguration _configuration;
    private readonly string _schedule;
    public TaskScheduleKarteAutoApprove(IServiceScopeFactory serviceScopeFactory, IConfiguration configuration) : base(serviceScopeFactory)
    {
        _configuration = configuration;
        _schedule = _configuration["ScheduleTask:KarteAutoApprove"] ?? "0 4 * * *";
    }

    protected override string Schedule => _schedule;
    public override Task ProcessInScope(IServiceProvider scopeServiceProvider)
    {
        IKarteAutoApproveService karteAutoApproveService = scopeServiceProvider.GetRequiredService<IKarteAutoApproveService>();

        karteAutoApproveService.AutoApprove();

        return Task.CompletedTask;
    }
}