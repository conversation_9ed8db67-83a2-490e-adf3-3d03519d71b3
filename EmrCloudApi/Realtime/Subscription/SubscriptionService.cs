﻿using Domain.Models.Subscription;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace EmrCloudApi.Realtime.Subscription
{
    public class SubscriptionService : ISubscriptionService
    {
        private readonly ISubscriptionRepository _subscriptionRepository;

        public SubscriptionService(ISubscriptionRepository subscriptionRepository)
        {
            _subscriptionRepository = subscriptionRepository;
        }

        public bool ExecuteSubscription(string functionCode, object message)
        {
            var messageJson = JsonConvert.SerializeObject(message, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                StringEscapeHandling = StringEscapeHandling.Default
            });

            return _subscriptionRepository.InsertSubscription(functionCode, messageJson);
        }

        public void DeleteSubscriptionHistory(string functionCode)
        {
            _subscriptionRepository.DeleteSubscription(functionCode);
        }
    }
}
