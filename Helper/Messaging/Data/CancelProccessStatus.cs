﻿namespace Helper.Messaging.Data
{
    public class CancelProccessStatus
    {
        public CancelProccessStatus(int hpid, int userId, int isSuccessRunning)
        {
            HpId = hpid;
            UserId = userId;
            IsSuccessRunning = isSuccessRunning;
        }
        public int HpId { get; private set; }

        public int UserId { get; private set; }

        public int IsSuccessRunning { get; private set; }

    }
}
