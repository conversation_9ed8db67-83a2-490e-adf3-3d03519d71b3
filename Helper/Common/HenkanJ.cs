﻿using Newtonsoft.Json.Linq;
using System.Reflection;
using System.Text;
using Umayadia.Kana;


namespace Helper.Common
{
    public class HenkanJ
    {
        private static HenkanJ? _instance;
        public static HenkanJ Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new HenkanJ();
                }
                return _instance;
            }
        }

        private readonly List<string> _japaneseCharacterList = new List<string>();
        private HenkanJ()
        {
            var assembly = Assembly.GetExecutingAssembly();
            if (assembly == null)
            {
                return;
            }
            var resourceStream = assembly.GetManifestResourceStream(@"Helper.Resources.Fullsize-Halfsize.txt");
            if (resourceStream == null)
            {
                return;
            }
            using (var reader = new StreamReader(resourceStream, Encoding.UTF8))
            {
                while (!reader.EndOfStream)
                {
                    string? splitMe = reader.ReadLine();
                    if (splitMe == null)
                    {
                        continue;
                    }
                    _japaneseCharacterList.Add(splitMe);
                }
            }
        }

        public static string ToHalfsize(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return string.Empty;
            }

            string result = value.ToLower();
            string fullToHalf = KanaConverter.ToNarrow(result);

            return fullToHalf;
        }

        public static string ToHalfsizeWithOutToLower(string result)
        {
            if (string.IsNullOrEmpty(result))
            {
                return string.Empty;
            }

            string fullToHalf = KanaConverter.ToNarrow(result);

            return fullToHalf;
        }

        public static string ToFullsize(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return string.Empty;
            }

            string result = value.ToLower();
            string halfToFull = KanaConverter.ToWide(result);

            return halfToFull;
        }

        public static string ToFullsizeNoLower(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return string.Empty;
            }

            string halfToFull = KanaConverter.ToWide(value);
            return halfToFull;
        }
    }
}
