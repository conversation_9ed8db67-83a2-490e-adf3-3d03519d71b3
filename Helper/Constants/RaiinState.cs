﻿namespace Helper.Constants;

public static class RaiinState
{
    /// <summary>
    /// 来院予定
    /// </summary>
    public const int Reservation = 0;

    /// <summary>
    /// 資格確認済
    /// </summary>
    public const int Confirmation = 1;

    /// <summary>
    /// 受付済
    /// </summary>
    public const int Receptionist = 2;

    /// <summary>
    /// 呼出済
    /// </summary>
    public const int Called = 3;

    /// <summary>
    /// 診察中
    /// </summary>
    public const int Consulting = 4;

    /// <summary>
    /// 診察終了
    /// </summary>
    public const int ConsultationCompleted = 5;

    /// <summary>
    /// 金額確定
    /// </summary>
    public const int AmountConfirmed = 6;

    /// <summary>
    /// 支払済
    /// </summary>
    public const int Paid = 7;

    /// <summary>
    /// FCO精算待ち
    /// </summary>
    public const int FcoWaiting = 8;

    /// <summary>
    /// 削除
    /// </summary>
    public const int Deleted = 9;

    public static Dictionary<int, string> ReceptionStatusToText => new()
    {
        { Reservation, "来院予定" },
        { Confirmation, "資格確認済" },
        { Receptionist, "受付済" },
        { Called, "呼出済" },
        { Consulting, "診察中" },
        { ConsultationCompleted, "診察終了" },
        { AmountConfirmed, "金額確定" },
        { Paid, "支払済" },
        { FcoWaiting, "FCO精算待ち" },
        { Deleted, "削除" },
    };
}
