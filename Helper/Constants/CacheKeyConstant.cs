﻿namespace Helper.Constants;

public static class CacheKeyConstant
{
    public const string SummaryInfGetList = "SummaryInfGetList";
    public const string AlrgyElseGetList = "AlrgyElseGetList";
    public const string AlrgyFoodGetList = "AlrgyFoodGetList";
    public const string KioRekiGetList = "KioRekiGetList";
    public const string InfectionGetList = "InfectionGetList";
    public const string OtherDrugGetList = "OtherDrugGetList";
    public const string OtcDrugGetList = "OtcDrugGetList";
    public const string PtAlrgyDrugGetList = "PtAlrgyDrugGetList";
    public const string SuppleGetList = "SuppleGetList";
    public const string PtPregnancyGetList = "PtPregnancyGetList";
    public const string PtCmtInfGetList = "PtCmtInfGetList";
    public const string SeikaturekiInfGetList = "SeikaturekiInfGetList";
    public const string CacheKeyTenantId = "CacheKeyTenantId";
    public const string UserInfoCacheService = "UserInfoCacheService";
    public const string KaCacheService = "KaCacheService";
    public const string GetNextOrderList = "GetNextOrderList";
    public const string ColumnSetting = "ColumnSetting";
    public const string GetStaGrp = "GetStaGrp";
    public const string PaymentMethodMsts = "PaymentMethodMsts";
    public const string StaGrpModel = "StaGrpModel";
    public const string SystemGenerationConf = "SystemGenerationConf";
    public const string JsonSettings = "JsonSettings";
    public const string SyoukiKbnMst = "SyoukiKbnMst";
    public const string KohiPriority = "KohiPriority";
    public const string GetListSystemConf = "GetListSystemConf";
    public const string PtPregnantGetList = "PtPregnantGetList";
    public const string PtSocialHistoryGetList = "PtSocialHistoryGetList";
    public const string PtFamilyGetList = "PtFamilyGetList ";
    public const string CustomButtonParamMstGetList = "CustomButtonParamMstGetList";
    public const string CustomButtonConfGetList = "CustomButtonConfGetList";
    public const string IsProcessUpdate = "IsProcessUpdate";
    public const string InsuranceList = "InsuranceList";
    public const string AgentSetting = "AgentSetting";
    public const string KarteAutoApprove = "KarteAutoApprove";
}
