﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Entity;

[Table("pharmacy_reserve")]
public class PharmacyReserve : EmrCloneable<PharmacyReserve>
{
    [Key]
    [Column("pharmacy_reserve_id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int PharmacyReserveId { get; set; }

    [Column("patient_id")]
    public long PatientId { get; set; }

    [Column("desired_date_status")]
    [CustomAttribute.DefaultValue(2)]
    public int DesiredDateStatus { get; set; }

    [Column("reserve_update_date")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime ReserveUpdateDate { get; set; }

    [Column("reserve_id")]
    public int? ReserveId { get; set; }

    [Column("sms_status")]
    [CustomAttribute.DefaultValue(1)]
    public int SmsStatus { get; set; }

    [Column("videocall_status")]
    [CustomAttribute.DefaultValue(1)]
    public int VideocallStatus { get; set; }

    [Column("postal_service_type")]
    [CustomAttribute.DefaultValue(1)]
    public int PostalServiceType { get; set; }

    [Column("csv_status")]
    [CustomAttribute.DefaultValue(1)]
    public int CsvStatus { get; set; }

    [Column("pharmacist_status")]
    [CustomAttribute.DefaultValue(1)]
    public int PharmacistStatus { get; set; }

    [Column("pharmacist_id")]
    public int? PharmacistId { get; set; }

    [Column("created_at")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime CreatedAt { get; set; } 

    [Column("created_by")]
    public string CreatedBy { get; set; }

    [Column("updated_at")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime UpdatedAt { get; set; }

    [Column("updated_by")]
    public string UpdatedBy { get; set; }

    [Column("is_deleted")]
    [CustomAttribute.DefaultValue(0)]
    public int IsDeleted { get; set; }
}