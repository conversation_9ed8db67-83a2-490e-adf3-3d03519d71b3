﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Entity;

[Table("pharmacy_reserve_detail")]
public class PharmacyReserveDetail: EmrCloneable<PharmacyReserveDetail>
{
    [Key]
    [Column("pharmacy_reserve_detail_id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int PharmacyReserveDetailId { get; set; }

    [Column("pharmacy_reserve_id")]
    public int PharmacyReserveId { get; set; }

    [Column("reserve_detail_id")]
    public int? ReserveDetailId { get; set; }

    [Column("patient_id")]
    public long PatientId { get; set; }

    [Column("prescription_type")]
    [CustomAttribute.DefaultValue(1)]
    public int PrescriptionType { get; set; }

    [Column("status")]
    [CustomAttribute.DefaultValue(1)]
    public int Status { get; set; }

    [Column("guidance_status")]
    [CustomAttribute.DefaultValue(1)]
    public int GuidanceStatus { get; set; }

    [Column("payment_status")]
    [CustomAttribute.DefaultValue(1)]
    public int PaymentStatus { get; set; }

    [Column("has_prescription_record")]
    [CustomAttribute.DefaultValue(false)]
    public bool HasPrescriptionRecord { get; set; }

    [Column("is_taking_medication")]
    [CustomAttribute.DefaultValue(false)]
    public bool IsTakingMedication { get; set; }

    [Column("taking_medication_names")]
    [MaxLength(100)]
    public string? TakingMedicationNames { get; set; }

    [Column("medication_form_answer_date")]
    public DateTime? MedicationFormAnswerDate { get; set; }

    [Column("created_at")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime CreatedAt { get; set; }

    [Column("created_by")]
    public string CreatedBy { get; set; }

    [Column("updated_at")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime UpdatedAt { get; set; } 

    [Column("updated_by")]
    public string UpdatedBy { get; set; }

    [Column("is_deleted")]
    [CustomAttribute.DefaultValue(0)]
    public int IsDeleted { get; set; } = 0;

    [Column("generic_drug_desire")]
    public int? GenericDrugDesire { get; set; }
}