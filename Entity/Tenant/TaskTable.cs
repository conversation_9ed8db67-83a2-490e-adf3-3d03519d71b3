﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Entity;

[Table("task")]
public class TaskTable : EmrCloneable<TaskTable>
{
    [Key]
    [Column("task_id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int TaskId { get; set; }

    [Column("title")]
    [MaxLength(500)]
    public string Title { get; set; }

    [Column("detail_html")]
    public string? DetailHtml { get; set; }

    [Column("detail_text")]
    public string? DetailText { get; set; }

    [Column("expired_at")]
    public DateTime? ExpiredAt { get; set; }

    [Column("hospital_id")]
    public int HospitalId { get; set; }

    [Column("task_category_id")]
    public int TaskCategoryId { get; set; }

    [Column("task_status_id")]
    public int TaskStatusId { get; set; }

    [Column("created_staff_id")]
    public int? CreatedStaffId { get; set; }

    [Column("responsible_staff_id")]
    public int ResponsibleStaffId { get; set; }

    [Column("is_auto_created")]
    [CustomAttribute.DefaultValue(false)]
    public bool IsAutoCreated { get; set; }

    [Column("patient_id")]
    public long? PatientId { get; set; }

    [Column("created_by")]
    [MaxLength(100)]
    [CustomAttribute.DefaultValue("system")]
    public string CreatedBy { get; set; }

    [Column("updated_by")]
    [MaxLength(100)]
    [CustomAttribute.DefaultValue("system")]
    public string UpdatedBy { get; set; }

    [Column("created_at")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime? UpdatedAt { get; set; }

    [Column("is_deleted")]
    [CustomAttribute.DefaultValue(0)]
    public int IsDeleted { get; set; }
}