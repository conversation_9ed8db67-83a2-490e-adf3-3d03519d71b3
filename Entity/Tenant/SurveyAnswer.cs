﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using Entity;

[Table("survey_answer")]
public class SurveyAnswer: EmrCloneable<SurveyAnswer>
{
    [Key]
    [Column("survey_answer_id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int SurveyAnswerId { get; set; }

    [Column("survey_answer", TypeName = "jsonb")]
    public JsonDocument? SurveyAnswerData { get; set; }

    [Column("reserve_detail_id")]
    public int? ReserveDetailId { get; set; }

    [Column("patient_id")]
    public long PatientId { get; set; }

    [Column("treatment_title")]
    public string? TreatmentTitle { get; set; }

    [Column("treatment_type")]
    public int? TreatmentType { get; set; }

    [Column("hospital_id")]
    public int HospitalId { get; set; }

    [Column("common_survey", TypeName = "jsonb")]
    public JsonDocument CommonSurvey { get; set; }

    [Column("pharmacy_survey", TypeName = "jsonb")]
    public JsonDocument? PharmacySurvey { get; set; }

    [Column("created_by")]
    [MaxLength(100)]
    [CustomAttribute.DefaultValue("system")]
    public string CreatedBy { get; set; } 

    [Column("updated_by")]
    [MaxLength(100)]
    [CustomAttribute.DefaultValue("system")]
    public string UpdatedBy { get; set; }

    [Column("created_at")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime CreatedAt { get; set; }

    [Column("updated_at")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime UpdatedAt { get; set; }

    [Column("is_deleted")]
    [CustomAttribute.DefaultValue(0)]
    public int IsDeleted { get; set; }

    [Column("pharmacy_reserve_detail_id")]
    public int? PharmacyReserveDetailId { get; set; }
}