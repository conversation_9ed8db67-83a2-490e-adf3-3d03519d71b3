﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Tenant
{
    [Table("meeting")]
    public class Meeting : EmrCloneable<Meeting>
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("meeting_id")]
        public int MeetingId { get; set; }

        [Column("chime_meeting_id")]
        public Guid? ChimeMeetingId { get; set; }

        [Column("reserve_id")]
        public int? ReserveId { get; set; }

        [Column("pharmacy_reserve_id")]
        public int? PharmacyReserveId { get; set; }

        [Column("patient_id")]
        public long PatientId { get; set; }

        [Column("hospital_id")]
        public int HospitalId { get; set; }

        [Column("status")]
        public int Status { get; set; }

        [Column("created_by")]
        [StringLength(100)]
        [CustomAttribute.DefaultValue("system")]
        public string CreatedBy { get; set; }

        [Column("updated_by")]
        [StringLength(100)]
        [CustomAttribute.DefaultValue("system")]
        public string UpdatedBy { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        [Column("is_deleted")]
        [CustomAttribute.DefaultValue(0)]
        public int IsDeleted { get; set; } = 0;
    }
}
