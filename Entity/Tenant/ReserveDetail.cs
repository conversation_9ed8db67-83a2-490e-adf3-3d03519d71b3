﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Tenant
{
    [Table("reserve_detail")]
    public class ReserveDetail : EmrCloneable<ReserveDetail>
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("reserve_detail_id")]
        public int ReserveDetailId { get; set; }

        [Column("reserve_id")]
        public int ReserveId { get; set; }

        [Column("patient_id")]
        public long? PatientId { get; set; }

        [Column("exam_detail_id")]
        public int? ExamDetailId { get; set; }

        [Column("queue_id")]
        public int? QueueId { get; set; }

        [Column("status")]
        public int Status { get; set; }

        [Column("reserve_cancel_type")]
        public int? ReserveCancelType { get; set; }

        [Column("reserve_type")]
        public int? ReserveType { get; set; }

        [Column("treatment_type")]
        public int? TreatmentType { get; set; }

        [Column("calendar_treatment_id")]
        public int CalendarTreatmentId { get; set; }

        [Column("memo")]
        [StringLength(250)]
        public string? Memo { get; set; }

        [Column("payment_status")]
        [CustomAttribute.DefaultValue(1)]
        public int PaymentStatus { get; set; }

        [Column("payment_card_id")]
        [StringLength(300)]
        public string? PaymentCardId { get; set; }

        [Column("fincode_customer_id")]
        [StringLength(300)]
        public string? FincodeCustomerId { get; set; }

        [Column("fincode_tenant_id")]
        [StringLength(50)]
        public string? FincodeTenantId { get; set; }

        [Column("exam_time_slot_id")]
        public int ExamTimeSlotId { get; set; }

        [Column("is_suspended")]
        [CustomAttribute.DefaultValue(false)]
        public bool IsSuspended { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("created_by")]
        [StringLength(150)]
        public string CreatedBy { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        [Column("updated_by")]
        [StringLength(150)]
        public string UpdatedBy { get; set; }
        [Column("is_reminder_sent")]

        [CustomAttribute.DefaultValue(false)]
        public bool IsReminderSent { get; set; }

        [Column("is_deleted")]
        [CustomAttribute.DefaultValue(0)]
        public int IsDeleted { get; set; }
    }
}
