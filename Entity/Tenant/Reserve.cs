﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Tenant
{
    [Table("reserve")]
    public class Reserve : EmrCloneable<Reserve>
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("reserve_id")]
        public int ReserveId { get; set; }

        [Column("patient_id")]
        public long? PatientId { get; set; }

        [Column("prescription_receive_method")]
        [CustomAttribute.DefaultValue(0)]
        public int PrescriptionReceiveMethod { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("created_by")]
        [StringLength(150)]
        public string CreatedBy { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        [Column("updated_by")]
        [StringLength(150)]
        public string UpdatedBy { get; set; }

        [Column("is_deleted")]
        [CustomAttribute.DefaultValue(0)]
        public int IsDeleted { get; set; }
    }
}
