﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Tenant
{
    [Table(name: "payment_clinic_detail_history")]
    public class PaymentClinicDetailHistory : EmrCloneable<PaymentClinicDetailHistory>
    {
        [Key]
        [Column("payment_clinic_detail_history_id")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long PaymentClinicDetailHistoryId { get; set; }

        [Column("payment_clinic_detail_id")]
        public long PaymentClinicDetailId { get; set; }

        [Column("hospital_id")]
        public int HospitalId { get; set; }

        [Column("patient_id")]
        public long PatientId { get; set; }

        [Column("customer_id")]
        public int CustomerId { get; set; }

        [Column("action_type")]
        public int ActionType { get; set; } = 1;

        [Column("payment_type")]
        public int PaymentType { get; set; }

        [Column("payment_date")]
        [CustomAttribute.DefaultValueSql("statement_timestamp")]
        public DateTime PaymentDate { get; set; }

        [Column("payment_status")]
        [CustomAttribute.DefaultValue(0)]
        public int PaymentStatus { get; set; }

        [Column("first_success_date")]
        public DateTime? FirstSuccessDate { get; set; }

        [Column("total_amount")]
        [CustomAttribute.DefaultValue(0)]
        public int TotalAmount { get; set; }

        [Column("deposit_amount")]
        [CustomAttribute.DefaultValue(0)]
        public int DepositAmount { get; set; }

        [Column("is_retryable")]
        [CustomAttribute.DefaultValue(0)]
        public int IsRetryable { get; set; }

        [Column("fincode_error_code")]
        [MaxLength(15)]
        public string? FincodeErrorCode { get; set; }

        [Column("error_message")]
        public string? ErrorMessage { get; set; }

        [Column("payment_fincode_order_id")]
        public long? PaymentFincodeOrderId { get; set; }

        [Column("reserve_detail_id")]
        public int ReserveDetailId { get; set; }

        [Column("exam_date")]
        public DateTime ExamDate { get; set; }

        [Column("treatment_category_id")]
        public int TreatmentCategoryId { get; set; }

        [Column("treatment_department_id")]
        public int TreatmentDepartmentId { get; set; }

        [Column("card_no")]
        [MaxLength(20)]
        public string CardNo { get; set; }

        [Column("expire")]
        [MaxLength(4)]
        public string Expire { get; set; }

        [Column("holder_name")]
        [MaxLength(60)]
        public string HolderName { get; set; }

        [Column("brand")]
        [MaxLength(60)]
        public string Brand { get; set; }

        [Column("created_at")]
        [CustomAttribute.DefaultValueSql("statement_timestamp")]
        public DateTime CreatedAt { get; set; }

        [Column("created_by")]
        public string CreatedBy { get; set; }

        [Column("updated_at")]
        [CustomAttribute.DefaultValueSql("statement_timestamp")]
        public DateTime UpdatedAt { get; set; } 

        [Column("updated_by")]
        public string UpdatedBy { get; set; }

        [Column("is_deleted")]
        [CustomAttribute.DefaultValue(0)]
        public int IsDeleted { get; set; }
    }
}
