﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Entity;

[Table("pharmacy_patient_file")]
public class PharmacyPatientFile : EmrCloneable<PharmacyPatientFile>
{
    [Key]
    [Column("pharmacy_patient_file_id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int PharmacyPatientFileId { get; set; }

    [Column("pharmacy_reserve_detail_id")]
    public int PharmacyReserveDetailId { get; set; }

    [Column("patient_id")]
    public long PatientId { get; set; }

    [Column("original_file_name")]
    public string OriginalFileName { get; set; }

    [Column("s3_key")]
    public string S3Key { get; set; }

    [Column("created_by")]
    [MaxLength(100)]
    [CustomAttribute.DefaultValue("system")]
    public string CreatedBy { get; set; }

    [Column("updated_by")]
    [MaxLength(100)]
    [CustomAttribute.DefaultValue("system")]
    public string UpdatedBy { get; set; }

    [Column("created_at")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    [CustomAttribute.DefaultValueSql("statement_timestamp")]
    public DateTime? UpdatedAt { get; set; }

    [Column("is_deleted")]
    public int IsDeleted { get; set; } = 0;

    [Column("type")]
    public int Type { get; set; } = 1;
}