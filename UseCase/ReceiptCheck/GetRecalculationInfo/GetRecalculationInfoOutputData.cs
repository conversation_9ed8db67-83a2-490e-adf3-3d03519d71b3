﻿using Domain.Models.Recalculation;
using UseCase.Core.Sync.Core;

namespace UseCase.ReceiptCheck.GetRecalculationInfo
{
    public class GetRecalculationInfoOutputData : IOutputData
    {
        public GetRecalculationInfoOutputData(RecalculationMessage processInfo, GetRecalculationInfoStatus status)
        {
            ProcessInfo = processInfo;
            Status = status;
        }
        public GetRecalculationInfoStatus Status { get; private set; }
        public RecalculationMessage ProcessInfo { get; private set; }

    }
}
