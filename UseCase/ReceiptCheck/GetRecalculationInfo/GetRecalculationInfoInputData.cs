﻿using UseCase.Core.Sync.Core;

namespace UseCase.ReceiptCheck.GetRecalculationInfo
{
    public class GetRecalculationInfoInputData : IInputData<GetRecalculationInfoOutputData>
    {
        public GetRecalculationInfoInputData(int hpId, int userId)
        {
            HpId = hpId;
            UserId = userId;
        }
        public int HpId { get; private set; }

        public int UserId { get; private set; }

    }
}
