﻿using Domain.Models.PatientInfor;
using UseCase.Core.Sync.Core;
using UseCase.PatientInfor.Save;

namespace UseCase.PatientInfor.SaveBasicInfo.SaveInsuranceInfo
{
    public class SaveInsuranceInfoOutputData: IOutputData
    {
        public SaveInsuranceInfoOutputData(IEnumerable<SavePatientInfoValidationResult> validateDetails, SavePatientInfoStatus status, long ptID, int hokenId, long onlineConfirmationHisId, PatientInforModel patientInforModel)
        {
            ValidateDetails = validateDetails;
            Status = status;
            PtID = ptID;
            HokenId = hokenId;
            OnlineConfirmationHisId = onlineConfirmationHisId;
            PatientInforModel = patientInforModel;
        }

        public IEnumerable<SavePatientInfoValidationResult> ValidateDetails { get; private set; }

        public SavePatientInfoStatus Status { get; private set; }

        public long PtID { get; private set; }
        
        public int HokenId { get; private set; }

        public long OnlineConfirmationHisId { get; private set; }

        public PatientInforModel PatientInforModel { get; private set; }
    }
}
