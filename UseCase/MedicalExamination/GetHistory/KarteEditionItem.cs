﻿using System.Text.Json.Serialization;
using Domain.Models.HistoryOrder;

namespace UseCase.MedicalExamination.GetHistory
{
    public class KarteEditionItem
    {
        [JsonPropertyName("hpId")]
        public int HpId { get; private set; }

        [JsonPropertyName("ptId")]
        public long PtId { get; private set; }

        [JsonPropertyName("raiinNo")]
        public long RaiinNo { get; private set; }

        [JsonPropertyName("edition")]
        public int Edition { get; private set; }

        [JsonPropertyName("karteStatus")]
        public int KarteStatus { get; private set; }

        [JsonPropertyName("KarteStatusText")]
        public string KarteStatusText
        {
            get
            {
                return KarteStatus switch
                {
                    0 => "来院予定",
                    1 => "資格確認済",
                    2 => "受付済",
                    3 => "呼出済",
                    4 => "診察中",
                    5 => "診察終了",
                    6 => "金額確定",
                    7 => "支払済",
                    9 => "削除",
                    _ => "",
                };
            }
        }

        [JsonPropertyName("isDeleted")]
        public int IsDeleted { get; private set; }

        [JsonPropertyName("createDate")]
        public DateTime CreateDate { get; private set; }

        [JsonPropertyName("createId")]
        public int CreateId { get; private set; }

        [JsonPropertyName("updateDate")]
        public DateTime UpdateDate { get; private set; }

        [JsonPropertyName("updateId")]
        public int UpdateId { get; private set; }

        [JsonPropertyName("approvalDate")]
        public DateTime? ApprovalDate { get; private set; }

        [JsonPropertyName("approvalId")]
        public int ApprovalId { get; private set; }

        [JsonPropertyName("updateName")]
        public string UpdateName { get; private set; }

        [JsonPropertyName("approvalName")]
        public string ApprovalName { get; private set; }

        [JsonPropertyName("hokenGroups")]
        public List<HokenGroupHistoryItem> HokenGroups { get; private set; }

        [JsonPropertyName("karteHistories")]
        public List<GrpKarteHistoryItem> KarteHistories { get; private set; }

        [JsonPropertyName("listKarteFiles")]
        public List<FileInfOutputItem> ListKarteFiles { get; private set; }

        [JsonPropertyName("headerOrderModels")]
        public List<HeaderOrderModel> HeaderOrderModels { get; private set; }

        public KarteEditionItem()
        {
            HokenGroups = new();
            KarteHistories = new();
            ListKarteFiles = new();
            HeaderOrderModels = new();
        }

        public KarteEditionItem(int hpId, long ptId, long raiinNo, int edition, int karteStatus, int isDeleted, DateTime createDate, int createId, DateTime updateDate, int updateId, DateTime? approvalDate, int approvalId, string updateName, string approvalName, List<HokenGroupHistoryItem> hokenGroups, List<GrpKarteHistoryItem> karteHistories, List<FileInfOutputItem> listKarteFiles)
        {
            HpId = hpId;
            PtId = ptId;
            RaiinNo = raiinNo;
            Edition = edition;
            KarteStatus = karteStatus;
            IsDeleted = isDeleted;
            CreateDate = createDate;
            CreateId = createId;
            UpdateDate = updateDate;
            UpdateId = updateId;
            ApprovalDate = approvalDate;
            ApprovalId = approvalId;
            UpdateName = updateName;
            ApprovalName = approvalName;
            HokenGroups = hokenGroups;
            KarteHistories = karteHistories;
            ListKarteFiles = listKarteFiles;
            HeaderOrderModels = new();
        }

        public KarteEditionItem(int hpId, long ptId, long raiinNo, int edition, int karteStatus, int isDeleted, DateTime createDate, int createId, DateTime updateDate, int updateId, DateTime? approvalDate, int approvalId, List<HokenGroupHistoryItem> hokenGroups, List<GrpKarteHistoryItem> karteHistories, List<FileInfOutputItem> listKarteFiles, List<HeaderOrderModel> headerOrderModels)
        {
            HpId = hpId;
            PtId = ptId;
            RaiinNo = raiinNo;
            Edition = edition;
            KarteStatus = karteStatus;
            IsDeleted = isDeleted;
            CreateDate = createDate;
            CreateId = createId;
            UpdateDate = updateDate;
            UpdateId = updateId;
            ApprovalDate = approvalDate;
            ApprovalId = approvalId;
            HokenGroups = hokenGroups;
            KarteHistories = karteHistories;
            ListKarteFiles = listKarteFiles;
            HeaderOrderModels = headerOrderModels;
        }

        public void SetHokenGroupValue(List<HokenGroupHistoryItem> hokenGroups)
        {
            HokenGroups = hokenGroups;
        }
    }
}
