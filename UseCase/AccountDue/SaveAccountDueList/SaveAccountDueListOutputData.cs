﻿using Domain.Constant;
using Domain.Models.AccountDue;
using Domain.Models.Reception;
using UseCase.Core.Sync.Core;

namespace UseCase.AccountDue.SaveAccountDueList;

public class SaveAccountDueListOutputData : IOutputData
{
    public SaveAccountDueListOutputData(SaveAccountDueListStatus status)
    {
        Status = status;
        AccountDueModel = new AccountDueListModel(new(), new(), new());
        ReceptionInfos = new();
        SameVisitList = new();
    }

    public SaveAccountDueListOutputData(SaveAccountDueListStatus status, string? errorCode, string? userMessage)
    {
        Status = status;
        AccountDueModel = new AccountDueListModel(new(), new(), new());
        ReceptionInfos = new();
        SameVisitList = new();
        ErrorCode = errorCode;
        UserMessage = userMessage;
    }

    public SaveAccountDueListOutputData(SaveAccountDueListStatus status, List<AccountDueModel> accountDueList, List<ReceptionForViewDto> receptionInfos, List<SameVisitModel> sameVisitList)
    {
        Status = status;
        AccountDueModel = new AccountDueListModel(accountDueList, new(), new());
        ReceptionInfos = receptionInfos;
        SameVisitList = sameVisitList;
    }

    public SaveAccountDueListStatus Status { get; private set; }

    public AccountDueListModel AccountDueModel { get; private set; }

    public List<ReceptionForViewDto> ReceptionInfos { get; private set; }

    public List<SameVisitModel> SameVisitList { get; private set; }

    public string? ErrorCode { get; private set; }

    public string? UserMessage { get; private set; }

}
