﻿using Helper.Messaging;
using UseCase.Core.Sync.Core;

namespace UseCase.Receipt.CancelProccessCaculation
{
    public class CancelProccessCalculationInputData : IInputData<CancelProccessCalculationOutputData>
    {
        public CancelProccessCalculationInputData(int hpId, int userId, IMessenger messenger)
        {
            HpId = hpId;
            UserId = userId;
            Messenger = messenger;
        }

        public int HpId { get; private set; }

        public int UserId { get; private set; }

        public IMessenger Messenger { get; private set; }

    }
}
