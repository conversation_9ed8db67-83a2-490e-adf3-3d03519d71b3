﻿using Domain.Models.Recalculation;
using UseCase.Core.Sync.Core;

namespace UseCase.Receipt.CancelProccessCaculation
{
    public class CancelProccessCalculationOutputData : IOutputData
    {
        public CancelProccessCalculationOutputData(CancelProccessCalculationStatus status, RecalculationMessage? message = null)
        {
            Status = status;
            Message = message;
        }

        public RecalculationMessage? Message { get; private set; }
        public CancelProccessCalculationStatus Status { get; private set; }
    }
}
