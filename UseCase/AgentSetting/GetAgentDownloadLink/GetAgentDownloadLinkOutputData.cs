using UseCase.Core.Sync.Core;

namespace UseCase.AgentSetting.GetAgentDownloadLink
{
    public sealed class GetAgentDownloadLinkOutputData : IOutputData
    {
        public GetAgentDownloadLinkOutputData(string downloadUrl, string fileName, GetAgentDownloadLinkStatus status)
        {
            DownloadUrl = downloadUrl;
            FileName = fileName;
            Status = status;
        }

        public string DownloadUrl { get; private set; }
        public string FileName { get; private set; }
        public GetAgentDownloadLinkStatus Status { get; private set; }
    }
}
