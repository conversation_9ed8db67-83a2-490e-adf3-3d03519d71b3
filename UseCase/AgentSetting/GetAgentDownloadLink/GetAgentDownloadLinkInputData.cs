using UseCase.Core.Sync.Core;

namespace UseCase.AgentSetting.GetAgentDownloadLink
{
    public class GetAgentDownloadLinkInputData : IInputData<GetAgentDownloadLinkOutputData>
    {
        public GetAgentDownloadLinkInputData(int hpId, string deviceType)
        {
            HpId = hpId;
            DeviceType = deviceType;
        }

        public int HpId { get; set; }
        public string DeviceType { get; set; } = string.Empty;
    }
}
