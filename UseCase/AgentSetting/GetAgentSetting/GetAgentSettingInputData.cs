﻿using UseCase.Core.Sync.Core;

namespace UseCase.AgentSetting.GetAgentSetting
{
    public class GetAgentSettingInputData : IInputData<GetAgentSettingOutputData>
    {
        public int HpId { get; set; }

        public string Host { get; set; } = string.Empty;

        public bool IncludeSetting { get; set; }

        public bool IncludeVersion { get; set; }

        public string FileNameApp { get; set; } = string.Empty;
    }
}
