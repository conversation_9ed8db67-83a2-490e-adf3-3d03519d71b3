# Table CRUD Documentation Generator

## 概要

`table_crud_generator.py`は、SmartKarteサーバーのREST API依存関係ドキュメント（`rest_api_dependencies.md`）を解析し、データベーステーブル中心のCRUD（作成、読み取り、更新、削除）ドキュメントを自動生成するPythonスクリプトです。

このスクリプトにより、APIエンドポイントベースのドキュメントとは別に、特定のデータベーステーブルがどのAPIから、どのような操作（CRUD）を受けているかを一覧で確認できるようになります。これにより、テーブルへの影響範囲の調査やリファクタリングが容易になります。

## 機能

- REST APIエンドポイントとリポジトリ呼び出しをMarkdownから解析
- リポジトリのメソッド名からCRUD操作を自動的に分類
- テーブルごとに全ての関連するCRUD操作を整理
- 目次付きの読みやすいMarkdownドキュメントを生成

## 前提条件

### 入力ファイルのフォーマット

このスクリプトは、特定のフォーマットで記述されたMarkdownファイルを入力として想定しています。`rest_api_dependencies.md`は以下の形式である必要があります。

- APIエンドポイントはテーブル形式でリストされていること。
- テーブルのヘッダーは `| HTTP Method | Endpoint | Controller | Method | Interactor | Repository Calls |` であること。
- `Repository Calls` 列には、`RepositoryName.methodName(\`table_name\`)` の形式でリポジトリ呼び出しが記載されていること。複数の呼び出しは `<br/>` で区切ります。

例：
```markdown
| HTTP Method | Endpoint | Controller | Method | Interactor | Repository Calls |
|---|---|---|---|---|---|
| POST | `/api/v1/users` | `UserController` | `CreateUser` | `CreateUserInteractor` | `UserRepository.save(\`users\`)<br/>AuditLogRepository.add(\`audit_logs\`)` |
```

## 使用方法

### 基本的な使用

リポジトリのルートディレクトリで以下のコマンドを実行します。

```bash
python3 scripts/table_crud_generator.py -i docs/rest_api_dependencies.md -o docs/table_crud_documentation.md
```

これにより：
- `docs/rest_api_dependencies.md` を入力として読み込み
- `docs/table_crud_documentation.md` を出力として生成します。

### オプション

```bash
python3 scripts/table_crud_generator.py --help
```

利用可能なオプション：
- `--input, -i`: 入力となるAPI依存関係Markdownファイルのパス。（デフォルト: `rest_api_dependencies.md`）
- `--output, -o`: 生成されるCRUDドキュメントの出力パス。（デフォルト: `table_crud_documentation.md`）
- `--verbose, -v`: 実行中に詳細なログを出力します。

### 例

#### 基本的な実行
```bash
python3 scripts/table_crud_generator.py -i docs/rest_api_dependencies.md -o docs/table_crud_documentation.md
```

#### カスタム入出力ファイル
```bash
python3 scripts/table_crud_generator.py -i docs/my_api_deps.md -o docs/my_crud_docs.md
```

## 依存関係

- Python 3.7以上（`dataclasses`モジュールを使用するため）
- 標準ライブラリのみ

## 出力形式

生成されるドキュメントは以下の構造を持ちます：

```markdown
# Table CRUD Documentation

This document provides a table-centric view of all CRUD operations in the system.

## Table of Contents
- [table1](#table1)
- [table2](#table2)
...

---

## table1

### CRUD Operations

| Table Name | CRUD | HTTP Method | Endpoint | Controller | Method | Interactor | Repository Calls |
|------------|------|-------------|----------|------------|--------|------------|------------------|
| table1 | C | POST | `/api/...` | `Controller` | `Method` | `Interactor` | `Repository.Method(\`table1\`)` |
...
```

## CRUD操作の分類

スクリプトはリポジトリのメソッド名に含まれるキーワードに基づいて、CRUD操作を以下のように分類します。

- **C (Create)**: `create`, `insert`, `add`, `save`, `register`, `upsert`, `generate`
- **R (Read)**: `get`, `find`, `search`, `check`, `list`, `fetch`, `select`, `query`, `exists`
- **U (Update)**: `update`, `edit`, `modify`, `change`, `set`, `refresh`
- **D (Delete)**: `delete`, `remove`, `destroy`, `clear`


## 実行結果の例

```bash
$ python3 scripts/table_crud_generator.py -i docs/rest_api_dependencies.md -o docs/table_crud_documentation.md

Summary:
  - Parsed 706 API endpoints
  - Found operations for 105 tables
  - Documentation written to docs/table_crud_documentation.md

Top 10 tables by operation count:
  - ten_mst: 142 operations
  - pt_inf: 123 operations
  - raiin_inf: 89 operations
  ...
```