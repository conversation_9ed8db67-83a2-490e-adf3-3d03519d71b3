# REST API Dependencies Analyzer

## 概要

`rest_api_dependencies.py` は、SmartKarteサーバーのC#ソースコードを静的解析し、REST APIのエンドポイントから、それが依存するコントローラ、インタラクタ、リポジトリ、そして最終的に関連するデータベーステーブルまでを追跡し、ドキュメント化するPythonスクリプトです。

## 目的

このスクリプトは、システム全体のデータフローを可視化することを目的としています。生成されるドキュメント（`rest_api_dependencies.md`）を参照することで、以下のことが可能になります。

- 特定のAPIエンドポイントがどのデータベーステーブルに影響を与えるかの影響範囲調査
- 特定のデータベーステーブルが、どのAPIから操作（CRUD）されているかの逆引き調査
- リファクタリングや機能追加時の依存関係の確認

## 機能

- **コントローラ解析**: `EmrCloudApi/Controller` と `EmrCalculateApi/Controllers` ディレクトリ内のC#ファイルを解析し、`[HttpGet]`, `[HttpPost]`などの属性からAPIエンドポイントを抽出します。
- **依存関係追跡**: `Controller` → `Interactor` → `Repository` の呼び出し関係を、コード内の命名規則（例: `...InputData`、`...Interactor`）やメソッド呼び出しを基に追跡します。
- **テーブル名の推定**: リポジトリのクラス名から、操作対象となるデータベーステーブル名を推定します。このマッピングはスクリプト内にハードコードされています。
- **Markdown生成**: 解析結果を `docs/rest_api_dependencies.md` に、見やすいMarkdownテーブル形式で出力します。

## 実行方法

プロジェクトのルートディレクトリから以下のコマンドを実行してください。

```bash
python3 scripts/rest_api_dependencies.py
```

- **入力**: スクリプトはプロジェクト内のソースコードを直接読み込みます。
- **出力**: `docs/rest_api_dependencies.md` が生成・上書きされます。

このスクリプトはコマンドライン引数を取りません。

## 依存関係

- Python 3.7以上（`dataclasses`モジュールを使用するため）
- 標準ライブラリのみで動作します。

## 出力形式

生成される`rest_api_dependencies.md`は以下のようになります。

```markdown
# REST API Dependencies

| HTTP Method | Endpoint | Controller | Method | Interactor | Repository Calls & Tables |
|-------------|----------|------------|--------|------------|---------------------------|
| GET | `api/User/Get` | `UserController` | `GetUser` | `GetUserInteractor` | `UserRepository.FindById (`user_mst`)`<br/>`CompanyRepository.Get (`company_mst`)` |
| POST | `api/User/Save` | `UserController` | `SaveUser` | `SaveUserInteractor` | `UserRepository.Save (`user_mst`)` |
```

## 仕組みと注意点

- **静的解析**: このスクリプトは、リフレクションや実際のコード実行ではなく、正規表現を用いてソースコードのテキストを解析（静的解析）しています。そのため、コードが特定の命名規則やコーディングパターンに従っていることを前提としています。
- **テーブル名の推定**: リポジトリとデータベーステーブルの関連付けは、`_guess_tables_from_method` 関数内の `table_mappings` という辞書に基づいています。これはあくまで**推定**であり、100%の正確性を保証するものではありません。新しいリポジトリを追加した場合や、命名規則から外れる場合は、この辞書を更新する必要があります。
- **プロジェクト構造への依存**: スクリプトは現在のプロジェクトのディレクトリ構造（`EmrCloudApi/Controller`など）に依存しています。ディレクトリ構成が変更された場合は、スクリプトの修正が必要です。 