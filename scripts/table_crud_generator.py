#!/usr/bin/env python3
"""
Table CRUD Documentation Generator for SmartKarte Server

This script parses REST API dependencies and generates table-centric CRUD documentation.
It extracts repository operations from rest_api_dependencies.md and creates a comprehensive
document showing all CRUD operations organized by database tables.
"""

import re
import json
from typing import Dict, List, Optional
from dataclasses import dataclass
from collections import defaultdict
import argparse
import logging
from pathlib import Path



@dataclass
class RepositoryCall:
    """Represents a repository method call with table information"""
    repository: str
    method: str
    table: str


@dataclass
class ApiEndpoint:
    """Represents a REST API endpoint with its dependencies"""
    http_method: str
    endpoint: str
    controller: str
    method: str
    interactor: str
    repository_calls: List[RepositoryCall]


@dataclass
class TableOperation:
    """Represents a CRUD operation on a table"""
    table_name: str
    crud_type: str  # C, R, U, D
    endpoint: ApiEndpoint


class RestApiParser:
    """Parser for REST API dependencies markdown file"""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        
    def parse(self) -> List[ApiEndpoint]:
        """Parse the markdown file and extract API endpoints"""
        endpoints = []
        
        with open(self.file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Skip header lines
        i = 0
        while i < len(lines) and not lines[i].strip().startswith('| GET'):
            i += 1
        
        # Parse table rows
        while i < len(lines):
            line = lines[i].strip()
            if line and line.startswith('|') and not line.startswith('|-'):
                parts = [p.strip() for p in line.split('|')[1:-1]]
                
                if len(parts) >= 6 and parts[0] in ['GET', 'POST', 'PUT', 'DELETE']:
                    endpoint = ApiEndpoint(
                        http_method=parts[0],
                        endpoint=parts[1].strip('`'),
                        controller=parts[2].strip('`'),
                        method=parts[3].strip('`'),
                        interactor=parts[4].strip('`'),
                        repository_calls=self._parse_repository_calls(parts[5])
                    )
                    endpoints.append(endpoint)
            
            i += 1
        
        return endpoints
    
    def _parse_repository_calls(self, calls_str: str) -> List[RepositoryCall]:
        """Parse repository calls from the string"""
        calls = []
        
        # Split by <br/> or newline
        call_parts = re.split(r'<br/>|\n', calls_str)
        
        for part in call_parts:
            part = part.strip()
            if not part:
                continue
            
            # Pattern: Repository.Method (`table_name`)
            match = re.match(r'(\w+Repository)\.(\w+)\s*\(`([^`]+)`\)', part)
            if match:
                repository, method, table = match.groups()
                calls.append(RepositoryCall(
                    repository=repository,
                    method=method,
                    table=table
                ))
        
        return calls


class TableOperationExtractor:
    """Extracts CRUD operations from API endpoints"""
    
    CRUD_MAPPING = {
        # Create operations
        'create': 'C', 'insert': 'C', 'add': 'C', 'save': 'C', 'register': 'C',
        'upsert': 'C', 'generate': 'C',
        
        # Read operations
        'get': 'R', 'find': 'R', 'search': 'R', 'check': 'R', 'list': 'R',
        'fetch': 'R', 'select': 'R', 'query': 'R', 'exists': 'R',
        
        # Update operations
        'update': 'U', 'edit': 'U', 'modify': 'U', 'change': 'U', 'set': 'U',
        'refresh': 'U',
        
        # Delete operations
        'delete': 'D', 'remove': 'D', 'destroy': 'D', 'clear': 'D'
    }
    
    def extract_operations(self, endpoints: List[ApiEndpoint]) -> Dict[str, List[TableOperation]]:
        """Extract table operations grouped by table name"""
        table_operations = defaultdict(list)
        
        for endpoint in endpoints:
            for repo_call in endpoint.repository_calls:
                crud_type = self._determine_crud_type(repo_call.method)
                if crud_type:
                    operation = TableOperation(
                        table_name=repo_call.table,
                        crud_type=crud_type,
                        endpoint=endpoint
                    )
                    table_operations[repo_call.table].append(operation)
        
        return dict(table_operations)
    
    def _determine_crud_type(self, method_name: str) -> Optional[str]:
        """Determine CRUD type from method name"""
        method_lower = method_name.lower()
        
        for keyword, crud_type in self.CRUD_MAPPING.items():
            if keyword in method_lower:
                return crud_type
        
        return None




class MarkdownDocumentGenerator:
    """Generates markdown documentation for table CRUD operations"""
    
    def __init__(self):
        pass
        
    def generate_document(self, table_operations: Dict[str, List[TableOperation]]) -> str:
        """Generate complete markdown document"""
        doc_parts = []
        
        # Header
        doc_parts.append("# Table CRUD Documentation\n")
        doc_parts.append("This document provides a table-centric view of all CRUD operations in the system.\n")
        
        # Table of Contents
        doc_parts.append("\n## Table of Contents\n")
        for table_name in sorted(table_operations.keys()):
            doc_parts.append(f"- [{table_name}](#{table_name})")
        
        doc_parts.append("\n---\n")
        
        # Generate section for each table
        for table_name in sorted(table_operations.keys()):
            doc_parts.append(self._generate_table_section(table_name, table_operations[table_name]))
            doc_parts.append("\n---\n")
        
        return '\n'.join(doc_parts)
    
    def _generate_table_section(self, table_name: str, operations: List[TableOperation]) -> str:
        """Generate documentation section for a single table"""
        section_parts = []
        
        # Table name header
        section_parts.append(f"\n## {table_name}\n")
        
        # CRUD Operations section
        section_parts.append("### CRUD Operations\n")
        
        # Group operations by CRUD type
        operations_by_crud = defaultdict(list)
        for op in operations:
            operations_by_crud[op.crud_type].append(op)
        
        # Generate operations table
        section_parts.append("| Table Name | CRUD | HTTP Method | Endpoint | Controller | Method | Interactor | Repository Calls |")
        section_parts.append("|------------|------|-------------|----------|------------|--------|------------|------------------|")
        
        for crud_type in ['C', 'R', 'U', 'D']:
            if crud_type in operations_by_crud:
                for op in operations_by_crud[crud_type]:
                    endpoint = op.endpoint
                    repo_calls = self._format_repository_calls(endpoint.repository_calls)
                    
                    section_parts.append(
                        f"| {table_name} | {crud_type} | {endpoint.http_method} | "
                        f"`{endpoint.endpoint}` | `{endpoint.controller}` | "
                        f"`{endpoint.method}` | `{endpoint.interactor}` | {repo_calls} |"
                    )
        
        return '\n'.join(section_parts)
    
    def _format_repository_calls(self, repo_calls: List[RepositoryCall]) -> str:
        """Format repository calls for table cell"""
        formatted_calls = []
        for call in repo_calls:
            formatted_calls.append(f"`{call.repository}.{call.method}` (`{call.table}`)")
        
        return '<br/>'.join(formatted_calls)


def main():
    """Main function to orchestrate the document generation"""
    parser = argparse.ArgumentParser(description='Generate table CRUD documentation from REST API dependencies')
    parser.add_argument('--input', '-i', 
                       default='rest_api_dependencies.md',
                       help='Input REST API dependencies file')
    parser.add_argument('--output', '-o',
                       default='table_crud_documentation.md',
                       help='Output documentation file')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s - %(levelname)s - %(message)s')
    
    
    # Parse REST API dependencies
    logging.info(f"Parsing {args.input}...")
    parser = RestApiParser(args.input)
    endpoints = parser.parse()
    logging.info(f"Found {len(endpoints)} API endpoints")
    
    # Extract table operations
    logging.info("Extracting table operations...")
    extractor = TableOperationExtractor()
    table_operations = extractor.extract_operations(endpoints)
    logging.info(f"Found operations for {len(table_operations)} tables")
    
    # Generate documentation
    logging.info("Generating documentation...")
    generator = MarkdownDocumentGenerator()
    document = generator.generate_document(table_operations)
    
    # Write output file
    with open(args.output, 'w', encoding='utf-8') as f:
        f.write(document)
    
    logging.info(f"Documentation written to {args.output}")
    
    # Print summary
    print(f"\nSummary:")
    print(f"  - Parsed {len(endpoints)} API endpoints")
    print(f"  - Found operations for {len(table_operations)} tables")
    print(f"  - Documentation written to {args.output}")
    
    # Print top tables by operation count
    table_op_counts = [(table, len(ops)) for table, ops in table_operations.items()]
    table_op_counts.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\nTop 10 tables by operation count:")
    for table, count in table_op_counts[:10]:
        print(f"  - {table}: {count} operations")


if __name__ == '__main__':
    main()