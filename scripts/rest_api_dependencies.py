#!/usr/bin/env python3
"""
SmartKarte REST API Dependencies Analyzer
This script analyzes the SmartKarte server codebase to extract REST API endpoints
and their dependencies (Controller -> Interactor -> Repository -> DB Tables).
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass, field
from collections import defaultdict


@dataclass
class ApiEndpoint:
    """Represents a REST API endpoint with its dependencies"""
    http_method: str
    route: str
    controller_name: str
    method_name: str
    interactor_name: str = ""
    repository_calls: List[Tuple[str, str]] = field(default_factory=list)  # (method, tables)


class SmartKarteApiAnalyzer:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.endpoints: List[ApiEndpoint] = []
        self.input_to_interactor_map: Dict[str, str] = {}
        
    def analyze(self):
        """Main analysis method"""
        print("Starting SmartKarte API analysis...")
        
        # Step 1: Analyze controllers
        print("\n1. Analyzing controllers...")
        self._analyze_controllers()
        
        # Step 2: Map InputData to Interactors
        print("\n2. Mapping InputData to Interactors...")
        self._map_input_to_interactors()
        
        # Step 3: Analyze Interactors for repository calls
        print("\n3. Analyzing Interactors for repository calls...")
        self._analyze_interactors()
        
        # Step 4: Generate output
        print("\n4. Generating output...")
        self._generate_markdown_output()
        
    def _analyze_controllers(self):
        """Analyze controller files to extract API endpoints"""
        controller_dirs = [
            self.project_root / "EmrCloudApi" / "Controller",
            self.project_root / "EmrCalculateApi" / "Controllers"
        ]
        
        for controller_dir in controller_dirs:
            if not controller_dir.exists():
                continue
                
            for controller_file in controller_dir.glob("*.cs"):
                if "HealthCheck" in controller_file.name:
                    continue
                    
                self._parse_controller_file(controller_file)
                
    def _parse_controller_file(self, file_path: Path):
        """Parse a single controller file"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # Extract controller name
            controller_match = re.search(r'public\s+class\s+(\w+Controller)', content)
            if not controller_match:
                return
                
            controller_name = controller_match.group(1)
            
            # Extract route prefix
            route_prefix_match = re.search(r'\[Route\("([^"]+)"\)\]', content)
            route_prefix = route_prefix_match.group(1) if route_prefix_match else ""
            
            # Find all action methods with more flexible pattern
            method_pattern = r'\[(Http(?:Get|Post|Put|Delete|Patch))(?:\s*\(\s*(?:ApiPath\.)?([^)]*)\s*\))?\]\s*\n\s*public\s+(?:async\s+)?(?:Task<)?ActionResult.*?>\s+(\w+)\s*\([^)]*\)'
            
            for match in re.finditer(method_pattern, content, re.MULTILINE | re.DOTALL):
                http_method = match.group(1).replace("Http", "").upper()
                route_suffix = match.group(2) or ""
                method_name = match.group(3)
                
                # Clean up route suffix (remove quotes and ApiPath references)
                route_suffix = route_suffix.strip('"').strip("'")
                if route_suffix.startswith("ApiPath."):
                    # Try to resolve ApiPath constant
                    route_suffix = self._resolve_api_path_constant(route_suffix)
                
                # Build full route
                if route_prefix:
                    route = route_prefix.replace("[controller]", controller_name.replace("Controller", ""))
                    if route_suffix:
                        route = f"{route}/{route_suffix}"
                else:
                    route = route_suffix or f"api/{controller_name.replace('Controller', '')}"
                
                # Extract InputData usage
                method_content = self._extract_method_content(content, method_name)
                input_data_match = re.search(r'new\s+(\w+InputData)\s*\(', method_content)
                
                # Also check for _bus.Handle pattern
                bus_handle_match = re.search(r'_bus\.Handle\s*\(\s*(?:new\s+)?(\w+InputData)', method_content)
                if not input_data_match and bus_handle_match:
                    input_data_match = bus_handle_match
                
                endpoint = ApiEndpoint(
                    http_method=http_method,
                    route=route,
                    controller_name=controller_name,
                    method_name=method_name
                )
                
                if input_data_match:
                    input_data_name = input_data_match.group(1)
                    endpoint.interactor_name = self._guess_interactor_name(input_data_name)
                
                self.endpoints.append(endpoint)
                
        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            
    def _extract_method_content(self, content: str, method_name: str) -> str:
        """Extract the content of a specific method"""
        pattern = rf'public\s+(?:async\s+)?(?:Task<)?ActionResult.*?>\s+{method_name}\s*\([^)]*\)\s*\{{'
        match = re.search(pattern, content, re.MULTILINE | re.DOTALL)
        
        if not match:
            return ""
            
        start = match.end()
        brace_count = 1
        end = start
        
        while brace_count > 0 and end < len(content):
            if content[end] == '{':
                brace_count += 1
            elif content[end] == '}':
                brace_count -= 1
            end += 1
            
        return content[start:end-1]
        
    def _resolve_api_path_constant(self, api_path_ref: str) -> str:
        """Try to resolve ApiPath constant to actual value"""
        # Common ApiPath constants based on the codebase
        api_path_mappings = {
            "ApiPath.Get": "Get",
            "ApiPath.GetList": "GetList",
            "ApiPath.Save": "Save",
            "ApiPath.Update": "Update",
            "ApiPath.Delete": "Delete",
            "ApiPath.SaveList": "SaveList",
            "ApiPath.Check": "Check",
            "ApiPath.Validate": "Validate",
        }
        
        for key, value in api_path_mappings.items():
            if api_path_ref.startswith(key):
                # Handle concatenated paths like ApiPath.Get + "Something"
                remaining = api_path_ref[len(key):].strip()
                if remaining.startswith('+'):
                    remaining = remaining[1:].strip().strip('"').strip("'")
                    return value + remaining
                return value
                
        # If not found in mappings, return the last part after dot
        parts = api_path_ref.split('.')
        if len(parts) > 1:
            return parts[-1]
        return api_path_ref
        
    def _guess_interactor_name(self, input_data_name: str) -> str:
        """Guess interactor name from InputData name"""
        # Remove "InputData" suffix and add "Interactor"
        base_name = input_data_name.replace("InputData", "")
        return f"{base_name}Interactor"
        
    def _map_input_to_interactors(self):
        """Scan Interactor directory to map InputData to Interactor classes"""
        interactor_dir = self.project_root / "Interactor"
        
        if not interactor_dir.exists():
            return
            
        for interactor_file in interactor_dir.rglob("*.cs"):
            try:
                content = interactor_file.read_text(encoding='utf-8')
                
                # Find interactor class name
                class_match = re.search(r'public\s+class\s+(\w+Interactor)\s*:', content)
                if not class_match:
                    continue
                    
                interactor_name = class_match.group(1)
                
                # Find Handle method with InputData parameter
                handle_match = re.search(r'public\s+\w+\s+Handle\s*\(\s*(\w+InputData)', content)
                if handle_match:
                    input_data_name = handle_match.group(1)
                    self.input_to_interactor_map[input_data_name] = interactor_name
                    
            except Exception as e:
                print(f"Error parsing {interactor_file}: {e}")
                
    def _analyze_interactors(self):
        """Analyze interactor files to extract repository calls"""
        interactor_dir = self.project_root / "Interactor"
        
        if not interactor_dir.exists():
            return
            
        # Update endpoints with correct interactor names
        for endpoint in self.endpoints:
            # Try to find correct interactor name from input data mapping
            for input_data, interactor in self.input_to_interactor_map.items():
                if input_data.replace("InputData", "") in endpoint.method_name:
                    endpoint.interactor_name = interactor
                    break
                    
        # Analyze each interactor for repository calls
        for endpoint in self.endpoints:
            if endpoint.interactor_name:
                self._analyze_interactor_file(endpoint)
                
    def _analyze_interactor_file(self, endpoint: ApiEndpoint):
        """Analyze a specific interactor file for repository calls"""
        # Find interactor file
        interactor_file = None
        for path in self.project_root.rglob(f"{endpoint.interactor_name}.cs"):
            if "Interactor" in str(path):
                interactor_file = path
                break
                
        if not interactor_file or not interactor_file.exists():
            return
            
        try:
            content = interactor_file.read_text(encoding='utf-8')
            
            # Find repository field declarations (both interface and concrete)
            repo_fields = []
            # Pattern for interface fields
            interface_pattern = r'private\s+(?:readonly\s+)?I(\w+Repository)\s+_(\w+);'
            for match in re.finditer(interface_pattern, content):
                repo_fields.append((match.group(1), match.group(2)))
            
            # Pattern for concrete repository fields
            concrete_pattern = r'private\s+(?:readonly\s+)?(\w+Repository)\s+_(\w+);'
            for match in re.finditer(concrete_pattern, content):
                if not match.group(1).startswith('I'):  # Skip interfaces
                    repo_fields.append((match.group(1), match.group(2)))
            
            # Find repository method calls
            repo_calls = []
            seen_calls = set()  # To avoid duplicates
            
            for repo_type, repo_var in repo_fields:
                # Find all method calls on this repository
                pattern = rf'_{repo_var}\.(\w+)\s*\('
                for match in re.finditer(pattern, content):
                    method_name = match.group(1)
                    
                    # Skip common non-data methods
                    if method_name in ['ReleaseResource', 'Dispose', 'BeginTransaction', 'Commit', 'Rollback']:
                        continue
                    
                    call_key = f"{repo_type}.{method_name}"
                    if call_key not in seen_calls:
                        seen_calls.add(call_key)
                        # Try to extract table names from the repository implementation
                        tables = self._guess_tables_from_method(repo_type, method_name)
                        repo_calls.append((call_key, tables))
                    
            endpoint.repository_calls = repo_calls
            
        except Exception as e:
            print(f"Error analyzing interactor {endpoint.interactor_name}: {e}")
            
    def _guess_tables_from_method(self, repo_type: str, method_name: str) -> str:
        """Guess table names from repository type and method name"""
        # Extended table mappings based on SmartKarte database schema
        table_mappings = {
            "PatientRepository": "pt_inf",
            "PatientInforRepository": "pt_inf",
            "HospitalRepository": "hp_inf",
            "HpInfRepository": "hp_inf",
            "StaffRepository": "user_mst",
            "UserRepository": "user_mst",
            "InsuranceRepository": "hoken_inf",
            "KohiRepository": "kohi_inf",
            "ReservationRepository": "reserve_detail",
            "ReserveDetailRepository": "reserve_detail",
            "OrderRepository": "ord_inf",
            "OrdInfRepository": "ord_inf",
            "TodayOdrRepository": "today_odr",
            "PrescriptionRepository": "prescription",
            "KensaRepository": "kensa_inf",
            "KensaInfRepository": "kensa_inf",
            "ReceiptRepository": "receden_inf",
            "ReceSeikyuRepository": "rece_seikyu",
            "AccountingRepository": "kaikei_inf",
            "AuditLogRepository": "audit_trail_log",
            "CalendarRepository": "calendar",
            "MessageRepository": "message",
            "MessageChannelRepository": "message_channel",
            "DocumentRepository": "doc_inf",
            "SchemaRepository": "schema_image",
            "SchemaImageRepository": "schema_image",
            "TaskRepository": "task",
            "PaymentRepository": "payment_detail",
            "KarteInfRepository": "karte_inf",
            "KarteFileRepository": "karte_file",
            "MstItemRepository": "ten_mst",
            "TenMstRepository": "ten_mst",
            "SystemConfRepository": "system_conf",
            "SystemGenerationConfRepository": "system_generation_conf",
            "GroupInfRepository": "group_inf",
            "PatientGroupMstRepository": "patient_group_mst",
            "InsuranceMstRepository": "hoken_sya_mst",
            "HokenMstRepository": "hoken_mst",
            "RaiinKubunMstRepository": "raiin_kbn_mst",
            "ReceptionRepository": "raiin_inf",
            "SmartKartePortRepository": "smartkarte_port",
            "AgentSettingRepository": "agent_setting",
            "VisitingListSettingRepository": "visiting_list_setting",
        }
        
        # Try exact match first
        if repo_type in table_mappings:
            return f"`{table_mappings[repo_type]}`"
            
        # Try partial match
        for repo_pattern, table in table_mappings.items():
            if repo_pattern.lower() in repo_type.lower():
                return f"`{table}`"
                
        # Fallback: guess from repository name
        base_name = repo_type.replace("Repository", "").lower()
        # Common suffixes in SmartKarte
        if base_name.endswith("inf"):
            return f"`{base_name}`"
        elif base_name.endswith("mst"):
            return f"`{base_name}`"
        else:
            return f"`{base_name}_inf`"
        
    def _generate_markdown_output(self):
        """Generate markdown output file"""
        output_path = self.project_root / "docs" / "rest_api_dependencies.md"
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# REST API Dependencies\n\n")
            f.write("| HTTP Method | Endpoint | Controller | Method | Interactor | Repository Calls & Tables |\n")
            f.write("|-------------|----------|------------|--------|------------|---------------------------|\n")
            
            # Sort endpoints by controller and method name
            sorted_endpoints = sorted(self.endpoints, key=lambda x: (x.controller_name, x.method_name))
            
            for endpoint in sorted_endpoints:
                # Format repository calls
                repo_calls_str = ""
                if endpoint.repository_calls:
                    repo_calls_str = "<br/>".join([f"{call[0]} ({call[1]})" for call in endpoint.repository_calls])
                
                f.write(f"| {endpoint.http_method} | `{endpoint.route}` | `{endpoint.controller_name}` | `{endpoint.method_name}` | `{endpoint.interactor_name}` | {repo_calls_str} |\n")
                
        print(f"\nOutput generated: {output_path}")
        print(f"Total endpoints analyzed: {len(self.endpoints)}")


def main():
    """Main entry point"""
    # Get the project root directory
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    analyzer = SmartKarteApiAnalyzer(project_root)
    analyzer.analyze()


if __name__ == "__main__":
    main()