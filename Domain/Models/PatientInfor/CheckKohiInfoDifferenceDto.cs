﻿namespace Domain.Models.PatientInfor
{
    public class CheckKohiInfoDifferenceDto
    {
        public CheckKohiInfoDifferenceDto(
            bool isMapAll
        )
        {
            IsMapAll = isMapAll;
        }
        public CheckKohiInfoDifferenceDto(
            bool isMapAll,
            long seqNo,
            int hokenId
        )
        {
            IsMapAll = isMapAll;
            SeqNo = seqNo;
            HokenId = hokenId;
        }
        public CheckKohiInfoDifferenceDto
            (FutansyaNoInfor funtansyaNo, 
            JyukyusyaNoInfor jyukyusyaNo, 
            StartDateInfo startDate, 
            EndDateInfo endDate, 
            GendogakuInfo gendogaku,
            BirthDayInformation birthDay,
            bool isMapAll,
            long seqNo,
            int hokenId,
            string kohiName)
        {
            FutansyaNo = funtansyaNo;
            JyukyusyaNo = jyukyusyaNo;
            StartDate = startDate;
            EndDate = endDate;
            Gendogaku = gendogaku;
            BirthDay = birthDay;
            IsMapAll = isMapAll;
            SeqNo = seqNo;
            HokenId = hokenId;
            KohiName = kohiName;
        }

        public CheckKohiInfoDifferenceDto(StartDateInfo startDate, 
            EndDateInfo endDate, 
            HokenEdaNo hokenEdaNo,
            bool isMapAll,
            long seqNo,
            int hokenId,
            string kohiName)
        {
            StartDate = startDate;
            EndDate = endDate;
            HokenEdaNo = hokenEdaNo;
            IsMapAll = isMapAll;
            SeqNo = seqNo;
            HokenId = hokenId;
            KohiName = kohiName;
        }

        public FutansyaNoInfor FutansyaNo { get; private set; }
        public JyukyusyaNoInfor JyukyusyaNo { get; private set; }
        public StartDateInfo StartDate { get; private set; }
        public EndDateInfo EndDate { get; private set; }
        public GendogakuInfo Gendogaku { get; private set; }
        public HokenEdaNo HokenEdaNo { get; private set; }
        public BirthDayInformation BirthDay { get; private set; }
        public bool IsMapAll { get; private set; }
        public long SeqNo { get; private set; }
        public int HokenId { get; private set; }
        public string KohiName { get; private set; }
    }
    public class FutansyaNoInfor
    {
        public string Value { get; set; } 
        public string XmlValue { get; set; }
        public bool IsMap { get; set; }
    }

    public class JyukyusyaNoInfor
    {
        public string Value { get; set; }
        public string XmlValue { get; set; }
        public bool IsMap { get; set; }
    }

    public class StartDateInfo
    {
        public int Value { get; set; }
        public int XmlValue { get; set; }
        public bool IsMap { get; set; }
    }

    public class EndDateInfo
    {
        public int Value { get; set; }
        public int XmlValue { get; set; }
        public bool IsMap { get; set; }
    }

    public class GendogakuInfo
    {
        public int Value { get; set; }
        public int XmlValue { get; set; }
        public bool IsMap { get; set; }
    }

    public class BirthDayInformation
    {
        public int Value { get; set; }
        public int XmlValue { get; set; }
        public bool IsMap { get; set; }
    }
}
