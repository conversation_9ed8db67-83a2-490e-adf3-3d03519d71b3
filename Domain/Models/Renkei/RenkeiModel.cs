﻿using Entity.Tenant;

namespace Domain.Models.Renkei
{
    public class RenkeiModel
    {
        RenkeiMst RenkeiMst { get; } = null;
        RenkeiConf RenkeiConf { get; } = null;
        RenkeiPathConf RenkeiPathConf { get; } = null;
        RenkeiTimingConf RenkeiTimingConf { get; } = null;
        RenkeiTemplateMst RenkeiTemplateMst { get; } = null;

        public RenkeiModel(RenkeiMst renkeiMst, RenkeiConf renkeiConf, RenkeiPathConf renkeiPathConf, RenkeiTimingConf renkeiTimingConf, RenkeiTemplateMst renkeiTemplateMst)
        {
            RenkeiMst = renkeiMst;
            RenkeiConf = renkeiConf;
            RenkeiPathConf = renkeiPathConf;
            RenkeiTimingConf = renkeiTimingConf;
            RenkeiTemplateMst = renkeiTemplateMst;

            Path = RenkeiPathConf.Path ?? "";

            //if (string.IsNullOrEmpty(Path) == false)
            //{
            //    Append = Path.IndexOf("/add") > 0;
            //    Rename = Path.IndexOf("/rnm") > 0;

            //    Path = Path.Replace(@"/add", "").Trim();
            //    Path = Path.Replace(@"/rnm", "").Trim();

            //    if (Path.EndsWith(@"\")
            //        && RenkeiTemplateMst != null)
            //    {
            //        Path = Path + RenkeiTemplateMst.File;
            //    }
            //}
        }

        public bool Append { get; private set; } = false;
        public bool Rename { get; private set; } = false;

        public string EventCd
        {
            get { return RenkeiTimingConf.EventCd; }
        }

        public int FunctionType
        {
            get { return RenkeiMst.FunctionType; }
        }

        public int RenkeiId
        {
            get { return RenkeiMst.RenkeiId; }
        }

        public string Param
        {
            get
            {
                string ret = RenkeiConf.Param;

                if (string.IsNullOrEmpty(ret) && RenkeiTemplateMst != null)
                {
                    ret = RenkeiTemplateMst.Param;
                }
                return ret ?? "";
            }

            set
            {
                if (RenkeiConf != null && RenkeiConf.Param != value)
                {
                    RenkeiConf.Param = value;
                }
            }
        }
        private string _path = "";
        public string Path
        {
            get { return _path; }

            set
            {
                //if (_path != value)
                //{
                _path = value;
                if (string.IsNullOrEmpty(_path) == false)
                {
                    Append = _path.IndexOf("/add") > 0;
                    Rename = _path.IndexOf("/rnm") > 0;

                    _path = _path.Replace(@"/add", "").Trim();
                    _path = _path.Replace(@"/rnm", "").Trim();

                    if (_path.EndsWith(@"\")
                        && RenkeiTemplateMst != null)
                    {
                        _path = _path + RenkeiTemplateMst.File;
                    }
                }
                //}
            }
        }

        public int CharCd
        {
            get { return RenkeiPathConf.CharCd; }
            set
            {
                if (RenkeiPathConf.CharCd != value)
                {
                    RenkeiPathConf.CharCd = value;
                }
            }
        }

        public int PtNumLength
        {
            get { return RenkeiConf.PtNumLength; }
            set
            {
                if (RenkeiConf.PtNumLength != value)
                {
                    RenkeiConf.PtNumLength = value;
                }
            }
        }
        public int ConfSeqNo
        {
            get { return RenkeiConf.SeqNo; }
        }
        public int ConfSortNo
        {
            get { return RenkeiConf.SortNo; }
        }
        public int EdaNo
        {
            get { return RenkeiPathConf.EdaNo; }
        }
        public string Biko
        {
            get { return RenkeiConf.Biko ?? ""; }
        }

        public bool IsNotEmpty
        {
            get =>
                !string.IsNullOrEmpty(RenkeiPathConf.Param) &&
                RenkeiPathConf.Param.ToLower().Contains("/notempty");
        }

        public bool IsMatchRegex { get; set; } = false;
    }
}
