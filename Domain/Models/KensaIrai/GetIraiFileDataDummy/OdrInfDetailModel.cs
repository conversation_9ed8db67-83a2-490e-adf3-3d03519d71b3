﻿using Entity.Tenant;

namespace Domain.Models.KensaIrai.GetIraiFileDataDummy
{
    public class OdrInfDetailModel
    {
        public OdrInfDetail OdrInfDetail { get; } = null;
        public TenMst TenMst { get; } = null;
        public CommonCenterKensaMst? CommonCenterKensaMst { get; } = null;

        public OdrInfDetailModel(OdrInfDetail odrInfDetail, TenMst tenMst, CommonCenterKensaMst commonCenterKensaMst)
        {
            this.OdrInfDetail = odrInfDetail;
            this.TenMst = tenMst;
            this.CommonCenterKensaMst = commonCenterKensaMst;
        }

        public int HpId { get { return OdrInfDetail.HpId; } }
        public long PtId { get { return OdrInfDetail.PtId; } }
        public int SinDate { get { return OdrInfDetail.SinDate; } }
        public long RaiinNo { get { return OdrInfDetail.RaiinNo; } }
        public long RpNo { get { return OdrInfDetail.RpNo; } }
        public long RpEdaNo { get { return OdrInfDetail.RpEdaNo; } }
        public int RowNo { get { return OdrInfDetail.RowNo; } }
        public int SinKouiKbn { get { return OdrInfDetail.SinKouiKbn; } }
        public string ItemCd { get { return OdrInfDetail.ItemCd; } }
        public string ItemName { get { return OdrInfDetail.ItemName; } }
        public double Suryo { get { return OdrInfDetail.Suryo; } }
        public string UnitName { get { return OdrInfDetail.UnitName; } }
        public int UnitSBT { get { return OdrInfDetail.UnitSBT; } }
        public double TermVal { get { return OdrInfDetail.TermVal; } }
        public int KohatuKbn { get { return OdrInfDetail.KohatuKbn; } }
        public int SyohoKbn { get { return OdrInfDetail.SyohoKbn; } }
        public int SyohoLimitKbn { get { return OdrInfDetail.SyohoLimitKbn; } }
        public int DrugKbn { get { return OdrInfDetail.DrugKbn; } }
        public int YohoKbn { get { return OdrInfDetail.YohoKbn; } }
        public string Kokuji1 { get { return OdrInfDetail.Kokuji1; } }
        public string Kokiji2 { get { return OdrInfDetail.Kokiji2; } }
        public int IsNodspRece { get { return OdrInfDetail.IsNodspRece; } }
        public string IpnCd { get { return OdrInfDetail.IpnCd; } }
        public string IpnName { get { return OdrInfDetail.IpnName; } }
        public int JissiKbn { get { return OdrInfDetail.JissiKbn; } }
        public DateTime? JissiDate { get { return OdrInfDetail.JissiDate; } }
        public int JissiId { get { return OdrInfDetail.JissiId; } }
        public string JissiMachine { get { return OdrInfDetail.JissiMachine; } }
        public string ReqCd { get { return OdrInfDetail.ReqCd; } }
        public string Bunkatu { get { return OdrInfDetail.Bunkatu; } }
        public string CmtName { get { return OdrInfDetail.CmtName; } }
        public string CmtOpt { get { return OdrInfDetail.CmtOpt; } }
        public string FontColor { get { return OdrInfDetail.FontColor; } }
        public int CommentNewline { get { return OdrInfDetail.CommentNewline; } }

        public string KensaItemCd
        {
            get
            {
                return CommonCenterKensaMst != null ? CommonCenterKensaMst.KensaItemCd : "";
            }
        }

        public long KeyNo = 0;
        public bool IsAddNew = false;
        public bool IsUpdate = false;
    }
}
