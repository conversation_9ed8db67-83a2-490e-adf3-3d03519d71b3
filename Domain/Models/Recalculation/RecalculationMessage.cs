﻿using Helper.Messaging.Data;
using Newtonsoft.Json;

namespace Domain.Models.Recalculation
{
    public class RecalculationMessage
    {
        [JsonProperty("hpid")]
        public int HpId { get; set; }

        [JsonProperty("userid")]
        public int UserId { get; set; }

        [JsonProperty("isrecalculationcheckbox")]
        public bool IsRecalculationCheckBox { get; set; }

        [JsonProperty("isreceiptaggregationcheckbox")]
        public bool IsReceiptAggregationCheckBox { get; set; }

        [JsonProperty("ischeckerrorcheckbox")]
        public bool IsCheckErrorCheckBox { get; set; }

        [JsonProperty("sinym")]
        public int SinYm { get; set; }

        [JsonProperty("ptidlist")]
        public List<PtItem> PtIdList { get; set; } = new();

        [JsonProperty("isoperator")]
        public int IsOperator { get; set; }

        [JsonProperty("operatorname")]
        public string OperatorName { get; set; } = string.Empty;

        [JsonProperty("proccesskey")]
        public string ProccessKey { get; set; } = string.Empty;

        [JsonProperty("issuccessrunning")]
        public int IsSuccessRunning { get; set; } = 0;

    }
}
