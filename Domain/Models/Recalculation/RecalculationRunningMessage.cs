﻿using System.Text.Json.Serialization;

namespace Domain.Models.Recalculation
{
    public class RecalculationRunningMessage
    {
        public RecalculationRunningMessage(int hpId, int userId, bool done, int type, int length, int successCount, string message = "")
        {
            HpId = hpId;
            UserId = userId;
            Done = done;
            Type = type;
            Length = length;
            SuccessCount = successCount;
            Message = message;
        }

        [JsonPropertyName("hpId")]
        public int HpId { get; set; }

        [JsonPropertyName("userId")]
        public int UserId { get; set; }

        [JsonPropertyName("done")]
        public bool Done { get; set; }

        [JsonPropertyName("type")]
        public int Type { get; set; }

        [JsonPropertyName("length")]
        public int Length { get; set; }

        [JsonPropertyName("successCount")]
        public int SuccessCount { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

    }
}
