using Domain.Models.MstItem;
using Domain.Models.OrdInf;
using Domain.Models.TodayOdr;
using Domain.CustomException;
using Helper.Exceptions;
using Interactor.MstItem;
using Moq;
using UseCase.MstItem.SaveSetDataTenMst;
using CalculateService.Interface;

namespace CloudUnitTest.Interactor.MstItem
{
    public class SaveSetDataTenMstInteractorTest : BaseUT
    {
        [Test]
        public void ValidateInput_InvalidHpId()
        {
            //Arrange
            var mockIMstItemRepository = new Mock<IMstItemRepository>();
            var mockIEmrLogger = new Mock<IEmrLogger>();

            var saveSetDataTenMstInteractor = new SaveSetDataTenMstInteractor(
                mockIMstItemRepository.Object,
                mockIEmrLogger.Object
            );

            int hpId = 0, userId = 999;
            string itemCd = "TEST001";
            List<TenMstOriginModel> tenOrigins = new List<TenMstOriginModel>();
            SetDataTenMstOriginModel setData = CreateDummySetData();

            SaveSetDataTenMstInputData inputData = new SaveSetDataTenMstInputData(
                hpId, userId, itemCd, tenOrigins, setData);

            //Act
            var result = saveSetDataTenMstInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status == SaveSetDataTenMstStatus.InvalidHpId);
        }

        [Test]
        public void ValidateInput_InvalidUserId()
        {
            //Arrange
            var mockIMstItemRepository = new Mock<IMstItemRepository>();
            var mockIEmrLogger = new Mock<IEmrLogger>();

            var saveSetDataTenMstInteractor = new SaveSetDataTenMstInteractor(
                mockIMstItemRepository.Object,
                mockIEmrLogger.Object
            );

            int hpId = 99999999, userId = 0;
            string itemCd = "TEST001";
            List<TenMstOriginModel> tenOrigins = new List<TenMstOriginModel>();
            SetDataTenMstOriginModel setData = CreateDummySetData();

            SaveSetDataTenMstInputData inputData = new SaveSetDataTenMstInputData(
                hpId, userId, itemCd, tenOrigins, setData);

            //Act
            var result = saveSetDataTenMstInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status == SaveSetDataTenMstStatus.InvalidUserId);
        }

        [Test]
        public void ValidateInput_InvalidItemCd_Null()
        {
            //Arrange
            var mockIMstItemRepository = new Mock<IMstItemRepository>();
            var mockIEmrLogger = new Mock<IEmrLogger>();

            var saveSetDataTenMstInteractor = new SaveSetDataTenMstInteractor(
                mockIMstItemRepository.Object,
                mockIEmrLogger.Object
            );

            int hpId = 99999999, userId = 999;
            string itemCd = null;
            List<TenMstOriginModel> tenOrigins = new List<TenMstOriginModel>();
            SetDataTenMstOriginModel setData = CreateDummySetData();

            SaveSetDataTenMstInputData inputData = new SaveSetDataTenMstInputData(
                hpId, userId, itemCd, tenOrigins, setData);

            //Act
            var result = saveSetDataTenMstInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status == SaveSetDataTenMstStatus.InvalidItemCd);
        }

        [Test]
        public void ValidateInput_InvalidItemCd_Empty()
        {
            //Arrange
            var mockIMstItemRepository = new Mock<IMstItemRepository>();
            var mockIEmrLogger = new Mock<IEmrLogger>();

            var saveSetDataTenMstInteractor = new SaveSetDataTenMstInteractor(
                mockIMstItemRepository.Object,
                mockIEmrLogger.Object
            );

            int hpId = 99999999, userId = 999;
            string itemCd = "";
            List<TenMstOriginModel> tenOrigins = new List<TenMstOriginModel>();
            SetDataTenMstOriginModel setData = CreateDummySetData();

            SaveSetDataTenMstInputData inputData = new SaveSetDataTenMstInputData(
                hpId, userId, itemCd, tenOrigins, setData);

            //Act
            var result = saveSetDataTenMstInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status == SaveSetDataTenMstStatus.InvalidItemCd);
        }

        [Test]
        public void Return_Successful()
        {
            //Arrange
            var mockIMstItemRepository = new Mock<IMstItemRepository>();
            var mockIEmrLogger = new Mock<IEmrLogger>();

            var saveSetDataTenMstInteractor = new SaveSetDataTenMstInteractor(
                mockIMstItemRepository.Object,
                mockIEmrLogger.Object
            );

            int hpId = 99999999, userId = 999;
            string itemCd = "TEST001";
            List<TenMstOriginModel> tenOrigins = new List<TenMstOriginModel>();
            SetDataTenMstOriginModel setData = CreateDummySetData();

            mockIMstItemRepository.Setup(x => x.SaveTenMstOriginSetData(
                It.IsAny<IEnumerable<Helper.Enum.CategoryItemEnums>>(),
                It.IsAny<string>(),
                It.IsAny<List<TenMstOriginModel>>(),
                It.IsAny<SetDataTenMstOriginModel>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(true);

            SaveSetDataTenMstInputData inputData = new SaveSetDataTenMstInputData(
                hpId, userId, itemCd, tenOrigins, setData);

            //Act
            var result = saveSetDataTenMstInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status == SaveSetDataTenMstStatus.Successful);
        }

        [Test]
        public void Return_DuplicateKey()
        {
            //Arrange
            var mockIMstItemRepository = new Mock<IMstItemRepository>();
            var mockIEmrLogger = new Mock<IEmrLogger>();

            var saveSetDataTenMstInteractor = new SaveSetDataTenMstInteractor(
                mockIMstItemRepository.Object,
                mockIEmrLogger.Object
            );

            int hpId = 99999999, userId = 999;
            string itemCd = "TEST001";
            List<TenMstOriginModel> tenOrigins = new List<TenMstOriginModel>();
            SetDataTenMstOriginModel setData = CreateDummySetData();

            mockIMstItemRepository.Setup(x => x.SaveTenMstOriginSetData(
                It.IsAny<IEnumerable<Helper.Enum.CategoryItemEnums>>(),
                It.IsAny<string>(),
                It.IsAny<List<TenMstOriginModel>>(),
                It.IsAny<SetDataTenMstOriginModel>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Throws(new DuplicateEntryException("Duplicate entry"));

            SaveSetDataTenMstInputData inputData = new SaveSetDataTenMstInputData(
                hpId, userId, itemCd, tenOrigins, setData);

            //Act
            var result = saveSetDataTenMstInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status == SaveSetDataTenMstStatus.DuplicateKey);
        }

        [Test]
        public void Return_InteractorCustomException()
        {
            //Arrange
            var mockIMstItemRepository = new Mock<IMstItemRepository>();
            var mockIEmrLogger = new Mock<IEmrLogger>();

            var saveSetDataTenMstInteractor = new SaveSetDataTenMstInteractor(
                mockIMstItemRepository.Object,
                mockIEmrLogger.Object
            );

            int hpId = 99999999, userId = 999;
            string itemCd = "TEST001";
            List<TenMstOriginModel> tenOrigins = new List<TenMstOriginModel>();
            SetDataTenMstOriginModel setData = CreateDummySetData();

            mockIMstItemRepository.Setup(x => x.SaveTenMstOriginSetData(
                It.IsAny<IEnumerable<Helper.Enum.CategoryItemEnums>>(),
                It.IsAny<string>(),
                It.IsAny<List<TenMstOriginModel>>(),
                It.IsAny<SetDataTenMstOriginModel>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Throws(new System.Exception("Unexpected error"));

            SaveSetDataTenMstInputData inputData = new SaveSetDataTenMstInputData(
                hpId, userId, itemCd, tenOrigins, setData);

            //Act & Assert
            Assert.Throws<InteractorCustomException>(
                () => saveSetDataTenMstInteractor.Handle(inputData));
        }

        [Test]
        public void RealData_Successful()
        {
            //Arrange
            var mockIMstItemRepository = new Mock<IMstItemRepository>();
            var mockIEmrLogger = new Mock<IEmrLogger>();

            var saveSetDataTenMstInteractor = new SaveSetDataTenMstInteractor(
                mockIMstItemRepository.Object,
                mockIEmrLogger.Object
            );

            int hpId = 99999999, userId = 999;
            string itemCd = "J1";

            List<TenMstOriginModel> tenOrigins = new List<TenMstOriginModel>
            {
                new TenMstOriginModel(
                    hpId: 99999999,
                    itemCd: "J1",
                    startDate: 20250722,
                    endDate: 99999999,
                    masterSbt: "",
                    sinKouiKbn: 96,
                    name: "テスト",
                    kanaName1: "ｼﾞﾋ",
                    kanaName2: "",
                    kanaName3: "",
                    kanaName4: "",
                    kanaName5: "",
                    kanaName6: "",
                    kanaName7: "",
                    ryosyuName: "",
                    receName: "テスト",
                    tenId: 1,
                    ten: 1000.0,
                    receUnitCd: "",
                    receUnitName: "",
                    odrUnitName: "",
                    cnvUnitName: "",
                    odrTermVal: 0.0,
                    cnvTermVal: 0.0,
                    defaultVal: 0.0,
                    isAdopted: 1,
                    koukiKbn: 0,
                    hokatuKensa: 0,
                    byomeiKbn: 0,
                    igakukanri: 0,
                    jitudayCount: 0,
                    jituday: 0,
                    dayCount: 0,
                    drugKanrenKbn: 0,
                    kizamiId: 0,
                    kizamiMin: 0,
                    kizamiMax: 0,
                    kizamiVal: 0,
                    kizamiTen: 0.0,
                    kizamiErr: 0,
                    maxCount: 0,
                    maxCountErr: 0,
                    tyuCd: "",
                    tyuSeq: "",
                    tusokuAge: 0,
                    minAge: "",
                    maxAge: "",
                    ageCheck: 0,
                    timeKasanKbn: 0,
                    futekiKbn: 0,
                    futekiSisetuKbn: 0,
                    syotiNyuyojiKbn: 0,
                    lowWeightKbn: 0,
                    handanKbn: 0,
                    handanGrpKbn: 0,
                    teigenKbn: 0,
                    sekituiKbn: 0,
                    keibuKbn: 0,
                    autoHougouKbn: 0,
                    gairaiKanriKbn: 0,
                    tusokuTargetKbn: 0,
                    hokatuKbn: 0,
                    tyoonpaNaisiKbn: 0,
                    autoFungoKbn: 0,
                    tyoonpaGyokoKbn: 0,
                    gazoKasan: 0,
                    kansatuKbn: 0,
                    masuiKbn: 0,
                    fukubikuNaisiKasan: 0,
                    fukubikuKotunanKasan: 0,
                    masuiKasan: 0,
                    moniterKasan: 0,
                    toketuKasan: 0,
                    tenKbnNo: "",
                    shortstayOpe: 0,
                    buiKbn: 0,
                    sisetucd1: 0,
                    sisetucd2: 0,
                    sisetucd3: 0,
                    sisetucd4: 0,
                    sisetucd5: 0,
                    sisetucd6: 0,
                    sisetucd7: 0,
                    sisetucd8: 0,
                    sisetucd9: 0,
                    sisetucd10: 0,
                    agekasanMin1: "",
                    agekasanMax1: "",
                    agekasanCd1: "",
                    agekasanMin2: "",
                    agekasanMax2: "",
                    agekasanCd2: "",
                    agekasanMin3: "",
                    agekasanMax3: "",
                    agekasanCd3: "",
                    agekasanMin4: "",
                    agekasanMax4: "",
                    agekasanCd4: "",
                    kensaCmt: 0,
                    madokuKbn: 0,
                    sinkeiKbn: 0,
                    seibutuKbn: 0,
                    zoueiKbn: 0,
                    drugKbn: 0,
                    zaiKbn: 0,
                    zaikeiPoint: 0.0,
                    capacity: 0,
                    kohatuKbn: 0,
                    tokuzaiAgeKbn: 0,
                    sansoKbn: 0,
                    tokuzaiSbt: 0,
                    maxPrice: 0,
                    maxTen: 0,
                    syukeiSaki: "0",
                    cdKbn: "",
                    cdSyo: 0,
                    cdBu: 0,
                    cdKbnno: 0,
                    cdEdano: 0,
                    cdKouno: 0,
                    kokujiKbn: "",
                    kokujiSyo: 0,
                    kokujiBu: 0,
                    kokujiKbnNo: 0,
                    kokujiEdaNo: 0,
                    kokujiKouNo: 0,
                    kokuji1: "",
                    kokuji2: "",
                    kohyoJun: 0,
                    yjCd: "",
                    yakkaCd: "",
                    syusaiSbt: 0,
                    syohinKanren: "",
                    updDate: 0,
                    delDate: 0,
                    keikaDate: 0,
                    rousaiKbn: 0,
                    sisiKbn: 0,
                    shotCnt: 0,
                    isNosearch: 0,
                    isNodspPaperRece: 1,
                    isNodspRece: 1,
                    isNodspRyosyu: 0,
                    isNodspKarte: 0,
                    isNodspYakutai: 0,
                    jihiSbt: 1,
                    kazeiKbn: 0,
                    yohoKbn: 0,
                    ipnNameCd: "",
                    fukuyoRise: 0,
                    fukuyoMorning: 0,
                    fukuyoDaytime: 0,
                    fukuyoNight: 0,
                    fukuyoSleep: 0,
                    suryoRoundupKbn: 0,
                    kouseisinKbn: 0,
                    chusyaDrugSbt: 0,
                    kensaFukusuSantei: 0,
                    santeiItemCd: "9999999999",
                    santeigaiKbn: 0,
                    kensaItemCd: "",
                    kensaItemSeqNo: 0,
                    renkeiCd1: "",
                    renkeiCd2: "",
                    saiketuKbn: 0,
                    cmtKbn: 0,
                    cmtCol1: 0,
                    cmtColKeta1: 0,
                    cmtCol2: 0,
                    cmtColKeta2: 0,
                    cmtCol3: 0,
                    cmtColKeta3: 0,
                    cmtCol4: 0,
                    cmtColKeta4: 0,
                    selectCmtId: 0,
                    kensaLabel: 0,
                    isUpdated: false,
                    isAddNew: true,
                    isDeleted: 0,
                    isStartDateKeyUpdated: false,
                    originStartDate: 0,
                    isSelected: false,
                    yohoHosokuKbn: 0,
                    yohoHosokuRec: 0
                )
            };

            SetDataTenMstOriginModel setData = CreateRealSetData();

            mockIMstItemRepository.Setup(x => x.SaveTenMstOriginSetData(
                It.IsAny<IEnumerable<Helper.Enum.CategoryItemEnums>>(),
                It.IsAny<string>(),
                It.IsAny<List<TenMstOriginModel>>(),
                It.IsAny<SetDataTenMstOriginModel>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(true);

            SaveSetDataTenMstInputData inputData = new SaveSetDataTenMstInputData(
                hpId, userId, itemCd, tenOrigins, setData);

            //Act
            var result = saveSetDataTenMstInteractor.Handle(inputData);

            //Assert
            Assert.That(result.Status == SaveSetDataTenMstStatus.Successful);

            // リポジトリのメソッドが正しく呼ばれたことを確認
            mockIMstItemRepository.Verify(x => x.SaveTenMstOriginSetData(
                It.IsAny<IEnumerable<Helper.Enum.CategoryItemEnums>>(),
                It.Is<string>(s => s == "J1"),
                It.Is<List<TenMstOriginModel>>(list => list.Count == 1),
                It.IsAny<SetDataTenMstOriginModel>(),
                It.Is<int>(u => u == 999),
                It.Is<int>(h => h == 99999999)
            ), Times.Once);

            // ReleaseResourceが呼ばれたことを確認
            mockIMstItemRepository.Verify(x => x.ReleaseResource(), Times.Once);
        }

        private SetDataTenMstOriginModel CreateDummySetData()
        {
            var basicSettingTab = new BasicSettingTabModel(new List<CmtKbnMstModel>());
            var ijiSettingTab = new IjiSettingTabModel();
            var precriptionSettingTab = new PrecriptionSettingTabModel(
                new List<M10DayLimitModel>(),
                new List<IpnMinYakkaMstModel>(),
                new List<DrugDayLimitModel>(),
                new DosageMstModel(),
                new IpnNameMstModel());
            var usageSettingTab = new UsageSettingTabModel();
            var drugInfomationTab = new DrugInfomationTabModel(
                new List<DrugInfModel>(),
                new PiImageModel(),
                new PiImageModel());
            var teikyoByomeiTab = new TeikyoByomeiTabModel(
                new List<TeikyoByomeiModel>(),
                new TekiouByomeiMstExcludedModel());
            var santeiKaishuTab = new SanteiKaishuTabModel(
                new List<DensiSanteiKaisuModel>());
            var haihanTab = new HaihanTabModel(
                new List<DensiHaihanModel>(),
                new List<DensiHaihanModel>(),
                new List<DensiHaihanModel>());
            var houkatsuTab = new HoukatsuTabModel(
                new List<DensiHoukatuModel>(),
                new List<DensiHoukatuGrpModel>(),
                new List<DensiHoukatuModel>());
            var combinedContraindicationTab = new CombinedContraindicationTabModel(
                new List<CombinedContraindicationModel>());

            return new SetDataTenMstOriginModel(
                basicSettingTab,
                ijiSettingTab,
                precriptionSettingTab,
                usageSettingTab,
                drugInfomationTab,
                teikyoByomeiTab,
                santeiKaishuTab,
                haihanTab,
                houkatsuTab,
                combinedContraindicationTab);
        }

        private SetDataTenMstOriginModel CreateRealSetData()
        {
            var basicSettingTab = new BasicSettingTabModel(new List<CmtKbnMstModel>());

            var ijiSettingTab = new IjiSettingTabModel(
                searchItemName: "",
                agekasanCd1Note: "",
                agekasanCd2Note: "",
                agekasanCd3Note: "",
                agekasanCd4Note: ""
            );

            var precriptionSettingTab = new PrecriptionSettingTabModel(
                new List<M10DayLimitModel>(),
                new List<IpnMinYakkaMstModel>(),
                new List<DrugDayLimitModel>(),
                new DosageMstModel(),
                new IpnNameMstModel());

            var usageSettingTab = new UsageSettingTabModel(yohoInfMstPrefix: "");

            var drugInfomationTab = new DrugInfomationTabModel(
                new List<DrugInfModel>(),
                new PiImageModel(),
                new PiImageModel());

            var teikyoByomeiTab = new TeikyoByomeiTabModel(
                new List<TeikyoByomeiModel>(),
                new TekiouByomeiMstExcludedModel());

            var santeiKaishuTab = new SanteiKaishuTabModel(
                new List<DensiSanteiKaisuModel>());

            var haihanTab = new HaihanTabModel(
                new List<DensiHaihanModel>(),
                new List<DensiHaihanModel>(),
                new List<DensiHaihanModel>());

            var houkatsuTab = new HoukatsuTabModel(
                new List<DensiHoukatuModel>(),
                new List<DensiHoukatuGrpModel>(),
                new List<DensiHoukatuModel>());

            var combinedContraindicationTab = new CombinedContraindicationTabModel(
                new List<CombinedContraindicationModel>());

            return new SetDataTenMstOriginModel(
                basicSettingTab,
                ijiSettingTab,
                precriptionSettingTab,
                usageSettingTab,
                drugInfomationTab,
                teikyoByomeiTab,
                santeiKaishuTab,
                haihanTab,
                houkatsuTab,
                combinedContraindicationTab);
        }
    }
}
