using CloudUnitTest.SampleData.AccountingRepository;
using Infrastructure.Repositories;
using Microsoft.Extensions.Configuration;
using Moq;
using Reporting.Accounting.DB;
using Reporting.Accounting.Model;

namespace CloudUnitTest.Repository.Accounting
{
    public class FindKaikeiInfListTest : BaseUT
    {
        [Test]
        public void FindKaikeiInfList_001_HokenSeikyuTrue_JihiSeikyuTrue_ShouldFilterCorrectly()
        {
            // Arrange
            SetupTestEnvironment(out CoAccountingFinder coAccountingFinder);
            var tenant = TenantProvider.GetNoTrackingDataContext();
            
            int hpId = 998;
            int startDate = ********;
            int endDate = ********;
            var ptConditions = new List<(long ptId, int hokenId)>();
            var grpConditions = new List<(int grpId, string grpCd)>();
            int sort = 1;
            int miseisanKbn = 1;
            int saiKbn = 1;
            int misyuKbn = 0;
            int seikyuKbn = 1;
            int hokenKbn = 1;
            bool hokenSeikyu = true;  // 保険請求ありのみ
            bool jihiSeikyu = true;   // 自費請求ありのみ
            
            var warningMessages = new List<CoWarningMessage>();

            // テストデータを準備（必要最小限のデータのみ）
            var kaikeiInfs = AccountingRepositoryData.ReadKaikeiInf();

            try
            {
                // テストデータをデータベースに追加
                tenant.AddRange(kaikeiInfs);
                tenant.SaveChanges();

                // Act
                var result = coAccountingFinder.FindKaikeiInfList(
                    hpId, startDate, endDate, ptConditions, grpConditions,
                    sort, miseisanKbn, saiKbn, misyuKbn, seikyuKbn, hokenKbn,
                    hokenSeikyu, jihiSeikyu, ref warningMessages);

                // Assert
                Assert.NotNull(result);
                // 保険請求あり（PtFutan > 0）かつ自費請求あり（JihiFutan > 0）のレコードのみが含まれていることを確認
                foreach (var item in result)
                {
                    Assert.Greater(item.PtFutan, 0, "保険請求ありの条件で絞り込まれているはず");
                    Assert.Greater(item.JihiFutan, 0, "自費請求ありの条件で絞り込まれているはず");
                }
            }
            finally
            {
                // テストデータをクリーンアップ
                tenant.RemoveRange(kaikeiInfs);
                tenant.SaveChanges();
                CleanupResources(coAccountingFinder);
            }
        }

        [Test]
        public void FindKaikeiInfList_002_HokenSeikyuTrue_JihiSeikyuFalse_ShouldFilterCorrectly()
        {
            // Arrange
            SetupTestEnvironment(out CoAccountingFinder coAccountingFinder);
            var tenant = TenantProvider.GetNoTrackingDataContext();
            
            int hpId = 998;
            int startDate = ********;
            int endDate = ********;
            var ptConditions = new List<(long ptId, int hokenId)>();
            var grpConditions = new List<(int grpId, string grpCd)>();
            int sort = 1;
            int miseisanKbn = 1;
            int saiKbn = 1;
            int misyuKbn = 0;
            int seikyuKbn = 1;
            int hokenKbn = 1;
            bool hokenSeikyu = true;  // 保険請求ありのみ
            bool jihiSeikyu = false;  // 自費請求は問わない
            
            var warningMessages = new List<CoWarningMessage>();

            // テストデータを準備（必要最小限のデータのみ）
            var kaikeiInfs = AccountingRepositoryData.ReadKaikeiInf();

            try
            {
                // テストデータをデータベースに追加
                tenant.AddRange(kaikeiInfs);
                tenant.SaveChanges();

                // Act
                var result = coAccountingFinder.FindKaikeiInfList(
                    hpId, startDate, endDate, ptConditions, grpConditions,
                    sort, miseisanKbn, saiKbn, misyuKbn, seikyuKbn, hokenKbn,
                    hokenSeikyu, jihiSeikyu, ref warningMessages);

                // Assert
                Assert.NotNull(result);
                // 保険請求あり（PtFutan > 0）のレコードのみが含まれていることを確認（自費請求は問わない）
                foreach (var item in result)
                {
                    Assert.Greater(item.PtFutan, 0, "保険請求ありの条件で絞り込まれているはず");
                }
            }
            finally
            {
                // テストデータをクリーンアップ
                tenant.RemoveRange(kaikeiInfs);
                tenant.SaveChanges();
                CleanupResources(coAccountingFinder);
            }
        }

        [Test]
        public void FindKaikeiInfList_003_HokenSeikyuFalse_JihiSeikyuTrue_ShouldFilterCorrectly()
        {
            // Arrange
            SetupTestEnvironment(out CoAccountingFinder coAccountingFinder);
            var tenant = TenantProvider.GetNoTrackingDataContext();
            
            int hpId = 998;
            int startDate = ********;
            int endDate = ********;
            var ptConditions = new List<(long ptId, int hokenId)>();
            var grpConditions = new List<(int grpId, string grpCd)>();
            int sort = 1;
            int miseisanKbn = 1;
            int saiKbn = 1;
            int misyuKbn = 0;
            int seikyuKbn = 1;
            int hokenKbn = 1;
            bool hokenSeikyu = false; // 保険請求は問わない
            bool jihiSeikyu = true;   // 自費請求ありのみ
            
            var warningMessages = new List<CoWarningMessage>();

            // テストデータを準備（必要最小限のデータのみ）
            var kaikeiInfs = AccountingRepositoryData.ReadKaikeiInf();

            try
            {
                // テストデータをデータベースに追加
                tenant.AddRange(kaikeiInfs);
                tenant.SaveChanges();

                // Act
                var result = coAccountingFinder.FindKaikeiInfList(
                    hpId, startDate, endDate, ptConditions, grpConditions,
                    sort, miseisanKbn, saiKbn, misyuKbn, seikyuKbn, hokenKbn,
                    hokenSeikyu, jihiSeikyu, ref warningMessages);

                // Assert
                Assert.NotNull(result);
                // 自費請求あり（JihiFutan > 0）のレコードのみが含まれていることを確認（保険請求は問わない）
                foreach (var item in result)
                {
                    Assert.Greater(item.JihiFutan, 0, "自費請求ありの条件で絞り込まれているはず");
                }
            }
            finally
            {
                // テストデータをクリーンアップ
                tenant.RemoveRange(kaikeiInfs);
                tenant.SaveChanges();
                CleanupResources(coAccountingFinder);
            }
        }

        [Test]
        public void FindKaikeiInfList_004_HokenSeikyuFalse_JihiSeikyuFalse_ShouldNotFilter()
        {
            // Arrange
            SetupTestEnvironment(out CoAccountingFinder coAccountingFinder);
            var tenant = TenantProvider.GetNoTrackingDataContext();
            
            int hpId = 998;
            int startDate = ********;
            int endDate = ********;
            var ptConditions = new List<(long ptId, int hokenId)>();
            var grpConditions = new List<(int grpId, string grpCd)>();
            int sort = 1;
            int miseisanKbn = 1;
            int saiKbn = 1;
            int misyuKbn = 0;
            int seikyuKbn = 1;
            int hokenKbn = 1;
            bool hokenSeikyu = false; // 保険請求は問わない
            bool jihiSeikyu = false;  // 自費請求は問わない
            
            var warningMessages = new List<CoWarningMessage>();

            // テストデータを準備（必要最小限のデータのみ）
            var kaikeiInfs = AccountingRepositoryData.ReadKaikeiInf();

            try
            {
                // テストデータをデータベースに追加
                tenant.AddRange(kaikeiInfs);
                tenant.SaveChanges();

                // Act
                var result = coAccountingFinder.FindKaikeiInfList(
                    hpId, startDate, endDate, ptConditions, grpConditions,
                    sort, miseisanKbn, saiKbn, misyuKbn, seikyuKbn, hokenKbn,
                    hokenSeikyu, jihiSeikyu, ref warningMessages);

                // Assert
                Assert.NotNull(result);
                // 絞り込み条件がfalseなので、すべてのレコードが含まれることを確認
                // データが存在する場合は、絞り込みが適用されていないことを確認
                if (result.Count > 0)
                {
                    // 絞り込みが適用されていないため、PtFutanやJihiFutanの値に関係なく結果が返される
                    Assert.Pass("絞り込み条件がfalseのため、すべてのレコードが返される");
                }
            }
            finally
            {
                // テストデータをクリーンアップ
                tenant.RemoveRange(kaikeiInfs);
                tenant.SaveChanges();
                CleanupResources(coAccountingFinder);
            }
        }

        [Test]
        public void FindKaikeiInfList_005_CheckKaikeiInfList_WithHokenSeikyuJihiSeikyu_ShouldWorkCorrectly()
        {
            // Arrange
            SetupTestEnvironment(out CoAccountingFinder coAccountingFinder);
            var tenant = TenantProvider.GetNoTrackingDataContext();
            
            int hpId = 998;
            int startDate = ********;
            int endDate = ********;
            var ptConditions = new List<(long ptId, int hokenId)>();
            var grpConditions = new List<(int grpId, string grpCd)>();
            int sort = 1;
            int miseisanKbn = 1;
            int saiKbn = 1;
            int misyuKbn = 0;
            int seikyuKbn = 1;
            int hokenKbn = 1;
            bool hokenSeikyu = true;  // 保険請求ありのみ
            bool jihiSeikyu = true;   // 自費請求ありのみ
            
            // テストデータを準備（必要最小限のデータのみ）
            var kaikeiInfs = AccountingRepositoryData.ReadKaikeiInf();

            try
            {
                // テストデータをデータベースに追加
                tenant.AddRange(kaikeiInfs);
                tenant.SaveChanges();

                // Act
                var result = coAccountingFinder.CheckKaikeiInfList(
                    hpId, startDate, endDate, ptConditions, grpConditions,
                    sort, miseisanKbn, saiKbn, misyuKbn, seikyuKbn, hokenKbn,
                    hokenSeikyu, jihiSeikyu);

                // Assert
                Assert.NotNull(result);
                // CheckKaikeiInfListメソッドが正常に動作することを確認
                Assert.IsInstanceOf<List<CoWarningMessage>>(result);
            }
            finally
            {
                // テストデータをクリーンアップ
                tenant.RemoveRange(kaikeiInfs);
                tenant.SaveChanges();
                CleanupResources(coAccountingFinder);
            }
        }

        private void SetupTestEnvironment(out CoAccountingFinder coAccountingFinder)
        {
            coAccountingFinder = new CoAccountingFinder(TenantProvider);
        }

        private void CleanupResources(CoAccountingFinder coAccountingFinder)
        {
            // 必要に応じてリソースのクリーンアップを行う
        }
    }
} 