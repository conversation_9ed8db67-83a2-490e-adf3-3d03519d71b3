# REST API Dependencies

| HTTP Method | Endpoint | Controller | Method | Interactor | Repository Calls & Tables |
|-------------|----------|------------|--------|------------|---------------------------|
| GET | `api/AccountDue/GetList` | `AccountDueController` | `GetList` | `GetAccountDueListInteractor` | AccountDueRepository.GetUketsukeSbt (`accountdue_inf`)<br/>AccountDueRepository.GetPaymentMethod (`accountdue_inf`)<br/>AccountDueRepository.GetAccountDueList (`accountdue_inf`)<br/>AccountDueRepository.GetDictRaiinInfModel (`accountdue_inf`)<br/>ReceptionRepository.GetList (`raiin_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`) |
| GET | `api/AccountDue/IsNyukinExisted` | `AccountDueController` | `IsNyukinExisted` | `IsNyukinExistedInteractor` | AccountDueRepository.IsNyukinExisted (`accountdue_inf`) |
| POST | `api/AccountDue/SaveList` | `AccountDueController` | `SaveList` | `SaveAccountDueListInteractor` | AccountDueRepository.GetListSyunoSeikyuModel (`accountdue_inf`)<br/>AccountDueRepository.GetListSyunoNyukinModel (`accountdue_inf`)<br/>AccountDueRepository.GetDictRaiinInfModel (`accountdue_inf`)<br/>AccountDueRepository.GetPaymentMethod (`accountdue_inf`)<br/>AccountDueRepository.SaveAccountDueList (`accountdue_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>ReceptionRepository.GetRecptionList (`raiin_inf`)<br/>ReceptionRepository.GetListSameVisit (`raiin_inf`) |
| POST | `api/Accounting/CheckAccounting` | `AccountingController` | `CheckAccounting` | `CheckAccountingStatusInteractor` | AccountingRepository.GetRaiinNos (`kaikei_inf`)<br/>AccountingRepository.GetListSyunoSeikyu (`kaikei_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`) |
| GET | `api/Accounting/CheckOpenAccounting` | `AccountingController` | `CheckOpenAccounting` | `CheckOpenAccountingInteractor` | AccountingRepository.CheckIsOpenAccounting (`kaikei_inf`) |
| GET | `api/Accounting/GetAccountingFormMst` | `AccountingController` | `GetAccountingFormMst` | `GetAccountingFormMstInteractor` | AccountingRepository.GetAccountingFormMstModels (`kaikei_inf`) |
| GET | `api/Accounting/GetHeaderInf` | `AccountingController` | `GetHeaderInf` | `GetHeaderInfInteractor` | OrdInfRepository.GetHeaderInfo (`ord_inf`)<br/>ReceptionRepository.GetHeader (`raiin_inf`)<br/>RaiinListTagRepository.Get (`raiinlisttag_inf`) |
| GET | `api/Accounting/GetList` | `AccountingController` | `GetList` | `GetAccountingInteractor` | AccountingRepository.GetListRaiinInf (`kaikei_inf`)<br/>AccountingRepository.GetListSyunoSeikyu (`kaikei_inf`)<br/>AccountingRepository.GetListKohiByKohiId (`kaikei_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`) |
| GET | `api/Accounting/GetListHokenSelect` | `AccountingController` | `GetListHokenSelect` | `GetListHokenSelectInteractor` | AccountingRepository.GetListRaiinInf (`kaikei_inf`)<br/>AccountingRepository.GetListHokenSelect (`kaikei_inf`) |
| GET | `api/Accounting/GetMeiHoGai` | `AccountingController` | `GetMeiHoGai` | `GetMeiHoGaiInteractor` | AccountingRepository.GetJihiOuttaxPoint (`kaikei_inf`)<br/>AccountingRepository.GetListJihiSbtMst (`kaikei_inf`) |
| GET | `api/Accounting/GetSystemConfig` | `AccountingController` | `GetSystemConfig` | `GetSystemConfInteractor` | SystemConfRepository.GetByGrpCd (`system_conf`) |
| GET | `api/Accounting/PaymentMethod` | `AccountingController` | `PaymentMethod` | `GetPaymentMethodInteractor` | AccountingRepository.GetListPaymentMethodMst (`kaikei_inf`) |
| GET | `api/Accounting/PtByoMei` | `AccountingController` | `PtByoMei` | `GetPtByoMeiInteractor` | AccountingRepository.GetPtByoMeiList (`kaikei_inf`) |
| POST | `api/Accounting/Recaculation` | `AccountingController` | `Recaculation` | `RecaculationInteractor` |  |
| POST | `api/Accounting/SaveAccounting` | `AccountingController` | `SaveAccounting` | `SaveAccountingInteractor` | AccountingRepository.GetRaiinInfModel (`kaikei_inf`)<br/>AccountingRepository.GetListRaiinInf (`kaikei_inf`)<br/>AccountingRepository.GetListSyunoSeikyu (`kaikei_inf`)<br/>AccountingRepository.SaveChanges (`kaikei_inf`)<br/>AccountingRepository.CreateSyunoNyukin (`kaikei_inf`)<br/>AccountingRepository.UpdateStatusSyunoSeikyu (`kaikei_inf`)<br/>AccountingRepository.UpdateStatusRaiinInf (`kaikei_inf`)<br/>AccountingRepository.GetListSyunoSeikyuModel (`kaikei_inf`)<br/>AccountingRepository.SaveAccounting (`kaikei_inf`)<br/>AccountingRepository.GetRaiinInfOyaCnt (`kaikei_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`)<br/>UserRepository.CheckExistedUserId (`user_mst`)<br/>UserRepository.GetPermissionByScreenCode (`user_mst`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>ReceptionRepository.GetRecptionList (`raiin_inf`)<br/>ReceptionRepository.GetListSameVisit (`raiin_inf`)<br/>AuditLogRepository.AddAuditTrailLog (`audit_trail_log`)<br/>FincodeRepository.RequestReservePayment (`fincode_inf`) |
| POST | `api/Accounting/UpdateAccountingFormMst` | `AccountingController` | `UpdateAccountingFormMst` | `UpdateAccountingFormMstInteractor` | AccountingRepository.UpdateAccountingFormMst (`kaikei_inf`) |
| GET | `api/Accounting/WarningMemo` | `AccountingController` | `WarningMemo` | `GetWarningMemoInteractor` | AccountingRepository.GetListRaiinInf (`kaikei_inf`)<br/>AccountingRepository.GetCalcLog (`kaikei_inf`) |
| GET | `api/Agent/Authentication` | `AgentController` | `AppToken` | `GetUserByLoginIdInteractor` | UserRepository.GetByLoginId (`user_mst`) |
| GET | `api/Agent/GetAgentDownloadLink` | `AgentController` | `GetAgentDownloadLink` | `GetAgentDownloadLinkInteractor` |  |
| GET | `api/Agent/GetAgentSetting` | `AgentController` | `GetAgentSetting` | `GetAgentSettingInteractor` | AgentSettingRepository.GetAgentSetting (`agent_setting`) |
| GET | `api/Agent/GetDetailCustomButtonConf` | `AgentController` | `GetDetailCustomButtonConf` | `GetDetailCustomButtonConfInteractor` | CustomButtonConfRepository.GetDetailCustomButtonConfList (`custombuttonconf_inf`) |
| POST | `api/Agent/GetDispensingInfFromCsvData` | `AgentController` | `GetDispensingInfFromCsvData` | `GetDispensingInfFromCsvDataInteractor` |  |
| GET | `api/Agent/GetEpsDispensings` | `AgentController` | `GetEpsDispensings` | `GetDispensingInfListInteractor` | EpsRepository.GetDispensingInfList (`eps_inf`) |
| POST | `api/Agent/GetListRenkei` | `AgentController` | `GetListRenkei` | `GetListRenKeiInteractor` | RenkeiRepository.GetRenkeiModels (`renkei_inf`) |
| POST | `api/Agent/GetPreRegistrationData` | `AgentController` | `GetPreRegistrationData` | `GetPreRegistrationDataInteractor` | EpsRepository.GetPreRegistrationCheckingData (`eps_inf`) |
| POST | `api/Agent/GetPrescriptionIdList` | `AgentController` | `GetPrescriptionIdList` | `GetPrescriptionIdListInteractor` | EpsRepository.GetPrescriptionIdList (`eps_inf`) |
| GET | `api/Agent/GetList` | `AgentController` | `GetSystemConfList` | `GetSystemConfInteractor` | SystemConfRepository.GetByGrpCd (`system_conf`) |
| GET | `api/Agent/GetSystemConfListXmlPath` | `AgentController` | `GetSystemConfListXmlPath` | `GetSystemConfInteractor` | SystemConfRepository.GetByGrpCd (`system_conf`) |
| POST | `api/Agent/InsertOnlineConfirmation + "ByXml` | `AgentController` | `InsertOnlineConfirmationByXml` | `InsertOnlineConfirmationInteractor` | OnlineRepository.SaveOnlineConfirmation (`online_inf`) |
| POST | `api/Agent/ProcessKensaIrai` | `AgentController` | `ProcessKensaIrai` | `ProcessKensaIraiInteractor` | KensaIraiRepository.GetPtInf (`kensairai_inf`)<br/>KensaIraiRepository.GetRaiinInf (`kensairai_inf`)<br/>KensaIraiRepository.GetIngaiKensaOdrInf (`kensairai_inf`)<br/>KensaIraiRepository.GetIngaiKensaOdrInfDetail (`kensairai_inf`)<br/>SystemConfRepository.GetSettingParams (`system_conf`)<br/>RenkeiRepository.GetRenkeiModels (`renkei_inf`)<br/>RenkeiOutputDataRepository.GetRenkeiOutputDataModel (`renkeioutputdata_inf`)<br/>RenkeiOutputDataRepository.SaveRenkeiOutputData (`renkeioutputdata_inf`)<br/>KensaCenterPartnershipRepository.GetKensaCenterPartnershipBySinDate (`kensacenterpartnership_inf`) |
| POST | `api/Agent/ProcessRenkei` | `AgentController` | `ProcessRenkei` | `ProcessRenkeiInteractor` | RenkeiRepository.GetRenkeiModels (`renkei_inf`)<br/>RenkeiRepository.GetRenkei130Data (`renkei_inf`)<br/>RenkeiRepository.GetRenkei260OdrInf (`renkei_inf`)<br/>RenkeiRepository.GetUserMst (`renkei_inf`)<br/>RenkeiRepository.GetPtInf (`renkei_inf`)<br/>RenkeiRepository.GetPtGrpInf (`renkei_inf`)<br/>RenkeiRepository.GetPtMemo (`renkei_inf`)<br/>RenkeiRepository.GetRaiinInf (`renkei_inf`)<br/>RenkeiRepository.GetRaiinKbnInf (`renkei_inf`)<br/>RenkeiRepository.GetKaikeiInf (`renkei_inf`)<br/>RenkeiRepository.GetHokenPid (`renkei_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`) |
| POST | `api/Agent/SavePrescriptionInfo` | `AgentController` | `SavePrescriptionInfo` | `SavePrescriptionInfoInteractor` | EpsRepository.SavePrescriptionInfo (`eps_inf`) |
| POST | `api/Agent/UpdateAgentSetting` | `AgentController` | `UpdateAgentSetting` | `UpdateAgentSettingInteractor` | AgentSettingRepository.SaveAgentSetting (`agent_setting`) |
| POST | `api/Agent/UpdateEpsDispensings` | `AgentController` | `UpdateEpsDispensings` | `UpdateEpsDispensingsInteractor` | EpsRepository.UpdateEpsDispensings (`eps_inf`) |
| POST | `api/Agent/UpdatePrescriptionStatusByIds` | `AgentController` | `UpdatePrescriptionStatusByIds` | `UpdatePrescriptionInteractor` | ReceptionRepository.UpdatePrescription (`raiin_inf`)<br/>ReceptionRepository.Get (`raiin_inf`) |
| POST | `api/Agent/UpsertEpsRegister` | `AgentController` | `UpsertEpsRegister` | `UpsertEpsRegisterInteractor` | EpsRepository.UpsertEpsReq (`eps_inf`) |
| POST | `api/Agent/WriteListLog` | `AgentController` | `WriteListLogForAgent` | `WriteListLogInteractor` |  |
| POST | `api/AuditLog/Save` | `AuditLogController` | `Save` | `SaveAuditTrailLogInteractor` | AuditLogRepository.SaveAuditLog (`audit_trail_log`) |
| GET | `api/Auth/Authentication` | `AuthController` | `AppToken` | `GetUserByLoginIdInteractor` | UserRepository.GetByLoginId (`user_mst`) |
| POST | `api/Auth/RefreshToken` | `AuthController` | `RefreshAccessToken` | `RefreshTokenByUserInteractor` | AdminRepository.RefreshTokenByUser (`admin_inf`) |
| POST | `api/Calculate/RunCalculateOne` | `CalculateController` | `RunCalculateMonth` | `CalculateInteractor` | UserRepository.NotAllowSaveMedicalExamination (`user_mst`) |
| POST | `api/ChartApproval/CheckSaveLogOutChartApporval` | `ChartApprovalController` | `CheckSaveLogOut` | `CheckSaveLogOutInteractor` | ApprovalInfRepository.NeedApprovalInf (`approvalinf`)<br/>UserRepository.GetPermissionByScreenCode (`user_mst`)<br/>SystemConfRepository.GetSettingValue (`system_conf`)<br/>SystemConfRepository.GetSettingParams (`system_conf`) |
| GET | `api/ChartApproval/GetList` | `ChartApprovalController` | `GetList` | `GetApprovalInfListInteractor` | ApprovalInfRepository.GetList (`approvalinf`) |
| POST | `api/ChartApproval/Save` | `ChartApprovalController` | `Update` | `SaveApprovalInfListInteractor` | ApprovalInfRepository.SaveApprovalInfs (`approvalinf`)<br/>UserRepository.GetCheckDoctorPermission (`user_mst`) |
| POST | `api/ColumnSetting/GetColumnSettingByTableNameList` | `ColumnSettingController` | `GetColumnSettingByTableNameList` | `GetColumnSettingByTableNameListInteractor` |  |
| GET | `api/ColumnSetting/GetList` | `ColumnSettingController` | `GetList` | `GetColumnSettingListInteractor` | ColumnSettingRepository.GetList (`columnsetting_inf`) |
| POST | `api/ColumnSetting/SaveList` | `ColumnSettingController` | `SaveList` | `SaveColumnSettingListInteractor` | ColumnSettingRepository.SaveList (`columnsetting_inf`) |
| POST | `api/ConsultationResult/Update` | `ConsultationResultController` | `Update` | `ConsultationResultUpdateInteractor` | KarteVSPHYSRepository.SaveKartePhysicalsConsulationResult (`kartevsphys_inf`)<br/>KarteMedicalHistorySocialRepository.SaveKarteMedicalHistorySocialConsultationResult (`kartemedicalhistorysocial_inf`)<br/>PregnantInfoRepository.SavePregnantInfoConsultationResult (`pregnantinfo_inf`) |
| DELETE | `api/CustomButtonConf/DeleteCustomButtonConf` | `CustomButtonConfController` | `DeleteCustomButtonConf` | `DeleteCustomButtonConfInteractor` | CustomButtonConfRepository.DeleteCustomButtonConf (`custombuttonconf_inf`)<br/>CustomButtonConfRepository.CheckExistCustomButtonConf (`custombuttonconf_inf`) |
| GET | `api/CustomButtonConf/GetDetailCustomButtonConf` | `CustomButtonConfController` | `GetDetailCustomButtonConf` | `GetDetailCustomButtonConfInteractor` | CustomButtonConfRepository.GetDetailCustomButtonConfList (`custombuttonconf_inf`) |
| GET | `api/CustomButtonConf/ListAllCustomButtonConf` | `CustomButtonConfController` | `ListAllCustomButtonConfList` | `GetCustomButtonConfInteractor` | CustomButtonConfRepository.GetCustomButtonConfList (`custombuttonconf_inf`) |
| POST | `api/CustomButtonConf/SaveCustomButtonConf` | `CustomButtonConfController` | `SaveCustomButtonConfList` | `SaveCustomButtonConfInteractor` | CustomButtonConfRepository.SaveCustomButtonConf (`custombuttonconf_inf`)<br/>CustomButtonConfRepository.CheckExistCustomButtonConf (`custombuttonconf_inf`) |
| POST | `api/CustomButtonConf/UpdateSortForListCustomButtonConf` | `CustomButtonConfController` | `UpdateSortForListCustomButtonConf` | `UpdateSortCustomButtonConfInteractor` |  |
| GET | `api/CustomButtonParamMst/ListCustomButtonParamMsts` | `CustomButtonParamMstController` | `GetCustomButtonParamMstList` | `GetCustomButtonParamMstInteractor` | CustomButtonParamMstRepository.GetCustomButtonParamMstList (`custombuttonparammst`) |
| POST | `api/Database/MigrateDatabase` | `DatabaseController` | `GetList` | `MigrateDatabaseInteractor` |  |
| GET | `api/Diseases/GetAllByomeiByPtId` | `DiseasesController` | `GetAllByomeiByPtId` | `GetAllByomeiByPtIdInteractor` | PtDiseaseRepository.GetAllByomeiByPtId (`ptdisease_inf`) |
| GET | `api/Diseases/GetByomeisInMonth` | `DiseasesController` | `GetByomeisInMonth` | `GetByomeisInMonthInteractor` | PtDiseaseRepository.GetByomeisInMonth (`ptdisease_inf`) |
| GET | `api/Diseases/GetList` | `DiseasesController` | `GetDiseaseListMedicalExamination` | `GetDiseaseListInteractor` | MstItemRepository.DiseaseSearch (`ten_mst`) |
| GET | `api/Diseases/GetListRecentRegisteredDisease` | `DiseasesController` | `GetListRecentRegisteredDisease` | `GetListRecentRegisteredDiseaseInteractor` | PtDiseaseRepository.GetListRecentRegisteredDisease (`ptdisease_inf`) |
| GET | `api/Diseases/GetSetByomeiTree` | `DiseasesController` | `GetSetByomeiTree` | `GetSetByomeiTreeInteractor` | PtDiseaseRepository.GetDataTreeSetByomei (`ptdisease_inf`) |
| GET | `api/Diseases/IsHokenInfInUsed` | `DiseasesController` | `IsHokenInfInUsed` | `IsHokenInfInUsedInteractor` | PtDiseaseRepository.IsHokenInfInUsed (`ptdisease_inf`) |
| POST | `api/Diseases/UpdateByomeiSetMst` | `DiseasesController` | `UpdateByomeiSetMst` | `UpdateByomeiSetMstInteractor` | PtDiseaseRepository.UpdateByomeiSetMst (`ptdisease_inf`) |
| POST | `api/Diseases/Upsert` | `DiseasesController` | `Upsert` | `UpsertPtDiseaseListInteractor` | PtDiseaseRepository.Upsert (`ptdisease_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>AuditLogRepository.AddAuditTrailLog (`audit_trail_log`) |
| POST | `api/Diseases/Validate` | `DiseasesController` | `Validate` | `ValidationPtDiseaseListInteractor` | PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>InsuranceRepository.CheckExistHokenPIdList (`hoken_inf`) |
| POST | `api/Document/UploadTemplateToCategory` | `DocumentController` | `AddTemplateToCategory` | `UploadTemplateToCategoryInteractor` | DocumentRepository.CheckExistDocCategory (`doc_inf`)<br/>HpInfRepository.CheckHpId (`hp_inf`) |
| POST | `api/Document/CheckExistFileName` | `DocumentController` | `CheckExistFileName` | `CheckExistFileNameInteractor` | DocumentRepository.CheckExistDocCategory (`doc_inf`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>PatientInforRepository.GetById (`pt_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`) |
| GET | `api/Document/ConfirmReplaceDocParam` | `DocumentController` | `ConfirmReplaceDocParam` | `ConfirmReplaceDocParamInteractor` | DocumentRepository.GetListDocComment (`doc_inf`) |
| PUT | `api/Document/DeleteDocCategory` | `DocumentController` | `DeleteDocCategory` | `DeleteDocCategoryInteractor` | DocumentRepository.DeleteDocCategory (`doc_inf`)<br/>DocumentRepository.CheckExistDocCategory (`doc_inf`)<br/>DocumentRepository.MoveDocInf (`doc_inf`)<br/>DocumentRepository.DeleteDocInfs (`doc_inf`)<br/>PatientInforRepository.GetById (`pt_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`) |
| PUT | `api/Document/DeleteDocInf` | `DocumentController` | `DeleteDocInf` | `DeleteDocInfInteractor` | DocumentRepository.GetDocInfDetail (`doc_inf`)<br/>DocumentRepository.DeleteDocInf (`doc_inf`)<br/>PatientInforRepository.GetById (`pt_inf`) |
| PUT | `api/Document/DeleteDocTemplate` | `DocumentController` | `DeleteDocTemplate` | `DeleteDocTemplateInteractor` |  |
| POST | `api/Document/DowloadDocumentTemplate` | `DocumentController` | `ExportTemplate` | `DownloadDocumentTemplateInteractor` |  |
| GET | `api/Document/GetDetailDocumentCategory` | `DocumentController` | `GetDetail` | `GetDocCategoryDetailInteractor` | DocumentRepository.CheckExistDocCategory (`doc_inf`)<br/>DocumentRepository.GetDocCategoryDetail (`doc_inf`)<br/>DocumentRepository.GetDocInfByCategoryCd (`doc_inf`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>PatientInforRepository.GetById (`pt_inf`) |
| GET | `api/Document/GetListDocumentCategory` | `DocumentController` | `GetList` | `GetListDocCategoryInteractor` | DocumentRepository.GetAllTemplateDocInf (`doc_inf`)<br/>HpInfRepository.CheckHpId (`hp_inf`) |
| POST | `api/Document/GetListDocComment` | `DocumentController` | `GetListDocComment` | `GetListDocCommentInteractor` | DocumentRepository.GetListDocComment (`doc_inf`) |
| GET | `api/Document/GetListParamTemplate` | `DocumentController` | `GetListParamTemplate` | `GetListParamTemplateInteractor` |  |
| PUT | `api/Document/MoveTemplateToOtherCategory` | `DocumentController` | `MoveTemplateToOtherCategory` | `MoveTemplateToOtherCategoryInteractor` | DocumentRepository.CheckExistDocCategory (`doc_inf`) |
| POST | `api/Document/SaveDocInf` | `DocumentController` | `SaveDocInf` | `SaveDocInfInteractor` | DocumentRepository.SaveDocInf (`doc_inf`)<br/>DocumentRepository.CheckExistDocCategory (`doc_inf`)<br/>DocumentRepository.GetDocInfDetail (`doc_inf`)<br/>PatientInforRepository.GetById (`pt_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>AuditLogRepository.AddAuditTrailLog (`audit_trail_log`) |
| POST | `api/Document/SaveListDocumentCategory` | `DocumentController` | `SaveList` | `SaveListDocCategoryInteractor` | DocumentRepository.SaveListDocCategory (`doc_inf`)<br/>DocumentRepository.CheckExistDocCategory (`doc_inf`)<br/>DocumentRepository.CheckDuplicateCategoryName (`doc_inf`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`) |
| PUT | `api/Document/SortDocCategory` | `DocumentController` | `SortDocCategory` | `SortDocCategoryInteractor` | DocumentRepository.SortDocCategory (`doc_inf`)<br/>DocumentRepository.CheckExistDocCategory (`doc_inf`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`) |
| GET | `api/Eps/CheckCsvHokenInf` | `EpsController` | `CheckCsvHokenInf` | `CheckCsvHokenInfInteractor` | EpsRepository.CheckCsvHokenInf (`eps_inf`) |
| POST | `api/Eps/CheckErrorForPreRegistration` | `EpsController` | `CheckErrorForPreRegistration` | `CheckErrorPreRegistrationInteractor` | EpsRepository.FindPtHokenPatternList (`eps_inf`)<br/>EpsRepository.GetIpnKasanMst (`eps_inf`)<br/>EpsRepository.GetIpnKasanExclude (`eps_inf`)<br/>MstItemRepository.FindTenMst (`ten_mst`)<br/>SystemConfRepository.GetSettingValue (`system_conf`) |
| POST | `api/Eps/CheckErrorTodayOdrForEPSRegistration` | `EpsController` | `CheckErrorTodayOdrForEPSRegistration` | `CheckErrorTodayOdrForEPSRegistrationInteractor` | EpsRepository.FindPtHokenPatternList (`eps_inf`)<br/>EpsRepository.GetIpnKasanMst (`eps_inf`)<br/>EpsRepository.GetIpnKasanExclude (`eps_inf`)<br/>EpsRepository.GetPrintSettingFlag (`eps_inf`)<br/>EpsRepository.GetEpsDispensingByResultType (`eps_inf`)<br/>EpsRepository.GetRaiinInfByRaiinNo (`eps_inf`)<br/>EpsRepository.GetOdrInfs (`eps_inf`)<br/>EpsRepository.FindPtHokenPatternById (`eps_inf`)<br/>MstItemRepository.FindTenMst (`ten_mst`)<br/>SystemConfRepository.GetSettingValue (`system_conf`) |
| POST | `api/Eps/CreateEpsReference` | `EpsController` | `CreateEpsReference` | `CreateEpsReferenceInteractor` | EpsRepository.CreateEpsReference (`eps_inf`) |
| POST | `api/Eps/GetDispensingInfFromCsvData` | `EpsController` | `GetDispensingInfFromCsvData` | `GetDispensingInfFromCsvDataInteractor` |  |
| GET | `api/Eps/GetDuplicateMedicationCheck` | `EpsController` | `GetDuplicateMedicationCheck` | `GetDuplicateMedicationCheckInteractor` | EpsChkRepository.GetEpsChks (`epschk_inf`) |
| GET | `api/Eps/GetEpsDispensings` | `EpsController` | `GetEpsDispensings` | `GetDispensingInfListInteractor` | EpsRepository.GetDispensingInfList (`eps_inf`) |
| GET | `api/Eps/GetEpsInsuranceInfo` | `EpsController` | `GetEpsInsuranceInfo` | `GetEpsInsuranceInfInteractor` | EpsRepository.GetInsuranceInf (`eps_inf`) |
| POST | `api/Eps/GetOutDrugCsvData` | `EpsController` | `GetOutDrugCsvData` | `GetOutDrugCsvDataInteractor` | PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>KaRepository.GetList (`ka_inf`)<br/>AccountingRepository.FindPtHokenPatternById (`kaikei_inf`) |
| GET | `api/Eps/GetPreCheckOldPrescription` | `EpsController` | `GetPreCheckOldPrescription` | `GetPreCheckOldPrescriptionInteractor` | EpsRepository.PreCheckOldPrescription (`eps_inf`) |
| POST | `api/Eps/GetPreRegistrationData` | `EpsController` | `GetPreRegistrationData` | `GetPreRegistrationDataInteractor` | EpsRepository.GetPreRegistrationCheckingData (`eps_inf`) |
| POST | `api/Eps/GetPrescriptionFromCsvData` | `EpsController` | `GetPrescriptionFromCsvData` | `GetPrescriptionFromCsvDataInteractor` | EpsRepository.GetYohoMst (`eps_inf`) |
| POST | `api/Eps/GetPrescriptionIdList` | `EpsController` | `GetPrescriptionIdList` | `GetPrescriptionIdListInteractor` | EpsRepository.GetPrescriptionIdList (`eps_inf`) |
| GET | `api/Eps/GetPrescriptionInfList` | `EpsController` | `GetPrescriptionInfList` | `GetPrescriptionInfListInteractor` | EpsRepository.GetPrescriptionInfList (`eps_inf`) |
| POST | `api/Eps/SaveDispensingResultList` | `EpsController` | `SaveDispensingResultList` | `SaveDispensingResultListInteractor` | EpsRepository.SaveDispensingResultList (`eps_inf`) |
| POST | `api/Eps/SaveDuplicateMedicationCheck` | `EpsController` | `SaveDuplicateMedicationCheck` | `SaveDuplicateMedicationCheckInteractor` | EpsChkRepository.SaveEpsChk (`epschk_inf`) |
| POST | `api/Eps/SavePrescriptionInfo` | `EpsController` | `SavePrescriptionInfo` | `SavePrescriptionInfoInteractor` | EpsRepository.SavePrescriptionInfo (`eps_inf`) |
| POST | `api/Eps/UpdateDispensingByResponse` | `EpsController` | `UpdateDispensingByResponse` | `UpdateDispensingByResponseInteractor` | EpsRepository.updateDispensingByResponse (`eps_inf`) |
| POST | `api/Eps/UpdateEpsDispensings` | `EpsController` | `UpdateEpsDispensings` | `UpdateEpsDispensingsInteractor` | EpsRepository.UpdateEpsDispensings (`eps_inf`) |
| POST | `api/Eps/UpdatePrescriptionStatus` | `EpsController` | `UpdatePrescriptionStatus` | `UpdatePrescriptionInteractor` | ReceptionRepository.UpdatePrescription (`raiin_inf`)<br/>ReceptionRepository.Get (`raiin_inf`) |
| POST | `api/Eps/UpdatePrescriptionStatusByIds` | `EpsController` | `UpdatePrescriptionStatusByIds` | `UpdatePrescriptionInteractor` | ReceptionRepository.UpdatePrescription (`raiin_inf`)<br/>ReceptionRepository.Get (`raiin_inf`) |
| POST | `api/Eps/UpsertDispensingInf` | `EpsController` | `UpsertDispensingInf` | `UpsertDispensingInfInteractor` | EpsRepository.UpsertDispensingInf (`eps_inf`) |
| POST | `api/Eps/UpsertEpsRegister` | `EpsController` | `UpsertEpsRegister` | `UpsertEpsRegisterInteractor` | EpsRepository.UpsertEpsReq (`eps_inf`) |
| GET | `api/Eps/ValidateBeforePrinting` | `EpsController` | `ValidateBeforePrinting` | `ValidateBeforePrintingInteractor` | EpsRepository.ValidateBeforePrinting (`eps_inf`) |
| GET | `api/ExamResult/GetExamResults` | `ExamResultController` | `GetExamResults` | `GetExamResultListInteractor` | ExamResultsRepository.GetExamResults (`examresults_inf`) |
| POST | `api/ExamResult/Save` | `ExamResultController` | `SaveExamResults` | `SaveKarteVSPHYSInteractor` | KarteVSPHYSRepository.SaveKartePhysicals (`kartevsphys_inf`)<br/>KarteVSPHYSRepository.CheckIsExistedKensaTime (`kartevsphys_inf`)<br/>KarteVSPHYSRepository.CheckIsExistedKensaTimeWithIraiCd (`kartevsphys_inf`) |
| GET | `api/Family/GetRaiinInfList` | `FamilyController` | `GetListRaiinInf` | `GetListRaiinInfInteractor` | ReceptionRepository.GetListRaiinInf (`raiin_inf`) |
| POST | `api/FcoApiKey/CreateFcoApiKey` | `FcoApiKeyController` | `CreateFcoApiKey` | `CreateFcoApiKeyInteractor` |  |
| POST | `api/FcoApiKey/DeleteFcoApiKey` | `FcoApiKeyController` | `DeleteFcoApiKey` | `DeleteFcoApiKeyInteractor` |  |
| GET | `api/FcoApiKey/GetFcoApiKeys` | `FcoApiKeyController` | `GetFcoApiKeys` | `GetFcoApiKeysInteractor` |  |
| POST | `api/FcoApiKey/UpdateFcoApiKey` | `FcoApiKeyController` | `UpdateFcoApiKey` | `UpdateFcoApiKeyInteractor` |  |
| GET | `fco/UnaccountedTransactions` | `FcoLinkController` | `GetFcoList` | `GetFcoLinkListInteractor` | FcoLinkRepository.SearchPtInfByPtNum (`fcolink_inf`)<br/>FcoLinkRepository.GetSeikyuInfList (`fcolink_inf`)<br/>FcoLinkRepository.GetSinKouiList (`fcolink_inf`)<br/>FcoLinkRepository.GetOdrInfList (`fcolink_inf`)<br/>FcoLinkRepository.IsExistsFcoApiKeys (`fcolink_inf`) |
| POST | `fco/Deposits` | `FcoLinkController` | `SaveDeposits` | `SaveDepositsInteractor` | FcoLinkRepository.GetListSeikyuInf (`fcolink_inf`)<br/>FcoLinkRepository.SaveChanges (`fcolink_inf`)<br/>FcoLinkRepository.IsExistsFcoApiKeys (`fcolink_inf`)<br/>FcoLinkRepository.FcoSaveNyukin (`fcolink_inf`)<br/>AuditLogRepository.AddAuditTrailLog (`audit_trail_log`)<br/>ReceptionRepository.GetReceptionListByRaiinNoes (`raiin_inf`) |
| GET | `api/FlowSheet/GetList + "FlowSheet` | `FlowSheetController` | `GetListFlowSheet` | `GetListFlowSheetInteractor` | FlowSheetRepository.GetHolidayMst (`flowsheet_inf`)<br/>FlowSheetRepository.GetListFlowSheet (`flowsheet_inf`)<br/>FlowSheetRepository.GetRaiinListMsts (`flowsheet_inf`)<br/>FlowSheetRepository.GetRaiinListInf (`flowsheet_inf`)<br/>FlowSheetRepository.GetRaiinListInfForNextOrder (`flowsheet_inf`) |
| GET | `api/FlowSheet/GetList + "Holiday` | `FlowSheetController` | `GetListHoliday` | `GetListFlowSheetInteractor` | FlowSheetRepository.GetHolidayMst (`flowsheet_inf`)<br/>FlowSheetRepository.GetListFlowSheet (`flowsheet_inf`)<br/>FlowSheetRepository.GetRaiinListMsts (`flowsheet_inf`)<br/>FlowSheetRepository.GetRaiinListInf (`flowsheet_inf`)<br/>FlowSheetRepository.GetRaiinListInfForNextOrder (`flowsheet_inf`) |
| GET | `api/FlowSheet/GetList + "RaiinMst` | `FlowSheetController` | `GetListRaiinMst` | `GetListFlowSheetInteractor` | FlowSheetRepository.GetHolidayMst (`flowsheet_inf`)<br/>FlowSheetRepository.GetListFlowSheet (`flowsheet_inf`)<br/>FlowSheetRepository.GetRaiinListMsts (`flowsheet_inf`)<br/>FlowSheetRepository.GetRaiinListInf (`flowsheet_inf`)<br/>FlowSheetRepository.GetRaiinListInfForNextOrder (`flowsheet_inf`) |
| GET | `api/FlowSheet/GetToolTip` | `FlowSheetController` | `GetToolTip` | `GetTooltipInteractor` |  |
| POST | `api/FlowSheet/Upsert` | `FlowSheetController` | `Upsert` | `UpsertFlowSheetInteractor` | FlowSheetRepository.UpsertTag (`flowsheet_inf`)<br/>FlowSheetRepository.UpsertCmt (`flowsheet_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>ReceptionRepository.CheckListNo (`raiin_inf`) |
| GET | `api/History/GetDataPrintKarte2` | `HistoryController` | `GetDataPrintKarte2` | `GetDataPrintKarte2Interactor` |  |
| POST | `api/History/GetHistoryIndex` | `HistoryController` | `GetHistoryIndex` | `GetHistoryIndexInteractor` | HistoryOrderRepository.GetHistoryIndex (`ord_inf`) |
| POST | `api/History/GetList` | `HistoryController` | `GetList` | `GetMedicalExaminationHistoryInteractor` | HistoryOrderRepository.GetList (`ord_inf`)<br/>InsuranceRepository.GetInsuranceSummaryList (`hoken_inf`) |
| POST | `api/History/Search` | `HistoryController` | `Search` | `SearchHistoryInteractor` | HistoryOrderRepository.Search (`ord_inf`) |
| POST | `api/Holiday/SaveHolidayMst` | `HolidayController` | `SaveHolidayMst` | `SaveHolidayMstInteractor` | FlowSheetRepository.SaveHolidayMst (`flowsheet_inf`)<br/>UserRepository.GetPermissionByScreenCode (`user_mst`) |
| GET | `api/InputItem/DrugDataSelectedTree` | `InputItemController` | `DrugDataSelectedTree` | `GetDrugDetailDataInteractor` | DrugDetailRepository.GetDataDrugSeletedTree (`drugdetail_inf`) |
| GET | `api/InputItem/GetDataPrintDrugInfo` | `InputItemController` | `GetDataPrintDrugInfo` | `GetDataPrintDrugInfoInteractor` |  |
| GET | `api/InputItem/GetDrugInf` | `InputItemController` | `GetDrugInformation` | `GetDrugInforInteractor` |  |
| GET | `api/InputItem/GetDrugMenuTree` | `InputItemController` | `GetDrugMenuTree` | `GetDrugDetailInteractor` | DrugDetailRepository.GetDrugMenu (`drugdetail_inf`) |
| GET | `api/InputItem/GetListUsageTreeSet` | `InputItemController` | `GetUsageTree` | `GetUsageTreeSetInteractor` | UsageTreeSetRepository.GetGenerationId (`usagetreeset_inf`)<br/>UsageTreeSetRepository.GetTanSetInfs (`usagetreeset_inf`) |
| GET | `api/InputItem/GetYohoSetMstByItemCd` | `InputItemController` | `GetYohoSetMstByItemCd` | `GetYohoMstByItemCdInteractor` | YohoSetMstRepository.GetByItemCd (`yohosetmst`) |
| GET | `api/InputItem/ShowKanjaMuke` | `InputItemController` | `ShowKanjaMuke` | `ShowKanjaMukeInteractor` |  |
| GET | `api/InputItem/ShowMdbByomei` | `InputItemController` | `ShowMdbByomei` | `ShowMdbByomeiInteractor` |  |
| GET | `api/InputItem/ShowProductInf` | `InputItemController` | `ShowProductInf` | `ShowProductInfInteractor` |  |
| POST | `api/InputItem/UpdateListSetMst` | `InputItemController` | `UpdateListSetMst` | `UpdateListSetMstInteractor` | ListSetMstRepository.UpdateTreeListSetMst (`listsetmst`) |
| POST | `api/InsuranceMst/Delete + "HokenMaster` | `InsuranceMstController` | `DeleteHokenMaster` | `DeleteHokenMasterInteractor` | InsuranceMstRepository.DeleteHokenMaster (`hoken_sya_mst`) |
| GET | `api/InsuranceMst/FindHokenInfByPtId` | `InsuranceMstController` | `FindHokenInfByPtId` | `FindHokenInfByPtIdInteractor` | InsuranceRepository.FindHokenInfByPtId (`hoken_inf`) |
| GET | `api/InsuranceMst/GetAutomobileInsuranceCombobox` | `InsuranceMstController` | `GetAutomobileInsuranceCombobox` | `GetInsuranceCommonComboboxInteractor` | InsuranceMstRepository.GetInitDataInsuranceMst (`hoken_sya_mst`) |
| GET | `api/InsuranceMst/GetHokenMasterReadOnly` | `InsuranceMstController` | `GetHokenMasterReadOnly` | `GetHokenMasterReadOnlyInteractor` | InsuranceMstRepository.GetHokenMasterReadOnly (`hoken_sya_mst`) |
| GET | `api/InsuranceMst/Get + "InfoCloneInsuranceMst` | `InsuranceMstController` | `GetInfoCloneInsuranceMst` | `GetInfoCloneInsuranceMstInteractor` | InsuranceMstRepository.GetInfoCloneInsuranceMst (`hoken_sya_mst`) |
| GET | `api/InsuranceMst/GetInsuranceCardCombobox` | `InsuranceMstController` | `GetInsuranceCardCombobox` | `GetInsurancesDataByPtIdInteractor` | InsuranceRepository.GetInsurancesDataById (`hoken_inf`) |
| GET | `api/InsuranceMst/GetList + "InsuranceMstDetail` | `InsuranceMstController` | `GetList` | `GetInsuranceMasterDetailInteractor` | InsuranceMstRepository.GetInsuranceMasterDetails (`hoken_sya_mst`) |
| GET | `api/InsuranceMst/GetListHokenFund` | `InsuranceMstController` | `GetListHokenFund` | `GetListHokenFundInteractor` | InsuranceMstRepository.GetListHokenFund (`hoken_sya_mst`) |
| GET | `api/InsuranceMst/Get + "HokenInf` | `InsuranceMstController` | `GetListHokenInf` | `GetHokenInfInteractor` | InsuranceRepository.GetHokenInf (`hoken_inf`) |
| GET | `api/InsuranceMst/GetPublicExpenseCombobox` | `InsuranceMstController` | `GetPublicExpenseCombobox` | `GetInsuranceCommonComboboxInteractor` | InsuranceMstRepository.GetInitDataInsuranceMst (`hoken_sya_mst`) |
| GET | `api/InsuranceMst/Get + "SelectMaintenance` | `InsuranceMstController` | `GetSelectMaintenance` | `GetSelectMaintenanceInteractor` | InsuranceMstRepository.GetSelectMaintenance (`hoken_sya_mst`) |
| GET | `api/InsuranceMst/GetWorkRelatedInjuryCombobox` | `InsuranceMstController` | `GetWorkRelatedInjuryCombobox` | `GetInsuranceCommonComboboxInteractor` | InsuranceMstRepository.GetInitDataInsuranceMst (`hoken_sya_mst`) |
| POST | `api/InsuranceMst/Save + "InsuranceMst` | `InsuranceMstController` | `SaveInsuranceMst` | `SaveHokenMasterInteractor` | InsuranceMstRepository.CheckDuplicateKey (`hoken_sya_mst`)<br/>InsuranceMstRepository.CreateHokenMaster (`hoken_sya_mst`)<br/>InsuranceMstRepository.UpdateHokenMaster (`hoken_sya_mst`) |
| POST | `api/InsuranceMst/Save + "OrderInsuranceMstList` | `InsuranceMstController` | `SaveOrderInsuranceMstList` | `SaveOrdInsuranceMstInteractor` | InsuranceMstRepository.SaveOrdInsuranceMst (`hoken_sya_mst`) |
| GET | `api/JsonSetting/Get` | `JsonSettingController` | `Get` | `GetJsonSettingInteractor` | JsonSettingRepository.Get (`jsonsetting_inf`) |
| GET | `api/JsonSetting/GetList` | `JsonSettingController` | `GetList` | `GetAllJsonSettingInteractor` | JsonSettingRepository.GetListFollowUserId (`jsonsetting_inf`) |
| POST | `api/JsonSetting/Upsert` | `JsonSettingController` | `Upsert` | `UpsertJsonSettingInteractor` | JsonSettingRepository.Upsert (`jsonsetting_inf`) |
| GET | `api/Ka/GetKaCodeMstYossi` | `KaController` | `GetKaCodeMstYossi` | `GetKaCodeMstInteractor` | KaRepository.GetListKacode (`ka_inf`) |
| GET | `api/Ka/GetKaCodeYousikiMst` | `KaController` | `GetKaCodeYousikiMst` | `GetKaCodeYousikiMstInteractor` | KaRepository.GetKacodeYousikiMst (`ka_inf`) |
| GET | `api/Ka/GetListKaCode` | `KaController` | `GetListKaCodeMst` | `GetKaCodeMstInteractor` | KaRepository.GetListKacode (`ka_inf`) |
| GET | `api/Ka/GetList + "Mst` | `KaController` | `GetListMst` | `GetKaMstListInteractor` | KaRepository.GetList (`ka_inf`) |
| POST | `api/Ka/SaveListKaMst` | `KaController` | `Save` | `SaveKaMstInteractor` | KaRepository.GetListKacode (`ka_inf`)<br/>KaRepository.SaveKaMst (`ka_inf`) |
| GET | `api/KarteAllergy/GetKarteAllergyList` | `KarteAllergyController` | `GetKarteAllergyList` | `GetKarteAllergyInteractor` | KarteAllergyOthersRepository.GetAllergyElseList (`karteallergyothers_inf`)<br/>KarteAllergyFoodRepository.GetAllergyFoodList (`karteallergyfood_inf`)<br/>KarteAllergyMedicineRepository.GetAllergyDrugList (`karteallergymedicine_inf`) |
| POST | `api/KarteAllergy/SaveKarteAllergyMedicine` | `KarteAllergyController` | `SaveKarteAllergyMedicine` | `SaveKarteAllergyMedicineInteractor` | KarteAllergyMedicineRepository.SaveDrugItems (`karteallergymedicine_inf`) |
| POST | `api/KarteAllergy/SaveKarteAllergyOthers` | `KarteAllergyController` | `SaveKarteAllergyOthers` | `SaveKarteAllergyOthersInteractor` | KarteAllergyOthersRepository.SaveOtherDrugItems (`karteallergyothers_inf`) |
| POST | `api/KarteAllergy/SaveKarteAllergyFood` | `KarteAllergyController` | `SaveKaterAllergyFood` | `SaveKarteAllergyFoodInteractor` | KarteAllergyFoodRepository.SaveKarteAllergyFood (`karteallergyfood_inf`) |
| POST | `api/KarteFile/AddKarteFile` | `KarteFileController` | `AddKarteFile` | `AddKarteFileInteractor` | KarteFileRepository.AddKarteFile (`karte_file`)<br/>HpInfRepository.CheckHpId (`hp_inf`) |
| POST | `api/KarteFile/AttachKarteFile` | `KarteFileController` | `AttachKarteFile` | `AttachKarteFileInteractor` | KarteFileRepository.GetKarteFileInfo (`karte_file`)<br/>KarteFileRepository.AttachKarteFile (`karte_file`)<br/>KarteFileRepository.GetCopyFilesByName (`karte_file`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`) |
| POST | `api/KarteFile/DeleteKarteFile` | `KarteFileController` | `DeleteKarteFile` | `DeleteKarteFileInteractor` | KarteFileRepository.GetS3FileNameOfKarteFile (`karte_file`)<br/>KarteFileRepository.DeleteKarteFile (`karte_file`) |
| POST | `api/KarteFile/GeneratePreSignedUrl` | `KarteFileController` | `GeneratePreSignedUrl` | `GeneratePreSignedUrlInteractor` |  |
| GET | `api/KarteFile/Get` | `KarteFileController` | `GetLinkDownload` | `GetKarteFileInteractor` | KarteFileRepository.GetLinkDownload (`karte_file`) |
| GET | `api/KarteFile/GetListFileVersion` | `KarteFileController` | `GetListFileVersion` | `GetListFileVersionInteractor` | KarteFileRepository.GetListVersionFile (`karte_file`) |
| GET | `api/KarteFile/GetListKarteFile` | `KarteFileController` | `GetListKarteFile` | `GetListKarteFileInteractor` | KarteFileRepository.GetKarteFiles (`karte_file`) |
| POST | `api/KarteFile/MoveKarteFileCategory` | `KarteFileController` | `MoveKarteFileCategory` | `UpdateKarteFileInteractor` | KarteFileRepository.MoveKarteFileCategory (`karte_file`)<br/>KarteFileRepository.UpdateKarteFileInfo (`karte_file`) |
| POST | `api/KarteFile/UpdateKarteFileInfo` | `KarteFileController` | `UpdateKarteFileInfo` | `UpdateKarteFileInteractor` | KarteFileRepository.MoveKarteFileCategory (`karte_file`)<br/>KarteFileRepository.UpdateKarteFileInfo (`karte_file`) |
| POST | `api/KarteFile/UpdateKarteFileSoap` | `KarteFileController` | `UpdateKarteFileSoap` | `UpdateKarteFileInteractor` | KarteFileRepository.MoveKarteFileCategory (`karte_file`)<br/>KarteFileRepository.UpdateKarteFileInfo (`karte_file`) |
| GET | `api/KarteFilter/GetList` | `KarteFilterController` | `GetList` | `GetKarteFilterInteractor` |  |
| POST | `api/KarteFilter/SaveList` | `KarteFilterController` | `SaveList` | `SaveKarteFilterInteractor` |  |
| POST | `api/KarteInf/ConvertTextToRichText` | `KarteInfController` | `ConvertTextToRichText` | `ConvertTextToRichTextInteractor` | KarteInfRepository.ConvertTextToRichText (`karte_inf`) |
| GET | `api/KarteInf/GetList` | `KarteInfController` | `GetList` | `GetListKarteInfInteractor` | KarteInfRepository.GetList (`karte_inf`)<br/>ReceptionRepository.GetStatusRaiinInf (`raiin_inf`) |
| GET | `api/KarteMedicalHistory/GetKarteMedicalHistory` | `KarteMedicalHistoryController` | `GetKarteMedicalHistory` | `GetKarteMedicalHistoryInteractor` | ImportantNoteRepository.GetOtherDrugList (`importantnote_inf`)<br/>ImportantNoteRepository.GetOtcDrugList (`importantnote_inf`)<br/>ImportantNoteRepository.GetSuppleList (`importantnote_inf`)<br/>ImportantNoteRepository.GetKioRekiList (`importantnote_inf`)<br/>KarteMedicalHistoryRepository.GetPregnantList (`kartemedicalhistory_inf`)<br/>KarteMedicalHistoryRepository.GetPtSocialHistoryList (`kartemedicalhistory_inf`)<br/>KarteMedicalHistoryRepository.GetFamilyList (`kartemedicalhistory_inf`) |
| POST | `api/KarteMedicalHistory/SaveKarteMedicalHistoryFamily` | `KarteMedicalHistoryController` | `SaveKarteMedicalHistoryFamily` | `SaveKarteMedicalHistoryFamilyInteractor` | KarteMedicalHistoryFamilyRepository.SavePtFamilies (`kartemedicalhistoryfamily_inf`) |
| POST | `api/KarteMedicalHistory/SaveKarteMedicalHistorySocial` | `KarteMedicalHistoryController` | `SaveKarteMedicalHistoryPast` | `SaveKarteMedicalHistoryPastInteractor` | KarteMedicalHistoryPastRepository.SaveKarteMedicalHistoryPast (`kartemedicalhistorypast_inf`) |
| POST | `api/KarteMedicalHistory/SaveKarteMedicalHistoryPast` | `KarteMedicalHistoryController` | `SaveKarteMedicalHistoryPast` | `SaveKarteMedicalHistoryPastInteractor` | KarteMedicalHistoryPastRepository.SaveKarteMedicalHistoryPast (`kartemedicalhistorypast_inf`) |
| POST | `api/KarteMedicalHistory/SaveKarteMedicalHistorySupply` | `KarteMedicalHistoryController` | `SaveKarteMedicalHistorySupply` | `SaveKarteMedicalHistorySupplyInteractor` | KarteMedicalHistorySupplyRepository.SavePtSupples (`kartemedicalhistorysupply_inf`) |
| POST | `api/KarteMedicalHistory/SaveOtcMedicine` | `KarteMedicalHistoryController` | `SaveOtcMedicine` | `SaveOtcMedicineInteractor` | OtcMedicineRepository.SaveOtcMedicineItems (`otcmedicine_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`) |
| POST | `api/KarteMedicalHistory/SaveKarteMedicalHistoryOtherDrug` | `KarteMedicalHistoryController` | `SaveOtherDrugList` | `SaveKarteMedicalHistoryOtherDrugInteractor` | KarteMedicalHistoryOtherDrugRepository.SaveOtherDrug (`kartemedicalhistoryotherdrug_inf`) |
| POST | `api/KarteMedicalHistory/SavePregnantInfo` | `KarteMedicalHistoryController` | `SavePregnantInfo` | `SavePregnantInfoInteractor` | PregnantInfoRepository.SavePregnantInfo (`pregnantinfo_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`) |
| GET | `api/KarteVSPHYS/GetList` | `KarteVSPHYSController` | `GetList` | `GetKarteVSPHYSListInteractor` | KarteVSPHYSRepository.GetKarteVSPHYSList (`kartevsphys_inf`) |
| POST | `api/KarteVSPHYS/Save` | `KarteVSPHYSController` | `SaveKartePhysicals` | `SaveKarteVSPHYSInteractor` | KarteVSPHYSRepository.SaveKartePhysicals (`kartevsphys_inf`)<br/>KarteVSPHYSRepository.CheckIsExistedKensaTime (`kartevsphys_inf`)<br/>KarteVSPHYSRepository.CheckIsExistedKensaTimeWithIraiCd (`kartevsphys_inf`) |
| GET | `api/KensaCenterPartnership/GetKensaCenterPartnership` | `KensaCenterPartnershipController` | `GetkensaCenterPartnership` | `GetKensaCenterPartnershipInteractor` |  |
| POST | `api/KensaCenterPartnership/RegisterKensaCenterPartnership` | `KensaCenterPartnershipController` | `RegisterKensaCenterPartnership` | `RegisterKensaCenterPartnershipInteractor` |  |
| POST | `api/KensaCenterPartnership/UnregisterKensaCenterPartnership` | `KensaCenterPartnershipController` | `UnregisterKensaCenterPartnership` | `UnregisterKensaCenterPartnershipInteractor` |  |
| POST | `api/KensaCenterPartnership/UpdateKensaCenterPartnership` | `KensaCenterPartnershipController` | `UpdateKensaCenterPartnership` | `UpdateKensaCenterPartnershipInteractor` |  |
| POST | `api/KensaCenterPartnership/UpdateKensaCenterPartnershipMstUpdateDate` | `KensaCenterPartnershipController` | `UpdateKensaCenterPartnershipMstUpdateDate` | `UpdateKensaCenterPartnershipMstUpdateDateInteractor` |  |
| GET | `api/KensaHistory/GetKensaInfDetailByIraiCd` | `KensaHistoryController` | `GetKensaInfDetailByIraiCd` | `GetKensaInfDetailByIraiCdInteractor` | KensaSetRepository.GetKensaInfDetailByIraiCd (`kensaset_inf`) |
| GET | `api/KensaHistory/GetListKensaCmtMst` | `KensaHistoryController` | `GetListKensaCmtMst` | `GetListKensaCmtMstInteractor` | KensaSetRepository.GetListKensaCmtMst (`kensaset_inf`) |
| GET | `api/KensaHistory/GetListKensaInfDetail` | `KensaHistoryController` | `GetListKensaInfDetail` | `GetListKensaInfDetailInteractor` | KensaSetRepository.GetListKensaInfDetail (`kensaset_inf`) |
| GET | `api/KensaHistory/GetListKensaSet` | `KensaHistoryController` | `GetListKensaSet` | `GetListKensaSetInteractor` | KensaSetRepository.GetListKensaSet (`kensaset_inf`) |
| GET | `api/KensaHistory/GetListKensaSetDetail` | `KensaHistoryController` | `GetListKensaSetDetail` | `GetListKensaSetDetailInteractor` | KensaSetRepository.GetListKensaSetDetail (`kensaset_inf`) |
| POST | `api/KensaHistory/UpdateKensaInfDetail` | `KensaHistoryController` | `UpdateKensaInfDetail` | `UpdateKensaInfDetailInteractor` | KensaSetRepository.UpdateKensaInfDetail (`kensaset_inf`) |
| POST | `api/KensaHistory/UpdateKensaSet` | `KensaHistoryController` | `UpdateKensaSet` | `UpdateKensaSetInteractor` | KensaSetRepository.UpdateKensaSet (`kensaset_inf`) |
| POST | `api/KensaMst/CreateKensaMst` | `KensaMstController` | `CreateKensaMst` | `CreateKensaMstInteractor` |  |
| POST | `api/KensaMst/DeleteKensaMst` | `KensaMstController` | `DeleteKensaMst` | `DeleteKensaMstInteractor` |  |
| GET | `api/KensaMst/GetInHospitalKensaMst` | `KensaMstController` | `GetInHospitalKensaMst` | `GetInHospitalKensaMstInteractor` |  |
| GET | `api/KensaMst/GetKensaMsts` | `KensaMstController` | `GetKensaMsts` | `GetKensaMstInteractor` |  |
| POST | `api/KensaMst/UpdateKensaMst` | `KensaMstController` | `UpdateKensaMst` | `UpdateKensaMstInteractor` | MstItemRepository.UpdateKensaMst (`ten_mst`) |
| GET | `api/LabelMst/GetList` | `LabelMstController` | `GetList` | `GetLabelMstInteractor` | LabelMstRepository.GetList (`labelmst`) |
| POST | `api/Lock/AcceptLockMedicalTab` | `LockController` | `AcceptLockMedicalTab` | `AcceptLockMedicalTabInteractor` | LockRepository.AcceptLockRequest (`lock_inf`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`) |
| POST | `api/Lock/AddLock` | `LockController` | `AddLock` | `AddLockInteractor` | LockRepository.AddLock (`lock_inf`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`)<br/>LockRepository.GetResponseLockModel (`lock_inf`)<br/>LockRepository.GetLock (`lock_inf`)<br/>LockRepository.GetFunctionNameLock (`lock_inf`)<br/>UserRepository.GetByUserId (`user_mst`) |
| POST | `api/Lock/CheckIsExistedOQLockInfo` | `LockController` | `CheckIsExistedOQLockInfo` | `CheckIsExistedOQLockInfoInteractor` | LockRepository.CheckIsExistedOQLockInfo (`lock_inf`) |
| POST | `api/Lock/CheckLock` | `LockController` | `CheckLock` | `CheckLockInteractor` | LockRepository.GetLock (`lock_inf`) |
| GET | `api/Lock/CheckLockVisiting` | `LockController` | `CheckLockVisiting` | `CheckLockVisitingInteractor` | LockRepository.GetVisitingLockStatus (`lock_inf`) |
| GET | `api/Lock/CheckExistFunctionCode` | `LockController` | `CheckOpenSpecialNote` | `CheckExistFunctionCodeInteractor` | LockRepository.CheckOpenSpecialNote (`lock_inf`) |
| POST | `api/Lock/GetLockInfo` | `LockController` | `GetList` | `GetLockInfoInteractor` | LockRepository.GetLockInfo (`lock_inf`) |
| GET | `api/Lock/GetListLockMedicalTab` | `LockController` | `GetListLockMedicalTab` | `GetListLockMedicalTabInteractor` | LockRepository.GetLockMedicalTab (`lock_inf`) |
| GET | `api/Lock/GetLockInf` | `LockController` | `GetLockInf` | `GetLockInfInteractor` | LockRepository.GetLockInfModels (`lock_inf`)<br/>LockRepository.GetLockInf (`lock_inf`) |
| GET | `api/Lock/RemoveAllLock` | `LockController` | `RemoveAllLock` | `RemoveLockInteractor` | LockRepository.RemoveAllLock (`lock_inf`)<br/>LockRepository.RemoveAllLockMedical (`lock_inf`)<br/>LockRepository.RemoveLockMedicalByTab (`lock_inf`)<br/>LockRepository.RemoveLock (`lock_inf`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`)<br/>LockRepository.GetResponseLockModel (`lock_inf`) |
| POST | `api/Lock/RemoveAllLockMedicalTab` | `LockController` | `RemoveAllLockMedicalTab` | `RemoveLockInteractor` | LockRepository.RemoveAllLock (`lock_inf`)<br/>LockRepository.RemoveAllLockMedical (`lock_inf`)<br/>LockRepository.RemoveLockMedicalByTab (`lock_inf`)<br/>LockRepository.RemoveLock (`lock_inf`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`)<br/>LockRepository.GetResponseLockModel (`lock_inf`) |
| GET | `api/Lock/RemoveAllLockPtId` | `LockController` | `RemoveAllLockPtId` | `RemoveLockInteractor` | LockRepository.RemoveAllLock (`lock_inf`)<br/>LockRepository.RemoveAllLockMedical (`lock_inf`)<br/>LockRepository.RemoveLockMedicalByTab (`lock_inf`)<br/>LockRepository.RemoveLock (`lock_inf`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`)<br/>LockRepository.GetResponseLockModel (`lock_inf`) |
| POST | `api/Lock/RemoveLock` | `LockController` | `RemoveLock` | `RemoveLockInteractor` | LockRepository.RemoveAllLock (`lock_inf`)<br/>LockRepository.RemoveAllLockMedical (`lock_inf`)<br/>LockRepository.RemoveLockMedicalByTab (`lock_inf`)<br/>LockRepository.RemoveLock (`lock_inf`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`)<br/>LockRepository.GetResponseLockModel (`lock_inf`) |
| POST | `api/Lock/RemoveLockMedicalByTab` | `LockController` | `RemoveLockMedicalByTab` | `RemoveLockInteractor` | LockRepository.RemoveAllLock (`lock_inf`)<br/>LockRepository.RemoveAllLockMedical (`lock_inf`)<br/>LockRepository.RemoveLockMedicalByTab (`lock_inf`)<br/>LockRepository.RemoveLock (`lock_inf`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`)<br/>LockRepository.GetResponseLockModel (`lock_inf`) |
| POST | `api/Lock/RemoveLockWhenLogOut` | `LockController` | `RemoveLockWhenLogOut` | `RemoveLockInteractor` | LockRepository.RemoveAllLock (`lock_inf`)<br/>LockRepository.RemoveAllLockMedical (`lock_inf`)<br/>LockRepository.RemoveLockMedicalByTab (`lock_inf`)<br/>LockRepository.RemoveLock (`lock_inf`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`)<br/>LockRepository.GetResponseLockModel (`lock_inf`) |
| POST | `api/Lock/RequestLockMedicalTab` | `LockController` | `RequestLockMedicalTab` | `RequestLockMedicalTabInteractor` | LockRepository.GetLockOfFunctionInfor (`lock_inf`)<br/>LockRepository.AddLock (`lock_inf`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`) |
| POST | `api/Lock/Unlock` | `LockController` | `Unlock` | `UnlockInteractor` | LockRepository.Unlock (`lock_inf`) |
| POST | `api/Logging/WriteListLog` | `LoggingController` | `WriteListLog` | `WriteListLogInteractor` |  |
| POST | `api/Logging/WriteLog` | `LoggingController` | `WriteLog` | `WriteLogInteractor` |  |
| POST | `api/MainMenu/CreateDataKensaIraiRenkei` | `MainMenuController` | `CreateDataKensaIraiRenkei` | `CreateDataKensaIraiRenkeiInteractor` | KensaIraiRepository.CreateDataKensaIraiRenkei (`kensairai_inf`)<br/>KensaIraiRepository.ReCreateDataKensaIraiRenkei (`kensairai_inf`)<br/>KensaIraiRepository.CheckExistCenterCd (`kensairai_inf`)<br/>ReceptionRepository.CheckExistRaiinNo (`raiin_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`) |
| POST | `api/MainMenu/DeleteKensaInf` | `MainMenuController` | `DeleteKensaInf` | `DeleteKensaInfInteractor` | KensaIraiRepository.DeleteKensaInfModel (`kensairai_inf`)<br/>KensaIraiRepository.CheckExistIraiCdList (`kensairai_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`) |
| GET | `api/MainMenu/DownloadKensaIraiLog` | `MainMenuController` | `DownloadKensaIraiLog` | `DownloadKensaIraiLogInteractor` | KensaIraiRepository.DownloadKensaIraiLogModels (`kensairai_inf`) |
| GET | `api/MainMenu/FindPtHokenList` | `MainMenuController` | `FindPtHokenList` | `FindPtHokenListInteractor` | InsuranceRepository.FindPtHokenList (`hoken_inf`) |
| GET | `api/MainMenu/GetKensaCenterMstList` | `MainMenuController` | `GetKensaCenterMstList` | `GetKensaCenterMstListInteractor` | MstItemRepository.GetListKensaCenterMst (`ten_mst`) |
| GET | `api/MainMenu/GetKensaInf` | `MainMenuController` | `GetKensaInf` | `GetKensaInfInteractor` | KensaIraiRepository.GetKensaInfModels (`kensairai_inf`) |
| GET | `api/MainMenu/GetKensaIrai` | `MainMenuController` | `GetKensaIrai` | `GetKensaIraiInteractor` | KensaIraiRepository.GetKensaIraiModels (`kensairai_inf`) |
| POST | `api/MainMenu/GetKensaIraiByList` | `MainMenuController` | `GetKensaIraiByList` | `GetKensaIraiInteractor` | KensaIraiRepository.GetKensaIraiModels (`kensairai_inf`) |
| GET | `api/MainMenu/GetKensaIraiLog` | `MainMenuController` | `GetKensaIraiLog` | `GetKensaIraiInteractor` | KensaIraiRepository.GetKensaIraiModels (`kensairai_inf`) |
| GET | `api/MainMenu/GetStatisticMenuList` | `MainMenuController` | `GetList` | `GetStatisticMenuInteractor` | StatisticRepository.GetStaGrp (`statistic_inf`)<br/>StatisticRepository.GetStatisticMenu (`statistic_inf`) |
| GET | `api/MainMenu/GetListQualificationInf` | `MainMenuController` | `GetListQualificationInf` | `GetListQualificationInfInteractor` | OnlineRepository.GetListQualificationInf (`online_inf`) |
| GET | `api/MainMenu/GetListStaticReport` | `MainMenuController` | `GetListStaticReport` | `` |  |
| GET | `api/MainMenu/GetLoadListVersion` | `MainMenuController` | `GetLoadListVersion` | `GetLoadListVersionInteractor` | ReleasenoteReadRepository.GetLoadListVersion (`releasenoteread_inf`)<br/>ReleasenoteReadRepository.CheckShowReleaseNote (`releasenoteread_inf`) |
| GET | `api/MainMenu/GetRsvInfToConfirm` | `MainMenuController` | `GetRsvInfToConfirm` | `GetRsvInfToConfirmInteractor` | RsvInfRepository.GetListRsvInfToConfirmModel (`rsvinf`) |
| GET | `api/MainMenu/$"{ApiPath.GetRsvInfToConfirm}Validity` | `MainMenuController` | `GetRsvInfToConfirmValidity` | `GetRsvInfToConfirmInteractor` | RsvInfRepository.GetListRsvInfToConfirmModel (`rsvinf`) |
| GET | `api/MainMenu/GetStaCsvMst` | `MainMenuController` | `GetStaCsvMst` | `GetStaCsvMstInteractor` | StatisticRepository.GetStaCsvMstModels (`statistic_inf`) |
| POST | `api/MainMenu/ImportKensaIrai` | `MainMenuController` | `ImportKensaIrai` | `ImportKensaIraiInteractor` | KensaIraiRepository.SaveKensaIraiImport (`kensairai_inf`)<br/>KensaIraiRepository.GetIraiCdNotExistList (`kensairai_inf`) |
| POST | `api/MainMenu/KensaIraiReport` | `MainMenuController` | `KensaIraiReport` | `KensaIraiReportInteractor` | KensaIraiRepository.SaveKensaIraiLog (`kensairai_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`)<br/>SystemConfRepository.GetSettingParamsByVal (`system_conf`)<br/>ReceptionRepository.GetRaiinInf (`raiin_inf`) |
| POST | `api/MainMenu/ReCreateDataKensaIraiRenkei` | `MainMenuController` | `ReCreateDataKensaIraiRenkei` | `CreateDataKensaIraiRenkeiInteractor` | KensaIraiRepository.CreateDataKensaIraiRenkei (`kensairai_inf`)<br/>KensaIraiRepository.ReCreateDataKensaIraiRenkei (`kensairai_inf`)<br/>KensaIraiRepository.CheckExistCenterCd (`kensairai_inf`)<br/>ReceptionRepository.CheckExistRaiinNo (`raiin_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`) |
| POST | `api/MainMenu/SaveStatisticMenuList` | `MainMenuController` | `SaveList` | `SaveStatisticMenuInteractor` | StatisticRepository.SaveStatisticMenu (`statistic_inf`)<br/>StatisticRepository.GetStatisticMenu (`statistic_inf`)<br/>StatisticRepository.GetStaGrp (`statistic_inf`)<br/>UserRepository.GetPermissionByScreenCode (`user_mst`) |
| POST | `api/MainMenu/SaveStaCsvMst` | `MainMenuController` | `SaveStaCsvMst` | `SaveStaCsvMstInteractor` | StatisticRepository.SaveStaCsvMst (`statistic_inf`) |
| POST | `api/MainMenu/UpdateListReleasenote` | `MainMenuController` | `UpdateListReleasenote` | `UpdateListReleasenoteInteractor` |  |
| POST | `api/MedicalExamination/Calculate` | `MedicalExaminationController` | `Calculate` | `CalculateInteractor` | UserRepository.NotAllowSaveMedicalExamination (`user_mst`) |
| POST | `api/MedicalExamination/CheckTrialAccounting` | `MedicalExaminationController` | `CheckTrialAccounting` | `CheckOpenTrialAccountingInteractor` | AccountingRepository.FindPtHokenPatternList (`kaikei_inf`)<br/>TodayOdrRepository.GetValidGairaiRiha (`today_odr`)<br/>TodayOdrRepository.GetValidJihiYobo (`today_odr`) |
| POST | `api/MedicalExamination/CheckedAfter327Screen` | `MedicalExaminationController` | `CheckedAfter327Screen` | `CheckedAfter327ScreenInteractor` | MedicalExaminationRepository.GetCheckedAfter327Screen (`medicalexamination_inf`) |
| POST | `api/MedicalExamination/GetCheckDiseases` | `MedicalExaminationController` | `GetCheckDiseases` | `GetCheckDiseaseInteractor` | TodayOdrRepository.GetCheckDiseases (`today_odr`) |
| POST | `api/MedicalExamination/GetCheckedOrder` | `MedicalExaminationController` | `GetCheckedOrder` | `GetCheckedOrderInteractor` | MedicalExaminationRepository.IgakuTokusitu (`medicalexamination_inf`)<br/>MedicalExaminationRepository.IgakuTokusituIsChecked (`medicalexamination_inf`)<br/>MedicalExaminationRepository.SihifuToku1 (`medicalexamination_inf`)<br/>MedicalExaminationRepository.InitPriorityCheckDetail (`medicalexamination_inf`)<br/>MedicalExaminationRepository.GairaiZaitakuRiha (`medicalexamination_inf`)<br/>MedicalExaminationRepository.IryoJyohoSyutoku (`medicalexamination_inf`)<br/>MedicalExaminationRepository.Zanyaku (`medicalexamination_inf`)<br/>MedicalExaminationRepository.SihifuToku2 (`medicalexamination_inf`)<br/>MedicalExaminationRepository.TouyakuTokusyoSyoho (`medicalexamination_inf`)<br/>MedicalExaminationRepository.IgakuTenkan (`medicalexamination_inf`)<br/>MedicalExaminationRepository.ChikiHokatu (`medicalexamination_inf`)<br/>MedicalExaminationRepository.IgakuNanbyo (`medicalexamination_inf`)<br/>MedicalExaminationRepository.YakkuZai (`medicalexamination_inf`)<br/>MedicalExaminationRepository.SiIkuji (`medicalexamination_inf`)<br/>MedicalExaminationRepository.TrialIryoJyohoKibanCalculation (`medicalexamination_inf`)<br/>MedicalExaminationRepository.IryoDX (`medicalexamination_inf`)<br/>MedicalExaminationRepository.TrialGairaiZaitakuRiha (`medicalexamination_inf`)<br/>MedicalExaminationRepository.TrialIryoJyohoSyutoku (`medicalexamination_inf`)<br/>MedicalExaminationRepository.IryoJyohoSyutoku202412 (`medicalexamination_inf`)<br/>ReceptionRepository.CheckRaiinInf (`raiin_inf`)<br/>ReceptionRepository.Get (`raiin_inf`)<br/>TodayOdrRepository.GetMaxRpNo (`today_odr`)<br/>MstItemRepository.GetTenMstInfoList (`ten_mst`)<br/>MstItemRepository.GetExistedTenMstItemCds (`ten_mst`)<br/>MstItemRepository.ExistedTenMstItem (`ten_mst`)<br/>MstItemRepository.GetTenMstInfo (`ten_mst`)<br/>OnlineRepository.ExistOnlineConsent (`online_inf`)<br/>SystemConfRepository.GetAllSystemConfig (`system_conf`) |
| POST | `api/MedicalExamination/GetContainerMst` | `MedicalExaminationController` | `GetContainerMst` | `GetContainerMstInteractor` | MedicalExaminationRepository.GetContainerMstModels (`medicalexamination_inf`) |
| GET | `api/MedicalExamination/GetDefaultSelectedTime` | `MedicalExaminationController` | `GetDefaultSelectedTime` | `GetDefaultSelectedTimeInteractor` | TimeZoneRepository.IsHoliday (`timezone_inf`)<br/>TimeZoneRepository.GetTimeZoneConfs (`timezone_inf`)<br/>TimeZoneRepository.GetLatestTimeZoneDayInf (`timezone_inf`)<br/>TimeZoneRepository.CheckPediatricExist (`timezone_inf`) |
| GET | `api/MedicalExamination/GetHeaderVistitDate` | `MedicalExaminationController` | `GetHeaderVistitDate` | `GetHeaderVistitDateInteractor` | UserConfRepository.GetSettingValue (`userconf_inf`)<br/>ReceptionRepository.GetFirstVisitWithSyosin (`raiin_inf`)<br/>ReceptionRepository.GetLastVisit (`raiin_inf`) |
| GET | `api/MedicalExamination/GetHistoryFollowSinDate` | `MedicalExaminationController` | `GetHistoryFollowSinDate` | `GetHistoryFollowSindateInteractor` | HistoryOrderRepository.GetListByRaiin (`ord_inf`)<br/>InsuranceRepository.GetInsuranceList (`hoken_inf`)<br/>PatientInforRepository.GetById (`pt_inf`) |
| POST | `api/MedicalExamination/GetInfCheckedSpecialItem` | `MedicalExaminationController` | `GetInfCheckedSpecialItem` | `CheckedSpecialItemInteractor` | TodayOdrRepository.FindDensiSanteiKaisuList (`today_odr`)<br/>TodayOdrRepository.MonthsAfterExcludeHoliday (`today_odr`)<br/>TodayOdrRepository.SanteiCount (`today_odr`)<br/>MstItemRepository.FindTenMst (`ten_mst`)<br/>MstItemRepository.FindItemGrpMst (`ten_mst`)<br/>MstItemRepository.GetCmtCheckMsts (`ten_mst`)<br/>InsuranceRepository.GetHokenKbnList (`hoken_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`)<br/>ReceptionRepository.GetFirstVisitWithSyosin (`raiin_inf`) |
| GET | `api/MedicalExamination/GetKensaAuditTrailLog` | `MedicalExaminationController` | `GetKensaAuditTrailLog` | `GetKensaAuditTrailLogInteractor` | MedicalExaminationRepository.GetKensaAuditTrailLogs (`medicalexamination_inf`) |
| GET | `api/MedicalExamination/GetMaxAuditTrailLogDateForPrint` | `MedicalExaminationController` | `GetMaxAuditTrailLogDateForPrint` | `GetMaxAuditTrailLogDateForPrintInteractor` | MedicalExaminationRepository.GetMaxAuditTrailLogDateForPrint (`medicalexamination_inf`) |
| GET | `api/MedicalExamination/GetOrderSheetGroup` | `MedicalExaminationController` | `GetOrderSheetGroup` | `GetOrderSheetGroupInteractor` | OrdInfRepository.GetList (`ord_inf`)<br/>UserConfRepository.GetSettingValue (`userconf_inf`)<br/>UserConfRepository.GetSettingValues (`userconf_inf`) |
| GET | `api/MedicalExamination/GetOrdersForOneOrderSheetGroup` | `MedicalExaminationController` | `GetOrdersForOneOrderSheetGroup` | `GetOrdersForOneOrderSheetGroupInteractor` | HistoryOrderRepository.GetOrdersForOneOrderSheetGroup (`ord_inf`)<br/>InsuranceRepository.GetInsuranceList (`hoken_inf`) |
| GET | `api/MedicalExamination/GetSinkouCountInMonth` | `MedicalExaminationController` | `GetSinkouCountInMonth` | `GetSinkouCountInMonthInteractor` | MedicalExaminationRepository.GetSinkouCountInMonth (`medicalexamination_inf`) |
| GET | `api/MedicalExamination/GetSummaryInf` | `MedicalExaminationController` | `GetSummaryInf` | `SummaryInfInteractor` | PatientInforRepository.GetPtInf (`pt_inf`)<br/>PatientInfoRepository.GetListKensaInfDetailModel (`patientinfo_inf`)<br/>PatientInfoRepository.GetPregnancyList (`patientinfo_inf`)<br/>PatientInfoRepository.GetSeikaturekiInfList (`patientinfo_inf`)<br/>ImportantNoteRepository.GetAlrgyElseList (`importantnote_inf`)<br/>ImportantNoteRepository.GetAlrgyFoodList (`importantnote_inf`)<br/>ImportantNoteRepository.GetAlrgyDrugList (`importantnote_inf`)<br/>ImportantNoteRepository.GetKioRekiList (`importantnote_inf`)<br/>ImportantNoteRepository.GetInfectionList (`importantnote_inf`)<br/>ImportantNoteRepository.GetOtherDrugList (`importantnote_inf`)<br/>ImportantNoteRepository.GetOtcDrugList (`importantnote_inf`)<br/>ImportantNoteRepository.GetSuppleList (`importantnote_inf`)<br/>SanteiInfRepository.GetCalculationInfo (`santeiinf`)<br/>PtCmtInfRepository.GetPtCmtInfo (`ptcmtinf`)<br/>InsuranceRepository.GetInsuranceListById (`hoken_inf`)<br/>FamilyRepository.GetFamilyListByPtId (`family_inf`)<br/>RaiinCmtInfRepository.GetRaiinCmtByPtId (`raiincmtinf`)<br/>UserConfRepository.GetSettingParam (`userconf_inf`)<br/>UserConfRepository.GetList (`userconf_inf`)<br/>UserConfRepository.GetListUserConf (`userconf_inf`)<br/>RsvInfRepository.GetList (`rsvinf`)<br/>TodayOdrRepository.GetConfirmationType (`today_odr`)<br/>SummaryInfRepository.Get (`summaryinf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`) |
| POST | `api/MedicalExamination/InitKbnSetting` | `MedicalExaminationController` | `InitKbnSetting` | `InitKbnSettingInteractor` | TodayOdrRepository.InitDefaultByTodayOrder (`today_odr`)<br/>RaiinKubunMstRepository.GetRaiinKbns (`raiin_kbn_mst`)<br/>RaiinKubunMstRepository.GetRaiinKouiKbns (`raiin_kbn_mst`)<br/>RaiinKubunMstRepository.GetRaiinKbnItems (`raiin_kbn_mst`)<br/>RaiinKubunMstRepository.InitDefaultByRsv (`raiin_kbn_mst`)<br/>NextOrderRepository.InitDefaultByNextOrder (`ord_inf`) |
| POST | `api/MedicalExamination/OrderRealtimeChecker` | `MedicalExaminationController` | `OrderRealtimeChecker` | `GetOrderCheckerInteractor` |  |
| POST | `api/MedicalExamination/SaveKensaIrai` | `MedicalExaminationController` | `SaveKensaIrai` | `SaveKensaIraiInteractor` |  |
| POST | `api/MedicalExamination/SaveMedical` | `MedicalExaminationController` | `SaveMedical` | `SaveMedicalInteractor` | OrdInfRepository.GetCheckIpnMinYakkaMsts (`ord_inf`)<br/>OrdInfRepository.CheckIsGetYakkaPrices (`ord_inf`)<br/>OrdInfRepository.GetIpnMst (`ord_inf`)<br/>OrdInfRepository.GetListToCheckValidate (`ord_inf`)<br/>ReceptionRepository.GetList (`raiin_inf`)<br/>ReceptionRepository.GetListSameVisit (`raiin_inf`)<br/>ReceptionRepository.CheckListNo (`raiin_inf`)<br/>KaRepository.CheckKaId (`ka_inf`)<br/>MstItemRepository.GetCheckIpnCds (`ten_mst`)<br/>MstItemRepository.GetCheckTenItemModels (`ten_mst`)<br/>SystemGenerationConfRepository.GetSettingValue (`system_generation_conf`)<br/>PatientInforRepository.GetById (`pt_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>InsuranceRepository.CheckExistHokenPid (`hoken_inf`)<br/>InsuranceRepository.GetCheckListHokenInf (`hoken_inf`)<br/>UserRepository.NotAllowSaveMedicalExamination (`user_mst`)<br/>UserRepository.GetPermissionByScreenCode (`user_mst`)<br/>UserRepository.CheckExistedUserId (`user_mst`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>KarteInfRepository.ListCheckIsSchema (`karte_inf`)<br/>KarteInfRepository.SaveListFileKarte (`karte_inf`)<br/>KarteInfRepository.ClearTempData (`karte_inf`)<br/>SaveMedicalRepository.Upsert (`savemedical_inf`)<br/>SummaryInfRepository.Get (`summaryinf`)<br/>SystemConfRepository.GetByGrpCd (`system_conf`)<br/>AuditLogRepository.AddListAuditTrailLog (`audit_trail_log`) |
| PUT | `api/MedicalExamination/SaveTreatmentDepartmentAndDoctor` | `MedicalExaminationController` | `SaveTreatmentDepartmentAndDoctor` | `SaveTreatmentDepartmentAndDoctorInteractor` | MedicalExaminationRepository.SaveTreatmentDepartmentAndDoctor (`medicalexamination_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>ReceptionRepository.GetRecptionList (`raiin_inf`)<br/>CommonRepository.GetSinDateByRaiinNo (`common_inf`) |
| POST | `api/MedicalExamination/TrialAccounting` | `MedicalExaminationController` | `TrialAccounting` | `GetTrialAccountingInteractor` | ReceptionRepository.Get (`raiin_inf`)<br/>AccountingRepository.GetRaiinInfModel (`kaikei_inf`)<br/>AccountingRepository.GetListJihiSbtMst (`kaikei_inf`)<br/>UserRepository.NotAllowSaveMedicalExamination (`user_mst`) |
| GET | `api/Monshin/GetMonshinInf` | `MonshinController` | `GetMonshinInf` | `GetMonshinInforListInteractor` | MonshinInforRepository.GetMonshinInfor (`monshininfor_inf`) |
| POST | `api/Monshin/SaveMonshinInf` | `MonshinController` | `SaveList` | `SaveMonshinInteractor` |  |
| GET | `api/MstItem/CheckIsTenMstUsed` | `MstItemController` | `CheckIsTenMstUsed` | `CheckIsTenMstUsedInteractor` | MstItemRepository.IsTenMstUsed (`ten_mst`) |
| GET | `api/MstItem/CheckJihiSbtExistsInTenMst` | `MstItemController` | `CheckJihiSbtExistsInTenMst` | `CheckJihiSbtExistsInTenMstInteractor` | MstItemRepository.CheckJihiSbtExistsInTenMst (`ten_mst`) |
| POST | `api/MstItem/ContainerMasterUpdate` | `MstItemController` | `ContainerMasterUpdate` | `ContainerMasterUpdateInteractor` | MstItemRepository.ContainerMasterUpdate (`ten_mst`) |
| POST | `api/MstItem/ConvertStringChkJISKj` | `MstItemController` | `ConvertStringChkJISKj` | `ConvertStringChkJISKjInteractor` |  |
| POST | `api/MstItem/DeleteOrRecoverTenMst` | `MstItemController` | `DeleteOrRecoverTenMst` | `DeleteOrRecoverTenMstInteractor` | MstItemRepository.IsTenMstItemCdUsed (`ten_mst`)<br/>MstItemRepository.SaveDeleteOrRecoverTenMstOrigin (`ten_mst`) |
| POST | `api/MstItem/DiseaseNameMstSearch` | `MstItemController` | `DiseaseNameMstSearch` | `DiseaseNameMstSearchInteractor` | MstItemRepository.DiseaseNameMstSearch (`ten_mst`) |
| GET | `api/MstItem/DiseaseSearch` | `MstItemController` | `DiseaseSearch` | `DiseaseSearchInteractor` | MstItemRepository.DiseaseSearch (`ten_mst`) |
| GET | `api/MstItem/DiseaseSearchForPatient` | `MstItemController` | `DiseaseSearchForPatient` | `DiseaseSearchForPatientInteractor` | MstItemForPatientRepository.DiseaseSearchForPatient (`pt_inf`) |
| GET | `api/MstItem/ExistUsedKensaItemCd` | `MstItemController` | `ExistUsedKensaItemCd` | `ExistUsedKensaItemCdInteractor` | MstItemRepository.ExistUsedKensaItemCd (`ten_mst`) |
| POST | `api/MstItem/F17Common` | `MstItemController` | `F17Common` | `F17CommonInteractor` | MstItemRepository.GetTenOfKNItem (`ten_mst`)<br/>MstItemRepository.GetTenMstsWithStartDate (`ten_mst`)<br/>MstItemRepository.GetKensaStdMstModels (`ten_mst`)<br/>MstItemRepository.GetUsedKensaItemCds (`ten_mst`)<br/>MstItemRepository.GetTenItemCds (`ten_mst`)<br/>MstItemRepository.GetMaterialMsts (`ten_mst`)<br/>MstItemRepository.GetContainerMsts (`ten_mst`)<br/>MstItemRepository.GetKensaCenterMsts (`ten_mst`)<br/>MstItemRepository.GetTenOfItem (`ten_mst`) |
| GET | `api/MstItem/FindTenMst` | `MstItemController` | `FindTenMst` | `FindTenMstInteractor` | MstItemRepository.FindTenMst (`ten_mst`) |
| POST | `api/MstItem/GetAdoptedItemList` | `MstItemController` | `GetAdoptedItemList` | `GetAdoptedItemListInteractor` | MstItemRepository.GetAdoptedItems (`ten_mst`) |
| GET | `api/MstItem/GetAllCmtCheckMst` | `MstItemController` | `GetAllCmtCheckMst` | `GetAllCmtCheckMstInteractor` | MstItemRepository.GetAllCmtCheckMst (`ten_mst`) |
| GET | `api/MstItem/GetByomeiByCode` | `MstItemController` | `GetByomeiByCode` | `GetByomeiByCodeInteractor` | MstItemRepository.GetByomeiByCode (`ten_mst`) |
| POST | `api/MstItem/GetCmtCheckMstList` | `MstItemController` | `GetCmtCheckMstList` | `GetCmtCheckMstListInteractor` | MstItemRepository.GetCmtCheckMsts (`ten_mst`) |
| GET | `api/MstItem/GetList + "CommonKensaUnitMst` | `MstItemController` | `GetCommonKensaUnitMst` | `GetCommonKensaUnitMstInteractor` | MstItemRepository.GetCommonKensaUnitMst (`ten_mst`) |
| GET | `api/MstItem/GetDefaultPrecautions` | `MstItemController` | `GetDefaultPrecautions` | `GetDefaultPrecautionsInteractor` | MstItemRepository.GetPrecautions (`ten_mst`) |
| POST | `api/MstItem/GetDiseaseList` | `MstItemController` | `GetDiseaseList` | `GetDiseaseListInteractor` | MstItemRepository.DiseaseSearch (`ten_mst`) |
| POST | `api/MstItem/GetDosageDrugList` | `MstItemController` | `GetDosageDrugList` | `GetDosageDrugListInteractor` | MstItemRepository.GetDosages (`ten_mst`) |
| GET | `api/MstItem/GetDrugAction` | `MstItemController` | `GetDrugAction` | `GetDrugActionInteractor` | MstItemRepository.GetDrugAction (`ten_mst`) |
| GET | `api/MstItem/GetFoodAlrgy` | `MstItemController` | `GetFoodAlrgy` | `GetFoodAlrgyInteractor` | MstItemRepository.GetFoodAlrgyMasterData (`ten_mst`) |
| GET | `api/MstItem/GetJihiMstList` | `MstItemController` | `GetJihiMstList` | `GetJihiSbtMstListInteractor` |  |
| GET | `api/MstItem/SearchPostCode` | `MstItemController` | `GetList` | `SearchPostCodeInteractor` | MstItemRepository.SearchAddress (`ten_mst`) |
| GET | `api/MstItem/GetListByomeiSetGeneration` | `MstItemController` | `GetListByomeiSetGeneration` | `GetListByomeiSetGenerationMstInteractor` | ByomeiSetGenerationMstRepository.GetAll (`byomeisetgenerationmst`) |
| GET | `api/MstItem/GetListDrugImage` | `MstItemController` | `GetListDrugImage` | `GetListDrugImageInteractor` |  |
| GET | `api/MstItem/GetListKensaIjiSetting` | `MstItemController` | `GetListKensaIjiSetting` | `GetListKensaIjiSettingInteractor` | MstItemRepository.GetListKensaIjiSettingModel (`ten_mst`) |
| GET | `api/MstItem/GetListKensaMst` | `MstItemController` | `GetListKensaMst` | `GetListKensaMstInteractor` | MstItemRepository.GetListKensaMst (`ten_mst`) |
| GET | `api/MstItem/GetListSetGeneration` | `MstItemController` | `GetListSetGeneration` | `GetListSetGenerationMstInteractor` |  |
| GET | `api/MstItem/GetListTenMstOrigin` | `MstItemController` | `GetListTenMstOrigin` | `GetListTenMstOriginInteractor` | MstItemRepository.GetGroupTenMst (`ten_mst`) |
| GET | `api/MstItem/GetListUser` | `MstItemController` | `GetListUser` | `GetListUserInteractor` | MstItemRepository.GetListUser (`ten_mst`) |
| GET | `api/MstItem/GetListYohoSetMstModelByUserID` | `MstItemController` | `GetListYohoSetMstModelByUserID` | `GetListYohoSetMstModelByUserIDInteractor` | MstItemRepository.GetListYohoSetMstModelByUserID (`ten_mst`) |
| GET | `api/MstItem/ParrentKensaMst` | `MstItemController` | `GetParrentKensaMst` | `GetParrentKensaMstListInteractor` | MstItemRepository.GetParrentKensaMstModels (`ten_mst`) |
| GET | `api/MstItem/GetRenkeiConf` | `MstItemController` | `GetRenkeiConf` | `GetRenkeiConfInteractor` | MstItemRepository.GetRenkeiConfModels (`ten_mst`)<br/>MstItemRepository.GetRenkeiMstModels (`ten_mst`)<br/>MstItemRepository.GetRenkeiTemplateMstModels (`ten_mst`) |
| GET | `api/MstItem/GetRenkeiMst` | `MstItemController` | `GetRenkeiMst` | `GetRenkeiMstInteractor` | MstItemRepository.GetRenkeiMst (`ten_mst`) |
| GET | `api/MstItem/GetRenkeiTiming` | `MstItemController` | `GetRenkeiTiming` | `GetRenkeiTimingInteractor` | MstItemRepository.GetRenkeiTimingModel (`ten_mst`) |
| GET | `api/MstItem/GetSelectiveComment` | `MstItemController` | `GetSelectiveComment` | `GetSelectiveCommentInteractor` | MstItemRepository.GetSelectiveComment (`ten_mst`) |
| GET | `api/MstItem/GetSetDataTenMst` | `MstItemController` | `GetSetDataTenMstOrigin` | `GetSetDataTenMstInteractor` | MstItemRepository.GetListCmtKbnMstModelByItemCd (`ten_mst`)<br/>MstItemRepository.GetYohoInfMstPrefixByItemCd (`ten_mst`)<br/>MstItemRepository.GetTenMstOriginModel (`ten_mst`)<br/>MstItemRepository.GetTenMstName (`ten_mst`)<br/>MstItemRepository.GetM10DayLimitModels (`ten_mst`)<br/>MstItemRepository.GetIpnMinYakkaMstModels (`ten_mst`)<br/>MstItemRepository.GetDrugDayLimitModels (`ten_mst`)<br/>MstItemRepository.GetDosageMstModel (`ten_mst`)<br/>MstItemRepository.GetIpnNameMstModel (`ten_mst`)<br/>MstItemRepository.GetDrugInfByItemCd (`ten_mst`)<br/>MstItemRepository.GetImagePiByItemCd (`ten_mst`)<br/>MstItemRepository.GetTeikyoByomeiModel (`ten_mst`)<br/>MstItemRepository.GetTekiouByomeiMstExcludedModelByItemCd (`ten_mst`)<br/>MstItemRepository.GetDensiSanteiKaisuByItemCd (`ten_mst`)<br/>MstItemRepository.GetDensiHaihans (`ten_mst`)<br/>MstItemRepository.GetListDensiHoukatuByItemCd (`ten_mst`)<br/>MstItemRepository.GetListDensiHoukatuGrpByItemCd (`ten_mst`)<br/>MstItemRepository.GetListDensiHoukatuMaster (`ten_mst`)<br/>MstItemRepository.GetContraindicationModelList (`ten_mst`) |
| GET | `api/MstItem/GetSetNameMnt` | `MstItemController` | `GetSetNameMnt` | `GetSetNameMntInteractor` | MstItemRepository.GetGenerationId (`ten_mst`)<br/>MstItemRepository.GetSetNameMnt (`ten_mst`)<br/>MstItemRepository.GetListSetKbnMst (`ten_mst`) |
| GET | `api/MstItem/GetSingleDoseMstAndMedicineUnitList` | `MstItemController` | `GetSingleDoseMstAndMedicineUnitList` | `GetSingleDoseMstAndMedicineUnitListInteractor` | MstItemRepository.GetListSingleDoseModel (`ten_mst`)<br/>MstItemRepository.GetListMedicineUnitModel (`ten_mst`) |
| GET | `api/MstItem/GetTeikyoByomei` | `MstItemController` | `GetTeikyoByomei` | `GetTeikyoByomeiInteractor` | MstItemRepository.GetTeikyoByomeiModel (`ten_mst`) |
| GET | `api/MstItem/GetTenMstByCode` | `MstItemController` | `GetTenMstByCode` | `GetTenMstByCodeInteractor` | MstItemRepository.GetTenMstByCode (`ten_mst`) |
| POST | `api/MstItem/GetList` | `MstItemController` | `GetTenMstList` | `GetTenMstListInteractor` | MstItemRepository.GetTenMstList (`ten_mst`) |
| GET | `api/MstItem/GetTenMstListByItemType` | `MstItemController` | `GetTenMstListByItemType` | `GetTenMstListByItemTypeInteractor` | MstItemRepository.GetTenMstListByItemType (`ten_mst`) |
| GET | `api/MstItem/GetTenMstOriginInfoCreate` | `MstItemController` | `GetTenMstOriginInfoCreate` | `GetTenMstOriginInfoCreateInteractor` | MstItemRepository.GetMaxItemCdByTypeForAdd (`ten_mst`)<br/>MstItemRepository.GetMinJihiSbtMst (`ten_mst`) |
| GET | `api/MstItem/GetTreeByomeiSet` | `MstItemController` | `GetTreeByomeiSet` | `GetTreeByomeiSetInteractor` | PtDiseaseRepository.GetDataTreeSetByomei (`ptdisease_inf`) |
| GET | `api/MstItem/GetTreeListSet` | `MstItemController` | `GetTreeListSet` | `GetTreeListSetInteractor` | ListSetMstRepository.GetGenerationId (`listsetmst`)<br/>ListSetMstRepository.GetListSetMst (`listsetmst`) |
| GET | `api/MstItem/IsKensaItemOrdering` | `MstItemController` | `IsKensaItemOrdering` | `IsKensaItemOrderingInteractor` | MstItemRepository.IsKensaItemOrdering (`ten_mst`) |
| POST | `api/MstItem/IsUsingKensa` | `MstItemController` | `IsUsingKensa` | `IsUsingKensaInteractor` | MstItemRepository.IsUsingKensa (`ten_mst`) |
| POST | `api/MstItem/SaveAddressMst` | `MstItemController` | `SaveAddressMst` | `SaveAddressMstInteractor` | MstItemRepository.CheckPostCodeExist (`ten_mst`)<br/>MstItemRepository.SaveAddressMaster (`ten_mst`) |
| POST | `api/MstItem/SaveCompareTenMst` | `MstItemController` | `SaveCompareTenMst` | `CompareTenMstInteractor` | MstItemRepository.SearchCompareTenMst (`ten_mst`) |
| POST | `api/MstItem/SaveRenkei` | `MstItemController` | `SaveRenkei` | `SaveRenkeiInteractor` | MstItemRepository.SaveRenkei (`ten_mst`)<br/>MstItemRepository.GetRenkeiMstModels (`ten_mst`)<br/>MstItemRepository.GetRenkeiTemplateMstModels (`ten_mst`)<br/>MstItemRepository.GetEventMstModelList (`ten_mst`) |
| POST | `api/MstItem/SaveSetDataTenMst` | `MstItemController` | `SaveSetDataTenMst` | `SaveSetDataTenMstInteractor` | MstItemRepository.SaveTenMstOriginSetData (`ten_mst`) |
| POST | `api/MstItem/SaveSetNameMnt` | `MstItemController` | `SaveSetNameMnt` | `SaveSetNameMntInteractor` | MstItemRepository.SaveSetNameMnt (`ten_mst`) |
| POST | `api/MstItem/SearchCompareTenMst` | `MstItemController` | `SearchCompareTenMst` | `CompareTenMstInteractor` | MstItemRepository.SearchCompareTenMst (`ten_mst`) |
| POST | `api/MstItem/SearchOTC` | `MstItemController` | `SearchOTC` | `SearchOtcInteractor` | MstItemRepository.SearchOTCModels (`ten_mst`) |
| POST | `api/MstItem/SearchSupplement` | `MstItemController` | `SearchSupplement` | `SearchSupplementInteractor` | MstItemRepository.GetListSupplement (`ten_mst`) |
| POST | `api/MstItem/SearchTenItem` | `MstItemController` | `SearchTenItem` | `SearchTenItemInteractor` | MstItemRepository.SearchTenMst (`ten_mst`) |
| POST | `api/MstItem/SearchTenMstItem` | `MstItemController` | `SearchTenMstItem` | `SearchTenMstItemInteractor` | MstItemRepository.SearchTenMasterItem (`ten_mst`)<br/>MstItemRepository.SearchSuggestionTenMstItem (`ten_mst`) |
| POST | `api/MstItem/SearchTenMstItemForPatient` | `MstItemController` | `SearchTenMstItemForPatient` | `SearchTenMstItemForPatientInteractor` | MstItemForPatientRepository.SearchSuggestionTenMstItemForPatient (`pt_inf`) |
| GET | `api/MstItem/SearchUsageItem` | `MstItemController` | `SearchUsageItem` | `SearchUsageItemInteractor` | MstItemRepository.SearchUsageItem (`ten_mst`) |
| POST | `api/MstItem/UpdateAdoptedByomei` | `MstItemController` | `UpdateAdoptedByomei` | `UpdateAdoptedByomeiInteractor` | MstItemRepository.UpdateAdoptedByomei (`ten_mst`) |
| POST | `api/MstItem/UpdateAdoptedInputItem` | `MstItemController` | `UpdateAdoptedInputItem` | `UpdateAdoptedTenItemInteractor` | MstItemRepository.UpdateAdoptedItemAndItemConfig (`ten_mst`) |
| POST | `api/MstItem/UpdateAdoptedItemList` | `MstItemController` | `UpdateAdoptedItemList` | `UpdateAdoptedItemListInteractor` | MstItemRepository.UpdateAdoptedItems (`ten_mst`) |
| POST | `api/MstItem/UpdateByomeiMst` | `MstItemController` | `UpdateByomeiMst` | `UpdateByomeiMstInteractor` | MstItemRepository.UpdateByomeiMst (`ten_mst`) |
| POST | `api/MstItem/UpdateCmtCheckMst` | `MstItemController` | `UpdateCmtCheckMst` | `UpdateCmtCheckMstInteractor` | MstItemRepository.UpdateCmtCheckMst (`ten_mst`) |
| POST | `api/MstItem/UpdateJihiSbtMst` | `MstItemController` | `UpdateJihiSbtMst` | `UpdateJihiSbtMstInteractor` | MstItemRepository.UpdateJihiSbtMst (`ten_mst`) |
| POST | `api/MstItem/UpdateKensaMst` | `MstItemController` | `UpdateKensaMst` | `UpdateKensaMstInteractor` | MstItemRepository.UpdateKensaMst (`ten_mst`) |
| POST | `api/MstItem/UpdateKensaStdMst` | `MstItemController` | `UpdateKensaStdMst` | `UpdateKensaStdMstInteractor` | MstItemRepository.UpdateKensaStdMst (`ten_mst`) |
| POST | `api/MstItem/UpdateSingleDoseMst` | `MstItemController` | `UpdateSingleDoseMst` | `UpdateSingleDoseMstInteractor` | MstItemRepository.UpdateSingleDoseMst (`ten_mst`) |
| POST | `api/MstItem/UpdateYohoSetMst` | `MstItemController` | `UpdateYohoSetMst` | `UpdateYohoSetMstInteractor` | MstItemRepository.UpdateYohoSetMst (`ten_mst`) |
| POST | `api/MstItem/UploadImageDrugInf` | `MstItemController` | `UploadImageDrugInf` | `UploadImageDrugInfInteractor` |  |
| POST | `api/MstItem/UpsertMaterialMaster` | `MstItemController` | `UpsertMaterialMaster` | `UpsertMaterialMasterInteractor` | MstItemRepository.UpsertMaterialMaster (`ten_mst`) |
| GET | `api/NextOrder/CheckNextOrdHaveOdr` | `NextOrderController` | `CheckNextOrdHaveOdr` | `CheckNextOrdHaveInteractor` | NextOrderRepository.CheckNextOrdHaveOdr (`ord_inf`) |
| GET | `api/NextOrder/CheckUpsertNextOrder` | `NextOrderController` | `CheckUpsertNextOrder` | `CheckUpsertNextOrderInteractor` | NextOrderRepository.CheckUpsertNextOrder (`ord_inf`) |
| GET | `api/NextOrder/Get` | `NextOrderController` | `Get` | `GetNextOrderInteractor` | NextOrderRepository.GetByomeis (`ord_inf`)<br/>NextOrderRepository.GetOrderInfs (`ord_inf`)<br/>NextOrderRepository.GetKarteInf (`ord_inf`)<br/>NextOrderRepository.GetRsvkrtMstsData (`ord_inf`)<br/>NextOrderRepository.GetNextOrderFiles (`ord_inf`)<br/>InsuranceRepository.GetListHokenPattern (`hoken_inf`)<br/>PatientInforRepository.GetById (`pt_inf`) |
| GET | `api/NextOrder/GetList` | `NextOrderController` | `GetList` | `GetNextOrderListInteractor` | NextOrderRepository.GetList (`ord_inf`) |
| GET | `api/NextOrder/GetNextOrderNextCheck` | `NextOrderController` | `GetNextOrderNextCheck` | `GetNextOrderNextCheckInteractor` | NextOrderRepository.GetList (`ord_inf`) |
| POST | `api/NextOrder/Upsert` | `NextOrderController` | `Upsert` | `UpsertNextOrderListInteractor` | NextOrderRepository.GetCheckOrderInfs (`ord_inf`)<br/>NextOrderRepository.Upsert (`ord_inf`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>PatientInforRepository.CheckExistListId (`pt_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`)<br/>InsuranceRepository.GetCheckListHokenInf (`hoken_inf`)<br/>MstItemRepository.GetCheckItemCds (`ten_mst`)<br/>MstItemRepository.GetCheckIpnCds (`ten_mst`) |
| POST | `api/NextOrder/UpsertSortNoDataDrop` | `NextOrderController` | `UpsertSortNoDataDrop` | `UpsertSortNoDataDropInteractor` | NextOrderRepository.DropSortData (`ord_inf`)<br/>NextOrderRepository.GetList (`ord_inf`) |
| POST | `api/NextOrder/Validate` | `NextOrderController` | `Validate` | `ValidationNextOrderListInteractor` | NextOrderRepository.GetCheckOrderInfs (`ord_inf`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`)<br/>InsuranceRepository.GetCheckListHokenInf (`hoken_inf`)<br/>MstItemRepository.GetCheckItemCds (`ten_mst`)<br/>MstItemRepository.GetCheckIpnCds (`ten_mst`) |
| POST | `api/Online/CancelPatientHomeVisit` | `OnlineController` | `CancelPatientHomeVisit` | `CancelPatientHomeVisitInteractor` | OnlineRepository.CancelPatientHomeVisit (`online_inf`) |
| POST | `api/Online/ConvertXmlToOQSmuhvq01XmlMsg` | `OnlineController` | `ConvertXmlToOQSmuhvq01res` | `ConvertXmlToOQSXmlMsgInteractor` |  |
| POST | `api/Online/ConvertXmlToOQSmuhvq02XmlMsg` | `OnlineController` | `ConvertXmlToOQSmuhvq02res` | `ConvertXmlToOQSXmlMsgInteractor` |  |
| POST | `api/Online/ConvertXmlToOQSmuonq01XmlMsg` | `OnlineController` | `ConvertXmlToOQSmuonq01res` | `ConvertXmlToOQSXmlMsgInteractor` |  |
| POST | `api/Online/ConvertXmlToOQSmuonq02XmlMsg` | `OnlineController` | `ConvertXmlToOQSmuonq02res` | `ConvertXmlToOQSXmlMsgInteractor` |  |
| POST | `api/Online/ConvertXmlToOQSmuquc01XmlMsg` | `OnlineController` | `ConvertXmlToOQSmuquc01res` | `ConvertXmlToOQSXmlMsgInteractor` |  |
| POST | `api/Online/ConvertXmlToOQSmuquc02XmlMsg` | `OnlineController` | `ConvertXmlToOQSmuquc02res` | `ConvertXmlToOQSXmlMsgInteractor` |  |
| POST | `api/Online/ConvertXmlToOQSmutic01XmlMsg` | `OnlineController` | `ConvertXmlToOQSmutic01res` | `ConvertXmlToOQSXmlMsgInteractor` |  |
| POST | `api/Online/ConvertXmlToOQSmutic02XmlMsg` | `OnlineController` | `ConvertXmlToOQSmutic02res` | `ConvertXmlToOQSXmlMsgInteractor` |  |
| POST | `api/Online/ConvertXmlToOQSsihvd01XmlMsg` | `OnlineController` | `ConvertXmlToOQSsihvd01res` | `ConvertXmlToOQSXmlMsgInteractor` |  |
| POST | `api/Online/ConvertXmlToQCXmlMsg` | `OnlineController` | `ConvertXmlToQCXmlMsgResponse` | `` |  |
| DELETE | `api/Online/DeletedOnlineConfirmation` | `OnlineController` | `DeletedOnlineConfirmation` | `DeletedConfirmOnlineHisInteractor` |  |
| GET | `api/Online/GetBatchOnlineCheck` | `OnlineController` | `GetBatchOnlineCheck` | `GetBatchOnlineCheckInteractor` | OnlineRepository.GetBatchOnlineChecks (`online_inf`) |
| GET | `api/Online/GetListOnlineConfirmationHistoryByPtId` | `OnlineController` | `GetListOnlineConfirmationHistoryByPtId` | `GetListOnlineConfirmationHistoryModelInteractor` | PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>OnlineRepository.GetListOnlineConfirmationHistoryModel (`online_inf`)<br/>OnlineRepository.GetListOnlineConfirmationHistoryModelById (`online_inf`) |
| POST | `api/Online/GetListOnlineConfirmationHistoryModel` | `OnlineController` | `GetListOnlineConfirmationHistoryModel` | `GetListOnlineConfirmationHistoryModelInteractor` | PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>OnlineRepository.GetListOnlineConfirmationHistoryModel (`online_inf`)<br/>OnlineRepository.GetListOnlineConfirmationHistoryModelById (`online_inf`) |
| GET | `api/Online/GetListOnlineConfirmationHistoryModel + "ById` | `OnlineController` | `GetListOnlineConfirmationHistoryModelById` | `GetListOnlineConfirmationHistoryModelInteractor` | PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>OnlineRepository.GetListOnlineConfirmationHistoryModel (`online_inf`)<br/>OnlineRepository.GetListOnlineConfirmationHistoryModelById (`online_inf`) |
| GET | `api/Online/GetOnlineConsent` | `OnlineController` | `GetOnlineConsent` | `GetOnlineConsentInteractor` | OnlineRepository.GetOnlineConsentModel (`online_inf`) |
| GET | `api/Online/GetPatientHomeVisit` | `OnlineController` | `GetPatientHomeVisit` | `GetPatientHomeVisitInteractor` | OnlineRepository.GetPatientHomeVisit (`online_inf`) |
| GET | `api/Online/GetRegisterdPatientsFromOnline` | `OnlineController` | `GetRegisterdPatientsFromOnline` | `GetRegisterdPatientsFromOnlineInteractor` | OnlineRepository.GetRegisterdPatientsFromOnline (`online_inf`) |
| GET | `api/Online/GetViewResult` | `OnlineController` | `GetViewResult` | `GetViewResultInteractor` |  |
| POST | `api/Online/InsertOnlineConfirmHistory` | `OnlineController` | `InsertOnlineConfirmHistory` | `InsertOnlineConfirmHistoryInteractor` | OnlineRepository.InsertOnlineConfirmHistory (`online_inf`) |
| POST | `api/Online/InsertOnlineConfirmation` | `OnlineController` | `InsertOnlineConfirmation` | `InsertOnlineConfirmationInteractor` | OnlineRepository.SaveOnlineConfirmation (`online_inf`) |
| POST | `api/Online/InsertOnlineConfirmation + "ByXml` | `OnlineController` | `InsertOnlineConfirmationByXml` | `InsertOnlineConfirmationInteractor` | OnlineRepository.SaveOnlineConfirmation (`online_inf`) |
| GET | `api/Online/OnlineConfirmationDetailById` | `OnlineController` | `OnlineConfirmationDetailById` | `GetOnlineConfirmationDetailByIdInteractor` |  |
| POST | `api/Online/OnlineViewResultUpdateConfirm` | `OnlineController` | `OnlineViewResultUpdateConfirm` | `` |  |
| POST | `api/Online/ProcessXMLOQSmuhvq01res` | `OnlineController` | `ProcessXMLOQSmuhvq01res` | `ProcessXMLInteractor` | OnlineRepository.ProcessOQSXML (`online_inf`)<br/>SystemConfRepository.UpdateSystemConf (`system_conf`) |
| POST | `api/Online/ProcessXMLOQSmuonq01res` | `OnlineController` | `ProcessXMLOQSmuonq01res` | `ProcessXMLInteractor` | OnlineRepository.ProcessOQSXML (`online_inf`)<br/>SystemConfRepository.UpdateSystemConf (`system_conf`) |
| POST | `api/Online/ProcessXMLOQSmuquc01res` | `OnlineController` | `ProcessXMLOQSmuquc01res` | `ProcessXMLInteractor` | OnlineRepository.ProcessOQSXML (`online_inf`)<br/>SystemConfRepository.UpdateSystemConf (`system_conf`) |
| POST | `api/Online/ProcessXMLOQSmutic01res` | `OnlineController` | `ProcessXMLOQSmutic01res` | `ProcessXMLInteractor` | OnlineRepository.ProcessOQSXML (`online_inf`)<br/>SystemConfRepository.UpdateSystemConf (`system_conf`) |
| POST | `api/Online/ProcessXmlOQSmuhvq02res` | `OnlineController` | `ProcessXmlOQSmuhvq02res` | `ProcessXmlOQSmuhvq02resInteractor` | OnlineRepository.UpdateOnlineConfirmation (`online_inf`)<br/>OnlineRepository.SaveOnlineConfirmationComposite (`online_inf`)<br/>PatientInforRepository.GetPtInfByRefNo (`pt_inf`)<br/>PatientInforRepository.BulkUpdateHoumonAgreed (`pt_inf`)<br/>InsuranceRepository.HasMaruchoDifference (`hoken_inf`)<br/>InsuranceRepository.GetKogakuValue (`hoken_inf`)<br/>InsuranceRepository.GetHokenInf (`hoken_inf`)<br/>InsuranceRepository.HasKohiInfoDifference (`hoken_inf`) |
| POST | `api/Online/ProcessXmlOQSmuonq02res` | `OnlineController` | `ProcessXmlOQSmuonq02res` | `ProcessXmlOQSmuonq02resInteractor` | OnlineRepository.UpdateOnlineConfirmation (`online_inf`)<br/>OnlineRepository.SaveOnlineConfirmationComposite (`online_inf`)<br/>PatientInforRepository.GetPtInfByRefNo (`pt_inf`)<br/>InsuranceRepository.HasMaruchoDifference (`hoken_inf`)<br/>InsuranceRepository.GetKogakuValue (`hoken_inf`)<br/>InsuranceRepository.GetHokenInf (`hoken_inf`)<br/>InsuranceRepository.HasKohiInfoDifference (`hoken_inf`) |
| POST | `api/Online/ProcessXmlOQSmuquc02res` | `OnlineController` | `ProcessXmlOQSmuquc02res` | `ProcessXmlOQSmuquc02resInteractor` | OnlineRepository.UpdateOnlineConfirmation (`online_inf`)<br/>OnlineRepository.SaveOnlineConfirmationComposite (`online_inf`)<br/>PatientInforRepository.GetPtInfByRefNo (`pt_inf`)<br/>InsuranceRepository.GetHokenInf (`hoken_inf`)<br/>InsuranceRepository.HasKohiInfoDifference (`hoken_inf`) |
| POST | `api/Online/ProcessXmlOQSmutic02res` | `OnlineController` | `ProcessXmlOQSmutic02res` | `ProcessXmlOQSmutic02resInteractor` | OnlineRepository.UpdateOnlineConfirmation (`online_inf`)<br/>OnlineRepository.SaveOnlineConfirmationComposite (`online_inf`)<br/>PatientInforRepository.GetPtInfByRefNo (`pt_inf`)<br/>InsuranceRepository.HasKohiInfoDifference (`hoken_inf`) |
| POST | `api/Online/SaveAllOQConfirmation` | `OnlineController` | `SaveAllOQConfirmation` | `SaveAllOQConfirmationInteractor` | OnlineRepository.SaveAllOQConfirmation (`online_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`) |
| POST | `api/Online/SaveOQConfirmation` | `OnlineController` | `SaveOQConfirmation` | `SaveOQConfirmationInteractor` | OnlineRepository.SaveOQConfirmation (`online_inf`)<br/>OnlineRepository.CheckExistIdList (`online_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>ReceptionRepository.GetList (`raiin_inf`) |
| POST | `api/Online/SaveOnlineConfirmHistory` | `OnlineController` | `SaveOnlineConfirmHistory` | `SaveOnlineConfirmationHisInteractor` |  |
| POST | `api/Online/UpdateOQConfirmation` | `OnlineController` | `UpdateOQConfirmation` | `UpdateOQConfirmationInteractor` | OnlineRepository.CheckExistIdList (`online_inf`)<br/>OnlineRepository.UpdateOQConfirmation (`online_inf`) |
| POST | `api/Online/UpdateOnlineConfirmation` | `OnlineController` | `UpdateOnlineConfirmation` | `UpdateOnlineConfirmationInteractor` | OnlineRepository.SaveOnlineConfirmation (`online_inf`)<br/>OnlineRepository.UpdateRaiinInfByResResult (`online_inf`)<br/>OnlineRepository.InsertListOnlConfirmHistory (`online_inf`) |
| POST | `api/Online/UpdateOnlineConfirmationHistory` | `OnlineController` | `UpdateOnlineConfirmationHistory` | `UpdateOnlineConfirmationInteractor` | OnlineRepository.SaveOnlineConfirmation (`online_inf`)<br/>OnlineRepository.UpdateRaiinInfByResResult (`online_inf`)<br/>OnlineRepository.InsertListOnlConfirmHistory (`online_inf`) |
| POST | `api/Online/UpdateOnlineConsents` | `OnlineController` | `UpdateOnlineConsents` | `UpdateOnlineConsentsInteractor` | OnlineRepository.UpdateOnlineConsents (`online_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`) |
| POST | `api/Online/UpdateOnlineHistoryById` | `OnlineController` | `UpdateOnlineHistoryById` | `UpdateOnlineHistoryByIdInteractor` | OnlineRepository.UpdateOnlineHistoryById (`online_inf`)<br/>OnlineRepository.CheckExistIdList (`online_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`) |
| POST | `api/Online/UpdateOnlineInRaiinInf` | `OnlineController` | `UpdateOnlineInRaiinInf` | `UpdateOnlineInRaiinInfInteractor` | OnlineRepository.UpdateOnlineInRaiinInf (`online_inf`)<br/>ReceptionRepository.GetList (`raiin_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`) |
| POST | `api/Online/UpdatePtInfOnlineQualify` | `OnlineController` | `UpdatePtInfOnlineQualify` | `UpdatePtInfOnlineQualifyInteractor` | PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>OnlineRepository.UpdatePtInfOnlineQualify (`online_inf`)<br/>SystemConfRepository.GetList (`system_conf`) |
| POST | `api/Online/UpdateRefNo` | `OnlineController` | `UpdateRefNo` | `UpdateRefNoInteractor` | PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>OnlineRepository.UpdateRefNo (`online_inf`) |
| GET | `api/OrdInf/CheckOrdInfInDrug` | `OrdInfController` | `CheckOrdInfInDrug` | `CheckOrdInfInDrugInteractor` | OrdInfRepository.CheckOrdInfInDrug (`ord_inf`) |
| POST | `api/OrdInf/ConvertInputItemToTodayOrder` | `OrdInfController` | `ConvertInputItemToTodayOrder` | `ConvertInputItemToTodayOrderInteractor` | TodayOdrRepository.ConvertInputItemToTodayOdr (`today_odr`) |
| GET | `api/OrdInf/GetHeaderInf` | `OrdInfController` | `GetHeaderInf` | `GetHeaderInfInteractor` | OrdInfRepository.GetHeaderInfo (`ord_inf`)<br/>ReceptionRepository.GetHeader (`raiin_inf`)<br/>RaiinListTagRepository.Get (`raiinlisttag_inf`) |
| GET | `api/OrdInf/GetList` | `OrdInfController` | `GetList` | `GetOrdInfListTreeInteractor` | OrdInfRepository.GetKarteEdition (`ord_inf`)<br/>InsuranceRepository.GetListHokenPattern (`hoken_inf`)<br/>MstItemRepository.GetListRecedenByItemCds (`ten_mst`) |
| POST | `api/OrdInf/GetMaxRpNo` | `OrdInfController` | `GetMaxRpNo` | `GetMaxRpNoInteractor` | OrdInfRepository.GetMaxRpNo (`ord_inf`) |
| POST | `api/OrdInf/ValidateInputItem` | `OrdInfController` | `ValidateInputItem` | `ValidationInputItemInteractor` | OrdInfRepository.GetCheckIpnMinYakkaMsts (`ord_inf`)<br/>OrdInfRepository.CheckIsGetYakkaPrices (`ord_inf`)<br/>MstItemRepository.GetRecedens (`ten_mst`)<br/>MstItemRepository.GetCheckTenItemModels (`ten_mst`)<br/>SystemGenerationConfRepository.GetSettingValue (`system_generation_conf`) |
| POST | `api/PatientInfor/CheckAllowDeleteGroupMst` | `PatientInforController` | `CheckAllowDeleteGroupMst` | `CheckAllowDeleteGroupMstInteractor` | GroupNameMstRepository.IsInUseGroupName (`groupnamemst`)<br/>GroupNameMstRepository.IsInUseGroupItem (`groupnamemst`) |
| POST | `api/PatientInfor/CheckAllowDeletePatientInfo` | `PatientInforController` | `CheckAllowDeletePatientInfo` | `CheckAllowDeletePatientInfoInteractor` | PatientInforRepository.IsAllowDeletePatient (`pt_inf`) |
| GET | `api/PatientInfor/CheckDeleteHoken` | `PatientInforController` | `CheckDeleteHoken` | `CheckDeleteHokenInteractor` | InsuranceRepository.CheckDeleteHoken (`hoken_inf`) |
| POST | `api/PatientInfor/CheckDrawerLedgerDataExisted` | `PatientInforController` | `CheckDrawerLedgerDataExisted` | `CheckDrawerLedgerDataExistedInteractor` | PatientInforRepository.CheckDrawerLedgerDataExisted (`pt_inf`) |
| POST | `api/PatientInfor/CheckHokenPatternUsed` | `PatientInforController` | `CheckHokenPatternUsed` | `HokenPatternUsedInteractor` | InsuranceRepository.CheckHokenPatternUsed (`hoken_inf`) |
| POST | `api/PatientInfor/CheckKohiInfoDifference` | `PatientInforController` | `CheckKohiInfoDifference` | `CheckKohiInfoDifferenceInteractor` | PatientInforRepository.CheckKohiInfoDifference (`pt_inf`) |
| POST | `api/PatientInfor/CheckPatientHokenInfoDifference` | `PatientInforController` | `CheckPatientHokenInfoDifference` | `CheckPatientHokenInfoDifferenceInteractor` | PatientInforRepository.CheckPatientHokenInfoDifference (`pt_inf`) |
| GET | `api/PatientInfor/CheckPatientInfoDifference` | `PatientInforController` | `CheckPatientInfoDifference` | `CheckPatientInfoDifferenceInteractor` | PatientInforRepository.CheckPatientInfoDifference (`pt_inf`) |
| POST | `api/PatientInfor/CheckPmhKohiInfoDifference` | `PatientInforController` | `CheckPmhKohiInfoDifference` | `CheckPmhKohiInfoDifferenceInteractor` | PatientInforRepository.GetListPmhKohiInf (`pt_inf`)<br/>PatientInforRepository.GetPrefNo (`pt_inf`) |
| POST | `api/PatientInfor/CheckValidSamePatient` | `PatientInforController` | `CheckValidSamePatient` | `CheckValidSamePatientInteractor` | PatientInforRepository.FindSamePatient (`pt_inf`) |
| POST | `api/PatientInfor/DeleteInsurance` | `PatientInforController` | `DeleteInsurance` | `DeleteInsuranceInteractor` | InsuranceRepository.DeleteKohi (`hoken_inf`)<br/>InsuranceRepository.DeleteHokenInf (`hoken_inf`) |
| POST | `api/PatientInfor/DeletePatientInfo` | `PatientInforController` | `DeletePatientInfo` | `DeletePatientInfoInteractor` | PatientInforRepository.IsAllowDeletePatient (`pt_inf`)<br/>PatientInforRepository.DeletePatientInfo (`pt_inf`)<br/>ReceptionRepository.GetList (`raiin_inf`)<br/>ReceptionRepository.GetListSameVisit (`raiin_inf`) |
| POST | `api/PatientInfor/EditBasicInfoOverwrite` | `PatientInforController` | `EditBasicInfoOverwrite` | `EditBasicInfoOverwriteInteractor` | PatientInforRepository.EditBasicInfoOverwrite (`pt_inf`)<br/>PatientInforRepository.GetById (`pt_inf`) |
| GET | `api/PatientInfor/GetBasicPatientInfo` | `PatientInforController` | `GetBasicPatientInfo` | `GetBasicPatientInfoInteractor` | PatientInforRepository.GetBasicPatientInfo (`pt_inf`) |
| GET | `api/PatientInfor/GetDetailHokenMst` | `PatientInforController` | `GetDetailHokenMst` | `GetDetailHokenMstInteractor` | HokenMstRepository.GetHokenMaster (`hoken_mst`) |
| POST | `api/PatientInfor/GetGroupNameMst` | `PatientInforController` | `GetGroupNameMst` | `GetGroupNameMstInteractor` | GroupNameMstRepository.GetListGroupNameMst (`groupnamemst`) |
| GET | `api/PatientInfor/GetHokenInfByPtId` | `PatientInforController` | `GetHokenInfByPtId` | `GetHokenInfByPtIdInteractor` | InsuranceRepository.GetHokenInfByPtId (`hoken_inf`) |
| GET | `api/PatientInfor/GetHokenMstByFutansyaNo` | `PatientInforController` | `GetHokenMstByFutansyaNo` | `GetHokenMstInteractor` | HokenMstRepository.FindHokenMst (`hoken_mst`) |
| GET | `api/PatientInfor/GetHokenPatternByPtId` | `PatientInforController` | `GetHokenPatternByPtId` | `GetHokenPatternByPtIdInteractor` | InsuranceRepository.GetHokenPatternByPtId (`hoken_inf`) |
| GET | `api/PatientInfor/GetHokenSyaMst` | `PatientInforController` | `GetHokenSyaMst` | `GetHokenSyaMstInteractor` | InsuranceMstRepository.FindHokenSyaMstInf (`hoken_sya_mst`) |
| GET | `api/PatientInfor/GetInsuranceCardImage` | `PatientInforController` | `GetInsuranceCardImage` | `GetInsuranceCardImageInteractor` | PatientInforRepository.GetInsuranceImageCard (`pt_inf`) |
| GET | `api/PatientInfor/InsuranceListByPtId` | `PatientInforController` | `GetInsuranceListByPtId` | `GetInsuranceListInteractor` | InsuranceRepository.GetInsuranceListById (`hoken_inf`) |
| GET | `api/PatientInfor/GetInsuranceMasterLinkage` | `PatientInforController` | `GetInsuranceMasterLinkage` | `GetInsuranceMasterLinkageInteractor` | PatientInforRepository.GetDefHokenNoModels (`pt_inf`) |
| GET | `api/PatientInfor/GetKohiDetail` | `PatientInforController` | `GetKohiDetail` | `GetKohiDetailInteractor` | InsuranceRepository.GetKohiDetail (`hoken_inf`) |
| GET | `api/PatientInfor/GetKohiInfByPtId` | `PatientInforController` | `GetKohiInfByPtId` | `GetKohiInfByPtIdInteractor` | InsuranceRepository.GetKohiInfByPtId (`hoken_inf`) |
| GET | `api/PatientInfor/GetKohiPriorityList` | `PatientInforController` | `GetKohiPriorityList` | `GetKohiPriorityListInteractor` | InsuranceRepository.GetKohiPriorityList (`hoken_inf`) |
| GET | `api/PatientInfor/GetList` | `PatientInforController` | `GetList` | `GetListPatientInfoInteractor` | PatientInforRepository.SearchPatient (`pt_inf`) |
| GET | `api/PatientInfor/GetListCalculationPatient` | `PatientInforController` | `GetListCalculationPatient` | `CalculationInfInteractor` | CalculationInfRepository.GetListDataCalculationInf (`calculationinf`) |
| GET | `api/PatientInfor/GetListGroupInfo` | `PatientInforController` | `GetListGroupInfo` | `GroupInfInteractor` | GroupInfRepository.GetDataGroup (`group_inf`) |
| GET | `api/PatientInfor/Get + "PatientComment` | `PatientInforController` | `GetListPatientComment` | `GetPatientCommentInteractor` | PatientInforRepository.PatientCommentModels (`pt_inf`) |
| GET | `api/PatientInfor/GetListPatientGroup` | `PatientInforController` | `GetListPatientGroup` | `GetListGroupInfInteractor` |  |
| GET | `api/PatientInfor/GetMaxMoneyByPtId` | `PatientInforController` | `GetMaxMoneyByPtId` | `GetMaxMoneyByPtIdInteractor` |  |
| GET | `api/PatientInfor/GetPatientById` | `PatientInforController` | `GetPatientById` | `GetPatientInforByIdInteractor` | PatientInforRepository.GetById (`pt_inf`) |
| GET | `api/PatientInfor/GetPatientGroupMst` | `PatientInforController` | `GetPatientGroupMst` | `GetListPatientGroupMstInteractor` | PatientGroupMstRepository.GetAll (`patient_group_mst`) |
| GET | `api/PatientInfor/GetPatientInfoBetweenTimesList` | `PatientInforController` | `GetPatientInfoBetweenTimesList` | `GetPatientInfoBetweenTimesListInteractor` | PatientInforRepository.SearchPatient (`pt_inf`) |
| POST | `api/PatientInfor/GetPmhKohiDefault` | `PatientInforController` | `GetPmhKohiDefault` | `GetPmhKohiDefaultInteractor` | PatientInforRepository.GetPrefNo (`pt_inf`)<br/>PatientInforRepository.GetHokenMstForPmhKohi (`pt_inf`) |
| GET | `api/PatientInfor/GetPtInfByRefNo` | `PatientInforController` | `GetPtInfByRefNo` | `GetPtInfByRefNoInteractor` | PatientInforRepository.GetPtInfByRefNo (`pt_inf`) |
| GET | `api/PatientInfor/GetPtInfModelsByName` | `PatientInforController` | `GetPtInfModelsByName` | `GetPtInfModelsByNameInteractor` | PatientInforRepository.GetPtInfModelsByName (`pt_inf`) |
| GET | `api/PatientInfor/GetPtInfModelsByRefNo` | `PatientInforController` | `GetPtInfModelsByRefNo` | `GetPtInfModelsByRefNoInteractor` | PatientInforRepository.GetPtInfModels (`pt_inf`) |
| GET | `api/PatientInfor/GetPtKyuseiInf` | `PatientInforController` | `GetPtKyuseiInf` | `GetPtKyuseiInfInteractor` | PatientInforRepository.PtKyuseiInfModels (`pt_inf`) |
| GET | `api/PatientInfor/GetTokkiMstList` | `PatientInforController` | `GetTokkiMstList` | `GetTokkiMstListInteractor` | PatientInforRepository.GetListTokki (`pt_inf`) |
| GET | `api/PatientInfor/GetVisitTimesManagementModels` | `PatientInforController` | `GetVisitTimesManagementModels` | `GetVisitTimesManagementModelsInteractor` | PatientInforRepository.GetVisitTimesManagementModels (`pt_inf`) |
| GET | `api/PatientInfor/GetInsurancesDataByPtId` | `PatientInforController` | `InsuranceListByPtId` | `GetInsuranceInteractor` |  |
| POST | `api/PatientInfor/SaveHokenCheck` | `PatientInforController` | `SaveConfirmDate` | `SaveHokenCheckInteractor` | PatientInforRepository.SaveHokenCheck (`pt_inf`)<br/>InsuranceRepository.DeleteKeyInsuranceList (`hoken_inf`) |
| POST | `api/PatientInfor/SaveGroupNameMst` | `PatientInforController` | `SaveGroupNameMst` | `SaveGroupNameMstInteractor` | GroupNameMstRepository.SaveGroupNameMst (`groupnamemst`) |
| POST | `api/PatientInfor/SaveHokenSyaMst` | `PatientInforController` | `SaveHokenSyaMst` | `SaveHokenSyaMstInteractor` | InsuranceMstRepository.SaveHokenSyaMst (`hoken_sya_mst`) |
| POST | `api/PatientInfor/SaveInsuranceInfo` | `PatientInforController` | `SaveInsuranceInfor` | `SaveInsuranceInfoInteractor` | PatientInforRepository.SaveInsuranceInfo (`pt_inf`)<br/>PatientInforRepository.GetById (`pt_inf`)<br/>InsuranceRepository.DeleteKeyInsuranceList (`hoken_inf`) |
| POST | `api/PatientInfor/SaveInsuranceMasterLinkage` | `PatientInforController` | `SaveInsuranceMasterLinkage` | `SaveInsuranceMasterLinkageInteractor` | PatientInforRepository.SaveInsuranceMasterLinkage (`pt_inf`)<br/>HokenMstRepository.CheckExistHokenEdaNo (`hoken_mst`) |
| POST | `api/PatientInfor/SaveKohi` | `PatientInforController` | `SaveKohi` | `SaveKohiInteractor` | InsuranceRepository.SaveKohi (`hoken_inf`)<br/>InsuranceRepository.DeleteKeyInsuranceList (`hoken_inf`) |
| POST | `api/PatientInfor/SaveOnlineMyCardBeforeReception` | `PatientInforController` | `SaveOnlineMyCardBeforeReception` | `SaveOnlineMyCardBeforeReceptionInteractor` | PatientInforRepository.SaveOnlineMyCardBeforeReception (`pt_inf`) |
| POST | `api/PatientInfor/SavePatient` | `PatientInforController` | `SavePatient` | `SaveBasicPatientInforInteractor` | PatientInforRepository.SavePatientInfo (`pt_inf`)<br/>PatientInforRepository.GetById (`pt_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`)<br/>ReceptionRepository.GetRecptionList (`raiin_inf`) |
| POST | `api/PatientInfor/SavePatientGroupMst` | `PatientInforController` | `SavePatientGroupMst` | `SaveListPatientGroupMstInteractor` | PatientGroupMstRepository.SaveListPatientGroup (`patient_group_mst`) |
| POST | `api/PatientInfor/SavePatientInfo` | `PatientInforController` | `SavePatientInfo` | `SavePatientInfoInteractor` | PatientInforRepository.CreatePatientInfo (`pt_inf`)<br/>PatientInforRepository.UpdatePatientInfo (`pt_inf`)<br/>PatientInforRepository.GetById (`pt_inf`)<br/>PatientInforRepository.FindSamePatient (`pt_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`)<br/>SystemConfRepository.GetSettingParams (`system_conf`)<br/>PtDiseaseRepository.GetPtByomeisByHokenId (`ptdisease_inf`) |
| POST | `api/PatientInfor/SavePtKyusei` | `PatientInforController` | `SavePtKyuseiPatient` | `SavePtKyuseiInteractor` | PatientInforRepository.SavePtKyusei (`pt_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>PatientInforRepository.PtKyuseiInfModels (`pt_inf`) |
| POST | `api/PatientInfor/SearchAdvanced` | `PatientInforController` | `SearchAdvanced` | `SearchPatientInfoAdvancedInteractor` | PatientInforRepository.GetAdvancedSearchResults (`pt_inf`)<br/>GroupInfRepository.GetAllByPtIdList (`group_inf`) |
| GET | `api/PatientInfor/SearchEmptyId` | `PatientInforController` | `SearchEmptyId` | `SearchEmptyIdInteractor` | PatientInforRepository.SearchEmptyId (`pt_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`) |
| GET | `api/PatientInfor/SearchHokensyaMst` | `PatientInforController` | `SearchHokensyaMst` | `SearchHokensyaMstInteractor` | InsuranceMstRepository.SearchListDataHokensyaMst (`hoken_sya_mst`) |
| POST | `api/PatientInfor/SearchPatientInfoByPtIdList` | `PatientInforController` | `SearchPatientInfoByPtIdList` | `SearchPatientInfoByPtIdListInteractor` | PatientInforRepository.SearchPatient (`pt_inf`) |
| GET | `api/PatientInfor/SearchPatientInfoByPtNum` | `PatientInforController` | `SearchPatientInfoByPtNum` | `SearchPatientInfoByPtNumInteractor` | PatientInforRepository.SearchExactlyPtNum (`pt_inf`) |
| GET | `api/PatientInfor/SearchSimple` | `PatientInforController` | `SearchSimple` | `SearchPatientInfoSimpleInteractor` | PatientInforRepository.SearchExactlyPtNum (`pt_inf`)<br/>PatientInforRepository.SearchContainPtNum (`pt_inf`)<br/>PatientInforRepository.SearchBySindate (`pt_inf`)<br/>PatientInforRepository.SearchPhone (`pt_inf`)<br/>PatientInforRepository.SearchName (`pt_inf`)<br/>GroupInfRepository.GetAllByPtIdList (`group_inf`) |
| POST | `api/PatientInfor/SwapHoken` | `PatientInforController` | `SwapHokenParttern` | `SaveSwapHokenInteractor` | SwapHokenRepository.CountOdrInf (`swaphoken_inf`)<br/>SwapHokenRepository.GetListSeikyuYms (`swaphoken_inf`)<br/>SwapHokenRepository.GetSeikyuYmsInPendingSeikyu (`swaphoken_inf`)<br/>SwapHokenRepository.SwapHokenParttern (`swaphoken_inf`)<br/>SwapHokenRepository.ExistRaiinInfUsedOldHokenId (`swaphoken_inf`)<br/>SwapHokenRepository.UpdateReceSeikyu (`swaphoken_inf`) |
| POST | `api/PatientInfor/SwapHokenPattern` | `PatientInforController` | `SwapHokenParttern` | `SaveSwapHokenInteractor` | SwapHokenRepository.CountOdrInf (`swaphoken_inf`)<br/>SwapHokenRepository.GetListSeikyuYms (`swaphoken_inf`)<br/>SwapHokenRepository.GetSeikyuYmsInPendingSeikyu (`swaphoken_inf`)<br/>SwapHokenRepository.SwapHokenParttern (`swaphoken_inf`)<br/>SwapHokenRepository.ExistRaiinInfUsedOldHokenId (`swaphoken_inf`)<br/>SwapHokenRepository.UpdateReceSeikyu (`swaphoken_inf`) |
| POST | `api/PatientInfor/UpdateVisitTimesManagement` | `PatientInforController` | `UpdateVisitTimesManagement` | `UpdateVisitTimesManagementInteractor` | PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>PatientInforRepository.GetVisitTimesManagementModels (`pt_inf`)<br/>PatientInforRepository.UpdateVisitTimesManagement (`pt_inf`) |
| POST | `api/PatientInfor/UpdateVisitTimesManagementNeedSave` | `PatientInforController` | `UpdateVisitTimesManagementNeedSave` | `UpdateVisitTimesManagementInteractor` | PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>PatientInforRepository.GetVisitTimesManagementModels (`pt_inf`)<br/>PatientInforRepository.UpdateVisitTimesManagement (`pt_inf`) |
| POST | `api/PatientInfor/ValidHokenInfAllType` | `PatientInforController` | `ValidHokenInfAllType` | `ValidHokenInfAllTypeInteractor` | SystemConfRepository.GetSettingValue (`system_conf`)<br/>SystemConfRepository.GetSettingParams (`system_conf`)<br/>PatientInforRepository.GetHokenMstByInfor (`pt_inf`)<br/>PatientInforRepository.GetHokenSyaMstByInfor (`pt_inf`)<br/>PatientInforRepository.GetListTokki (`pt_inf`) |
| POST | `api/PatientInfor/ValidateHoken` | `PatientInforController` | `ValidateHoken` | `ValidHokenInfInteractor` |  |
| GET | `api/PatientInfor/ValidateHokenPattern` | `PatientInforController` | `ValidateHokenPattern` | `ValidHokenPatternInteractor` | PatientInforRepository.GetHokenInfByInfo (`pt_inf`)<br/>PatientInforRepository.GetCheckDateByInfo (`pt_inf`)<br/>PatientInforRepository.GetHokenMst (`pt_inf`)<br/>PatientInforRepository.GetKohiInfByInf (`pt_inf`)<br/>PatientInforRepository.GetExpiredHokenMst (`pt_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`)<br/>SystemConfRepository.GetSettingParams (`system_conf`)<br/>InsuranceRepository.HasElderHoken (`hoken_inf`)<br/>InsuranceRepository.IsValidPeriod (`hoken_inf`) |
| POST | `api/PatientInfor/ValidateListPattern` | `PatientInforController` | `ValidateListPattern` | `ValidateInsuranceInteractor` | PatientInforRepository.GetListTokki (`pt_inf`)<br/>PatientInforRepository.GetHokenMstByInfor (`pt_inf`)<br/>PatientInforRepository.GetHokenSyaMstByInfor (`pt_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`)<br/>SystemConfRepository.GetSettingParams (`system_conf`) |
| POST | `api/PatientInfor/ValidateMainInsurance` | `PatientInforController` | `ValidateMainInsurance` | `ValidMainInsuranceInteractor` |  |
| POST | `api/PatientInfor/ValidateKohi` | `PatientInforController` | `ValidateOneKohi` | `ValidKohiInteractor` |  |
| POST | `api/PatientInfor/ValidateRousaiJibai` | `PatientInforController` | `ValidateRousaiJibai` | `ValidateRousaiJibaiInteractor` | SystemConfRepository.GetSettingValue (`system_conf`) |
| POST | `api/PatientInfor/ValidateSwapHoken` | `PatientInforController` | `ValidateSwapHoken` | `ValidateSwapHokenInteractor` | SwapHokenRepository.CountOdrInf (`swaphoken_inf`) |
| GET | `api/PatientManagement/GetHokenMst` | `PatientManagementController` | `GetHokenMst` | `GetHokenMstInteractor` | HokenMstRepository.FindHokenMst (`hoken_mst`) |
| GET | `api/PatientManagement/GetStaConfMenu` | `PatientManagementController` | `GetStaConfMenu` | `GetStaConfMenuInteractor` | StatisticRepository.GetStatisticMenuModels (`statistic_inf`)<br/>MstItemRepository.GetNameByItemCd (`ten_mst`)<br/>PtDiseaseRepository.GetByomeiMst (`ptdisease_inf`) |
| POST | `api/PatientManagement/SaveStaConfMenu` | `PatientManagementController` | `SaveStaConfMenu` | `SaveStaConfMenuInteractor` | StatisticRepository.SaveStaConfMenu (`statistic_inf`) |
| POST | `api/PatientManagement/SearchPtInfs` | `PatientManagementController` | `SearchPtInfs` | `SearchPtInfsInteractor` |  |
| GET | `api/PdfCreator/ReceiptReportBase64` | `PdfCreatorController` | `GenerateReceiptReportAIChart` | `` |  |
| POST | `api/PdfCreator/ExportSijisen` | `PdfCreatorController` | `GenerateSijisenReport` | `` |  |
| POST | `api/PdfCreator/GrowthCurve` | `PdfCreatorController` | `GetGrowthCurvePrintData` | `` |  |
| GET | `api/PdfCreator/InDrugBase64` | `PdfCreatorController` | `GetInDrugBase64` | `` |  |
| GET | `api/PdfCreator/OutDrugBase64` | `PdfCreatorController` | `GetOutDrugBase64` | `` |  |
| POST | `api/PdfCreator/KensaHistoryReport` | `PdfCreatorController` | `KensaHistoryReport` | `` |  |
| GET | `api/PrescriptionHistory/GetContentDrugUsageHistory` | `PrescriptionHistoryController` | `GetContentDrugUsageHistory` | `GetContentDrugUsageHistoryInteractor` | DrugInforRepository.GetDrugUsageHistoryList (`druginfor_inf`)<br/>DrugInforRepository.GetKouiKbnMstList (`druginfor_inf`)<br/>DrugInforRepository.GetSinrekiFilterMst (`druginfor_inf`)<br/>UserConfRepository.GetListUserConf (`userconf_inf`) |
| GET | `api/PrescriptionHistory/GetSinrekiFilterMstList` | `PrescriptionHistoryController` | `GetList` | `GetSinrekiFilterMstListInteractor` | DrugInforRepository.GetSinrekiFilterMstList (`druginfor_inf`) |
| POST | `api/PrescriptionHistory/SaveSinrekiFilterMstList` | `PrescriptionHistoryController` | `SaveSinrekiFilterMstList` | `SaveSinrekiFilterMstListInteractor` | DrugInforRepository.SaveSinrekiFilterMstList (`druginfor_inf`)<br/>DrugInforRepository.GetSinrekiFilterMstList (`druginfor_inf`)<br/>DrugInforRepository.CheckExistGrpCd (`druginfor_inf`)<br/>DrugInforRepository.CheckExistKouiKbn (`druginfor_inf`)<br/>DrugInforRepository.CheckExistSinrekiFilterMstKoui (`druginfor_inf`)<br/>DrugInforRepository.CheckExistSinrekiFilterMstDetail (`druginfor_inf`)<br/>MstItemRepository.GetCheckItemCds (`ten_mst`) |
| GET | `api/RaiinFilter/CheckDuplicateFilterName` | `RaiinFilterController` | `CheckDuplicateFilterName` | `CheckDuplicateFilterNameInteractor` | RaiinFilterMstRepository.CheckDuplicateFilterName (`raiinfiltermst`) |
| GET | `api/RaiinFilter/GetList + "Mst` | `RaiinFilterController` | `GetList` | `GetRaiinFilterMstListInteractor` | RaiinFilterMstRepository.GetList (`raiinfiltermst`) |
| POST | `api/RaiinFilter/SaveList + "Mst` | `RaiinFilterController` | `SaveList` | `SaveRaiinFilterMstListInteractor` | RaiinFilterMstRepository.SaveList (`raiinfiltermst`) |
| GET | `api/RaiinKubun/GetColumnName` | `RaiinKubunController` | `GetColumnName` | `GetColumnNameListInteractor` | RaiinKubunMstRepository.GetListColumnName (`raiin_kbn_mst`) |
| GET | `api/RaiinKubun/GetList + "Mst` | `RaiinKubunController` | `GetRaiinKubunMstList` | `GetRaiinKubunMstListInteractor` | RaiinKubunMstRepository.GetList (`raiin_kbn_mst`) |
| GET | `api/RaiinKubun/GetList + "KubunSetting` | `RaiinKubunController` | `LoadDataKubunSetting` | `LoadDataKubunSettingInteractor` | RaiinKubunMstRepository.GetMaxGrpId (`raiin_kbn_mst`)<br/>RaiinKubunMstRepository.LoadDataKubunSetting (`raiin_kbn_mst`) |
| POST | `api/RaiinKubun/SaveList + "KubunSetting` | `RaiinKubunController` | `SaveDataKubunSetting` | `SaveDataKubunSettingInteractor` | RaiinKubunMstRepository.SaveDataKubunSetting (`raiin_kbn_mst`) |
| POST | `api/RaiinKubun/Save + "RaiinKbnInfList` | `RaiinKubunController` | `SaveRaiinKbnInfList` | `SaveRaiinKbnInfListInteractor` | RaiinKubunMstRepository.SaveRaiinKbnInfs (`raiin_kbn_mst`) |
| GET | `api/RaiinListSetting/GetList + "DocCategoryRaiin` | `RaiinListSettingController` | `GetDocCategoryRaiin` | `GetDocCategoryRaiinInteractor` | DocumentRepository.GetAllDocCategory (`doc_inf`) |
| GET | `api/RaiinListSetting/GetList + "Filingcategory` | `RaiinListSettingController` | `GetFilingcategory` | `GetFilingcategoryInteractor` | RaiinListSettingRepository.GetFilingcategoryCollection (`raiinlistsetting_inf`) |
| GET | `api/RaiinListSetting/GetList + "RaiinListSetting` | `RaiinListSettingController` | `GetRaiinListSetting` | `GetRaiiinListSettingInteractor` | RaiinListSettingRepository.GetRaiiinListSetting (`raiinlistsetting_inf`) |
| POST | `api/RaiinListSetting/Save + "RaiinListSetting` | `RaiinListSettingController` | `SaveRaiinListSetting` | `SaveRaiinListSettingInteractor` | RaiinListSettingRepository.SaveRaiinListSetting (`raiinlistsetting_inf`) |
| GET | `api/RaiinStatusMst/GetList` | `RaiinStatusMstController` | `GetRaiinStatusMstcountList` | `RaiinStatusCountListInteractor` |  |
| GET | `api/Recalculation/DeleteReceiptInfEdit` | `RecalculationController` | `DeleteReceiptInfEdit` | `DeleteReceiptInfEditInteractor` | CalculationInfRepository.DeleteReceiptInfEdit (`calculationinf`) |
| POST | `api/ReceFutan/ReceFutanCalculateMain` | `ReceFutanController` | `GetListReceInf` | `` |  |
| POST | `api/ReceSeikyu/CancelSeikyu` | `ReceSeikyuController` | `CancelSeikyu` | `CancelSeikyuInteractor` | ReceSeikyuRepository.InsertNewReceSeikyu (`rece_seikyu`)<br/>ReceSeikyuRepository.GetReceSeikyuDuplicate (`rece_seikyu`)<br/>ReceSeikyuRepository.UpdateReceSeikyu (`rece_seikyu`)<br/>ReceiptRepository.GetReceInf (`receden_inf`) |
| GET | `api/ReceSeikyu/GetListReceSeikyu` | `ReceSeikyuController` | `GetListReceSeikyu` | `GetListReceSeikyuInteractor` | ReceSeikyuRepository.GetListReceSeikyModel (`rece_seikyu`) |
| GET | `api/ReceSeikyu/GetReceSeikyModelByPtNum` | `ReceSeikyuController` | `GetReceSeikyModelByPtNum` | `GetReceSeikyModelByPtNumInteractor` | ReceSeikyuRepository.GetReceSeikyModelByPtNum (`rece_seikyu`) |
| GET | `api/ReceSeikyu/GetRecedenHenJiyuuList` | `ReceSeikyuController` | `GetRecedenHenJiyuuList` | `GetRecedenHenJiyuuListInteractor` | ReceSeikyuRepository.GetRecedenHenJiyuuModels (`rece_seikyu`) |
| POST | `api/ReceSeikyu/ImportFileReceSeikyu` | `ReceSeikyuController` | `ImportFileReceSeikyu` | `ImportFileReceSeikyuInteractor` | ReceSeikyuRepository.InsertSingleRerikiInf (`rece_seikyu`)<br/>ReceSeikyuRepository.InsertSingleHenJiyuu (`rece_seikyu`)<br/>ReceSeikyuRepository.InsertSingleReceSeikyu (`rece_seikyu`)<br/>ReceSeikyuRepository.SaveChangeImportFileRececeikyus (`rece_seikyu`)<br/>ReceSeikyuRepository.IsReceSeikyuExisted (`rece_seikyu`)<br/>ReceSeikyuRepository.GetReceSeikyuPreHoken (`rece_seikyu`)<br/>ReceSeikyuRepository.DeleteReceSeikyu (`rece_seikyu`)<br/>ReceSeikyuRepository.DeleteHenJiyuuRireki (`rece_seikyu`)<br/>PatientInforRepository.GetPtIdFromPtNum (`pt_inf`)<br/>PatientInforRepository.GetCountRaiinAlreadyPaidOfPatientByDate (`pt_inf`)<br/>PatientInforRepository.GetPtInf (`pt_inf`) |
| GET | `api/ReceSeikyu/SearchReceInf` | `ReceSeikyuController` | `SearchReceInf` | `SearchReceInfInteractor` | ReceSeikyuRepository.SearchReceInf (`rece_seikyu`) |
| GET | `api/Receipt/CheckExisReceInfEdit` | `ReceiptController` | `CheckExisReceInfEdit` | `CheckExisReceInfEditInteractor` | ReceiptRepository.CheckExisReceInfEdit (`receden_inf`) |
| GET | `api/Receipt/CheckExistSyobyoKeika` | `ReceiptController` | `CheckExistSyobyoKeika` | `CheckExistSyobyoKeikaInteractor` | ReceiptRepository.CheckExistSyobyoKeikaSinDay (`receden_inf`) |
| GET | `api/Receipt/CheckExistsReceInf` | `ReceiptController` | `CheckExistsReceInf` | `CheckExistsReceInfInteractor` | ReceiptRepository.CheckExistsReceInf (`receden_inf`) |
| POST | `api/Receipt/CreateUKEFile` | `ReceiptController` | `CreateUKEFile` | `CreateUKEFileInteractor` |  |
| GET | `api/Receipt/DoReceCmt` | `ReceiptController` | `DoReceCmt` | `DoReceCmtInteractor` | ReceiptRepository.GetReceCmtList (`receden_inf`)<br/>ReceiptRepository.GetLastMonthReceCmt (`receden_inf`) |
| GET | `api/Receipt/GetDiseaseReceList` | `ReceiptController` | `GetDiseaseReceList` | `GetDiseaseReceListInteractor` | PtDiseaseRepository.GetPatientDiseaseList (`ptdisease_inf`)<br/>UserConfRepository.GetSettingValue (`userconf_inf`) |
| GET | `api/Receipt/GetInsuranceInf` | `ReceiptController` | `GetInsuranceInf` | `GetInsuranceInfInteractor` |  |
| GET | `api/Receipt/GetInsuranceReceInfList` | `ReceiptController` | `GetInsuranceReceInfList` | `GetInsuranceReceInfListInteractor` | ReceiptRepository.GetInsuranceReceInfList (`receden_inf`)<br/>InsuranceRepository.GetInsuranceListById (`hoken_inf`) |
| POST | `api/Receipt/GetList` | `ReceiptController` | `GetList` | `` |  |
| GET | `api/Receipt/GetListKaikeiInf` | `ReceiptController` | `GetListKaikeiInf` | `GetListKaikeiInfInteractor` | ReceiptRepository.GetListKaikeiInf (`receden_inf`) |
| GET | `api/Receipt/GetListRaiinInf` | `ReceiptController` | `GetListRaiinInf` | `GetListRaiinInfInteractor` | ReceptionRepository.GetListRaiinInf (`raiin_inf`) |
| GET | `api/Receipt/GetReceCmtList` | `ReceiptController` | `GetListReceCmt` | `GetReceCmtListInteractor` | ReceiptRepository.GetReceCmtList (`receden_inf`) |
| GET | `api/Receipt/GetListSinKoui` | `ReceiptController` | `GetListSinKoui` | `GetListSinKouiInteractor` | ReceiptRepository.GetListKaikeiInf (`receden_inf`) |
| GET | `api/Receipt/GetSyobyoKeikaList` | `ReceiptController` | `GetListSyobyoKeika` | `GetSyobyoKeikaListInteractor` | ReceiptRepository.GetSyobyoKeikaList (`receden_inf`) |
| GET | `api/Receipt/GetSyoukiInfList` | `ReceiptController` | `GetListSyoukiInf` | `GetSyoukiInfListInteractor` | ReceiptRepository.GetSyoukiInfList (`receden_inf`)<br/>ReceiptRepository.GetSyoukiKbnMstList (`receden_inf`) |
| GET | `api/Receipt/GetMedicalDetails` | `ReceiptController` | `GetMedicalDetails` | `GetMedicalDetailsInteractor` | MstItemRepository.FindHolidayMstList (`ten_mst`) |
| GET | `api/Receipt/GetNextUketukeNoBySetting` | `ReceiptController` | `GetNextUketukeNoBySetting` | `GetNextUketukeNoBySettingInteractor` | ReceptionRepository.GetNextUketukeNoBySetting (`raiin_inf`) |
| GET | `api/Receipt/GetReceByomeiChecking` | `ReceiptController` | `GetReceByomeiChecking` | `GetReceByomeiCheckingInteractor` | OrdInfRepository.GetOdrInfsBySinDate (`ord_inf`)<br/>PtDiseaseRepository.GetByomeiInThisMonth (`ptdisease_inf`)<br/>PtDiseaseRepository.GetTekiouByomeiByOrder (`ptdisease_inf`)<br/>MstItemRepository.FindTenMst (`ten_mst`) |
| GET | `api/Receipt/GetReceCheckOptionList` | `ReceiptController` | `GetReceCheckOptionList` | `GetReceCheckOptionListInteractor` | ReceiptRepository.GetReceCheckOptList (`receden_inf`) |
| GET | `api/Receipt/GetReceHenReason` | `ReceiptController` | `GetReceHenReason` | `GetReceHenReasonInteractor` | ReceiptRepository.GetReceReasonList (`receden_inf`) |
| GET | `api/Receipt/GetRecePreviewList` | `ReceiptController` | `GetRecePreviewList` | `GetRecePreviewListInteractor` | ReceiptRepository.GetReceInf (`receden_inf`) |
| GET | `api/Receipt/GetReceStatus` | `ReceiptController` | `GetReceStatus` | `GetReceStatusInteractor` | ReceiptRepository.GetReceStatus (`receden_inf`) |
| GET | `api/Receipt/GetReceiCheckList` | `ReceiptController` | `GetReceiCheckList` | `GetReceiCheckListInteractor` | ReceiptRepository.GetReceCheckCmtList (`receden_inf`)<br/>ReceiptRepository.GetReceCheckErrList (`receden_inf`) |
| GET | `api/Receipt/GetReceiptEdit` | `ReceiptController` | `GetReceiptEdit` | `GetReceiptEditInteractor` | ReceiptRepository.GetTokkiMstDictionary (`receden_inf`)<br/>ReceiptRepository.GetReceInfEdit (`receden_inf`)<br/>ReceiptRepository.GetReceInfPreEdit (`receden_inf`)<br/>ReceiptRepository.GetReceInf (`receden_inf`) |
| GET | `api/Receipt/GetSinDateRaiinInfList` | `ReceiptController` | `GetSinDateRaiinInfList` | `GetSinDateRaiinInfListInteractor` | ReceiptRepository.GetSinDateRaiinInfList (`receden_inf`) |
| GET | `api/Receipt/GetSinMeiInMonthList` | `ReceiptController` | `GetSinMeiInMonthList` | `GetSinMeiInMonthListInteractor` | MstItemRepository.FindHolidayMstList (`ten_mst`) |
| GET | `api/Receipt/GetListSokatuMst` | `ReceiptController` | `GetSokatuMstModels` | `GetListSokatuMstInteractor` | ReceiptRepository.GetSokatuMstModels (`receden_inf`) |
| GET | `api/Receipt/ReceCmtHistory` | `ReceiptController` | `ReceCmtHistory` | `ReceCmtHistoryInteractor` | ReceiptRepository.GetReceCmtList (`receden_inf`)<br/>InsuranceRepository.GetInsuranceListById (`hoken_inf`) |
| POST | `api/Receipt/SaveReceCmtList` | `ReceiptController` | `SaveListReceCmt` | `SaveReceCmtListInteractor` | ReceiptRepository.GetReceCmtList (`receden_inf`)<br/>ReceiptRepository.SaveReceCmtList (`receden_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>InsuranceRepository.CheckExistHokenId (`hoken_inf`)<br/>MstItemRepository.GetCheckItemCds (`ten_mst`) |
| POST | `api/Receipt/SaveSyobyoKeikaList` | `ReceiptController` | `SaveListSyobyoKeika` | `SaveSyobyoKeikaListInteractor` | ReceiptRepository.GetSyobyoKeikaList (`receden_inf`)<br/>ReceiptRepository.SaveSyobyoKeikaList (`receden_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>InsuranceRepository.GetHokenKbnByHokenId (`hoken_inf`) |
| POST | `api/Receipt/SaveSyoukiInfList` | `ReceiptController` | `SaveListSyoukiInf` | `SaveSyoukiInfListInteractor` | ReceiptRepository.GetSyoukiInfList (`receden_inf`)<br/>ReceiptRepository.SaveSyoukiInfList (`receden_inf`)<br/>ReceiptRepository.CheckExistSyoukiKbn (`receden_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>InsuranceRepository.CheckExistHokenId (`hoken_inf`) |
| POST | `api/Receipt/SaveReceCheckCmtList` | `ReceiptController` | `SaveReceCheckCmtList` | `SaveReceCheckCmtListInteractor` | ReceiptRepository.SaveReceCheckErrList (`receden_inf`)<br/>ReceiptRepository.SaveReceCheckCmtList (`receden_inf`)<br/>ReceiptRepository.CheckExistSeqNoReceCheckCmtList (`receden_inf`)<br/>ReceiptRepository.CheckExistSeqNoReceCheckErrorList (`receden_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>InsuranceRepository.CheckExistHokenId (`hoken_inf`) |
| POST | `api/Receipt/SaveReceCheckOpt` | `ReceiptController` | `SaveReceCheckOpt` | `SaveReceCheckOptInteractor` | ReceiptRepository.SaveReceCheckOpt (`receden_inf`) |
| POST | `api/Receipt/SaveReceStatus` | `ReceiptController` | `SaveReceStatus` | `SaveReceStatusInteractor` | ReceiptRepository.SaveReceStatus (`receden_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>InsuranceRepository.CheckExistHokenId (`hoken_inf`) |
| POST | `api/Receipt/SaveReceiptEdit` | `ReceiptController` | `SaveReceiptEdit` | `SaveReceiptEditInteractor` | ReceiptRepository.SaveReceiptEdit (`receden_inf`)<br/>ReceiptRepository.CheckExistReceiptEdit (`receden_inf`)<br/>ReceiptRepository.GetReceInf (`receden_inf`)<br/>ReceiptRepository.GetTokkiMstDictionary (`receden_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>InsuranceRepository.CheckExistHokenId (`hoken_inf`) |
| GET | `api/Receipt/SyobyoKeikaHistory` | `ReceiptController` | `SyobyoKeikaHistory` | `SyobyoKeikaHistoryInteractor` | ReceiptRepository.GetSyobyoKeikaList (`receden_inf`)<br/>InsuranceRepository.GetInsuranceListById (`hoken_inf`) |
| GET | `api/Receipt/SyoukiInfHistory` | `ReceiptController` | `SyoukiInfHistory` | `SyoukiInfHistoryInteractor` | ReceiptRepository.GetSyoukiInfList (`receden_inf`)<br/>ReceiptRepository.GetSyoukiKbnMstList (`receden_inf`)<br/>InsuranceRepository.GetInsuranceListById (`hoken_inf`) |
| GET | `api/Receipt/ValidateCreateUKEFile` | `ReceiptController` | `ValidateCreateUKEFile` | `CreateUKEFileInteractor` |  |
| POST | `api/Reception/AddPatientFile` | `ReceptionController` | `AddPatientFile` | `AddPatientFileInteractor` | ReceptionRepository.AddPatientFile (`raiin_inf`)<br/>HpInfRepository.CheckHpId (`hp_inf`) |
| POST | `api/Reception/BookingFromCalendarOrPortal` | `ReceptionController` | `BookingFromCalendarOrPortal` | `InsertFromCalendarOrPortalInteractor` | ReceptionRepository.InsertFromCalendarOrPortal (`raiin_inf`)<br/>ReceptionRepository.GetRecptionList (`raiin_inf`) |
| GET | `api/Reception/CheckLinkCard` | `ReceptionController` | `CheckLinkCard` | `CheckLinkCardInteractor` | ReceptionRepository.CheckLinkCard (`raiin_inf`) |
| POST | `api/Reception/CheckPatternSelectedExpirated` | `ReceptionController` | `CheckPatternSelectedExpirated` | `ValidPatternExpiratedInteractor` | ReceptionInsuranceRepository.IsValidAgeCheck (`hoken_inf`)<br/>ReceptionInsuranceRepository.HasElderHoken (`hoken_inf`) |
| PUT | `api/Reception/CombineSplitBill` | `ReceptionController` | `CombineSplitBill` | `SaveCombineBillInteractor` |  |
| POST | `api/Reception/Delete` | `ReceptionController` | `Delete` | `DeleteReceptionInteractor` | ReceptionRepository.CheckExistOfRaiinNos (`raiin_inf`)<br/>ReceptionRepository.Delete (`raiin_inf`)<br/>ReceptionRepository.GetListSameVisit (`raiin_inf`) |
| PUT | `api/Reception/DeletePatientFile` | `ReceptionController` | `DeletePatientFile` | `DeletePatientFileInteractor` | ReceptionRepository.DeletePatientFile (`raiin_inf`) |
| GET | `api/Reception/Get` | `ReceptionController` | `Get` | `GetReceptionInteractor` | ReceptionRepository.Get (`raiin_inf`) |
| GET | `api/Reception/GetDataReceptionDefault` | `ReceptionController` | `GetDataReceptionDefault` | `GetReceptionDefaultInteractor` | ReceptionRepository.GetDataDefaultReception (`raiin_inf`) |
| GET | `api/Reception/GetDefaultPrescription` | `ReceptionController` | `GetDefaultPrescription` | `GetDefaultPrescriptionInteractor` |  |
| GET | `api/Reception/GetDefaultSelectedTime` | `ReceptionController` | `GetDefaultSelectedTime` | `GetDefaultSelectedTimeInteractor` | TimeZoneRepository.IsHoliday (`timezone_inf`)<br/>TimeZoneRepository.GetTimeZoneConfs (`timezone_inf`)<br/>TimeZoneRepository.GetLatestTimeZoneDayInf (`timezone_inf`)<br/>TimeZoneRepository.CheckPediatricExist (`timezone_inf`) |
| GET | `api/Reception/GetHpInf` | `ReceptionController` | `GetHpInf` | `GetHpInfInteractor` | HpInfRepository.GetListHpInf (`hp_inf`) |
| GET | `api/Reception/GetLastKarute` | `ReceptionController` | `GetLastKarute` | `GetLastKaruteInteractor` | ReceptionRepository.GetLastKarute (`raiin_inf`) |
| GET | `api/Reception/GetLastRaiinInfs` | `ReceptionController` | `GetLastRaiinInfs` | `GetLastRaiinInfsInteractor` | ReceptionRepository.GetLastVisit (`raiin_inf`)<br/>ReceptionRepository.GetLastRaiinInfs (`raiin_inf`) |
| GET | `api/Reception/GetList` | `ReceptionController` | `GetList` | `GetListRaiinInfInteractor` | ReceptionRepository.GetListRaiinInf (`raiin_inf`) |
| GET | `api/Reception/GetRaiinListWithKanInf` | `ReceptionController` | `GetList` | `GetListRaiinInfInteractor` | ReceptionRepository.GetListRaiinInf (`raiin_inf`) |
| GET | `api/Reception/GetListSameVisit` | `ReceptionController` | `GetListSameVisit` | `GetReceptionSameVisitInteractor` | ReceptionSameVisitRepository.GetReceptionSameVisit (`receptionsamevisit_inf`) |
| GET | `api/Reception/GetMaxMoneyData` | `ReceptionController` | `GetMaxMoney` | `GetMaxMoneyInteractor` |  |
| GET | `api/Reception/GetOutDrugOrderList` | `ReceptionController` | `GetOutDrugOrderList` | `GetOutDrugOrderListInteractor` | ReceptionRepository.GetOutDrugOrderList (`raiin_inf`)<br/>KaRepository.GetList (`ka_inf`)<br/>UserRepository.GetAll (`user_mst`)<br/>PatientInforRepository.SearchPatient (`pt_inf`) |
| GET | `api/Reception/GetListFile` | `ReceptionController` | `GetPatientFile` | `GetPatientFileInteractor` | ReceptionRepository.GetPatientFiles (`raiin_inf`) |
| GET | `api/Reception/GetPatientRaiinKubun` | `ReceptionController` | `GetPatientRaiinKubun` | `GetPatientRaiinKubunListInteractor` | RaiinKubunMstRepository.GetPatientRaiinKubuns (`raiin_kbn_mst`) |
| GET | `api/Reception/GetRaiinInfBySinDate` | `ReceptionController` | `GetRaiinInfBySinDate` | `GetRaiinInfBySinDateInteractor` | ReceptionRepository.GetRaiinInfBySinDate (`raiin_inf`) |
| GET | `api/Reception/GetReceptionCombine` | `ReceptionController` | `GetReceptionCombine` | `GetReceptionCombineInteractor` | ReceptionRepository.GetReceptionCombine (`raiin_inf`) |
| GET | `api/Reception/Get + "ReceptionComment` | `ReceptionController` | `GetReceptionComment` | `GetReceptionCommentInteractor` | ReceptionRepository.GetReceptionComments (`raiin_inf`) |
| GET | `api/Reception/GetReceptionInsurance` | `ReceptionController` | `GetReceptionInsurance` | `ReceptionInsuranceInteractor` | ReceptionInsuranceRepository.GetReceptionInsurance (`hoken_inf`) |
| GET | `api/Reception/GetYoyakuRaiinInf` | `ReceptionController` | `GetYoyakuRaiinInf` | `GetYoyakuRaiinInfInteractor` | ReceptionRepository.GetYoyakuRaiinInf (`raiin_inf`) |
| GET | `api/Reception/InitDoctorCombo` | `ReceptionController` | `InitDoctorCombo` | `InitDoctorComboInteractor` | ReceptionRepository.InitDoctorCombobox (`raiin_inf`) |
| POST | `api/Reception/Insert` | `ReceptionController` | `InsertAsync` | `InsertReceptionInteractor` | ReceptionRepository.Insert (`raiin_inf`)<br/>ReceptionRepository.GetRecptionList (`raiin_inf`)<br/>ReceptionRepository.GetListSameVisit (`raiin_inf`)<br/>ReceptionRepository.GetMaxUketukeNo (`raiin_inf`) |
| PUT | `api/Reception/RevertDeleteNoRecept` | `ReceptionController` | `RevertDeleteNoRecept` | `RevertDeleteNoReceptInteractor` | ReceptionRepository.UpdateIsDeleted (`raiin_inf`)<br/>ReceptionRepository.GetList (`raiin_inf`) |
| POST | `api/Reception/SaveMaxMoneyData` | `ReceptionController` | `SaveMaxMoney` | `SaveMaxMoneyInteractor` |  |
| POST | `api/Reception/Update` | `ReceptionController` | `UpdateAsync` | `UpdateReceptionInteractor` | ReceptionRepository.Update (`raiin_inf`)<br/>ReceptionRepository.GetRecptionList (`raiin_inf`)<br/>ReceptionRepository.GetListSameVisit (`raiin_inf`)<br/>ReceptionRepository.GetMaxUketukeNo (`raiin_inf`) |
| POST | `api/Reception/UpdateBookingInfo` | `ReceptionController` | `UpdateBookingInfo` | `UpdateBookingInfoInteractor` | ReceptionRepository.UpdateBookingInfo (`raiin_inf`)<br/>ReceptionRepository.GetReceptionListByRaiinNoes (`raiin_inf`) |
| POST | `api/Reception/UpdatePrescription` | `ReceptionController` | `UpdatePrescription` | `UpdatePrescriptionInteractor` | ReceptionRepository.UpdatePrescription (`raiin_inf`)<br/>ReceptionRepository.Get (`raiin_inf`) |
| POST | `api/Reception/UpdateRaiinCmtByBooking` | `ReceptionController` | `UpdateRaiinCmtByBooking` | `UpdateRaiinCmtByBookingInteractor` | ReceptionRepository.UpdateRaiinCmtByBooking (`raiin_inf`)<br/>ReceptionRepository.GetReceptionListByRaiinNoes (`raiin_inf`) |
| POST | `api/Reception/UpdateTimeZoneDayInf` | `ReceptionController` | `UpdateTimeZoneDayInf` | `UpdateTimeZoneDayInfInteractor` | TimeZoneRepository.UpdateTimeZoneDayInf (`timezone_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`)<br/>HpInfRepository.CheckHpId (`hp_inf`) |
| GET | `api/Reception/ValidateReservation` | `ReceptionController` | `ValidateReservation` | `ValidateReservationInteractor` | ReceptionRepository.ValidateReservation (`raiin_inf`) |
| POST | `api/RemoveCache/RemoveAllCache` | `RemoveCacheController` | `RemoveAllCache` | `RemoveAllCacheInteractor` | RemoveCacheRepository.RemoveAllCache (`removecache_inf`) |
| POST | `api/RemoveCache/RemoveCache` | `RemoveCacheController` | `RemoveCache` | `RemoveCacheInteractor` | RemoveCacheRepository.RemoveCache (`removecache_inf`) |
| POST | `api/RequestExam/Delete` | `RequestExamController` | `DeleteRequestExams` | `DeleteRequestExamsInteractor` | KarteVSPHYSRepository.DeleteRequestExams (`kartevsphys_inf`) |
| GET | `api/RequestExam/GetRequestExam` | `RequestExamController` | `GetRequestExam` | `GetKensaIraiInteractor` | KensaIraiRepository.GetKensaIraiModels (`kensairai_inf`) |
| POST | `api/RequestExam/Save` | `RequestExamController` | `SaveRequestExams` | `SaveRequestExamsInteractor` | KarteVSPHYSRepository.SaveRequestExams (`kartevsphys_inf`) |
| GET | `api/Santei/GetList` | `SanteiController` | `GetList` | `GetListSanteiInfInteractor` | SanteiInfRepository.GetListSanteiInf (`santeiinf`)<br/>MstItemRepository.GetListSanteiByomeis (`ten_mst`) |
| POST | `api/Santei/SaveList` | `SanteiController` | `SaveListSanteiInf` | `SaveListSanteiInfInteractor` | SanteiInfRepository.SaveSantei (`santeiinf`)<br/>SanteiInfRepository.CheckExistItemCd (`santeiinf`)<br/>SanteiInfRepository.GetOnlyListSanteiInf (`santeiinf`)<br/>SanteiInfRepository.GetListSanteiInfDetails (`santeiinf`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`) |
| GET | `api/Schema/GetList` | `SchemaController` | `GetList` | `GetListImageTemplatesInteractor` |  |
| GET | `api/Schema/GetListInsuranceScan` | `SchemaController` | `GetListInsuranceScan` | `GetListInsuranceScanInteractor` | InsuranceRepository.GetListInsuranceScanByPtId (`hoken_inf`) |
| POST | `api/Schema/UploadListFileKarte` | `SchemaController` | `MultiUpload` | `SaveListFileTodayOrderInteractor` |  |
| GET | `api/Set/DOHistoryToSuperSet` | `SetController` | `DOHistoryToSuperSet` | `DOHistoryToSuperSetInteractor` | SuperSetDetailRepository.DOHistoryToSuperSet (`supersetdetail_inf`)<br/>MstItemRepository.GetListRecedenByItemCds (`ten_mst`) |
| POST | `api/Set/GetConversion` | `SetController` | `GetConversion` | `GetConversionInteractor` | SuperSetDetailRepository.GetConversionItem (`supersetdetail_inf`)<br/>MstItemRepository.GetTenMst (`ten_mst`) |
| GET | `api/Set/GetList` | `SetController` | `GetList` | `GetSetMstListInteractor` | SetMstRepository.GetList (`setmst`)<br/>SetGenerationMstRepository.GetGenerationIdBySinDate (`setgenerationmst`) |
| POST | `api/Set/GetOdrSetName` | `SetController` | `GetOdrSetName` | `GetOdrSetNameInteractor` | SuperSetDetailRepository.GetOdrSetName (`supersetdetail_inf`) |
| GET | `api/Set/GetSetGenerationMstList` | `SetController` | `GetSetGenerationMstList` | `GetSetGenerationMstListInteractor` | SetGenerationMstRepository.GetSetGenerationMstList (`setgenerationmst`) |
| GET | `api/Set/GetSuperSetDetail` | `SetController` | `GetSuperSetDetail` | `GetSuperSetDetailInteractor` | SuperSetDetailRepository.GetSuperSetDetail (`supersetdetail_inf`)<br/>MstItemRepository.GetListRecedenByItemCds (`ten_mst`) |
| GET | `api/Set/GetSuperSetDetailForTodayOrder` | `SetController` | `GetSuperSetDetailForTodayOrder` | `GetSuperSetDetailInteractor` | SuperSetDetailRepository.GetSuperSetDetail (`supersetdetail_inf`)<br/>MstItemRepository.GetListRecedenByItemCds (`ten_mst`) |
| GET | `api/Set/GetToolTip` | `SetController` | `GetToolTip` | `GetSetMstToolTipInteractor` | SetMstRepository.GetToolTip (`setmst`) |
| POST | `api/Set/Paste` | `SetController` | `PasteSetMst` | `CopyPasteSetMstInteractor` | SetMstRepository.PasteSetMst (`setmst`)<br/>UserRepository.NotAllowSaveMedicalExamination (`user_mst`) |
| POST | `api/Set/Reorder` | `SetController` | `Reorder` | `ReorderSetMstInteractor` | SetMstRepository.ReorderSetMst (`setmst`)<br/>UserRepository.NotAllowSaveMedicalExamination (`user_mst`) |
| POST | `api/Set/Save` | `SetController` | `Save` | `SaveSetMstInteractor` | SetMstRepository.CheckDeleteFolder (`setmst`)<br/>SetMstRepository.CheckMyFolder (`setmst`)<br/>SetMstRepository.SaveSetMstModel (`setmst`)<br/>UserRepository.NotAllowSaveMedicalExamination (`user_mst`)<br/>AuditLogRepository.AddAuditTrailLog (`audit_trail_log`) |
| POST | `api/Set/SaveConversion` | `SetController` | `SaveConversion` | `SaveConversionInteractor` | SuperSetDetailRepository.SaveConversionItemInf (`supersetdetail_inf`)<br/>MstItemRepository.CheckItemCd (`ten_mst`)<br/>MstItemRepository.GetCheckItemCds (`ten_mst`) |
| POST | `api/Set/SaveOdrSet` | `SetController` | `SaveOdrSet` | `SaveOdrSetInteractor` | SuperSetDetailRepository.SaveOdrSet (`supersetdetail_inf`)<br/>MstItemRepository.GetCheckItemCds (`ten_mst`)<br/>SetMstRepository.CheckExistSetMstBySetCd (`setmst`) |
| POST | `api/Set/SaveSuperSetDetail` | `SetController` | `SaveSuperSetDetail` | `SaveSuperSetDetailInteractor` | SuperSetDetailRepository.SaveSuperSetDetail (`supersetdetail_inf`)<br/>SuperSetDetailRepository.SaveListSetKarteFile (`supersetdetail_inf`)<br/>SuperSetDetailRepository.ClearTempData (`supersetdetail_inf`)<br/>SuperSetDetailRepository.GetOnlyKarteInfModel (`supersetdetail_inf`)<br/>SuperSetDetailRepository.GetOnlyListOrderInfModel (`supersetdetail_inf`)<br/>MstItemRepository.DiseaseSearch (`ten_mst`)<br/>PatientInforRepository.GetById (`pt_inf`)<br/>SetMstRepository.CheckExistSetMstBySetCd (`setmst`)<br/>SetMstRepository.CheckParentFolder (`setmst`)<br/>UserRepository.NotAllowSaveMedicalExamination (`user_mst`) |
| GET | `api/SetKbn/GetList` | `SetKbnController` | `GetList` | `GetSetKbnMstListInteractor` | SetKbnMstRepository.GetList (`setkbnmst`)<br/>SetGenerationMstRepository.GetList (`setgenerationmst`) |
| GET | `api/SetKbn/GetSetKbnMstListByGenerationId` | `SetKbnController` | `GetSetKbnMstListByGenerationId` | `GetSetKbnMstListByGenerationIdInteractor` | SetKbnMstRepository.GetSetKbnMstListByGenerationId (`setkbnmst`) |
| POST | `api/SetKbn/Upsert` | `SetKbnController` | `Upsert` | `UpsertSetKbnMstInteractor` | SetKbnMstRepository.Upsert (`setkbnmst`)<br/>KaRepository.CheckKaId (`ka_inf`)<br/>UserRepository.GetDoctorsList (`user_mst`)<br/>SetGenerationMstRepository.GetGenerationId (`setgenerationmst`)<br/>HpInfRepository.CheckHpId (`hp_inf`) |
| POST | `api/SetSendaiGeneration/Delete` | `SetSendaiGenerationController` | `Delete` | `DeleteSendaiGenerationInteractor` |  |
| GET | `api/SetSendaiGeneration/GetList` | `SetSendaiGenerationController` | `GetList` | `SetSendaiGenerationInteractor` |  |
| POST | `api/SinMei/GetSinMeiList` | `SinMeiController` | `GetSinMeiList` | `` |  |
| GET | `api/SmartKartePort/GetPort` | `SmartKartePortController` | `GetPort` | `GetPortInteractor` | SmartKartePortRepository.GetSignalRPort (`smartkarte_port`) |
| GET | `api/SmartKartePort/GetList` | `SmartKartePortController` | `GetPortList` | `GetPortInteractor` | SmartKartePortRepository.GetSignalRPort (`smartkarte_port`) |
| POST | `api/SmartKartePort/UpdatePort` | `SmartKartePortController` | `UpdatePort` | `UpdatePortInteractor` | SmartKartePortRepository.UpdateSignalRPort (`smartkarte_port`) |
| POST | `api/SpecialNote/AddAlrgyDrugList` | `SpecialNoteController` | `AddAlrgyDrugList` | `AddAlrgyDrugListInteractor` | ImportantNoteRepository.GetAlrgyDrugList (`importantnote_inf`)<br/>ImportantNoteRepository.AddAlrgyDrugList (`importantnote_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>MstItemRepository.CheckItemCd (`ten_mst`) |
| GET | `api/SpecialNote/Get` | `SpecialNoteController` | `Get` | `GetSpecialNoteInteractor` | KarteAllergyOthersRepository.GetAllergyElseList (`karteallergyothers_inf`)<br/>KarteAllergyFoodRepository.GetAllergyFoodList (`karteallergyfood_inf`)<br/>KarteAllergyMedicineRepository.GetAllergyDrugList (`karteallergymedicine_inf`)<br/>ImportantNoteRepository.GetOtherDrugList (`importantnote_inf`)<br/>ImportantNoteRepository.GetOtcDrugList (`importantnote_inf`)<br/>ImportantNoteRepository.GetSuppleList (`importantnote_inf`)<br/>ImportantNoteRepository.GetKioRekiList (`importantnote_inf`)<br/>KarteMedicalHistoryRepository.GetPregnantList (`kartemedicalhistory_inf`)<br/>KarteMedicalHistoryRepository.GetPtSocialHistoryList (`kartemedicalhistory_inf`)<br/>KarteMedicalHistoryRepository.GetFamilyList (`kartemedicalhistory_inf`)<br/>KarteVSPHYSRepository.GetKarteVSPHYSList (`kartevsphys_inf`)<br/>ExamResultsRepository.GetExamResults (`examresults_inf`)<br/>SummaryRepository.GetSummary (`summary_inf`)<br/>ImportantNoteRepository.GetInfectionList (`importantnote_inf`)<br/>PtCmtInfRepository.GetList (`ptcmtinf`)<br/>PatientInfoRepository.GetSeikaturekiInfList (`patientinfo_inf`)<br/>PatientInfoRepository.GetPregnancyList (`patientinfo_inf`) |
| GET | `api/SpecialNote/GetStdPoint` | `SpecialNoteController` | `GetStdPoint` | `GetStdPointInteractor` | PatientInfoRepository.GetStdPoint (`patientinfo_inf`) |
| POST | `api/SpecialNote/Save` | `SpecialNoteController` | `Save` | `SaveSpecialNoteInteractor` | SpecialNoteRepository.SaveSpecialNote (`specialnote_inf`)<br/>SummaryInfRepository.Get (`summaryinf`)<br/>UserRepository.GetPermissionByScreenCode (`user_mst`) |
| GET | `api/SpecialNote/GetPtWeight` | `SpecialNoteController` | `Save` | `SaveSpecialNoteInteractor` | SpecialNoteRepository.SaveSpecialNote (`specialnote_inf`)<br/>SummaryInfRepository.Get (`summaryinf`)<br/>UserRepository.GetPermissionByScreenCode (`user_mst`) |
| POST | `api/StickyNote/Delete` | `StickyNoteController` | `Delete` | `DeleteStickyNoteInteractor` | PtTagRepository.UpdateIsDeleted (`pttag_inf`) |
| GET | `api/StickyNote/Get` | `StickyNoteController` | `Get` | `GetStickyNoteInteractor` | PtTagRepository.SearchByPtId (`pttag_inf`) |
| GET | `api/StickyNote/Get + "Setting` | `StickyNoteController` | `GetSetting` | `GetSettingStickyNoteInteractor` | UserConfRepository.GetList (`userconf_inf`) |
| POST | `api/StickyNote/Revert` | `StickyNoteController` | `Revert` | `RevertStickyNoteInteractor` | PtTagRepository.UpdateIsDeleted (`pttag_inf`) |
| POST | `api/StickyNote/Save` | `StickyNoteController` | `Save` | `SaveStickyNoteInteractor` | PtTagRepository.SaveStickyNote (`pttag_inf`)<br/>PtTagRepository.GetStickyNoteModel (`pttag_inf`) |
| GET | `api/Summary/GetSummaryInf` | `SummaryController` | `GetSummary` | `GetSummaryInteractor` | SummaryRepository.GetSummary (`summary_inf`) |
| POST | `api/Summary/Save` | `SummaryController` | `Save` | `SaveSummaryInteractor` | SummaryRepository.SaveSummary (`summary_inf`) |
| GET | `api/SystemConf/GetDrugCheckSetting` | `SystemConfController` | `DrugCheckSetting` | `GetDrugCheckSettingInteractor` | SystemConfRepository.GetList (`system_conf`) |
| GET | `api/SystemConf/GetAllPath` | `SystemConfController` | `GetAllPath` | `GetPathAllInteractor` |  |
| GET | `api/SystemConf/Get` | `SystemConfController` | `GetByGrpCd` | `GetSystemConfInteractor` | SystemConfRepository.GetByGrpCd (`system_conf`) |
| GET | `api/SystemConf/GetSystemSetting` | `SystemConfController` | `GetList` | `GetSystemSettingInteractor` | SystemConfRepository.GetRoudouMst (`system_conf`)<br/>SystemConfRepository.GetListSystemConfMenuWithGeneration (`system_conf`)<br/>SystemConfRepository.GetListSystemConfMenu (`system_conf`)<br/>SystemConfRepository.GetListSystemConfMenuOnly (`system_conf`)<br/>SystemConfRepository.GetListCenterCd (`system_conf`)<br/>SanteiInfRepository.GetListAutoSanteiMst (`santeiinf`)<br/>MstItemRepository.GetListKensaCenterMst (`ten_mst`)<br/>HpInfRepository.GetListHpInf (`hp_inf`) |
| GET | `api/SystemConf/GetSystemConfForPrint` | `SystemConfController` | `GetSystemConfForPrint` | `GetSystemConfForPrintInteractor` | SystemConfRepository.GetConfigForPrintFunction (`system_conf`) |
| GET | `api/SystemConf/GetList` | `SystemConfController` | `GetSystemConfList` | `GetSystemConfInteractor` | SystemConfRepository.GetByGrpCd (`system_conf`) |
| GET | `api/SystemConf/GetSystemConfListXmlPath` | `SystemConfController` | `GetSystemConfListXmlPath` | `GetSystemConfInteractor` | SystemConfRepository.GetByGrpCd (`system_conf`) |
| POST | `api/SystemConf/SaveDrugCheckSetting` | `SystemConfController` | `SaveDrugCheckSetting` | `SaveDrugCheckSettingInteractor` | SystemConfRepository.SaveSystemConfigList (`system_conf`) |
| POST | `api/SystemConf/SavePath` | `SystemConfController` | `SavePath` | `SavePathInteractor` | SystemConfRepository.SavePathConfOnline (`system_conf`) |
| POST | `api/SystemConf/SaveSystemSetting` | `SystemConfController` | `SaveSystemSetting` | `SaveSystemSettingInteractor` | SystemConfRepository.SaveSystemGenerationConf (`system_conf`)<br/>SystemConfRepository.SaveSystemSetting (`system_conf`)<br/>HpInfRepository.SaveHpInf (`hp_inf`)<br/>SanteiInfRepository.SaveAutoSanteiMst (`santeiinf`)<br/>SanteiInfRepository.GetAutoSanteiMstByItemCd (`santeiinf`)<br/>MstItemRepository.SaveKensaCenterMst (`ten_mst`) |
| GET | `api/SystemGenerationConf/GetList` | `SystemGenerationConfController` | `GetList` | `GetSystemGenerationConfListInteractor` | SystemGenerationConfRepository.GetList (`system_generation_conf`) |
| GET | `api/SystemGenerationConf/GetSettingValue` | `SystemGenerationConfController` | `GetSettingValue` | `GetSystemGenerationConfInteractor` | SystemGenerationConfRepository.GetSettingValue (`system_generation_conf`) |
| GET | `api/TimeZoneConf/GetTimeZoneConfGroup` | `TimeZoneConfController` | `GetTimeZoneConfGroup` | `GetTimeZoneConfGroupInteractor` | TimeZoneRepository.GetTimeZoneConfGroupModels (`timezone_inf`)<br/>UserRepository.GetPermissionByScreenCode (`user_mst`) |
| POST | `api/TimeZoneConf/SaveTimeZoneConf` | `TimeZoneConfController` | `SaveTimeZoneConf` | `SaveTimeZoneConfInteractor` | TimeZoneRepository.SaveTimeZoneConf (`timezone_inf`)<br/>UserRepository.GetPermissionByScreenCode (`user_mst`) |
| POST | `api/TodayOrd/AddAutoItem` | `TodayOrdController` | `AddAutoItem` | `AddAutoItemInteractor` | TodayOdrRepository.AutoAddOrders (`today_odr`) |
| POST | `api/TodayOrd/AutoCheckOrder` | `TodayOrdController` | `AutoCheckOrder` | `AutoCheckOrderInteractor` | TodayOdrRepository.AutoCheckOrder (`today_odr`) |
| POST | `api/TodayOrd/ChangeAfterAutoCheckOrder` | `TodayOrdController` | `ChangeAfterAutoCheckOrder` | `ChangeAfterAutoCheckOrderInteractor` | TodayOdrRepository.ChangeAfterAutoCheckOrder (`today_odr`) |
| POST | `api/TodayOrd/CheckedExpired` | `TodayOrdController` | `CheckedExpired` | `CheckedExpiredInteractor` | MstItemRepository.GetTenMstList (`ten_mst`)<br/>MstItemRepository.GetCenterCdKensaCenter (`ten_mst`)<br/>MstItemRepository.GetConversionItem (`ten_mst`) |
| POST | `api/TodayOrd/ConvertFromHistoryToTodayOrder` | `TodayOrdController` | `ConvertFromHistoryToTodayOrder` | `ConvertFromHistoryTodayOrderInteractor` |  |
| POST | `api/TodayOrd/ConvertItem` | `TodayOrdController` | `ConvertItem` | `ConvertItemInteractor` | MstItemRepository.ExceConversionItem (`ten_mst`)<br/>MstItemRepository.GetListRecedenByItemCds (`ten_mst`)<br/>TodayOdrRepository.ConvertConversionItemToOrderInfModel (`today_odr`) |
| POST | `api/TodayOrd/ConvertNextOrderToTodayOrder` | `TodayOrdController` | `ConvertNextOrderToTodayOrder` | `ConvertNextOrderTodayOrdInteractor` | TodayOdrRepository.FromNextOrderToTodayOrder (`today_odr`)<br/>MstItemRepository.GetCheckIpnCds (`ten_mst`) |
| POST | `api/TodayOrd/GetAddedAutoItem` | `TodayOrdController` | `GetAddedAutoItem` | `GetAddedAutoItemInteractor` | TodayOdrRepository.GetAutoAddOrders (`today_odr`) |
| POST | `api/TodayOrd/GetDefaultSelectPattern` | `TodayOrdController` | `GetDefaultSelectPattern` | `GetDefaultSelectPatternInteractor` | InsuranceRepository.CheckExistHokenPids (`hoken_inf`)<br/>InsuranceRepository.GetListHistoryPid (`hoken_inf`) |
| POST | `api/TodayOrd/GetInfCheckedItemName` | `TodayOrdController` | `GetInfCheckedItemName` | `CheckedItemNameInteractor` | TodayOdrRepository.CheckNameChanged (`today_odr`) |
| GET | `api/TodayOrd/GetInsuranceComboList` | `TodayOrdController` | `GetInsuranceComboList` | `GetInsuranceComboListInteractor` | InsuranceRepository.GetInsuranceList (`hoken_inf`) |
| GET | `api/TodayOrd/GetLastDayInfoList` | `TodayOrdController` | `GetLastDayInfoListOrder` | `GetLastDayInfoListInteractor` | TodayOdrRepository.GetLastDayInfoList (`today_odr`) |
| POST | `api/TodayOrd/GetValidGairaiRiha` | `TodayOrdController` | `GetValidGairaiRiha` | `GetValidGairaiRihaInteractor` | TodayOdrRepository.GetValidGairaiRiha (`today_odr`) |
| POST | `api/TodayOrd/GetValidJihiYobo` | `TodayOrdController` | `GetValidJihiYobo` | `GetValidJihiYoboInteractor` | TodayOdrRepository.GetValidJihiYobo (`today_odr`) |
| POST | `api/TodayOrd/SaveSettingLastDayInfoList` | `TodayOrdController` | `SaveSettingLastDayInfoListOrder` | `SaveSettingLastDayInfoListInteractor` | TodayOdrRepository.SaveSettingLastDayInfo (`today_odr`) |
| POST | `api/TodayOrd/Upsert` | `TodayOrdController` | `Upsert` | `UpsertTodayOrdInteractor` | OrdInfRepository.GetCheckIpnMinYakkaMsts (`ord_inf`)<br/>OrdInfRepository.CheckIsGetYakkaPrices (`ord_inf`)<br/>OrdInfRepository.GetIpnMst (`ord_inf`)<br/>OrdInfRepository.GetListToCheckValidate (`ord_inf`)<br/>ReceptionRepository.GetList (`raiin_inf`)<br/>ReceptionRepository.GetListSameVisit (`raiin_inf`)<br/>ReceptionRepository.CheckNo (`raiin_inf`)<br/>MstItemRepository.GetCheckTenItemModels (`ten_mst`)<br/>MstItemRepository.GetRecedens (`ten_mst`)<br/>SystemGenerationConfRepository.GetSettingValue (`system_generation_conf`)<br/>PatientInforRepository.GetById (`pt_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>InsuranceRepository.CheckExistHokenPid (`hoken_inf`)<br/>InsuranceRepository.GetPtHokenInfByHokenPtId (`hoken_inf`)<br/>InsuranceRepository.FindHokenInfByPtId (`hoken_inf`)<br/>InsuranceRepository.GetCheckListHokenInf (`hoken_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>TodayOdrRepository.Upsert (`today_odr`)<br/>KarteInfRepository.SaveListFileKarte (`karte_inf`)<br/>KarteInfRepository.ClearTempData (`karte_inf`)<br/>KarteFileRepository.UpdateStatusSaveMedical (`karte_file`)<br/>LockRepository.RemoveAllLock (`lock_inf`)<br/>LockRepository.GetLockMedicalTab (`lock_inf`)<br/>InsuranceMstRepository.GetListHokenFund (`hoken_sya_mst`) |
| POST | `api/TodayOrd/UpsertPtHokenPattern` | `TodayOrdController` | `UpsertPtHokenPattern` | `UpsertPtHokenPatternInteractor` | InsuranceRepository.UpsertHokenInf (`hoken_inf`)<br/>InsuranceRepository.GetPtHokenInfByHokenId (`hoken_inf`)<br/>InsuranceRepository.GetInsuranceList (`hoken_inf`)<br/>InsuranceRepository.DeleteKeyInsuranceList (`hoken_inf`)<br/>InsuranceRepository.ProcessKohiId (`hoken_inf`)<br/>TodayOdrRepository.UpsertPtHokenPattern (`today_odr`)<br/>TodayOdrRepository.CheckValidPtId (`today_odr`)<br/>TodayOdrRepository.CheckHokenId (`today_odr`)<br/>TodayOdrRepository.CheckPtKohi (`today_odr`) |
| POST | `api/TodayOrd/Validate` | `TodayOrdController` | `Validate` | `ValidationTodayOrdInteractor` | OrdInfRepository.GetCheckIpnMinYakkaMsts (`ord_inf`)<br/>OrdInfRepository.CheckIsGetYakkaPrices (`ord_inf`)<br/>OrdInfRepository.GetIpnMst (`ord_inf`)<br/>OrdInfRepository.GetListToCheckValidate (`ord_inf`)<br/>MstItemRepository.GetCheckTenItemModels (`ten_mst`)<br/>MstItemRepository.GetRecedens (`ten_mst`)<br/>SystemGenerationConfRepository.GetSettingValue (`system_generation_conf`)<br/>HpInfRepository.CheckHpId (`hp_inf`)<br/>InsuranceRepository.GetCheckListHokenInf (`hoken_inf`)<br/>InsuranceRepository.CheckExistHokenPid (`hoken_inf`)<br/>InsuranceRepository.GetPtHokenInfByHokenPtId (`hoken_inf`)<br/>InsuranceRepository.FindHokenInfByPtId (`hoken_inf`)<br/>PatientInforRepository.CheckExistIdList (`pt_inf`)<br/>ReceptionRepository.CheckListNo (`raiin_inf`)<br/>UserRepository.CheckExistedUserId (`user_mst`)<br/>InsuranceMstRepository.GetListHokenFund (`hoken_sya_mst`) |
| GET | `api/TodoGrpMst/GetList` | `TodoGrpMstController` | `GetList` | `GetTodoGrpInteractor` | TodoGrpMstRepository.GetTodoGrpMsts (`todogrpmst`) |
| POST | `api/TodoGrpMst/UpsertList` | `TodoGrpMstController` | `Upsert` | `UpsertTodoGrpMstInteractor` | TodoGrpMstRepository.CheckExistedTodoGrpNo (`todogrpmst`)<br/>TodoGrpMstRepository.Upsert (`todogrpmst`) |
| GET | `api/TodoInf/GetList` | `TodoInfController` | `GetList` | `GetTodoInfFinderInteractor` | TodoInfRepository.GetList (`todoinf`) |
| POST | `api/TodoInf/UpsertList` | `TodoInfController` | `Upsert` | `UpsertTodoInfInteractor` | TodoInfRepository.CheckExist (`todoinf`)<br/>TodoInfRepository.Upsert (`todoinf`) |
| GET | `api/TodoKbn/GetList` | `TodoKbnController` | `GetList` | `GetTodoKbnInteractor` | TodoKbnMstRepository.GetTodoKbnMsts (`todokbnmst`) |
| GET | `api/TreatmentDepartment/GetTreatmentDepartmentList` | `TreatmentDepartmentController` | `GetTreatmentMenuList` | `GetTreatmentDepartmentListInteractor` | TreatmentDepartmentRepository.GetTreatmentStatusList (`treatmentdepartment_inf`) |
| GET | `api/UketukeSbt/Get + "BySinDate` | `UketukeSbtController` | `GetBySinDate` | `GetUketukeSbtMstBySinDateInteractor` | UketukeSbtMstRepository.GetList (`uketukesbtmst`)<br/>UketukeSbtDayInfRepository.GetListBySinDate (`uketukesbtdayinf`) |
| GET | `api/UketukeSbt/GetList + "Mst` | `UketukeSbtController` | `GetListMst` | `GetUketukeSbtMstListInteractor` | UketukeSbtMstRepository.GetList (`uketukesbtmst`) |
| GET | `api/UketukeSbt/Get + "Next` | `UketukeSbtController` | `GetNext` | `GetNextUketukeSbtMstInteractor` | UketukeSbtMstRepository.GetList (`uketukesbtmst`)<br/>UketukeSbtDayInfRepository.Upsert (`uketukesbtdayinf`) |
| POST | `api/UketukeSbt/Upsert + "Mst` | `UketukeSbtController` | `Upsert` | `UpsertUketukeSbtMstInteractor` | UketukeSbtMstRepository.Upsert (`uketukesbtmst`) |
| GET | `api/UserConf/GetList` | `UserConfController` | `GetList` | `GetUserConfListInteractor` | UserConfRepository.GetDic (`userconf_inf`) |
| GET | `api/UserConf/GetList + "ForModel` | `UserConfController` | `GetListForModel` | `GetUserConfModelListInteractor` | UserConfRepository.GetList (`userconf_inf`) |
| GET | `api/UserConf/GetListMedicalExaminationConfig` | `UserConfController` | `GetListMedicalExaminationConfig` | `GetListMedicalExaminationConfigInteractor` | UserConfRepository.GetList (`userconf_inf`)<br/>UserConfRepository.GetDefaultValue (`userconf_inf`)<br/>JsonSettingRepository.Get (`jsonsetting_inf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`) |
| POST | `api/UserConf/GetUserConfParam` | `UserConfController` | `GetUserConfParam` | `GetUserConfigParamInteractor` | UserConfRepository.GetListSettingParam (`userconf_inf`) |
| GET | `api/UserConf/Sagaku` | `UserConfController` | `Sagaku` | `SagakuInteractor` | UserConfRepository.Sagaku (`userconf_inf`) |
| PUT | `api/UserConf/Update` | `UserConfController` | `Update` | `UpdateUserConfInteractor` | UserConfRepository.UpdateUserConf (`userconf_inf`) |
| PUT | `api/UserConf/UpdateAdoptedByomeiConfig` | `UserConfController` | `UpdateAdoptedByomeiConfig` | `UpdateAdoptedByomeiConfigInteractor` | UserConfRepository.UpdateAdoptedByomeiConfig (`userconf_inf`) |
| POST | `api/UserConf/UpsertUserConfList` | `UserConfController` | `UpsertUserConfList` | `UpsertUserConfListInteractor` | UserConfRepository.UpsertUserConfs (`userconf_inf`) |
| GET | `api/User/CheckLockMedicalExamination` | `UserController` | `CheckLockMedicalExamination` | `CheckLockInteractor` | LockRepository.GetLock (`lock_inf`) |
| GET | `api/User/GetAllPermission` | `UserController` | `GetAllPermission` | `GetAllPermissionInteractor` | UserRepository.GetAllPermission (`user_mst`) |
| GET | `api/User/GetCheckDoctorPermission` | `UserController` | `GetCheckDoctorPermission` | `GetCheckDoctorPermissionInteractor` | UserRepository.GetCheckDoctorPermission (`user_mst`) |
| GET | `api/User/GetList` | `UserController` | `GetList` | `GetUserListInteractor` | UserRepository.GetAll (`user_mst`) |
| GET | `api/User/GetListFunctionPermission` | `UserController` | `GetListFunctionPermission` | `GetListFunctionPermissionInteractor` | UserRepository.GetListFunctionPermission (`user_mst`) |
| GET | `api/User/GetListJobMst` | `UserController` | `GetListJobMst` | `GetListJobMstInteractor` | UserRepository.GetListJob (`user_mst`) |
| GET | `api/User/GetListUserByCurrentUser` | `UserController` | `GetListUserByCurrentUser` | `GetListUserByCurrentUserInteractor` | UserRepository.GetUsersByPermission (`user_mst`)<br/>UserRepository.GetShowRenkeiCd1ColumnSetting (`user_mst`)<br/>UserRepository.GetByUserId (`user_mst`) |
| GET | `api/User/GetPermissionByScreen` | `UserController` | `GetPermissionByScreen` | `GetPermissionByScreenInteractor` | UserRepository.GetPermissionByScreenCode (`user_mst`) |
| GET | `api/User/GetUserInfo` | `UserController` | `GetUserInfo` | `GetUserInfoInteractor` | UserRepository.GetUserInfo (`user_mst`) |
| POST | `api/User/Update` | `UserController` | `Save` | `CreateUserInteractor` | UserRepository.MaxUserId (`user_mst`)<br/>UserRepository.Create (`user_mst`) |
| POST | `api/User/SaveListUserMst` | `UserController` | `SaveListUserMst` | `SaveListUserMstInteractor` | UserRepository.SaveListUserMst (`user_mst`)<br/>UserRepository.ListDepartmentValid (`user_mst`)<br/>UserRepository.ListJobCdValid (`user_mst`)<br/>UserRepository.GetByUserId (`user_mst`)<br/>UserRepository.UserIdIsExistInDb (`user_mst`) |
| GET | `api/User/UpdateHashPassword` | `UserController` | `UpdateHashPassword` | `UpdateHashPasswordInteractor` | UserRepository.UpdateHashPassword (`user_mst`) |
| POST | `api/User/UpsertList` | `UserController` | `Upsert` | `UpsertUserListInteractor` | UserRepository.CheckExistedId (`user_mst`)<br/>UserRepository.CheckExistedUserIdCreate (`user_mst`)<br/>UserRepository.CheckExistedUserIdUpdate (`user_mst`)<br/>UserRepository.CheckExistedLoginIdCreate (`user_mst`)<br/>UserRepository.CheckExistedLoginIdUpdate (`user_mst`)<br/>UserRepository.CheckExistedJobCd (`user_mst`)<br/>UserRepository.Upsert (`user_mst`)<br/>KaRepository.CheckKaId (`ka_inf`) |
| GET | `api/Visiting/Get + "ReceptionLock` | `VisitingController` | `GetList` | `GetReceptionLockInteractor` | ReceptionLockRepository.ReceptionLockModel (`receptionlock_inf`) |
| GET | `api/Visiting/GetList` | `VisitingController` | `GetList` | `GetReceptionLockInteractor` | ReceptionLockRepository.ReceptionLockModel (`receptionlock_inf`) |
| GET | `api/Visiting/Get + "ReceptionInfo` | `VisitingController` | `GetList` | `GetReceptionLockInteractor` | ReceptionLockRepository.ReceptionLockModel (`receptionlock_inf`) |
| GET | `api/Visiting/GetList + "MappingMember` | `VisitingController` | `GetListMappingMember` | `GetMappingMemberInteractor` | ReceptionRepository.GetMappingMeber (`raiin_inf`) |
| GET | `api/Visiting/GetPagingList` | `VisitingController` | `GetPagingList` | `GetReceptionPagingListInteractor` | ReceptionRepository.GetPagingList (`raiin_inf`) |
| GET | `api/Visiting/Get + "Settings` | `VisitingController` | `GetSettings` | `GetReceptionSettingsInteractor` | UserConfRepository.GetList (`userconf_inf`)<br/>SystemConfRepository.GetList (`system_conf`) |
| POST | `api/Visiting/Update + "MappingMember` | `VisitingController` | `LinkMappingMemberRecords` | `UpdateMappingMemberInteractor` | ReceptionRepository.UpdatePatientLinking (`raiin_inf`)<br/>ReceptionRepository.GetRecptionList (`raiin_inf`) |
| POST | `api/Visiting/SaveSettings` | `VisitingController` | `SaveSettings` | `SaveVisitingListSettingsInteractor` | VisitingListSettingRepository.Save (`visiting_list_setting`) |
| PUT | `api/Visiting/SubmitConfirmation` | `VisitingController` | `SubmitConfirmation` | `SubmitConfirmationInteractor` | ReceptionRepository.SubmitConfirmation (`raiin_inf`)<br/>ReceptionRepository.GetRecptionList (`raiin_inf`) |
| PUT | `api/Visiting/Update + "DynamicCell` | `VisitingController` | `UpdateDynamicCellAsync` | `UpdateReceptionDynamicCellInteractor` | RaiinKubunMstRepository.SoftDelete (`raiin_kbn_mst`)<br/>RaiinKubunMstRepository.Upsert (`raiin_kbn_mst`)<br/>ReceptionRepository.GetList (`raiin_inf`) |
| PUT | `api/Visiting/Update + "StaticCell` | `VisitingController` | `UpdateStaticCellAsync` | `UpdateReceptionStaticCellInteractor` | ReceptionRepository.GetRecptionList (`raiin_inf`)<br/>ReceptionRepository.UpdateStatus (`raiin_inf`)<br/>ReceptionRepository.UpdateTreatmentDepartment (`raiin_inf`)<br/>ReceptionRepository.UpdateTantoId (`raiin_inf`)<br/>ReceptionRepository.UpdateMonshinStatus (`raiin_inf`)<br/>RaiinCmtInfRepository.Upsert (`raiincmtinf`)<br/>UserRepository.GetByUserId (`user_mst`)<br/>PatientInforRepository.GetById (`pt_inf`)<br/>RaiinKubunMstRepository.SaveRaiinKbnInfs (`raiin_kbn_mst`)<br/>PtMemoRepository.Upsert (`ptmemo_inf`)<br/>RaiinStatusMstRepository.GetRaiinStatusCountList (`raiinstatusmst`) |
| POST | `api/WeightedSetConfirmation/IsOpenWeightChecking` | `WeightedSetConfirmationController` | `IsOpenWeightChecking` | `IsOpenWeightCheckingInteractor` | SystemConfRepository.GetSettingParams (`system_conf`)<br/>SystemConfRepository.GetSettingValue (`system_conf`) |
| POST | `api/Yousiki/AddYousiki` | `YousikiController` | `AddYousiki` | `AddYousikiInteractor` | YousikiRepository.GetListPtIdHealthInsuranceAccepted (`yousiki_inf`)<br/>YousikiRepository.AddYousikiInfByMonth (`yousiki_inf`)<br/>YousikiRepository.IsYousikiExist (`yousiki_inf`)<br/>PatientInforRepository.IsPatientExist (`pt_inf`) |
| POST | `api/Yousiki/DeleteYousikiInf` | `YousikiController` | `DeleteYousikiInf` | `DeleteYousikiInfInteractor` | YousikiRepository.IsYousikiExist (`yousiki_inf`)<br/>YousikiRepository.DeleteYousikiInf (`yousiki_inf`) |
| GET | `api/Yousiki/GetHistoryYousiki` | `YousikiController` | `GetHistoryYousiki` | `GetHistoryYousikiInteractor` | YousikiRepository.GetHistoryYousiki (`yousiki_inf`)<br/>YousikiRepository.GetKacodeYousikiMstDict (`yousiki_inf`) |
| GET | `api/Yousiki/GetKacodeYousikiMstDict` | `YousikiController` | `GetKacodeYousikiMstDict` | `GetKacodeYousikiMstDictInteractor` | YousikiRepository.GetKacodeYousikiMstDict (`yousiki_inf`) |
| GET | `api/Yousiki/GetVisitingInfs` | `YousikiController` | `GetVisitingInfs` | `GetVisitingInfsInteractor` | YousikiRepository.GetVisitingInfs (`yousiki_inf`) |
| GET | `api/Yousiki/GetYousiki1InfDetails` | `YousikiController` | `GetYousiki1InfDetails` | `GetYousiki1InfDetailsInteractor` | YousikiRepository.GetYousiki1InfDetails (`yousiki_inf`)<br/>YousikiRepository.GetKacodeYousikiMstDict (`yousiki_inf`) |
| GET | `api/Yousiki/GetYousiki1InfDetailsByCodeNo` | `YousikiController` | `GetYousiki1InfDetailsByCodeNo` | `GetYousiki1InfDetailsInteractor` | YousikiRepository.GetYousiki1InfDetails (`yousiki_inf`)<br/>YousikiRepository.GetKacodeYousikiMstDict (`yousiki_inf`) |
| GET | `api/Yousiki/GetYousiki1InfModel` | `YousikiController` | `GetYousiki1InfModel` | `GetYousiki1InfModelInteractor` | YousikiRepository.GetYousiki1InfModel (`yousiki_inf`) |
| GET | `api/Yousiki/GetYousiki1InfModelWithCommonInf` | `YousikiController` | `GetYousiki1InfModelWithCommonInf` | `GetYousiki1InfModelWithCommonInfInteractor` | YousikiRepository.GetYousiki1InfModelWithCommonInf (`yousiki_inf`) |
| POST | `api/Yousiki/SaveDetailDefault` | `YousikiController` | `SaveDetailDefault` | `SaveDetailDefaultInteractor` | YousikiRepository.SaveYousiki1InfDetailDefault (`yousiki_inf`) |
| POST | `api/Yousiki/UpdateYousiki` | `YousikiController` | `UpdateYosiki` | `UpdateYosikiInteractor` | YousikiRepository.UpdateYousiki (`yousiki_inf`) |
