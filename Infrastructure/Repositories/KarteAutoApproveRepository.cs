using System.Linq.Dynamic.Core;
using Domain.Models.KarteApproval;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Redis;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;

namespace Infrastructure.Repositories;

public class KarteAutoApproveRepository : RepositoryBase, IKarteAutoApproveRepository
{
    private readonly IConfiguration _configuration;
    private readonly string key;
    private readonly IDatabase _cache;
    public KarteAutoApproveRepository(ITenantProvider tenantProvider, IConfiguration configuration) : base(tenantProvider)
    {
        key = GetDomainKey();
        _configuration = configuration;
        GetRedis();
        _cache = RedisConnectorHelper.Connection.GetDatabase();
    }

    public void GetRedis()
    {
        string connection =
            string.Concat(_configuration["Redis:RedisHost"], ":", _configuration["Redis:RedisPort"]);
        if (RedisConnectorHelper.RedisHost != connection)
        {
            RedisConnectorHelper.RedisHost = connection;
        }
    }

    public void AutoApproval()
    {
        string key = GetDomainKey() + CacheKeyConstant.KarteAutoApprove;
        if (_cache.KeyExists(key))
        {
            return;
        }

        _cache.StringSet(key, "running", TimeSpan.FromMinutes(30));

        var result = _cache.StringGet(key);
        try
        {
            DateTime nowUtc = DateTime.UtcNow;

            // Get all yesterday raiin info and map to new approval info entities
            var raiinInfs = NoTrackingDataContext.RaiinInfs
                    .Where(item => item.IsDeleted == DeleteTypes.None && (
                                        item.CreateDate <= nowUtc ||
                                        item.UpdateDate <= nowUtc
                                    ))
                    .ToList();

            var raiinNoList = raiinInfs.Select(raiinInf => raiinInf.RaiinNo).Distinct();

            var validKarteEdition =
                TrackingDataContext.KarteEditions.Where(e => e.KarteStatus == 1 && raiinNoList.Contains(e.RaiinNo) && e.IsDeleted == DeleteTypes.None).ToList();


            // Get all yesterday approval info include both approved and have not approved
            var approvalInfoList = TrackingDataContext.ApprovalInfs
                .Where(item => item.CreateDate <= nowUtc && (item.UpdateDate == null || item.UpdateDate <= nowUtc)
                ).ToList();

            var updateApprovalInfList = new List<ApprovalInf>();
            foreach (var item in raiinInfs)
            {
                var existKarteEdition = validKarteEdition.Where(e => e.RaiinNo == item.RaiinNo && e.HpId == item.HpId).OrderByDescending(e => e.Edition).FirstOrDefault();
                if (existKarteEdition == null)
                {
                    continue;
                }

                var existApprovalInfo =
                    approvalInfoList.Where(a => a.HpId == item.HpId && a.RaiinNo == item.RaiinNo && a.SinDate == item.SinDate)
                        .OrderByDescending(a => a.UpdateDate)
                        .ThenByDescending(a => a.CreateDate)
                        .FirstOrDefault();

                // Add new when raiin info has no approval inf
                if (existApprovalInfo == null)
                {
                    var addApprovalInf = TrackingDataContext.ApprovalInfs.Add(
                        new ApprovalInf()
                        {
                            HpId = item.HpId,
                            PtId = item.PtId,
                            RaiinNo = item.RaiinNo,
                            SinDate = item.SinDate,
                            IsDeleted = DeleteTypes.None,
                            CreateId = item.TantoId,
                            CreateDate = CIUtil.GetJapanDateTimeNow()
                        });

                    TrackingDataContext.SaveChanges();
                    existKarteEdition.ApprovalId = addApprovalInf.Entity.Id;
                    existKarteEdition.ApprovalDate = CIUtil.GetJapanDateTimeNow();
                }
                // Update deleted to 0 when raiin info has a deleted (waiting) approval inf
                else if (existApprovalInfo.IsDeleted == DeleteTypes.Deleted)
                {
                    existApprovalInfo.IsDeleted = DeleteTypes.None;
                    existApprovalInfo.UpdateId = item.TantoId;
                    existApprovalInfo.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    updateApprovalInfList.Add(existApprovalInfo);
                    existKarteEdition.ApprovalId = existApprovalInfo.Id;
                    existKarteEdition.ApprovalDate = CIUtil.GetJapanDateTimeNow();
                }
                // If raiin inf has been approved, skip and go next
            }

            if (updateApprovalInfList.Any())
            {
                TrackingDataContext.ApprovalInfs.UpdateRange(updateApprovalInfList);
            }

            TrackingDataContext.SaveChanges();
        }
        finally
        {
            _cache.KeyDelete(key);
        }
    }

    public void ReleaseResource()
    {
        DisposeDataContext();
    }
}
