﻿using Domain.Constant;
using Domain.Models;
using Domain.Models.CalculationInf;
using Domain.Models.GroupInf;
using Domain.Models.Insurance;
using Domain.Models.InsuranceInfor;
using Domain.Models.InsuranceMst;
using Domain.Models.MaxMoney;
using Domain.Models.NewFolder;
using Domain.Models.Online;
using Domain.Models.PatientInfor;
using Domain.Models.PortalCustomer;
using Domain.Models.PortalCustomerFile;
using Domain.Models.PortalCustomerLogin;
using Domain.Models.Reception;
using Domain.Models.TodayOdr;
using Entity.Tenant;
using GraphQL.Client.Abstractions.Utilities;
using Helper.Common;
using Helper.Constants;
using Helper.Enum;
using Helper.Extension;
using Helper.Mapping;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using System.Runtime.CompilerServices;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using HokenInfModel = Domain.Models.Insurance.HokenInfModel;
using HonkeKbn = Domain.Models.PatientInfor.HonkeKbn;
using HokenInfo = Domain.Models.PatientInfor.HokenInfo;

namespace Infrastructure.Repositories
{
    public class PatientInforRepository : RepositoryBase, IPatientInforRepository
    {
        private const string startGroupOrderKey = "group_";
        private readonly IReceptionRepository _receptionRepository;
        public PatientInforRepository(ITenantProvider tenantProvider, IReceptionRepository receptionRepository) : base(tenantProvider)
        {
            _receptionRepository = receptionRepository;
        }

        (PatientInforModel ptInfModel, bool isFound) IPatientInforRepository.SearchExactlyPtNum(long ptNum, int hpId, int sinDate)
        {
            var ptInf = NoTrackingDataContext.PtInfs.Where(x => x.HpId == hpId && x.PtNum == ptNum && x.IsDelete == 0).FirstOrDefault();
            if (ptInf == null)
            {
                return (new PatientInforModel(), false);
            }

            long ptId = ptInf.PtId;

            //Get ptMemo
            string memo = string.Empty;
            PtMemo? ptMemo = NoTrackingDataContext.PtMemos.Where(x => x.PtId == ptId && x.HpId == hpId).FirstOrDefault();
            if (ptMemo != null)
            {
                memo = ptMemo.Memo ?? string.Empty;
            }

            var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                             item.PtId == ptId &&
                                                                             item.IsDeleted == DeleteTypes.None)
                                                              .OrderByDescending(item => item.SinDate);

            var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                     item.PtId == ptId &&
                                                                                     item.IsDeleted == DeleteTypes.None &&
                                                                                     item.KarteStatus == KarteStatusConst.Official);

            var lastVisitDate = (from raiinInf in listRaiinInf
                                 join karteEdition in listKarteEdition on
                                     raiinInf.RaiinNo equals karteEdition.RaiinNo
                                 select raiinInf)
                                .OrderByDescending(item => item.SinDate)
                                .Select(r => r.SinDate)
                                .FirstOrDefault();

            PatientInforModel ptInfModel = ToModel(ptInf, memo, lastVisitDate, sinDate);

            return new(ptInfModel, true);
        }

        public List<PatientInforModel> SearchContainPtNum(long ptNum, string keyword, int hpId, int pageIndex, int pageSize, Dictionary<string, string> sortData)
        {
            List<PatientInforModel> result = new();
            var karteEditions = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                  item.KarteStatus == KarteStatusConst.Official &&
                                                                                  item.IsDeleted == DeleteTypes.None);

            var ptInfWithLastVisitDate =
                from p in NoTrackingDataContext.PtInfs
                where p.HpId == hpId && p.IsDelete == 0 && (p.PtNum == ptNum || (p.KanaName != null && p.KanaName.Contains(keyword)) || (p.Name != null && p.Name.Contains(keyword)))
                orderby p.PtNum descending
                select new PatientInfQueryModel
                {
                    PtInf = p,
                    LastVisitDate = (
                        from r in NoTrackingDataContext.RaiinInfs
                        where r.HpId == hpId
                            && r.PtId == p.PtId
                            && r.IsDeleted == DeleteTypes.None
                        join k in karteEditions on
                            r.RaiinNo equals k.RaiinNo
                        orderby r.SinDate descending
                        select r.SinDate
                    ).FirstOrDefault()
                };
            bool sortGroup = sortData.Select(item => item.Key).ToList().Exists(item => item.StartsWith(startGroupOrderKey));
            result = sortGroup
                         ?
                         ptInfWithLastVisitDate
                         .AsEnumerable()
                         .Select(p => ToModel(p.PtInf, string.Empty, p.LastVisitDate))
                         .ToList()
                         :
                         SortData(ptInfWithLastVisitDate, sortData, pageIndex, pageSize);
            return result;
        }

        public PatientInforModel? GetById(int hpId, long ptId, int sinDate, long raiinNo, bool isShowKyuSeiName = false, List<int>? listStatus = null)
        {
            var itemData = NoTrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId);


            // Raiin Count
            // status = RaiinState Receptionist
            int raiinCount = NoTrackingDataContext.RaiinInfs.Count(u => u.HpId == hpId &&
                                                                        u.SinDate == sinDate &&
                                                                        u.RaiinNo != raiinNo &&
                                                                        u.IsDeleted == DeleteTypes.None &&
                                                                        (listStatus != null ? listStatus.Contains(u.Status) : u.Status == RaiinState.Receptionist));
            if (itemData == null)
            {
                return new PatientInforModel(raiinCount);
            }

            //Get ptMemo
            string memo = string.Empty;
            PatientMemo? ptMemo = NoTrackingDataContext.PatientMemos.FirstOrDefault(x => x.HpId == hpId && x.PtId == itemData.PtId && x.IsDeleted == DeleteTypes.None);
            if (ptMemo != null)
            {
                memo = ptMemo.MemoHtml ?? string.Empty;
            }

            //Get lastVisitDate
            int lastVisitDate = _receptionRepository.GetLastVisit(hpId, ptId, sinDate, true)?.SinDate ?? 0;

            //Get First Visit Date
            int firstDate = _receptionRepository.GetFirstVisitWithSyosin(hpId, ptId, sinDate);
            string comment = NoTrackingDataContext.PtCmtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == 0)?.Text ?? string.Empty;

            var name = itemData.Name ?? string.Empty;
            var kanaName = itemData.KanaName ?? string.Empty;
            bool isKyuSeiName = false;
            if (isShowKyuSeiName)
            {
                var ptKyusei = NoTrackingDataContext.PtKyuseis.Where(item => item.HpId == hpId
                                                                            && item.PtId == ptId
                                                                            && item.EndDate >= sinDate
                                                                            && item.IsDeleted != 1)
                                                              .OrderBy(x => x.EndDate)
                                                              .FirstOrDefault();
                if (ptKyusei != null)
                {
                    name = ptKyusei.Name;
                    kanaName = ptKyusei.KanaName;
                    isKyuSeiName = true;
                }
            }
            return new PatientInforModel(
                itemData.HpId,
                itemData.PtId,
                itemData.ReferenceNo,
                itemData.SeqNo,
                itemData.PtNum,
                kanaName ?? string.Empty,
                name ?? string.Empty,
                itemData.Sex,
                itemData.Birthday,
                itemData.LimitConsFlg,
                itemData.IsDead,
                itemData.DeathDate,
                itemData.HomePost ?? string.Empty,
                itemData.HomeAddress1 ?? string.Empty,
                itemData.HomeAddress2 ?? string.Empty,
                itemData.Tel1 ?? string.Empty,
                itemData.Tel2 ?? string.Empty,
                itemData.Mail ?? string.Empty,
                itemData.Setanusi ?? string.Empty,
                itemData.Zokugara ?? string.Empty,
                itemData.Job ?? string.Empty,
                itemData.RenrakuName ?? string.Empty,
                itemData.RenrakuPost ?? string.Empty,
                itemData.RenrakuAddress1 ?? string.Empty,
                itemData.RenrakuAddress2 ?? string.Empty,
                itemData.RenrakuTel ?? string.Empty,
                itemData.RenrakuMemo ?? string.Empty,
                itemData.OfficeName ?? string.Empty,
                itemData.OfficePost ?? string.Empty,
                itemData.OfficeAddress1 ?? string.Empty,
                itemData.OfficeAddress2 ?? string.Empty,
                itemData.OfficeTel ?? string.Empty,
                itemData.OfficeMemo ?? string.Empty,
                itemData.IsRyosyoDetail,
                itemData.PrimaryDoctor,
                itemData.IsTester,
                itemData.MainHokenPid,
                memo,
                lastVisitDate,
                firstDate,
                raiinCount,
                comment,
                sinDate,
                renrakuName2: itemData.RenrakuName2 ?? string.Empty,
                renrakuTel2: itemData.RenrakuTel2 ?? string.Empty,
                portalCustomerId: itemData.PortalCustomerId,
                isShowKyuSeiName: isKyuSeiName);
        }

        public bool CheckExistIdList(int hpId, List<long> ptIds)
        {
            ptIds = ptIds.Distinct().ToList();
            var countPtInfs = NoTrackingDataContext.PtInfs.Count(x => x.HpId == hpId && ptIds.Contains(x.PtId) && x.IsDelete != 1);
            return ptIds.Count == countPtInfs;
        }

        public List<PatientInforModel> SearchSimple(string keyword, bool isContainMode, int hpId)
        {
            long ptNum = keyword.AsLong();
            var karteEditions = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                  item.KarteStatus == KarteStatusConst.Official &&
                                                                                  item.IsDeleted == DeleteTypes.None);

            var ptInfWithLastVisitDate =
                from p in NoTrackingDataContext.PtInfs
                where p.HpId == hpId && p.IsDelete == 0 && (p.PtNum == ptNum || isContainMode && ((p.KanaName != null && p.KanaName.Contains(keyword)) || (p.Name != null && p.Name.Contains(keyword))))
                select new
                {
                    ptInf = p,
                    lastVisitDate = (
                        from r in NoTrackingDataContext.RaiinInfs
                        where r.HpId == hpId
                            && r.PtId == p.PtId
                            && r.IsDeleted == DeleteTypes.None
                        join k in karteEditions on
                            r.RaiinNo equals k.RaiinNo
                        orderby r.SinDate descending
                        select r.SinDate
                    ).FirstOrDefault()
                };

            return ptInfWithLastVisitDate.AsEnumerable().Select(p => ToModel(p.ptInf, string.Empty, p.lastVisitDate)).ToList();
        }

        public List<PatientInforModel> GetAdvancedSearchResults(PatientAdvancedSearchInput input, int hpId, int pageIndex, int pageSize, Dictionary<string, string> sortData)
        {
            bool sortGroup = sortData.Select(item => item.Key).ToList().Exists(item => item.StartsWith(startGroupOrderKey));

            var japanNow = CIUtil.GetJapanDateTimeNow();
            var ptInfQuery = NoTrackingDataContext.PtInfs
             .Where(pt => pt.HpId == hpId && pt.IsDelete == DeleteTypes.None)
             .Select(pt => new
             {
                 PtInf = pt,
                 NextAppointmentDate = (from rd in NoTrackingDataContext.ReserveDetails
                                        join e in NoTrackingDataContext.ExamTimeSlots
                                        on rd.ExamTimeSlotId equals e.ExamTimeSlotId into eGroup
                                        from e in eGroup.DefaultIfEmpty()
                                        where rd.PatientId == pt.PtId && rd.IsDeleted == 0 &&
                                              rd.Status == 0 && e.ExamStartDate > japanNow
                                        orderby e.ExamStartDate
                                        select (DateTime?)e.ExamStartDate).FirstOrDefault(),
                 LastAppointmentDate = (from rd in NoTrackingDataContext.ReserveDetails
                                        join e in NoTrackingDataContext.ExamTimeSlots
                                       on rd.ExamTimeSlotId equals e.ExamTimeSlotId into eGroup
                                        from e in eGroup.DefaultIfEmpty()
                                        where rd.PatientId == pt.PtId && rd.IsDeleted == 0 &&
                                             rd.Status == 2 && e.ExamStartDate < japanNow
                                        orderby e.ExamStartDate descending
                                        select (DateTime?)e.ExamStartDate).FirstOrDefault()
             });

            // PtNum
            if (input.FromPtNum > 0)
            {
                ptInfQuery = ptInfQuery.Where(p => p.PtInf.PtNum >= input.FromPtNum);
            }
            if (input.ToPtNum > 0)
            {
                ptInfQuery = ptInfQuery.Where(p => p.PtInf.PtNum <= input.ToPtNum);
            }
            // Name
            if (!string.IsNullOrEmpty(input.Name))
            {
                ptInfQuery = ptInfQuery.Where(p =>
                    (p.PtInf.Name != null && p.PtInf.Name.Contains(input.Name))
                    || (p.PtInf.KanaName != null && p.PtInf.KanaName.Contains(input.Name))
                    || (p.PtInf.Name != null && p.PtInf.Name.Replace(" ", string.Empty).Replace("\u3000", string.Empty).Contains(input.Name))
                    || (p.PtInf.KanaName != null && p.PtInf.KanaName.Replace(" ", string.Empty).Replace("\u3000", string.Empty).Contains(input.Name)));
            }
            // Sex
            if (input.Sex > 0)
            {
                ptInfQuery = ptInfQuery.Where(p => p.PtInf.Sex == input.Sex);
            }
            // BirthDay
            if (input.FromBirthDay > 0)
            {
                ptInfQuery = ptInfQuery.Where(p => p.PtInf.Birthday >= input.FromBirthDay);

            }
            if (input.ToBirthDay > 0)
            {
                ptInfQuery = ptInfQuery.Where(p => p.PtInf.Birthday <= input.ToBirthDay);

            }
            // PhoneNum
            if (!string.IsNullOrEmpty(input.PhoneNum))
            {
                ptInfQuery = ptInfQuery.Where(p => p.PtInf.Tel1!.Contains(input.PhoneNum) || p.PtInf.Tel2!.Contains(input.PhoneNum));
            }
            // Age
            if (input.FromAge > 0)
            {
                int fromBirthDay = GetBirthDayFromAge(input.FromAge);
                ptInfQuery = ptInfQuery.Where(p => p.PtInf.Birthday <= fromBirthDay);
            }
            if (input.ToAge > 0)
            {
                int toBirthDay = GetBirthDayFromAge(input.ToAge);
                ptInfQuery = ptInfQuery.Where(p => p.PtInf.Birthday >= toBirthDay);
            }
            // PostalCode
            if (!string.IsNullOrEmpty(input.HomePost))
            {
                ptInfQuery = ptInfQuery.Where(p => p.PtInf.HomePost!.Contains(input.HomePost));
            }
            // Address
            if (input.Address != string.Empty)
            {
                ptInfQuery = ptInfQuery.Where(p => p.PtInf.HomeAddress1!.Contains(input.Address) || p.PtInf.HomeAddress2!.Contains(input.Address));
            }

            // End simple search
            // Check if we can end the search here
            if (!ptInfQuery.Any()) return new();

            // Continue the search in the related tables. This is the slowest part.
            // VisitDate
            var raiinInfQuery = NoTrackingDataContext.RaiinInfs.Where(r => r.HpId == hpId && r.IsDeleted == DeleteTypes.None);
            var karteEditions = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                  item.KarteStatus == KarteStatusConst.Official &&
                                                                                  item.IsDeleted == DeleteTypes.None);

            if (input.FromVisitDate > 0 || input.ToVisitDate > 0)
            {
                var ptIdsBySinDateQuery = raiinInfQuery;
                if (input.FromVisitDate > 0)
                {
                    ptIdsBySinDateQuery = ptIdsBySinDateQuery.Where(r => r.SinDate >= input.FromVisitDate);
                }
                if (input.ToVisitDate > 0)
                {
                    ptIdsBySinDateQuery = ptIdsBySinDateQuery.Where(r => r.SinDate <= input.ToVisitDate);
                }

                var ptIds = ptIdsBySinDateQuery.Select(r => r.PtId).Distinct().ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }

            // LastVisitDate
            if (input.FromLastVisitDate > 0 || input.ToLastVisitDate > 0)
            {
                var lastVisitDateByPtIdQuery =
                    from raiinInf in raiinInfQuery
                    group raiinInf by raiinInf.PtId into raiinInfGroup
                    select new
                    {
                        ptId = raiinInfGroup.Key,
                        lastVisitDate = (
                            from r in raiinInfQuery
                            where r.PtId == raiinInfGroup.Key
                            join k in karteEditions on
                                r.RaiinNo equals k.RaiinNo
                            orderby r.SinDate descending
                            select r.SinDate
                        ).FirstOrDefault()
                    };

                if (input.FromLastVisitDate > 0)
                {
                    lastVisitDateByPtIdQuery = lastVisitDateByPtIdQuery.Where(x => x.lastVisitDate >= input.FromLastVisitDate);
                }
                if (input.ToLastVisitDate > 0)
                {
                    lastVisitDateByPtIdQuery = lastVisitDateByPtIdQuery.Where(x => x.lastVisitDate <= input.ToLastVisitDate);
                }

                var ptIds = lastVisitDateByPtIdQuery.Select(x => x.ptId).ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }

            // InsuranceNum
            // Declare variable here to be able to reuse it in another queries
            IEnumerable<PtHokenInf>? ptHokenInfs = null;
            if (input.FromInsuranceNum > 0 || input.ToInsuranceNum > 0)
            {
                ptHokenInfs = GetPtHokenInfs();
                var ptIdsByInsuranceNumQuery = ptHokenInfs.Where(p => !string.IsNullOrEmpty(p.HokensyaNo));
                if (input.FromInsuranceNum > 0)
                {
                    ptIdsByInsuranceNumQuery = ptIdsByInsuranceNumQuery.Where(p => long.Parse(p.HokensyaNo!) >= input.FromInsuranceNum);
                }
                if (input.ToInsuranceNum > 0)
                {
                    ptIdsByInsuranceNumQuery = ptIdsByInsuranceNumQuery.Where(p => long.Parse(p.HokensyaNo!) <= input.ToInsuranceNum);
                }

                var ptIds = ptIdsByInsuranceNumQuery.Select(p => p.PtId).Distinct().ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }
            // PublicExpensesNum
            // Declare variable here to be able to reuse it in another queries
            IEnumerable<PtKohi>? ptKohis = null;
            if (input.FromPublicExpensesNum > 0 || input.ToPublicExpensesNum > 0)
            {
                ptKohis = GetPtKohis();
                var ptIdsByPublicExpensesNumQuery = ptKohis.Where(p => !string.IsNullOrEmpty(p.FutansyaNo));
                if (input.FromPublicExpensesNum > 0)
                {
                    ptIdsByPublicExpensesNumQuery = ptIdsByPublicExpensesNumQuery.Where(p => long.Parse(p.FutansyaNo!) >= input.FromPublicExpensesNum);
                }
                if (input.ToPublicExpensesNum > 0)
                {
                    ptIdsByPublicExpensesNumQuery = ptIdsByPublicExpensesNumQuery.Where(p => long.Parse(p.FutansyaNo!) <= input.ToPublicExpensesNum);
                }

                var ptIds = ptIdsByPublicExpensesNumQuery.Select(p => p.PtId).Distinct().ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }
            // SpecialPublicExpensesNum
            if (!string.IsNullOrEmpty(input.FromSpecialPublicExpensesNum) || !string.IsNullOrEmpty(input.ToSpecialPublicExpensesNum))
            {
                if (ptKohis is null)
                {
                    ptKohis = GetPtKohis();
                }

                var ptIdsBySpecialPublicExpensesNumQuery = ptKohis.Where(p => !string.IsNullOrEmpty(p.TokusyuNo));
                if (!string.IsNullOrEmpty(input.FromSpecialPublicExpensesNum))
                {
                    ptIdsBySpecialPublicExpensesNumQuery = ptIdsBySpecialPublicExpensesNumQuery.Where(p => p.TokusyuNo!.PadLeft(20, '0').CompareTo(input.FromSpecialPublicExpensesNum.PadLeft(20, '0')) >= 0);
                }
                if (!string.IsNullOrEmpty(input.ToSpecialPublicExpensesNum))
                {
                    ptIdsBySpecialPublicExpensesNumQuery = ptIdsBySpecialPublicExpensesNumQuery.Where(p => p.TokusyuNo!.PadLeft(20, '0').CompareTo(input.ToSpecialPublicExpensesNum.PadLeft(20, '0')) <= 0);
                }

                var ptIds = ptIdsBySpecialPublicExpensesNumQuery.Select(p => p.PtId).Distinct().ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }
            // HokenNum
            if (input.HokenNum > 0)
            {
                if (ptHokenInfs is null)
                {
                    ptHokenInfs = GetPtHokenInfs();
                }

                var ptIds = ptHokenInfs.Where(p => p.HokenNo == input.HokenNum).Select(p => p.PtId).Distinct().ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }
            // Kohi
            if (input.Kohi1Num > 0)
            {
                var ptIds = GetPtIdsByKohi(input.Kohi1Num, input.Kohi1EdaNo);
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }
            if (input.Kohi2Num > 0)
            {
                var ptIds = GetPtIdsByKohi(input.Kohi2Num, input.Kohi2EdaNo);
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }
            if (input.Kohi3Num > 0)
            {
                var ptIds = GetPtIdsByKohi(input.Kohi3Num, input.Kohi3EdaNo);
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }
            if (input.Kohi4Num > 0)
            {
                var ptIds = GetPtIdsByKohi(input.Kohi4Num, input.Kohi4EdaNo);
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }

            // Search with TenMst
            var listTenMstSearch = input.TenMsts;
            if (listTenMstSearch.Count > 0)
            {
                var odrInf = NoTrackingDataContext.OdrInfs.Where(x => x.HpId == hpId && x.IsDeleted == 0);
                int index = 0;
                IQueryable<long> ptOdrDetailTemp = Enumerable.Empty<long>().AsQueryable();
                while (index < listTenMstSearch.Count)
                {
                    var item = listTenMstSearch[index];
                    bool isComment = item.IsComment;
                    var ptOdrDetailNexts = NoTrackingDataContext.OdrInfDetails.Where(odr => odrInf.Any(x => x.HpId == odr.HpId
                                                                                                             && x.PtId == odr.PtId
                                                                                                             && x.SinDate == odr.SinDate
                                                                                                             && x.RaiinNo == odr.RaiinNo
                                                                                                             && x.RpNo == odr.RpNo
                                                                                                             && x.RpEdaNo == odr.RpEdaNo
                                                                                                             && (isComment ? odr.ItemCd != null && odr.ItemName != null && odr.ItemName.Contains(item.InputName)
                                                                                                                            : odr.ItemCd != null && odr.ItemCd == item.ItemCd)))
                                                                           .Select(x => x.PtId).Distinct();
                    if (index == 0)
                    {
                        ptOdrDetailTemp = NoTrackingDataContext.OdrInfDetails.Where(odr => odrInf.Any(x => x.HpId == odr.HpId
                                                                                                             && x.PtId == odr.PtId
                                                                                                             && x.SinDate == odr.SinDate
                                                                                                             && x.RaiinNo == odr.RaiinNo
                                                                                                             && x.RpNo == odr.RpNo
                                                                                                             && x.RpEdaNo == odr.RpEdaNo
                                                                                                             && (isComment ? odr.ItemCd != null && odr.ItemName != null && odr.ItemName.Contains(item.InputName)
                                                                                                                            : odr.ItemCd != null && odr.ItemCd == item.ItemCd)))
                                                                          .Select(x => x.PtId).Distinct();
                    }
                    else
                    {
                        ptOdrDetailTemp = input.OrderLogicalOperator switch
                        {
                            LogicalOperator.And => ptOdrDetailTemp.Where(odr => ptOdrDetailNexts.Any(x => x == odr)),
                            LogicalOperator.Or => ptOdrDetailTemp.Union(ptOdrDetailNexts).Distinct(),
                            _ => ptOdrDetailTemp
                        };
                    }

                    index++;
                }

                // get ptId list from ptOdrDetailTemp
                var ptIdList = ptOdrDetailTemp.Distinct().ToList();
                ptInfQuery = ptInfQuery.Where(pt => ptIdList.Contains(pt.PtInf.PtId));
            }

            // Department
            if (input.DepartmentId > 0)
            {
                var ptIds = raiinInfQuery.Where(r => r.KaId == input.DepartmentId).Select(r => r.PtId).Distinct().ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }
            // Doctor
            if (input.DoctorId > 0)
            {
                var ptIds = raiinInfQuery.Where(r => r.TantoId == input.DoctorId).Select(r => r.PtId).Distinct().ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }

            // Treatment Department Id
            if (input.TreatmentDepartmentId > 0)
            {
                var ptIds = raiinInfQuery.Where(r => r.TreatmentDepartmentId == input.TreatmentDepartmentId).Select(r => r.PtId).Distinct().ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }

            // Byomeis
            var ptByomeiQuery = NoTrackingDataContext.PtByomeis.Where(b => b.HpId == hpId && b.IsDeleted == DeleteTypes.None);
            if (input.Byomeis.Any())
            {
                var trimmedByomeis = input.Byomeis.Select(b => new ByomeiSearchInput(b.Code.Trim(), b.Name.Trim(), b.IsFreeWord)).ToList();
                IQueryable<long> ptIdsByByomeisQuery = null!;
                for (int i = 0; i < trimmedByomeis.Count; i++)
                {
                    var byomei = trimmedByomeis[i];
                    var ptIdsByByomeiItemQuery = ptByomeiQuery.Where(p =>
                        p.HpId == hpId
                        && (byomei.IsFreeWord ? p.ByomeiCd!.Trim() == ByomeiConstant.FreeWordCode : p.ByomeiCd!.Trim() == byomei.Code)
                        && (!byomei.IsFreeWord || p.Byomei!.Trim().Contains(byomei.Name))
                        && (input.ResultKbn == -1 || p.TenkiKbn == input.ResultKbn)
                        && (input.ByomeiStartDate <= 0 || p.StartDate >= input.ByomeiStartDate)
                        && (input.ByomeiEndDate <= 0 || p.StartDate <= input.ByomeiEndDate)
                        && (!input.IsSuspectedDisease
                            || p.SyusyokuCd1!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd2!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd3!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd4!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd5!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd6!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd7!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd8!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd9!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd10!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd11!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd12!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd13!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd14!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd15!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd16!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd11!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd18!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd19!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd20!.Trim() == ByomeiConstant.SuspectedCode
                            || p.SyusyokuCd21!.Trim() == ByomeiConstant.SuspectedCode))
                        .Select(p => p.PtId).Distinct();

                    if (i == 0)
                    {
                        // Initialize
                        ptIdsByByomeisQuery = ptIdsByByomeiItemQuery;
                    }
                    else
                    {
                        if (input.ByomeiLogicalOperator == LogicalOperator.Or)
                        {
                            ptIdsByByomeisQuery = ptIdsByByomeisQuery!.Union(ptIdsByByomeiItemQuery);
                        }
                        else if (input.ByomeiLogicalOperator == LogicalOperator.And)
                        {
                            ptIdsByByomeisQuery = ptIdsByByomeisQuery!.Intersect(ptIdsByByomeiItemQuery);
                        }
                    }
                }

                var ptIds = ptIdsByByomeisQuery.ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }
            // ResultKbn
            if (input.ResultKbn != -1 && input.Byomeis.Count == 0)
            {
                var ptIds = ptByomeiQuery.Where(p => p.TenkiKbn == input.ResultKbn).Select(p => p.PtId).Distinct().ToList();
                if (ptIds.Count == 0) return new();
                ptInfQuery = ptInfQuery.Where(p => ptIds.Contains(p.PtInf.PtId));
            }

            // Add LastVisitDate to patient info
            var ptInfWithLastVisitDateQuery =
                from ptInf in ptInfQuery
                select new PatientInfQueryModel
                {
                    PtInf = ptInf.PtInf,
                    NextAppointmentDepartment = ptInf.NextAppointmentDate,
                    LastAppointmentDepartment = ptInf.LastAppointmentDate,
                    LastVisitDate = (
                        from r in raiinInfQuery
                        where r.PtId == ptInf.PtInf.PtId
                        join k in karteEditions on
                            r.RaiinNo equals k.RaiinNo
                        orderby r.SinDate descending
                        select r.SinDate
                    ).FirstOrDefault()
                };

            var result = sortGroup
                         ?
                         ptInfWithLastVisitDateQuery
                         .AsEnumerable()
                         .Select(p => ToPatientDenrakuModel(p.PtInf, string.Empty, p.LastVisitDate, p.LastAppointmentDepartment, p.NextAppointmentDepartment))
                         .ToList()
                         :
                         SortData(ptInfWithLastVisitDateQuery, sortData, pageIndex, pageSize);
            return result;

            #region Helper methods

            List<long> GetPtIdsByKohi(int kohiNum, int kohiEdaNo)
            {
                if (ptKohis is null)
                {
                    ptKohis = GetPtKohis();
                }

                return ptKohis.Where(p => p.HokenNo == kohiNum && p.HokenEdaNo == kohiEdaNo).Select(p => p.PtId).Distinct().ToList();
            }

            int GetBirthDayFromAge(int age)
            {
                var bithDay = CIUtil.GetJapanDateTimeNow().AddYears(-age);
                return CIUtil.ShowSDateToSDate(bithDay.ToString("yyyyMMdd"));
            }

            IEnumerable<PtHokenInf> GetPtHokenInfs()
            {
                return NoTrackingDataContext.PtHokenInfs.Where(p => p.HpId == hpId && p.IsDeleted == DeleteTypes.None).AsEnumerable();
            }

            IEnumerable<PtKohi> GetPtKohis()
            {
                return NoTrackingDataContext.PtKohis.Where(p => p.HpId == hpId && p.IsDeleted == DeleteTypes.None).AsEnumerable();
            }

            #endregion
        }

        public List<TokkiMstModel> GetListTokki(int sinDate)
        {
            return NoTrackingDataContext.TokkiMsts
                    .Where(entity => entity.StartDate <= sinDate && entity.EndDate >= sinDate)
                    .OrderBy(entity => entity.TokkiCd)
                    .Select(x => new TokkiMstModel(x.TokkiCd, x.TokkiName ?? string.Empty))
                    .ToList();
        }

        private PatientInforModel ToModel(PtInf p, string memo, int lastVisitDate, int sinDate)
        {
            return new PatientInforModel(
                p.HpId,
                p.PtId,
                p.ReferenceNo,
                p.SeqNo,
                p.PtNum,
                p.KanaName ?? string.Empty,
                p.Name ?? string.Empty,
                p.Sex,
                p.Birthday,
                p.LimitConsFlg,
                p.IsDead,
                p.DeathDate,
                p.HomePost ?? string.Empty,
                p.HomeAddress1 ?? string.Empty,
                p.HomeAddress2 ?? string.Empty,
                p.Tel1 ?? string.Empty,
                p.Tel2 ?? string.Empty,
                p.Mail ?? string.Empty,
                p.Setanusi ?? string.Empty,
                p.Zokugara ?? string.Empty,
                p.Job ?? string.Empty,
                p.RenrakuName ?? string.Empty,
                p.RenrakuPost ?? string.Empty,
                p.RenrakuAddress1 ?? string.Empty,
                p.RenrakuAddress2 ?? string.Empty,
                p.RenrakuTel ?? string.Empty,
                p.RenrakuMemo ?? string.Empty,
                p.OfficeName ?? string.Empty,
                p.OfficePost ?? string.Empty,
                p.OfficeAddress1 ?? string.Empty,
                p.OfficeAddress2 ?? string.Empty,
                p.OfficeTel ?? string.Empty,
                p.OfficeMemo ?? string.Empty,
                p.IsRyosyoDetail,
                p.PrimaryDoctor,
                p.IsTester,
                p.MainHokenPid,
                memo,
                lastVisitDate,
                0,
                0,
                string.Empty,
                sinDate);
        }

        private PatientInforModel ToModel(PtInf p, string memo, int lastVisitDate)
        {
            return new PatientInforModel(
                p.HpId,
                p.PtId,
                p.ReferenceNo,
                p.SeqNo,
                p.PtNum,
                p.KanaName ?? string.Empty,
                p.Name ?? string.Empty,
                p.Sex,
                p.Birthday,
                p.LimitConsFlg,
                p.IsDead,
                p.DeathDate,
                p.HomePost ?? string.Empty,
                p.HomeAddress1 ?? string.Empty,
                p.HomeAddress2 ?? string.Empty,
                p.Tel1 ?? string.Empty,
                p.Tel2 ?? string.Empty,
                p.Mail ?? string.Empty,
                p.Setanusi ?? string.Empty,
                p.Zokugara ?? string.Empty,
                p.Job ?? string.Empty,
                p.RenrakuName ?? string.Empty,
                p.RenrakuPost ?? string.Empty,
                p.RenrakuAddress1 ?? string.Empty,
                p.RenrakuAddress2 ?? string.Empty,
                p.RenrakuTel ?? string.Empty,
                p.RenrakuMemo ?? string.Empty,
                p.OfficeName ?? string.Empty,
                p.OfficePost ?? string.Empty,
                p.OfficeAddress1 ?? string.Empty,
                p.OfficeAddress2 ?? string.Empty,
                p.OfficeTel ?? string.Empty,
                p.OfficeMemo ?? string.Empty,
                p.IsRyosyoDetail,
                p.PrimaryDoctor,
                p.IsTester,
                p.MainHokenPid,
                memo,
                lastVisitDate,
                0,
                0,
                string.Empty,
                0);
        }

        private PatientInforModel ToPatientDenrakuModel(PtInf p, string memo, int lastVisitDate, DateTime? lastAppointmentDate, DateTime? nextAppointmentDate)
        {
            return new PatientInforModel(
                p.HpId,
                p.PtId,
                p.ReferenceNo,
                p.SeqNo,
                p.PtNum,
                p.KanaName ?? string.Empty,
                p.Name ?? string.Empty,
                p.Sex,
                p.Birthday,
                p.LimitConsFlg,
                p.IsDead,
                p.DeathDate,
                p.HomePost ?? string.Empty,
                p.HomeAddress1 ?? string.Empty,
                p.HomeAddress2 ?? string.Empty,
                p.Tel1 ?? string.Empty,
                p.Tel2 ?? string.Empty,
                p.Mail ?? string.Empty,
                p.Setanusi ?? string.Empty,
                p.Zokugara ?? string.Empty,
                p.Job ?? string.Empty,
                p.RenrakuName ?? string.Empty,
                p.RenrakuPost ?? string.Empty,
                p.RenrakuAddress1 ?? string.Empty,
                p.RenrakuAddress2 ?? string.Empty,
                p.RenrakuTel ?? string.Empty,
                p.RenrakuMemo ?? string.Empty,
                p.OfficeName ?? string.Empty,
                p.OfficePost ?? string.Empty,
                p.OfficeAddress1 ?? string.Empty,
                p.OfficeAddress2 ?? string.Empty,
                p.OfficeTel ?? string.Empty,
                p.OfficeMemo ?? string.Empty,
                p.IsRyosyoDetail,
                p.PrimaryDoctor,
                p.IsTester,
                p.MainHokenPid,
                memo,
                lastVisitDate,
                0,
                0,
                string.Empty,
                0,
                p.Mail ?? String.Empty,
                false,
                lastAppointmentDate,
                nextAppointmentDate);
        }

        public PatientInforModel PatientCommentModels(int hpId, long ptId)
        {
            var data = NoTrackingDataContext.PtCmtInfs
                .FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == 0);
            if (data is null)
                return new PatientInforModel();

            return new PatientInforModel(
                data.HpId,
                data.PtId,
                data.Text ?? string.Empty
                );
        }

        public PatientInforModel GetPtInfByRefNo(int hpId, long refNo)
        {
            var ptInfWithRefNo = NoTrackingDataContext.PtInfs.FirstOrDefault(item => item.HpId == hpId
                                                                                     && item.ReferenceNo == refNo
                                                                                     && item.ReferenceNo != 0
                                                                                     && item.IsDelete == 0
                                                                                     && item.IsTester == 0);
            if (ptInfWithRefNo == null)
            {
                return new();
            }
            return ToModel(ptInfWithRefNo, string.Empty, 0);
        }

        public List<PatientInforModel> GetPtInfModelsByName(int hpId, string kanaName, string name, int birthDate, int sex1, int sex2)
        {
            var ptInfs = NoTrackingDataContext.PtInfs.Where(item => item.HpId == hpId
                                                                    && (item.KanaName == kanaName || item.Name == name)
                                                                    && item.Birthday == birthDate
                                                                    && (item.Sex == sex1 || item.Sex == sex2)
                                                                    && item.IsDelete == 0
                                                                    && item.IsTester == 0)
                                                    .ToList();
            return ptInfs.Select(item => ToModel(item, string.Empty, 0)).ToList();
        }

        public List<PatientInforModel> GetPtInfModels(int hpId, long refNo)
        {
            var ptInfs = NoTrackingDataContext.PtInfs.Where(item => item.HpId == hpId
                                                                    && item.ReferenceNo == refNo)
                                                    .ToList();
            return ptInfs.Select(item => ToModel(item, string.Empty, 0)).ToList();
        }

        public List<PatientInforModel> SearchBySindate(int sindate, int hpId, int pageIndex, int pageSize, Dictionary<string, string> sortData)
        {
            var ptIdList = NoTrackingDataContext.RaiinInfs.Where(r => r.HpId == hpId && r.SinDate == sindate).GroupBy(r => r.PtId).Select(gr => gr.Key).ToList();
            var karteEditions = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                  item.KarteStatus == KarteStatusConst.Official &&
                                                                                  item.IsDeleted == DeleteTypes.None);

            var ptInfWithLastVisitDate =
                (from p in NoTrackingDataContext.PtInfs
                 where p.HpId == hpId && p.IsDelete == 0 && ptIdList.Contains(p.PtId)
                 orderby p.PtNum descending
                 select new PatientInfQueryModel
                 {
                     PtInf = p,
                     LastVisitDate = (
                         from r in NoTrackingDataContext.RaiinInfs
                         where r.HpId == hpId
                             && r.PtId == p.PtId
                             && r.IsDeleted == DeleteTypes.None
                         join k in karteEditions on
                             r.RaiinNo equals k.RaiinNo
                         orderby r.SinDate descending
                         select r.SinDate
                     ).FirstOrDefault()
                 });

            bool sortGroup = sortData.Select(item => item.Key).ToList().Exists(item => item.StartsWith(startGroupOrderKey));
            var result = sortGroup
                         ?
                         ptInfWithLastVisitDate
                         .AsEnumerable()
                         .Select(p => ToModel(p.PtInf, string.Empty, p.LastVisitDate))
                         .ToList()
                         :
                         SortData(ptInfWithLastVisitDate, sortData, pageIndex, pageSize);
            return result;
        }

        public List<PatientInforModel> SearchPhone(string keyword, bool isContainMode, int hpId, int pageIndex, int pageSize, Dictionary<string, string> sortData)
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return new();
            }

            var karteEditions = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                  item.KarteStatus == KarteStatusConst.Official &&
                                                                                  item.IsDeleted == DeleteTypes.None);

            var ptInfWithLastVisitDate =
            from p in NoTrackingDataContext.PtInfs
            where p.HpId == hpId && p.IsDelete == 0 && (p.Tel1 != null && (isContainMode && p.Tel1.Contains(keyword) || p.Tel1.StartsWith(keyword)) ||
                                      p.Tel2 != null && (isContainMode && p.Tel2.Contains(keyword) || p.Tel2.StartsWith(keyword)) ||
                                      p.Name == keyword)
            orderby p.PtNum descending
            select new PatientInfQueryModel
            {
                PtInf = p,
                LastVisitDate = (
                        from r in NoTrackingDataContext.RaiinInfs
                        where r.HpId == hpId
                            && r.PtId == p.PtId
                            && r.IsDeleted == DeleteTypes.None
                        join k in karteEditions on
                            r.RaiinNo equals k.RaiinNo
                        orderby r.SinDate descending
                        select r.SinDate
                    ).FirstOrDefault()
            };

            bool sortGroup = sortData.Select(item => item.Key).ToList().Exists(item => item.StartsWith(startGroupOrderKey));
            var result = sortGroup
                         ?
                         ptInfWithLastVisitDate
                         .AsEnumerable()
                         .Select(p => ToModel(p.PtInf, string.Empty, p.LastVisitDate))
                         .ToList()
                         :
                         SortData(ptInfWithLastVisitDate, sortData, pageIndex, pageSize);
            return result;
        }

        public List<PatientInforModel> SearchName(string originKeyword, string halfsizeKeyword, bool isContainMode, int hpId, int pageIndex, int pageSize, Dictionary<string, string> sortData)
        {
            if (string.IsNullOrWhiteSpace(originKeyword) ||
                string.IsNullOrWhiteSpace(halfsizeKeyword))
            {
                return new();
            }

            var karteEditions = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                  item.KarteStatus == KarteStatusConst.Official &&
                                                                                  item.IsDeleted == DeleteTypes.None);

            IQueryable<PatientInfQueryModel> ptInfWithLastVisitDate;
            if (isContainMode)
            {
                ptInfWithLastVisitDate = from p in NoTrackingDataContext.PtInfs
                                         where p.HpId == hpId && p.IsDelete == 0
                                         && ((p.Name != null && p.Name.Contains(originKeyword))
                                            || (p.KanaName != null && p.KanaName.Contains(originKeyword))
                                            || (p.Name != null && p.Name.Replace(" ", string.Empty).Replace("　", string.Empty).Contains(originKeyword))
                                            || (p.KanaName != null && p.KanaName.Replace(" ", string.Empty).Replace("　", string.Empty).Contains(originKeyword)))
                                         orderby p.PtNum descending
                                         select new PatientInfQueryModel
                                         {
                                             PtInf = p,
                                             LastVisitDate = (
                                                     from r in NoTrackingDataContext.RaiinInfs
                                                     where r.HpId == hpId
                                                         && r.PtId == p.PtId
                                                         && r.IsDeleted == DeleteTypes.None
                                                     join k in karteEditions on
                                                         r.RaiinNo equals k.RaiinNo
                                                     orderby r.SinDate descending
                                                     select r.SinDate
                                                 ).FirstOrDefault()
                                         };
            }
            else
            {
                ptInfWithLastVisitDate = from p in NoTrackingDataContext.PtInfs
                                         where p.HpId == hpId && p.IsDelete == 0
                                         && ((p.Name != null && p.Name.StartsWith(originKeyword))
                                            || (p.KanaName != null && p.KanaName.StartsWith(originKeyword))
                                            || (p.Name != null && p.Name.Replace(" ", string.Empty).Replace("　", string.Empty).Contains(originKeyword))
                                            || (p.KanaName != null && p.KanaName.Replace(" ", string.Empty).Replace("　", string.Empty).Contains(originKeyword)))
                                         orderby p.PtNum descending
                                         select new PatientInfQueryModel
                                         {
                                             PtInf = p,
                                             LastVisitDate = (
                                                     from r in NoTrackingDataContext.RaiinInfs
                                                     where r.HpId == hpId
                                                         && r.PtId == p.PtId
                                                         && r.IsDeleted == DeleteTypes.None
                                                     join k in karteEditions on
                                                         r.RaiinNo equals k.RaiinNo
                                                     orderby r.SinDate descending
                                                     select r.SinDate
                                                 ).FirstOrDefault()
                                         };
            }

            bool sortGroup = sortData.Select(item => item.Key).ToList().Exists(item => item.StartsWith(startGroupOrderKey));
            var result = sortGroup
                         ?
                         ptInfWithLastVisitDate
                         .AsEnumerable()
                         .Select(p => ToModel(p.PtInf, string.Empty, p.LastVisitDate))
                         .ToList()
                         :
                         SortData(ptInfWithLastVisitDate, sortData, pageIndex, pageSize);
            return result;
        }

        public List<PatientInforModel> SearchEmptyId(int hpId, long ptNum, int pageIndex, int pageSize, bool isPtNumCheckDigit, int autoSetting)
        {
            if (ptNum > **********)
            {
                return new();
            }
            int originPageSize = pageSize;
            if (isPtNumCheckDigit)
            {
                pageSize = pageSize * 10;
            }
            long endIndex = (pageIndex - 1) * pageSize + ptNum + pageSize;
            long startIndex = (pageIndex - 1) * pageSize + ptNum;
            List<PatientInforModel> result = new();

            var existPtNum = NoTrackingDataContext.PtInfs.Where(p => p.HpId == hpId && p.PtNum >= startIndex && p.PtNum <= endIndex).ToList();
            for (long i = startIndex; i < endIndex; i++)
            {
                if (result.Count > originPageSize || i > **********)
                {
                    break;
                }
                if (isPtNumCheckDigit && !CIUtil.PtNumCheckDigits(i))
                {
                    continue;
                }
                var checkExistPtNum = existPtNum.FirstOrDefault(p => p.PtNum == i && (autoSetting != 1 || p.IsDelete == 0));
                if (checkExistPtNum == null)
                {
                    result.Add(new PatientInforModel(hpId, 0, i, string.Concat(i, " (空き)")));
                }
                else
                {
                    result.Add(new PatientInforModel(checkExistPtNum.HpId, checkExistPtNum.PtId, checkExistPtNum.PtNum, string.Concat(checkExistPtNum.PtNum, " ", checkExistPtNum.Name)));
                }
            }

            return result;
        }

        public List<DefHokenNoModel> GetDefHokenNoModels(int hpId, string futansyaNo)
        {
            string digit1 = futansyaNo.Substring(0, 1);
            string digit2 = futansyaNo.Substring(1, 1);
            var listDefHoken = NoTrackingDataContext.DefHokenNos
            .Where(x => x.HpId == hpId
                        && x.Digit1.Equals(digit1)
                        && x.Digit2.Equals(digit2)
                        && x.IsDeleted == 0)
            .OrderBy(entity => entity.HpId)
            .ThenBy(entity => entity.HokenNo)
            .ThenBy(entity => entity.HokenEdaNo)
            .ThenBy(entity => entity.SortNo)
            .Select(x => new DefHokenNoModel(
                             x.Digit1,
                             x.Digit2,
                             x.Digit3 ?? string.Empty,
                             x.Digit4 ?? string.Empty,
                             x.Digit5 ?? string.Empty,
                             x.Digit6 ?? string.Empty,
                             x.Digit7 ?? string.Empty,
                             x.Digit8 ?? string.Empty,
                             x.SeqNo,
                             x.HokenNo,
                             x.HokenEdaNo,
                             x.SortNo,
                             x.IsDeleted
                ))
            .ToList();

            return listDefHoken;
        }

        public List<PtKyuseiInfModel> PtKyuseiInfModels(int hpId, long ptId, bool isDeleted)
        {
            var listPtKyusei = NoTrackingDataContext.PtKyuseis
                .Where(x => x.HpId == hpId && x.PtId == ptId && (isDeleted || x.IsDeleted == 0))
                .OrderByDescending(x => x.EndDate)
                .Select(x => new PtKyuseiInfModel(
                    x.HpId,
                    x.PtId,
                    x.SeqNo,
                    x.KanaName ?? string.Empty,
                    x.Name ?? string.Empty,
                    x.EndDate,
                    x.IsDeleted))
                .ToList();
            return listPtKyusei;
        }

        public PtKyuseiInfModel GetDocumentKyuSeiInf(int hpId, long ptId, int sinDay)
        {
            var ptKyusei = NoTrackingDataContext.PtKyuseis.Where(item => item.HpId == hpId
                                                                         && item.PtId == ptId
                                                                         && item.EndDate < sinDay
                                                                         && item.IsDeleted != 1
                                                           ).OrderByDescending(item => item.EndDate)
                                                           .FirstOrDefault() ?? new PtKyusei();

            return new PtKyuseiInfModel(
                       ptKyusei.HpId,
                       ptKyusei.PtId,
                       ptKyusei.SeqNo,
                       ptKyusei.KanaName ?? string.Empty,
                       ptKyusei.Name ?? string.Empty,
                       ptKyusei.EndDate,
                       ptKyusei.IsDeleted);
        }

        public bool SaveInsuranceMasterLinkage(List<DefHokenNoModel> defHokenNoModels, int hpId, int userId)
        {
            int sortNo = 1;
            foreach (var item in defHokenNoModels)
            {
                var checkExistDefHoken = NoTrackingDataContext.DefHokenNos
                    .FirstOrDefault(x => x.HpId == hpId && x.SeqNo == item.SeqNo && x.IsDeleted == 0);

                //Add new if data does not exist
                if (checkExistDefHoken == null)
                {
                    TrackingDataContext.DefHokenNos.Add(new DefHokenNo()
                    {
                        HpId = hpId,
                        Digit1 = item.Digit1,
                        Digit2 = item.Digit2,
                        Digit3 = item.Digit3,
                        Digit4 = item.Digit4,
                        Digit5 = item.Digit5,
                        Digit6 = item.Digit6,
                        Digit7 = item.Digit7,
                        Digit8 = item.Digit8,
                        HokenNo = item.HokenNo,
                        HokenEdaNo = item.HokenEdaNo,
                        IsDeleted = 0,
                        CreateDate = CIUtil.GetJapanDateTimeNow(),
                        CreateId = userId,
                        UpdateDate = CIUtil.GetJapanDateTimeNow(),
                        UpdateId = userId,
                        SortNo = sortNo
                    });
                }
                else if (checkExistDefHoken.Digit1 == item.Digit1 && checkExistDefHoken.Digit2 == item.Digit2
                    && (checkExistDefHoken.Digit3 != item.Digit3 || checkExistDefHoken.Digit4 != item.Digit4 || checkExistDefHoken.Digit5 != item.Digit5
                    || checkExistDefHoken.Digit6 != item.Digit6 || checkExistDefHoken.Digit7 != item.Digit7 || checkExistDefHoken.Digit8 != item.Digit8
                    || checkExistDefHoken.SortNo != item.SortNo || item.IsDeleted == 1))
                {
                    TrackingDataContext.DefHokenNos.Update(new DefHokenNo()
                    {
                        HpId = hpId,
                        Digit1 = checkExistDefHoken.Digit1,
                        Digit2 = checkExistDefHoken.Digit2,
                        Digit3 = item.Digit3,
                        Digit4 = item.Digit4,
                        Digit5 = item.Digit5,
                        Digit6 = item.Digit6,
                        Digit7 = item.Digit7,
                        Digit8 = item.Digit8,
                        SeqNo = checkExistDefHoken.SeqNo,
                        HokenNo = item.HokenNo,
                        HokenEdaNo = item.HokenEdaNo,
                        IsDeleted = item.IsDeleted,
                        CreateDate = DateTime.SpecifyKind(checkExistDefHoken.CreateDate, DateTimeKind.Utc),
                        CreateId = checkExistDefHoken.CreateId,
                        CreateMachine = checkExistDefHoken.CreateMachine,
                        UpdateDate = CIUtil.GetJapanDateTimeNow(),
                        UpdateId = userId,
                        SortNo = sortNo
                    });
                }

                sortNo++;
            }

            TrackingDataContext.SaveChanges();
            return true;
        }

        public (bool resultSave, long ptId) CreatePatientInfo(PatientInforSaveModel ptInf, List<PtKyuseiModel> ptKyuseis, List<CalculationInfModel> ptSanteis, List<InsuranceModel> insurances, List<HokenInfModel> hokenInfs, List<KohiInfModel> hokenKohis, List<GroupInfModel> ptGrps, List<LimitListModel> maxMoneys, Func<int, long, long, IEnumerable<InsuranceScanModel>> handlerInsuranceScans, int userId)
        {
            int defaultMaxDate = 99999999;
            int hpId = ptInf.HpId;

            PtInf patientInsert = Mapper.Map(ptInf, new PtInf(), (source, dest) => { return dest; });
            if (patientInsert.PtNum == 0)
            {
                patientInsert.PtNum = GetAutoPtNum(hpId);
            }
            else
            {
                var ptExists = NoTrackingDataContext.PtInfs.FirstOrDefault(x => x.PtNum == patientInsert.PtNum && x.HpId == hpId);
                if (ptExists != null)
                    patientInsert.PtNum = GetAutoPtNum(hpId);
            }
            patientInsert.CreateDate = CIUtil.GetJapanDateTimeNow();
            patientInsert.CreateId = userId;
            patientInsert.UpdateId = userId;
            patientInsert.UpdateDate = CIUtil.GetJapanDateTimeNow();
            patientInsert.HpId = hpId;

            string querySql = $"INSERT INTO \"pt_inf\"\r\n(\"hp_id\", \"pt_num\", \"kana_name\", \"name\", \"sex\", \"birthday\", \"is_dead\", \"death_date\", \"home_post\", \"home_address1\", \"home_address2\", \"tel1\", \"tel2\", \"mail\", \"setainusi\", \"zokugara\", \"job\", \"renraku_name\", \"renraku_post\", \"renraku_address1\", \"renraku_address2\", \"renraku_tel\", \"renraku_memo\", \"office_name\", \"office_post\", \"office_address1\", \"office_address2\", \"office_tel\", \"office_memo\", \"primary_doctor\", \"is_tester\", \"is_delete\", \"create_date\", \"create_id\", \"create_machine\", \"update_date\", \"update_id\", \"update_machine\", \"main_hoken_pid\", \"limit_cons_flg\") VALUES({patientInsert.HpId}, {patientInsert.PtNum}, '{patientInsert.KanaName}', '{patientInsert.Name}', {patientInsert.Sex}, {patientInsert.Birthday}, {patientInsert.IsDead}, {patientInsert.DeathDate}, '{patientInsert.HomePost}', '{patientInsert.HomeAddress1}', '{patientInsert.HomeAddress2}', '{patientInsert.Tel1}', '{patientInsert.Tel2}', '{patientInsert.Mail}', '{patientInsert.Setanusi}', '{patientInsert.Zokugara}', '{patientInsert.Job}', '{patientInsert.RenrakuName}', '{patientInsert.RenrakuPost}', '{patientInsert.RenrakuAddress1}', '{patientInsert.RenrakuAddress2}', '{patientInsert.RenrakuTel}', '{patientInsert.RenrakuMemo}', '{patientInsert.OfficeName}', '{patientInsert.OfficePost}', '{patientInsert.OfficeAddress1}', '{patientInsert.OfficeAddress2}', '{patientInsert.OfficeTel}', '{patientInsert.OfficeMemo}', {patientInsert.PrimaryDoctor}, {patientInsert.IsTester}, {patientInsert.IsDelete}, '{patientInsert.CreateDate.ToString("yyyy-MM-dd HH:mm:ss.fffZ")}', {patientInsert.CreateId}, '', '{patientInsert.UpdateDate.ToString("yyyy-MM-dd HH:mm:ss.fffZ")}', {patientInsert.UpdateId}, '', {patientInsert.MainHokenPid}, {patientInsert.LimitConsFlg}) ON CONFLICT DO NOTHING;";
            TrackingDataContext.Database.SetCommandTimeout(1200);
            bool resultCreatePatient = TrackingDataContext.Database.ExecuteSqlRaw(querySql) > 0;

            if (!resultCreatePatient)
            {
                return (false, 0);
            }
            else
            {
                patientInsert.PtId = NoTrackingDataContext.PtInfs.FirstOrDefault(p => p.HpId == hpId && p.PtNum == patientInsert.PtNum)?.PtId ?? 0;
            }

            if (ptSanteis != null && ptSanteis.Any())
            {
                var ptSanteiInserts = Mapper.Map<CalculationInfModel, PtSanteiConf>(ptSanteis, (src, dest) =>
                {
                    dest.CreateId = userId;
                    dest.PtId = patientInsert.PtId;
                    dest.HpId = hpId;
                    dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                    dest.UpdateId = userId;
                    dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    return dest;
                });
                TrackingDataContext.PtSanteiConfs.AddRange(ptSanteiInserts);
            }

            if (!string.IsNullOrEmpty(ptInf.Memo))
            {
                TrackingDataContext.PtMemos.Add(new PtMemo()
                {
                    HpId = hpId,
                    PtId = patientInsert.PtId,
                    Memo = ptInf.Memo,
                    CreateId = userId,
                    CreateDate = CIUtil.GetJapanDateTimeNow(),
                    UpdateDate = CIUtil.GetJapanDateTimeNow(),
                    UpdateId = userId
                });
            }

            if (ptGrps != null && ptGrps.Any())
            {
                var listPtGrpInf = Mapper.Map<GroupInfModel, PtGrpInf>(ptGrps, (src, dest) =>
                {
                    dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                    dest.CreateId = userId;
                    dest.HpId = hpId;
                    dest.PtId = patientInsert.PtId;
                    dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    dest.UpdateId = userId;
                    return dest;
                });
                TrackingDataContext.PtGrpInfs.AddRange(listPtGrpInf);
            }

            if (ptKyuseis != null && ptKyuseis.Any())
            {
                var ptKyuseiList = Mapper.Map<PtKyuseiModel, PtKyusei>(ptKyuseis, (src, dest) =>
                {
                    dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                    dest.CreateId = userId;
                    dest.UpdateId = userId;
                    dest.HpId = hpId;
                    dest.PtId = patientInsert.PtId;
                    dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    return dest;
                });
                TrackingDataContext.PtKyuseis.AddRange(ptKyuseiList);
            }

            #region Hoken parterrn
            List<PtHokenPattern> pthokenPartterns = Mapper.Map<InsuranceModel, PtHokenPattern>(insurances.Where(x => x.IsAddNew), (src, dest) =>
            {
                dest.CreateId = userId;
                dest.UpdateId = userId;
                dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                dest.PtId = patientInsert.PtId;
                dest.HpId = hpId;
                dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                return dest;
            });
            TrackingDataContext.PtHokenPatterns.AddRange(pthokenPartterns);
            #endregion Hoken parterrn

            #region HokenInf
            List<PtHokenInf> ptHokenInfs = Mapper.Map<HokenInfModel, PtHokenInf>(hokenInfs.Where(x => x.IsAddNew), (src, dest) =>
            {
                dest.CreateId = userId;
                dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                dest.UpdateId = userId;
                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                dest.PtId = patientInsert.PtId;
                dest.HpId = hpId;
                dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;

                #region PtRousaiTenki
                TrackingDataContext.PtRousaiTenkis.AddRange(Mapper.Map<RousaiTenkiModel, PtRousaiTenki>(src.ListRousaiTenki, (srcR, destR) =>
                {
                    destR.CreateId = userId;
                    destR.UpdateId = userId;
                    destR.PtId = patientInsert.PtId;
                    destR.HpId = hpId;
                    destR.Tenki = srcR.RousaiTenkiTenki;
                    destR.Sinkei = srcR.RousaiTenkiSinkei;
                    destR.EndDate = srcR.RousaiTenkiEndDate;
                    destR.HokenId = dest.HokenId;
                    destR.CreateDate = CIUtil.GetJapanDateTimeNow();
                    destR.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    return destR;
                }));
                #endregion

                #region PtHokenCheck
                TrackingDataContext.PtHokenChecks.AddRange(Mapper.Map<ConfirmDateModel, PtHokenCheck>(src.ConfirmDateList, (srcCf, destCf) =>
                {
                    destCf.CreateId = userId;
                    destCf.CreateDate = CIUtil.GetJapanDateTimeNow();
                    destCf.CheckDate = DateTime.SpecifyKind(CIUtil.IntToDate(srcCf.ConfirmDate), DateTimeKind.Utc);
                    destCf.CheckCmt = srcCf.CheckComment;
                    destCf.HokenId = dest.HokenId;
                    destCf.CheckId = userId;
                    destCf.PtID = patientInsert.PtId;
                    destCf.HokenGrp = 1;
                    destCf.HpId = hpId;
                    return destCf;
                }));
                #endregion
                return dest;
            });
            TrackingDataContext.PtHokenInfs.AddRange(ptHokenInfs);
            #endregion HokenInf

            #region PtKohiInf
            List<PtKohi> ptKohiInfs = Mapper.Map<KohiInfModel, PtKohi>(hokenKohis.Where(x => x.IsAddNew), (src, dest) =>
            {
                dest.UpdateId = userId;
                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                dest.CreateId = userId;
                dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                dest.PtId = patientInsert.PtId;
                dest.HpId = hpId;
                dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                #region PtHokenCheck
                TrackingDataContext.PtHokenChecks.AddRange(Mapper.Map<ConfirmDateModel, PtHokenCheck>(src.ConfirmDateList, (srcCf, destCf) =>
                {
                    destCf.CreateId = userId;
                    destCf.CreateDate = CIUtil.GetJapanDateTimeNow();
                    destCf.CheckDate = DateTime.SpecifyKind(CIUtil.IntToDate(srcCf.ConfirmDate), DateTimeKind.Utc);
                    destCf.CheckCmt = srcCf.CheckComment;
                    destCf.HokenId = dest.HokenId;
                    destCf.CheckId = userId;
                    destCf.PtID = patientInsert.PtId;
                    destCf.HokenGrp = 2;
                    destCf.HpId = hpId;
                    return destCf;
                }));
                #endregion
                return dest;
            });
            TrackingDataContext.PtKohis.AddRange(ptKohiInfs);
            #endregion PtKohiInf

            #region Maxmoney
            if (maxMoneys != null && maxMoneys.Any())
            {
                TrackingDataContext.LimitListInfs.AddRange(Mapper.Map<LimitListModel, LimitListInf>(maxMoneys, (src, dest) =>
                {
                    dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                    dest.PtId = patientInsert.PtId;
                    dest.HpId = hpId;
                    dest.SinDate = src.SinDateY * 10000 + src.SinDateM * 100 + src.SinDateD;
                    dest.UpdateId = userId;
                    dest.CreateId = userId;
                    return dest;
                }));
            }
            #endregion Maxmoney

            #region insurancesCan
            var insuranceScanDatas = handlerInsuranceScans(hpId, patientInsert.PtNum, patientInsert.PtId);
            if (insuranceScanDatas != null && insuranceScanDatas.Any())
            {
                TrackingDataContext.PtHokenScans.AddRange(Mapper.Map<InsuranceScanModel, PtHokenScan>(insuranceScanDatas, (src, dest) =>
                {
                    dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                    dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    dest.CreateId = userId;
                    dest.UpdateId = userId;
                    return dest;
                }));
            }
            #endregion

            int changeDatas = TrackingDataContext.ChangeTracker.Entries().Count(x => x.State == EntityState.Modified || x.State == EntityState.Added);
            if (changeDatas == 0 && resultCreatePatient)
                return (true, patientInsert.PtId);

            return (TrackingDataContext.SaveChanges() > 0, patientInsert.PtId);
        }

        public long GetAutoPtNum(int hpId)
        {
            var ptInf = NoTrackingDataContext.PtInfs.Where(ptInf => ptInf.HpId == hpId).OrderByDescending(e => e.PtNum).FirstOrDefault();
            if (ptInf == null) return 1;
            return ptInf.PtNum + 1;
        }

        public (bool resultSave, long ptId) UpdatePatientInfo(PatientInforSaveModel ptInf, List<PtKyuseiModel> ptKyuseis, List<CalculationInfModel> ptSanteis, List<InsuranceModel> insurances, List<HokenInfModel> hokenInfs, List<KohiInfModel> hokenKohis, List<GroupInfModel> ptGrps, List<LimitListModel> maxMoneys, Func<int, long, long, IEnumerable<InsuranceScanModel>> handlerInsuranceScans, int userId, List<int> hokenIdList)
        {
            int defaultMaxDate = 99999999;
            int hpId = ptInf.HpId;

            #region CloneByomeiWithNewHokenId
            if (hokenIdList.Any())
            {
                //if add new hoken => confirm clone byomei
                var hokenInf = hokenInfs.OrderByDescending(p => p.EndDateSort)
                                        .ThenByDescending(p => p.HokenId)
                                        .FirstOrDefault(p => p.IsDeleted == DeleteTypes.None && !p.IsAddNew);
                if (hokenInf != null)
                {
                    var ptByomeis = TrackingDataContext.PtByomeis.Where(item => item.PtId == ptInf.PtId
                                                                                && item.HpId == hpId
                                                                                && item.HokenPid == hokenInf.HokenId
                                                                                && item.IsDeleted == DeleteTypes.None
                                                                                && item.TenkiKbn == TenkiKbnConst.Continued)
                                                                 .ToList();
                    if (ptByomeis.Count > 0)
                    {
                        foreach (var hokenId in hokenIdList)
                        {
                            CloneByomeiWithNewHokenId(ptByomeis, hokenId, userId);
                        }
                    }
                }
            }
            #endregion

            #region Patient-info
            PtInf? patientInfo = TrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptInf.PtId);
            if (patientInfo is null)
                return (false, ptInf.PtId);

            Mapper.Map(ptInf, patientInfo, (source, dest) =>
            {
                dest.IsRyosyoDetail = 1; //default 1
                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                dest.UpdateId = userId;
                if (ptInf.IsDead > 0)
                {
                    var ptFamilyList = TrackingDataContext.PtFamilys.Where(x => x.HpId == hpId && x.FamilyPtId == ptInf.PtId && x.IsDeleted == DeleteTypes.None).ToList();
                    foreach (var ptFamily in ptFamilyList)
                    {
                        ptFamily.IsDead = ptInf.IsDead;
                        ptFamily.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        ptFamily.UpdateId = ptInf.HpId;
                    }
                }
                return dest;
            });
            #endregion

            #region Patient-memo
            PtMemo? memoCurrent = TrackingDataContext.PtMemos.FirstOrDefault(x => x.PtId == patientInfo.PtId && x.HpId == patientInfo.HpId && x.IsDeleted == 0);
            if (memoCurrent != null)
            {
                if (string.IsNullOrEmpty(ptInf.Memo))
                {
                    memoCurrent.IsDeleted = 1;
                    memoCurrent.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    memoCurrent.UpdateId = userId;
                }
                else
                {
                    if (memoCurrent.Memo != null && !memoCurrent.Memo.Equals(ptInf.Memo))
                    {
                        memoCurrent.IsDeleted = 1;
                        memoCurrent.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        memoCurrent.UpdateId = userId;
                        TrackingDataContext.PtMemos.Add(new PtMemo()
                        {
                            HpId = patientInfo.HpId,
                            PtId = patientInfo.PtId,
                            Memo = ptInf.Memo,
                            CreateId = userId,
                            UpdateDate = CIUtil.GetJapanDateTimeNow(),
                            UpdateId = userId,
                            CreateDate = CIUtil.GetJapanDateTimeNow()
                        });
                    }
                }

            }
            else
            {
                if (!string.IsNullOrEmpty(ptInf.Memo))
                {
                    TrackingDataContext.PtMemos.Add(new PtMemo()
                    {
                        HpId = patientInfo.HpId,
                        PtId = patientInfo.PtId,
                        Memo = ptInf.Memo,
                        CreateId = userId,
                        UpdateDate = CIUtil.GetJapanDateTimeNow(),
                        UpdateId = userId,
                        CreateDate = CIUtil.GetJapanDateTimeNow(),
                    });
                }
            }
            #endregion

            #region PtSantei
            var ptSanteiConfDb = TrackingDataContext.PtSanteiConfs.Where(x => x.PtId == patientInfo.PtId && x.IsDeleted == 0 && x.HpId == patientInfo.HpId).ToList();
            var ptSanteiConfRemoves = ptSanteiConfDb.Where(c => !ptSanteis.Any(_ => _.SeqNo == c.SeqNo));

            foreach (var item in ptSanteiConfRemoves)
            {
                item.UpdateId = userId;
                item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                item.IsDeleted = DeleteTypes.Deleted;
            }

            var ptSanteiConfListAdd = Mapper.Map<CalculationInfModel, PtSanteiConf>(ptSanteis.Where(x => x.SeqNo == 0), (src, dest) =>
            {
                dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                dest.CreateId = userId;
                dest.HpId = hpId;
                dest.PtId = patientInfo.PtId;
                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                dest.UpdateId = userId;
                return dest;
            });
            TrackingDataContext.PtSanteiConfs.AddRange(ptSanteiConfListAdd);

            foreach (var item in ptSanteis.Where(x => x.SeqNo != 0))
            {
                var ptSanteiUpdate = ptSanteiConfDb.FirstOrDefault(x => x.SeqNo == item.SeqNo);
                if (ptSanteiUpdate != null)
                {
                    ptSanteiUpdate.KbnNo = item.KbnNo;
                    ptSanteiUpdate.EdaNo = item.EdaNo;
                    ptSanteiUpdate.KbnVal = item.KbnVal;
                    ptSanteiUpdate.StartDate = item.StartDate;
                    ptSanteiUpdate.EndDate = item.EndDate;
                    ptSanteiUpdate.UpdateId = userId;
                    ptSanteiUpdate.UpdateDate = CIUtil.GetJapanDateTimeNow();
                }
            }
            #endregion

            #region PtKyusei

            var databaseKyuseis = TrackingDataContext.PtKyuseis.Where(x => x.PtId == patientInfo.PtId && x.HpId == hpId && x.IsDeleted == DeleteTypes.None).ToList();
            var KyuseiRemoves = databaseKyuseis.Where(c => !ptKyuseis.Any(_ => _.SeqNo == c.SeqNo));

            foreach (var item in KyuseiRemoves)
            {
                item.UpdateId = userId;
                item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                item.IsDeleted = DeleteTypes.Deleted;
            }

            var ptKyuseiListAdd = Mapper.Map<PtKyuseiModel, PtKyusei>(ptKyuseis.Where(x => x.SeqNo == 0), (src, dest) =>
            {
                dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                dest.CreateId = userId;
                dest.UpdateId = userId;
                dest.HpId = hpId;
                dest.PtId = patientInfo.PtId;
                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                return dest;
            });
            TrackingDataContext.PtKyuseis.AddRange(ptKyuseiListAdd);

            foreach (var item in ptKyuseis.Where(x => x.SeqNo != 0))
            {
                var kyuseiUpdate = databaseKyuseis.FirstOrDefault(x => x.SeqNo == item.SeqNo);
                if (kyuseiUpdate != null)
                {
                    kyuseiUpdate.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    kyuseiUpdate.Name = item.Name;
                    kyuseiUpdate.KanaName = item.KanaName;
                    kyuseiUpdate.EndDate = item.EndDate;
                    kyuseiUpdate.UpdateId = userId;
                }
            }
            #endregion

            #region GrpInf
            var databaseGrpInfs = TrackingDataContext.PtGrpInfs.Where(x => x.HpId == hpId && x.PtId == patientInfo.PtId && x.IsDeleted == DeleteTypes.None).ToList();

            var GrpInRemoves = databaseGrpInfs.Where(c => !ptGrps.Any(_ => _.GroupId == c.GroupId)
                                        || ptGrps.Any(_ => _.GroupId == c.GroupId && string.IsNullOrEmpty(_.GroupCode)));
            foreach (var item in GrpInRemoves)
            {
                item.UpdateId = userId;
                item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                item.IsDeleted = DeleteTypes.Deleted;
            }

            foreach (var item in ptGrps)
            {
                var info = databaseGrpInfs.FirstOrDefault(pt => pt.HpId == hpId && pt.PtId == patientInfo.PtId && pt.GroupId == item.GroupId);

                if (info != null && !string.IsNullOrEmpty(item.GroupCode))
                {
                    //Remove record old
                    info.UpdateId = userId;
                    info.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    info.IsDeleted = DeleteTypes.Deleted;

                    //clone new record
                    PtGrpInf model = Mapper.Map(item, new PtGrpInf(), (source, dest) =>
                    {
                        dest.CreateId = userId;
                        dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                        dest.UpdateId = userId;
                        dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        dest.PtId = patientInfo.PtId;
                        dest.HpId = hpId;
                        return dest;
                    });
                    TrackingDataContext.PtGrpInfs.Add(model);
                }
                else if (info == null && !string.IsNullOrEmpty(item.GroupCode))
                {
                    PtGrpInf model = Mapper.Map(item, new PtGrpInf(), (source, dest) =>
                    {
                        dest.CreateId = userId;
                        dest.UpdateId = userId;
                        dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                        dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        dest.PtId = patientInfo.PtId;
                        dest.HpId = hpId;
                        return dest;
                    });
                    TrackingDataContext.PtGrpInfs.Add(model);
                }
                else if (info != null && string.IsNullOrEmpty(item.GroupCode))
                {
                    //delete it
                    info.UpdateId = userId;
                    info.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    info.IsDeleted = DeleteTypes.Deleted;
                }
            }
            #endregion

            var databaseHokenPartterns = TrackingDataContext.PtHokenPatterns.Where(x => x.PtId == patientInfo.PtId && x.HpId == patientInfo.HpId && x.IsDeleted == DeleteTypes.None).ToList();
            var databaseHoKentInfs = TrackingDataContext.PtHokenInfs.Where(x => x.PtId == patientInfo.PtId && x.HpId == patientInfo.HpId && x.IsDeleted == DeleteTypes.None).ToList();
            var databasePtKohis = TrackingDataContext.PtKohis.Where(x => x.PtId == patientInfo.PtId && x.HpId == patientInfo.HpId && x.IsDeleted == DeleteTypes.None).ToList();
            var databaseHokenChecks = TrackingDataContext.PtHokenChecks.Where(c => c.PtID == patientInfo.PtId && c.HpId == patientInfo.HpId && c.IsDeleted == DeleteTypes.None).ToList();
            var databasePtRousaiTenkis = TrackingDataContext.PtRousaiTenkis.Where(c => c.PtId == patientInfo.PtId && c.HpId == patientInfo.HpId && c.IsDeleted == DeleteTypes.None).ToList();

            #region Hoken parterrn
            List<PtHokenPattern> deleteHokenPartterns = databaseHokenPartterns.Where(c => !insurances.Any(_ => _.SeqNo == c.SeqNo) && c.IsDeleted == 0).ToList();
            deleteHokenPartterns.ForEach(x =>
            {
                x.IsDeleted = DeleteTypes.Deleted;
                x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                x.UpdateId = userId;
            });

            List<PtHokenPattern> pthokenPartterns = Mapper.Map<InsuranceModel, PtHokenPattern>(insurances.Where(x => x.SeqNo == 0 && x.IsAddNew), (src, dest) =>
            {
                dest.CreateId = userId;
                dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                dest.UpdateId = userId;
                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                dest.PtId = patientInfo.PtId;
                dest.HpId = hpId;
                dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                return dest;
            });
            TrackingDataContext.PtHokenPatterns.AddRange(pthokenPartterns);

            foreach (var item in insurances.Where(x => x.SeqNo != 0))
            {
                PtHokenPattern? modelUpdate = databaseHokenPartterns.FirstOrDefault(x => x.SeqNo == item.SeqNo);
                if (modelUpdate != null)
                    Mapper.Map(item, modelUpdate, (src, dest) =>
                    {
                        dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                        dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        dest.UpdateId = userId;
                        return dest;
                    });
            }
            #endregion Hoken parterrn

            #region HokenInf
            //Add New
            List<PtHokenInf> ptHokenInfs = Mapper.Map<HokenInfModel, PtHokenInf>(hokenInfs.Where(x => x.SeqNo == 0 && x.IsAddNew), (src, dest) =>
            {
                dest.CreateId = userId;
                dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                dest.UpdateId = userId;
                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                dest.PtId = patientInfo.PtId;
                dest.HpId = hpId;
                dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;

                #region PtRousaiTenki
                TrackingDataContext.PtRousaiTenkis.AddRange(Mapper.Map<RousaiTenkiModel, PtRousaiTenki>(src.ListRousaiTenki, (srcR, destR) =>
                {
                    destR.CreateId = userId;
                    destR.PtId = patientInfo.PtId;
                    destR.HpId = hpId;
                    destR.Tenki = srcR.RousaiTenkiTenki;
                    destR.Sinkei = srcR.RousaiTenkiSinkei;
                    destR.EndDate = srcR.RousaiTenkiEndDate;
                    destR.HokenId = dest.HokenId;
                    destR.UpdateId = userId;
                    destR.CreateDate = CIUtil.GetJapanDateTimeNow();
                    destR.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    return destR;
                }));
                #endregion

                #region PtHokenCheck
                TrackingDataContext.PtHokenChecks.AddRange(Mapper.Map<ConfirmDateModel, PtHokenCheck>(src.ConfirmDateList, (srcCf, destCf) =>
                {
                    destCf.CreateId = userId;
                    destCf.UpdateId = userId;
                    destCf.CreateDate = CIUtil.GetJapanDateTimeNow();
                    destCf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    destCf.CheckDate = DateTime.SpecifyKind(CIUtil.IntToDate(srcCf.ConfirmDate), DateTimeKind.Utc);
                    destCf.CheckCmt = srcCf.CheckComment;
                    destCf.HokenId = dest.HokenId;
                    destCf.CheckId = userId;
                    destCf.PtID = patientInfo.PtId;
                    destCf.HpId = hpId;
                    destCf.HokenGrp = 1;
                    return destCf;
                }));
                #endregion
                return dest;
            });
            TrackingDataContext.PtHokenInfs.AddRange(ptHokenInfs);

            //Update
            foreach (var item in hokenInfs.Where(x => x.SeqNo != 0))
            {
                PtHokenInf? updateHokenInf = databaseHoKentInfs.FirstOrDefault(x => x.SeqNo == item.SeqNo);
                if (updateHokenInf != null)
                {
                    //Info inf
                    Mapper.Map(item, updateHokenInf, (src, dest) =>
                    {
                        dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                        dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        dest.UpdateId = userId;
                        return dest;
                    });

                    //ConfirmDate
                    UpdateHokenCheck(databaseHokenChecks, item.ConfirmDateList, patientInfo.HpId, patientInfo.PtId, updateHokenInf.HokenId, userId, false);

                    //RousaiTenki
                    var listAddTenki = Mapper.Map<RousaiTenkiModel, PtRousaiTenki>(item.ListRousaiTenki.Where(x => x.SeqNo == 0), (src, dest) =>
                    {
                        dest.Sinkei = src.RousaiTenkiSinkei;
                        dest.Tenki = src.RousaiTenkiTenki;
                        dest.EndDate = src.RousaiTenkiEndDate;
                        dest.PtId = patientInfo.PtId;
                        dest.HpId = hpId;
                        dest.HokenId = updateHokenInf.HokenId;
                        dest.CreateId = userId;
                        dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                        dest.UpdateId = userId;
                        dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        return dest;
                    });
                    TrackingDataContext.PtRousaiTenkis.AddRange(listAddTenki);

                    foreach (var rsTkUpdate in item.ListRousaiTenki.Where(x => x.SeqNo != 0))
                    {
                        var updateItem = databasePtRousaiTenkis.FirstOrDefault(x => x.HokenId == updateHokenInf.HokenId && x.SeqNo == rsTkUpdate.SeqNo);
                        if (updateItem != null)
                        {
                            updateItem.Sinkei = rsTkUpdate.RousaiTenkiSinkei;
                            updateItem.Tenki = rsTkUpdate.RousaiTenkiTenki;
                            updateItem.EndDate = rsTkUpdate.RousaiTenkiEndDate;
                            updateItem.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        }
                    }

                    var listDatabaseByHokenInf = databasePtRousaiTenkis.Where(x => x.HokenId == updateHokenInf.HokenId);
                    var listRemoves = listDatabaseByHokenInf.Where(x => !item.ListRousaiTenki.Any(m => m.SeqNo == x.SeqNo)).ToList();

                    listRemoves.ForEach(x =>
                    {
                        x.IsDeleted = 1;
                        x.UpdateId = userId;
                        x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    });
                }
            }
            #endregion HokenInf

            #region HokenKohi
            //Add new
            List<PtKohi> ptKohiInfs = Mapper.Map<KohiInfModel, PtKohi>(hokenKohis.Where(x => x.IsAddNew && x.SeqNo == 0), (src, dest) =>
            {
                dest.CreateId = userId;
                dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                dest.UpdateId = userId;
                dest.PtId = patientInfo.PtId;
                dest.HpId = hpId;
                dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                #region PtHokenCheck
                TrackingDataContext.PtHokenChecks.AddRange(Mapper.Map<ConfirmDateModel, PtHokenCheck>(src.ConfirmDateList, (srcCf, destCf) =>
                {
                    destCf.CreateId = userId;
                    destCf.CreateDate = CIUtil.GetJapanDateTimeNow();
                    destCf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    destCf.UpdateId = userId;
                    destCf.CheckDate = DateTime.SpecifyKind(CIUtil.IntToDate(srcCf.ConfirmDate), DateTimeKind.Utc);
                    destCf.CheckCmt = srcCf.CheckComment;
                    destCf.HokenId = dest.HokenId;
                    destCf.CheckId = userId;
                    destCf.PtID = patientInfo.PtId;
                    destCf.HokenGrp = 2;
                    destCf.HpId = hpId;
                    return destCf;
                }));
                #endregion
                return dest;
            });
            TrackingDataContext.PtKohis.AddRange(ptKohiInfs);

            //Update
            foreach (var item in hokenKohis.Where(x => !x.IsAddNew && x.SeqNo != 0))
            {
                PtKohi? updateKohi = databasePtKohis.FirstOrDefault(c => c.HokenId == item.HokenId && c.SeqNo == item.SeqNo);
                if (updateKohi != null)
                {
                    //Info Kohi
                    Mapper.Map(item, updateKohi, (src, dest) =>
                    {
                        dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                        dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        dest.UpdateId = userId;
                        return dest;
                    });

                    //ConfirmDate
                    UpdateHokenCheck(databaseHokenChecks, item.ConfirmDateList, patientInfo.HpId, patientInfo.PtId, updateKohi.HokenId, userId, true);
                }
            }
            #endregion HokenKohi

            #region Maxmoney
            List<LimitListInf> maxMoneyDatabases = TrackingDataContext.LimitListInfs.Where(x => x.HpId == hpId
                                                                   && x.PtId == patientInfo.PtId
                                                                   && x.IsDeleted == 0).ToList();

            foreach (var item in maxMoneyDatabases)
            {
                var exist = maxMoneys.FirstOrDefault(x => x.SeqNo == item.SeqNo && x.Id == item.Id);
                if (exist == null)
                {
                    item.IsDeleted = DeleteTypes.Deleted;
                    item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    item.UpdateId = userId;
                }
                else
                {
                    item.SortKey = exist.SortKey;
                    item.FutanGaku = exist.FutanGaku;
                    item.TotalGaku = exist.TotalGaku;
                    item.Biko = exist.Biko;
                    item.SinDate = exist.SinDateY * 10000 + exist.SinDateM * 100 + exist.SinDateD;
                    item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    item.UpdateId = userId;
                }
            }

            TrackingDataContext.LimitListInfs.AddRange(Mapper.Map<LimitListModel, LimitListInf>(maxMoneys.Where(x => x.SeqNo == 0 && x.Id == 0), (src, dest) =>
            {
                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                dest.PtId = patientInfo.PtId;
                dest.HpId = hpId;
                dest.SinDate = src.SinDateY * 10000 + src.SinDateM * 100 + src.SinDateD;
                dest.UpdateId = userId;
                dest.CreateId = userId;
                return dest;
            }));
            #endregion

            #region insuranceScan
            var insuranceScanDatabases = TrackingDataContext.PtHokenScans.Where(x => x.HpId == hpId && x.PtId == patientInfo.PtId && x.IsDeleted == DeleteTypes.None).ToList();
            var insuranceScanDatas = handlerInsuranceScans(hpId, patientInfo.PtNum, patientInfo.PtId);
            if (insuranceScanDatas != null && insuranceScanDatas.Any())
            {
                foreach (var scan in insuranceScanDatas)
                {
                    if (scan.IsDeleted == DeleteTypes.Deleted)
                    {
                        var deleteItem = insuranceScanDatabases.FirstOrDefault(x => x.SeqNo == scan.SeqNo);
                        if (deleteItem is not null)
                        {
                            deleteItem.IsDeleted = DeleteTypes.Deleted;
                            deleteItem.UpdateDate = CIUtil.GetJapanDateTimeNow();
                            deleteItem.UpdateId = userId;
                        }
                    }
                    else
                    {
                        if (scan.SeqNo == 0) //Create
                        {
                            TrackingDataContext.PtHokenScans.Add(Mapper.Map(scan, new PtHokenScan(), (src, dest) =>
                            {
                                dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                                dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                                dest.CreateId = userId;
                                return dest;
                            }));
                        }
                        else
                        {
                            var updateItem = insuranceScanDatabases.FirstOrDefault(x => x.SeqNo == scan.SeqNo);
                            if (updateItem is not null)
                            {
                                updateItem.UpdateDate = CIUtil.GetJapanDateTimeNow();
                                updateItem.UpdateId = userId;
                                updateItem.FileName = scan.FileName;
                            }
                        }
                    }
                }
            }
            #endregion

            return (TrackingDataContext.SaveChanges() > 0, patientInfo.PtId);
        }

        private double GetSettingValue(int groupCd, int hpId, int grpEdaNo = 0, int defaultValue = 0)
        {
            SystemConf? systemConf;
            systemConf = NoTrackingDataContext.SystemConfs.FirstOrDefault(p =>
                    p.HpId == hpId && p.GrpCd == groupCd && p.GrpEdaNo == grpEdaNo);
            return systemConf != null ? systemConf.Val : defaultValue;
        }

        public void UpdateHokenCheck(List<PtHokenCheck> databaseList, List<ConfirmDateModel> savingList, int hpId, long ptId, int hokenId, int actUserId, bool hokenKohi = false, int onlineConfirmationId = 0)
        {
            int hokenGrp = 1;
            if (hokenKohi)
            {
                hokenGrp = 2;
            }
            var checkDatabaseData = databaseList.Where(c => c.HokenId == hokenId && c.HokenGrp == hokenGrp).ToList();

            var date = CIUtil.GetJapanDateTimeNow();

            var confirmDates = Mapper.Map<ConfirmDateModel, PtHokenCheck>(savingList.Where(c => c.ConfirmDate > 0), (srcCf, destCf) =>
            {
                destCf.HpId = hpId;
                destCf.PtID = ptId;
                destCf.HokenGrp = hokenGrp;
                destCf.HokenId = hokenId;
                destCf.CheckDate = DateTime.SpecifyKind(CIUtil.IntToDate(srcCf.ConfirmDate), DateTimeKind.Utc);
                destCf.CheckId = actUserId;
                destCf.CheckCmt = srcCf.CheckComment;
                destCf.CreateId = actUserId;
                destCf.CreateDate = date;
                destCf.UpdateDate = date;
                destCf.UpdateId = actUserId;
                destCf.OnlineConfirmationId = srcCf.OnlineConfirmationId;
                return destCf;
            }).DistinctBy(e => e.CheckDate).ToList();

            foreach (var createItem in confirmDates)
            {
                if (createItem.SeqNo == 0)
                {
                    if (checkDatabaseData.Any(item => item.CheckDate.ToString("yyyyMMdd") == createItem.CheckDate.ToString("yyyyMMdd")))
                    {
                        continue;
                    }
                    createItem.OnlineConfirmationId = onlineConfirmationId == 0 ? createItem.OnlineConfirmationId : onlineConfirmationId;
                    TrackingDataContext.PtHokenChecks.Add(createItem);
                }
                else
                {
                    var checkDateDb = checkDatabaseData.FirstOrDefault(e => e.SeqNo == createItem.SeqNo);
                    if (checkDateDb != null)
                    {
                        checkDateDb.OnlineConfirmationId = onlineConfirmationId == 0 ? createItem.OnlineConfirmationId : onlineConfirmationId;
                        checkDateDb.UpdateDate = date;
                        checkDateDb.UpdateId = actUserId;
                        checkDateDb.CheckId = actUserId;
                    }
                }
            }
        }

        public bool DeletePatientInfo(long ptId, int hpId, int userId)
        {
            var patientInf = TrackingDataContext.PtInfs.FirstOrDefault(x => x.PtId == ptId && x.HpId == hpId && x.IsDelete == DeleteTypes.None);

            if (patientInf != null)
            {
                patientInf.IsDelete = DeleteTypes.Deleted;
                patientInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                patientInf.UpdateId = userId;
                #region PtMemo
                var ptMemos = TrackingDataContext.PtMemos.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None).ToList();
                foreach (var item in ptMemos)
                {
                    item.IsDeleted = DeleteTypes.Deleted;
                    item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    item.UpdateId = userId;
                }
                #endregion

                #region ptKyuseis
                var ptKyuseis = TrackingDataContext.PtKyuseis.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None).ToList();
                ptKyuseis.ForEach(x =>
                {
                    x.IsDeleted = DeleteTypes.Deleted;
                    x.UpdateId = userId;
                    x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                });
                #endregion

                #region ptSanteis
                var ptSanteis = TrackingDataContext.PtSanteiConfs.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None).ToList();
                ptSanteis.ForEach(x =>
                {
                    x.IsDeleted = DeleteTypes.Deleted;
                    x.UpdateId = userId;
                    x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                });
                #endregion

                #region HokenParttern
                var ptHokenParterns = TrackingDataContext.PtHokenPatterns.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None).ToList();
                ptHokenParterns.ForEach(x =>
                {
                    x.IsDeleted = DeleteTypes.Deleted;
                    x.UpdateId = userId;
                    x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                });
                #endregion

                #region HokenInf
                var ptHokenInfs = TrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None).ToList();
                ptHokenInfs.ForEach(x =>
                {
                    x.IsDeleted = DeleteTypes.Deleted;
                    x.UpdateId = userId;
                    x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                });
                #endregion

                #region HokenKohi
                var ptHokenKohis = TrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None).ToList();
                ptHokenKohis.ForEach(x =>
                {
                    x.IsDeleted = DeleteTypes.Deleted;
                    x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    x.UpdateId = userId;
                });
                #endregion

                #region HokenCheck
                var ptHokenChecks = TrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId && x.PtID == ptId && x.IsDeleted == DeleteTypes.None).ToList();
                ptHokenChecks.ForEach(x =>
                {
                    x.IsDeleted = DeleteTypes.Deleted;
                    x.UpdateId = userId;
                    x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                });
                #endregion

                #region RousaiTenki
                var ptRousaiTenkies = TrackingDataContext.PtRousaiTenkis.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None).ToList();
                ptRousaiTenkies.ForEach(x =>
                {
                    x.IsDeleted = DeleteTypes.Deleted;
                    x.UpdateId = userId;
                    x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                });
                #endregion

                #region RaiinInf
                var raiinInfList = TrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId && item.PtId == ptId
                                                                               && item.IsDeleted != DeleteTypes.Deleted)
                                                                .ToList();
                raiinInfList.ForEach(x =>
                {
                    x.IsDeleted = DeleteTypes.Deleted;
                    x.UpdateId = userId;
                    x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                });
                #endregion

            }
            return TrackingDataContext.SaveChanges() > 0;
        }

        public bool IsAllowDeletePatient(int hpId, long ptId)
        {
            var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                             item.PtId == ptId &&
                                                                             item.IsDeleted == DeleteTypes.None &&
                                                                             item.SinStartTime != null &&
                                                                             item.SinStartTime != string.Empty &&
                                                                             item.SinEndTime != null &&
                                                                             item.SinEndTime != string.Empty);

            var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                     item.PtId == ptId &&
                                                                                     item.IsDeleted == DeleteTypes.None);

            var raiinInf = (from raiinInfItem in listRaiinInf
                            join karteEdition in listKarteEdition on
                                raiinInfItem.RaiinNo equals karteEdition.RaiinNo
                            select raiinInfItem)
                           .FirstOrDefault();

            if (raiinInf != null)
            {
                return false;
            }
            return true;
        }

        public HokenMstModel GetHokenMstByInfor(int hpId, int hokenNo, int hokenEdaNo, int sinDate)
        {
            // Get all hoken follow hpInf
            var hpInf = NoTrackingDataContext.HpInfs.Where(h => h.HpId == hpId && h.StartDate <= sinDate).OrderByDescending(x => x.StartDate).FirstOrDefault();
            var prefCd = hpInf?.PrefNo;
            //Validate get all hokenMst
            var hokenMst = TrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && x.HokenNo == hokenNo
                                                                        && x.HokenEdaNo == hokenEdaNo
                                   && (x.PrefNo == prefCd
                            || x.PrefNo == 0
                            || x.IsOtherPrefValid == 1))
                                .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .ThenByDescending(e => e.StartDate)
                    .FirstOrDefault();
            if (hokenMst is null)
                return new HokenMstModel();

            return ConvertHokenMst(hokenMst);
        }

        public HokensyaMstModel GetHokenSyaMstByInfor(int hpId, string houbetu, string hokensya)
        {
            var hokensyaMst = TrackingDataContext.HokensyaMsts.Where(x => x.HpId == hpId && x.HokensyaNo == hokensya && x.Houbetu == houbetu).Select(x => new HokensyaMstModel(x.IsKigoNa)).FirstOrDefault();
            return hokensyaMst ?? new HokensyaMstModel();
        }

        public PatientInforModel GetPtInf(int hpId, long ptId)
        {
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(pt => pt.HpId == hpId && pt.PtId == ptId && pt.IsDelete != 1) ?? new PtInf();
            return new PatientInforModel(
                        ptInf.HpId,
                        ptInf.PtId,
                        ptInf.ReferenceNo,
                        ptInf.SeqNo,
                        ptInf.PtNum,
                        ptInf.KanaName ?? string.Empty,
                        ptInf.Name ?? string.Empty,
                        ptInf.Sex,
                        ptInf.Birthday,
                        ptInf.LimitConsFlg,
                        ptInf.IsDead,
                        ptInf.DeathDate,
                        ptInf.HomePost ?? string.Empty,
                        ptInf.HomeAddress1 ?? string.Empty,
                        ptInf.HomeAddress2 ?? string.Empty,
                        ptInf.Tel1 ?? string.Empty,
                        ptInf.Tel2 ?? string.Empty,
                        ptInf.Mail ?? string.Empty,
                        ptInf.Setanusi ?? string.Empty,
                        ptInf.Zokugara ?? string.Empty,
                        ptInf.Job ?? string.Empty,
                        ptInf.RenrakuName ?? string.Empty,
                        ptInf.RenrakuPost ?? string.Empty,
                        ptInf.RenrakuAddress1 ?? string.Empty,
                        ptInf.RenrakuAddress2 ?? string.Empty,
                        ptInf.RenrakuTel ?? string.Empty,
                        ptInf.RenrakuMemo ?? string.Empty,
                        ptInf.OfficeName ?? string.Empty,
                        ptInf.OfficePost ?? string.Empty,
                        ptInf.OfficeAddress1 ?? string.Empty,
                        ptInf.OfficeAddress2 ?? string.Empty,
                        ptInf.OfficeTel ?? string.Empty,
                        ptInf.OfficeMemo ?? string.Empty,
                        ptInf.IsRyosyoDetail,
                        ptInf.PrimaryDoctor,
                        ptInf.IsTester,
                        ptInf.MainHokenPid,
                        string.Empty,
                        0,
                        0,
                        0,
                        string.Empty,
                        0
                    );
        }

        public void ReleaseResource()
        {
            DisposeDataContext();
            _receptionRepository.ReleaseResource();
        }

        public List<PatientInforModel> SearchPatient(int hpId, long ptId, int pageIndex, int pageSize)
        {
            string keyword = ptId.ToString();

            List<PatientInforModel> result;
            var ptInfs = NoTrackingDataContext.PtInfs
                .Where(x => x.HpId == hpId && x.IsDelete == 0 && x.PtId.ToString().Contains(keyword))
                .OrderBy(x => x.PtNum)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var ptIdList = ptInfs.Select(p => p.PtId).ToList();

            var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                             item.IsDeleted == DeleteTypes.None &&
                                                                             ptIdList.Contains(item.PtId));

            var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                     item.IsDeleted == DeleteTypes.None &&
                                                                                     item.KarteStatus == KarteStatusConst.Official);

            var raiinInfs = (from raiinInf in listRaiinInf
                             join karteEdition in listKarteEdition on
                                    raiinInf.RaiinNo equals karteEdition.RaiinNo
                             select raiinInf)
                .GroupBy(raiinInf => new { raiinInf.HpId, raiinInf.PtId })
                .Select(grp => new
                {
                    grp.Key.PtId,
                    SinDate = grp.OrderByDescending(x => x.SinDate).Select(x => x.SinDate).FirstOrDefault()
                })
                .ToList();

            result = ptInfs.Select((x) => new PatientInforModel(
                            x.HpId,
                            x.PtId,
                            x.PtNum,
                            x.KanaName ?? string.Empty,
                            x.Name ?? string.Empty,
                            x.Birthday,
                            raiinInfs.Any(s => s.PtId == x.PtId) ? raiinInfs.First(s => s.PtId == x.PtId).SinDate : 0
                            ))
                            .ToList();
            return result;
        }

        public List<PatientInforModel> SearchPatient(int hpId, int startDate, string startTime, int endDate, string endTime)
        {
            var startTimeFormat = (startTime + "00").PadLeft(6, '0');
            var endTimeFormat = (endTime + "60").PadLeft(6, '0');
            var ptIdList = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId
                                                                         && item.Status >= RaiinState.ConsultationCompleted
                                                                         && item.IsDeleted == DeleteTypes.None
                                                                         && (item.SinDate > startDate || (item.SinDate == startDate && string.Compare(item.UketukeTime, startTimeFormat) >= 0))
                                                                         && (item.SinDate < endDate || (item.SinDate == endDate && string.Compare(item.UketukeTime, endTimeFormat) <= 0)))
                                                           .Select(item => item.PtId)
                                                           .Distinct()
                                                           .ToList();

            var result = NoTrackingDataContext.PtInfs.Where(item => item.HpId == hpId
                                                                    && item.IsDelete != 1
                                                                    && ptIdList.Contains(item.PtId))
                                                     .Select(item => new PatientInforModel(
                                                                            item.PtId,
                                                                            item.PtNum,
                                                                            item.Name ?? string.Empty,
                                                                            item.KanaName ?? string.Empty,
                                                                            item.Sex,
                                                                            item.Birthday))
                                                     .ToList();
            return result;
        }

        public List<PatientInforModel> SearchPatient(int hpId, List<long> ptIdList)
        {
            ptIdList = ptIdList.Distinct().ToList();
            var result = NoTrackingDataContext.PtInfs.Where(item => item.HpId == hpId
                                                                    && item.IsDelete != 1
                                                                    && ptIdList.Contains(item.PtId))
                                                     .Select(item => new PatientInforModel(
                                                                            item.PtId,
                                                                            item.PtNum,
                                                                            item.Name ?? string.Empty,
                                                                            item.KanaName ?? string.Empty,
                                                                            item.Sex,
                                                                            item.Birthday))
                                                     .ToList();
            return result;
        }

        public int GetIsRyosyoDetail(int hpId, long ptId)
        {
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(item => item.HpId == hpId && item.PtId == ptId);
            return ptInf?.IsRyosyoDetail ?? 0;
        }

        private class PatientInfQueryModel
        {
            public PtInf PtInf { get; set; } = new();

            public int LastVisitDate { get; set; }

            public DateTime? LastAppointmentDepartment { get; set; }

            public DateTime? NextAppointmentDepartment { get; set; }
        }

        private List<PatientInforModel> SortData(IQueryable<PatientInfQueryModel> ptInfWithLastVisitDate, Dictionary<string, string> sortData, int pageIndex, int pageSize)
        {
            if (!sortData.Any())
            {
                return ptInfWithLastVisitDate
                       .Skip((pageIndex - 1) * pageSize)
                       .Take(pageSize)
                       .AsEnumerable()
                       .Select(p => ToModel(p.PtInf, string.Empty, p.LastVisitDate))
                       .ToList();
            }
            int index = 1;
            var sortQuery = ptInfWithLastVisitDate.OrderBy(item => item.PtInf.PtId);
            foreach (var item in sortData)
            {
                int field = int.Parse(item.Key);
                string typeSort = item.Value.Replace(" ", string.Empty).ToLower();
                if (index == 1)
                {
                    sortQuery = OrderByAction((FieldSortPatientEnum)field, typeSort, sortQuery);
                    index++;
                    continue;
                }
                sortQuery = ThenOrderByAction((FieldSortPatientEnum)field, typeSort, sortQuery);
            }

            var result = sortQuery
                         .Skip((pageIndex - 1) * pageSize)
                         .Take(pageSize)
                         .AsEnumerable()
                         .Select(p => ToPatientDenrakuModel(p.PtInf, string.Empty, p.LastVisitDate, p.LastAppointmentDepartment, p.NextAppointmentDepartment))
                         .ToList();
            return result;
        }

        private IOrderedQueryable<PatientInfQueryModel> OrderByAction(FieldSortPatientEnum field, string typeSort, IOrderedQueryable<PatientInfQueryModel> sortQuery)
        {
            switch (field)
            {
                case FieldSortPatientEnum.PtId:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.PtId);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.PtId);
                    }
                    break;
                case FieldSortPatientEnum.PtNum:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.PtNum);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.PtNum);
                    }
                    break;
                case FieldSortPatientEnum.KanaName:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.KanaName);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.KanaName);
                    }
                    break;
                case FieldSortPatientEnum.Name:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.Name);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.Name);
                    }
                    break;
                case FieldSortPatientEnum.Birthday:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.Birthday);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.Birthday);
                    }
                    break;
                case FieldSortPatientEnum.Sex:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.Sex);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.Sex);
                    }
                    break;
                case FieldSortPatientEnum.Age:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.Birthday);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.Birthday);
                    }
                    break;
                case FieldSortPatientEnum.Tel1:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.Tel1);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.Tel1);
                    }
                    break;
                case FieldSortPatientEnum.Tel2:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.Tel2);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.Tel2);
                    }
                    break;
                case FieldSortPatientEnum.RenrakuTel:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.RenrakuTel);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.RenrakuTel);
                    }
                    break;
                case FieldSortPatientEnum.HomePost:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.HomePost);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.HomePost);
                    }
                    break;
                case FieldSortPatientEnum.HomeAddress:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.PtInf.HomeAddress1 + '\u3000' + item.PtInf.HomeAddress2);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.PtInf.HomeAddress1 + '\u3000' + item.PtInf.HomeAddress2);
                    }
                    break;
                case FieldSortPatientEnum.LastVisitDate:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.OrderByDescending(item => item.LastVisitDate);
                    }
                    else
                    {
                        sortQuery = sortQuery.OrderBy(item => item.LastVisitDate);
                    }
                    break;
            }
            return sortQuery;
        }

        private IOrderedQueryable<PatientInfQueryModel> ThenOrderByAction(FieldSortPatientEnum field, string typeSort, IOrderedQueryable<PatientInfQueryModel> sortQuery)
        {
            switch (field)
            {
                case FieldSortPatientEnum.PtId:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.PtId);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.PtId);
                    }
                    break;
                case FieldSortPatientEnum.PtNum:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.PtNum);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.PtNum);
                    }
                    break;
                case FieldSortPatientEnum.KanaName:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.KanaName);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.KanaName);
                    }
                    break;
                case FieldSortPatientEnum.Name:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.Name);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.Name);
                    }
                    break;
                case FieldSortPatientEnum.Birthday:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.Birthday);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.Birthday);
                    }
                    break;
                case FieldSortPatientEnum.Sex:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.Sex);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.Sex);
                    }
                    break;
                case FieldSortPatientEnum.Age:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.Birthday);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.Birthday);
                    }
                    break;
                case FieldSortPatientEnum.Tel1:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.Tel1);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.Tel1);
                    }
                    break;
                case FieldSortPatientEnum.Tel2:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.Tel2);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.Tel2);
                    }
                    break;
                case FieldSortPatientEnum.RenrakuTel:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.RenrakuTel);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.RenrakuTel);
                    }
                    break;
                case FieldSortPatientEnum.HomePost:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.HomePost);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.HomePost);
                    }
                    break;
                case FieldSortPatientEnum.HomeAddress:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.PtInf.HomeAddress1 + '\u3000' + item.PtInf.HomeAddress2);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.PtInf.HomeAddress1 + '\u3000' + item.PtInf.HomeAddress2);
                    }
                    break;
                case FieldSortPatientEnum.LastVisitDate:
                    if (typeSort.Equals("desc"))
                    {
                        sortQuery = sortQuery.ThenByDescending(item => item.LastVisitDate);
                    }
                    else
                    {
                        sortQuery = sortQuery.ThenBy(item => item.LastVisitDate);
                    }
                    break;
            }
            return sortQuery;
        }

        public long GetPtIdFromPtNum(int hpId, long ptNum)
        {
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(item => item.HpId == hpId
                                                                                            && item.PtNum == ptNum);
            if (ptInf != null)
            {
                return ptInf.PtId;
            }
            return 0;
        }

        public long IsPatientExist(int hpId, long ptNum)
        {
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(item => item.HpId == hpId
                                                                            && item.IsDelete == 0
                                                                            && item.PtNum == ptNum);
            return ptInf?.PtId ?? 0;
        }

        public int GetCountRaiinAlreadyPaidOfPatientByDate(int hpId, int fromDate, int toDate, long ptId, int raiintStatus)
        {
            return NoTrackingDataContext.RaiinInfs.Count(u => u.PtId == ptId && u.HpId == hpId &&
                                                                              u.SinDate >= fromDate &&
                                                                              u.SinDate <= toDate &&
                                                                              u.Status >= raiintStatus &&
                                                                              u.IsDeleted == DeleteTypes.None);
        }

        public List<PatientInforModel> FindSamePatient(int hpId, string kanjiName, int sex, int birthDay)
        {
            kanjiName = kanjiName.Replace("　", " ");
            return NoTrackingDataContext.PtInfs.Where(p => p.HpId == hpId
                                                        && p.Name != null && p.Name.Replace("　", " ") == kanjiName
                                                        && p.Sex == sex
                                                        && p.Birthday == birthDay
                                                        && p.IsDelete != DeleteTypes.Deleted)
                                               .Select(x => new PatientInforModel(x.HpId,
                                                                                  x.PtId,
                                                                                  x.ReferenceNo,
                                                                                  x.SeqNo,
                                                                                  x.PtNum,
                                                                                  x.KanaName ?? string.Empty,
                                                                                  x.Name ?? string.Empty,
                                                                                  x.Sex,
                                                                                  x.Birthday,
                                                                                  x.LimitConsFlg,
                                                                                  x.IsDead,
                                                                                  x.DeathDate,
                                                                                  x.HomePost ?? string.Empty,
                                                                                  x.HomeAddress1 ?? string.Empty,
                                                                                  x.HomeAddress2 ?? string.Empty,
                                                                                  x.Tel1 ?? string.Empty,
                                                                                  x.Tel2 ?? string.Empty,
                                                                                  x.Mail ?? string.Empty,
                                                                                  x.Setanusi ?? string.Empty,
                                                                                  x.Zokugara ?? string.Empty,
                                                                                  x.Job ?? string.Empty,
                                                                                  x.RenrakuName ?? string.Empty,
                                                                                  x.RenrakuPost ?? string.Empty,
                                                                                  x.RenrakuAddress1 ?? string.Empty,
                                                                                  x.RenrakuAddress2 ?? string.Empty,
                                                                                  x.RenrakuTel ?? string.Empty,
                                                                                  x.RenrakuMemo ?? string.Empty,
                                                                                  x.OfficeName ?? string.Empty,
                                                                                  x.OfficePost ?? string.Empty,
                                                                                  x.OfficeAddress1 ?? string.Empty,
                                                                                  x.OfficeAddress2 ?? string.Empty,
                                                                                  x.OfficeTel ?? string.Empty,
                                                                                  x.OfficeMemo ?? string.Empty,
                                                                                  x.IsRyosyoDetail,
                                                                                  x.PrimaryDoctor,
                                                                                  x.IsTester,
                                                                                  x.MainHokenPid,
                                                                                  string.Empty,
                                                                                  0,
                                                                                  0,
                                                                                  0,
                                                                                  string.Empty,
                                                                                  0,
                                                                                  false)).ToList();
        }

        public bool SavePtKyusei(int hpId, int userId, List<PtKyuseiModel> ptKyuseiList)
        {
            var seqNoList = ptKyuseiList.Select(item => item.SeqNo).Distinct().ToList();
            var ptKyuseiDBList = TrackingDataContext.PtKyuseis.Where(item => item.HpId == hpId && seqNoList.Contains(item.SeqNo)).ToList();
            foreach (var model in ptKyuseiList)
            {
                var entity = ptKyuseiDBList.FirstOrDefault(entity => entity.SeqNo == model.SeqNo && entity.PtId == model.PtId);
                if (entity == null)
                {
                    entity = new PtKyusei();
                    entity.HpId = hpId;
                    entity.SeqNo = 0;
                    entity.IsDeleted = 0;
                    entity.CreateDate = CIUtil.GetJapanDateTimeNow();
                    entity.CreateId = userId;
                    entity.PtId = model.PtId;
                }
                entity.Name = model.Name;
                entity.KanaName = model.KanaName;
                entity.EndDate = model.EndDate;
                entity.UpdateDate = CIUtil.GetJapanDateTimeNow();
                entity.UpdateId = userId;
                if (model.IsDeleted)
                {
                    entity.IsDeleted = 1;
                }
                if (entity.SeqNo == 0)
                {
                    TrackingDataContext.PtKyuseis.Add(entity);
                }
            }
            return TrackingDataContext.SaveChanges() > 0;
        }

        public bool CloneByomeiWithNewHokenId(List<PtByomei> ptByomeis, int hokenId, int userId)
        {
            List<PtByomei> newCloneByomeis = new();
            foreach (var ptByomei in ptByomeis)
            {
                var cloneByomei = ptByomei.Clone();
                cloneByomei.CreateId = userId;
                cloneByomei.UpdateId = userId;
                cloneByomei.CreateDate = CIUtil.GetJapanDateTimeNow();
                cloneByomei.UpdateDate = CIUtil.GetJapanDateTimeNow();
                cloneByomei.EntryDate = CIUtil.GetJapanDateTimeNow();
                cloneByomei.HokenPid = hokenId;
                cloneByomei.Id = 0;
                newCloneByomeis.Add(cloneByomei);
            }
            TrackingDataContext.PtByomeis.AddRange(newCloneByomeis);

            foreach (var newCloneByomei in newCloneByomeis)
            {
                newCloneByomei.SeqNo = newCloneByomei.Id;
            }

            return true;
        }

        public List<VisitTimesManagementModel> GetVisitTimesManagementModels(int hpId, int sinYm, long ptId, int kohiId)
        {
            var limitCntListInfList = NoTrackingDataContext.LimitCntListInfs.Where(item => item.HpId == hpId
                                                                                           && item.SinDate / 100 == sinYm
                                                                                           && item.PtId == ptId
                                                                                           && item.KohiId == kohiId
                                                                                           && item.IsDeleted == DeleteTypes.None)
                                                                            .ToList();
            return limitCntListInfList.Select(item => new VisitTimesManagementModel(
                                                          item.PtId,
                                                          item.SinDate,
                                                          item.HokenPid,
                                                          item.KohiId,
                                                          item.SeqNo,
                                                          item.SortKey ?? string.Empty))
                                      .OrderBy(item => item.SortKey)
                                      .ToList();
        }

        public bool UpdateVisitTimesManagement(int hpId, int userId, long ptId, int kohiId, int sinYm, List<VisitTimesManagementModel> visitTimesManagementList)
        {
            var limitCntListInfDBList = TrackingDataContext.LimitCntListInfs.Where(item => item.HpId == hpId
                                                                                           && item.PtId == ptId
                                                                                           && item.KohiId == kohiId)
                                                                            .ToList();
            var maxSeqNo = limitCntListInfDBList.Any() ? limitCntListInfDBList.Max(item => item.SeqNo) : 0;
            limitCntListInfDBList = limitCntListInfDBList.Where(item => item.IsDeleted == 0
                                                                        && item.SinDate / 100 == sinYm)
                                                         .ToList();

            var seqNoList = visitTimesManagementList.Where(item => item.SeqNo >= 0).Select(item => item.SeqNo).Distinct().ToList();
            var deletedVisitTimeList = limitCntListInfDBList.Where(item => item.HokenPid == 0 && !seqNoList.Contains(item.SeqNo)).ToList();
            foreach (var item in deletedVisitTimeList)
            {
                item.IsDeleted = 1;
                item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                item.UpdateId = userId;
            }

            foreach (var model in visitTimesManagementList)
            {
                bool isAddNew = false;
                var entity = limitCntListInfDBList.FirstOrDefault(item => model.SeqNo > 0 && item.SeqNo == model.SeqNo);
                if (entity == null)
                {
                    if (model.SeqNo == 0 && model.IsOutHospital)
                    {
                        entity = new LimitCntListInf();
                        entity.HpId = hpId;
                        entity.PtId = ptId;
                        entity.KohiId = kohiId;
                        entity.SinDate = model.SinDate;
                        entity.SeqNo = maxSeqNo + 1;
                        entity.IsDeleted = 0;
                        entity.CreateDate = CIUtil.GetJapanDateTimeNow();
                        entity.CreateId = userId;
                        entity.SortKey = model.SortKey;
                        maxSeqNo = entity.SeqNo;
                        isAddNew = true;
                    }
                    else
                    {
                        continue;
                    }
                }
                entity.UpdateDate = CIUtil.GetJapanDateTimeNow();
                entity.UpdateId = userId;
                if (model.IsDeleted)
                {
                    entity.IsDeleted = 1;
                }
                if (isAddNew)
                {
                    TrackingDataContext.LimitCntListInfs.Add(entity);
                }
            }
            return TrackingDataContext.SaveChanges() > 0;
        }

        public bool UpdateVisitTimesManagementNeedSave(int hpId, int userId, long ptId, List<VisitTimesManagementModel> visitTimesManagementList)
        {
            var kohiIdList = visitTimesManagementList.Select(item => item.KohiId).Distinct().ToList();
            var limitCntListInfDBList = TrackingDataContext.LimitCntListInfs.Where(item => item.HpId == hpId
                                                                                           && item.PtId == ptId
                                                                                           && kohiIdList.Contains(item.KohiId))
                                                                            .ToList();
            foreach (var kohiId in kohiIdList)
            {
                var visitTimesModelList = visitTimesManagementList.Where(item => item.KohiId == kohiId).ToList();
                var limitCntListInfByKohiDBList = limitCntListInfDBList.Where(item => item.KohiId == kohiId).ToList();
                var maxSeqNo = limitCntListInfByKohiDBList.Any() ? limitCntListInfByKohiDBList.Max(item => item.SeqNo) : 0;

                foreach (var model in visitTimesModelList)
                {
                    var limitCntListInf = new LimitCntListInf()
                    {
                        HpId = hpId,
                        CreateId = userId,
                        CreateDate = CIUtil.GetJapanDateTimeNow(),
                        UpdateId = userId,
                        UpdateDate = CIUtil.GetJapanDateTimeNow(),
                        PtId = ptId,
                        SinDate = model.SinDate,
                        KohiId = kohiId,
                        SeqNo = maxSeqNo + 1,
                        SortKey = model.SortKey,
                    };
                    TrackingDataContext.LimitCntListInfs.Add(limitCntListInf);
                    maxSeqNo = limitCntListInf.SeqNo;
                }
            }
            return TrackingDataContext.SaveChanges() > 0;
        }

        #region Edit Basic Infor - New
        public (bool resultSave, long ptId, List<long> listPtNum, long raiinNo) SavePatientInfo(PatientInforSaveModel ptInf, int userId, string ptMemo, bool isSaveDuplicateInfo, bool isConfirmOnline, int onlineConfirmationId = 0, int sinDate = 0)
        {
            int defaultMaxDate = 99999999;
            int hpId = ptInf.HpId;
            var date = CIUtil.GetJapanDateTimeNow();
            long raiinNo = 0;
            var raiinInf = new RaiinInf();

            if (ptInf.PtId == 0)
            {
                PtInf patientInsert = Mapper.Map(ptInf, new PtInf(), (source, dest) => { return dest; });
                if (patientInsert.PtNum == 0)
                {
                    patientInsert.PtNum = GetAutoPtNum(hpId);
                }
                else
                {
                    var ptExists = NoTrackingDataContext.PtInfs.FirstOrDefault(x => x.PtNum == patientInsert.PtNum && x.HpId == hpId);
                    if (ptExists != null)
                        return (false, ptExists.PtId, new(), raiinNo);
                }

                if (!isSaveDuplicateInfo)
                {
                    var listPtNum = NoTrackingDataContext.PtInfs.Where(x => x.KanaName == patientInsert.KanaName && x.HpId == hpId && x.Birthday == patientInsert.Birthday && x.IsDelete == DeleteTypes.None).Select(e => e.PtNum).ToList();
                    if (listPtNum != null && listPtNum.Count() > 0)
                    {
                        return (false, 0, listPtNum, raiinNo);
                    }
                }
                patientInsert.CreateDate = date;
                patientInsert.CreateId = userId;
                patientInsert.UpdateId = userId;
                patientInsert.UpdateDate = date;
                patientInsert.HpId = hpId;
                //if (isConfirmOnline) patientInsert.ReferenceNo = patientInsert.PtNum;

                string querySql = $"INSERT INTO \"pt_inf\"\r\n(\"hp_id\", \"pt_num\", \"kana_name\", \"name\", \"sex\", \"birthday\", \"is_dead\", \"death_date\", \"home_post\", \"home_address1\", \"home_address2\", \"tel1\", \"tel2\", \"mail\", \"setainusi\", \"zokugara\", \"job\", \"renraku_name\", \"renraku_post\", \"renraku_address1\", \"renraku_address2\", \"renraku_tel\", \"renraku_memo\", \"office_name\", \"office_post\", \"office_address1\", \"office_address2\", \"office_tel\", \"office_memo\", \"primary_doctor\", \"is_tester\", \"is_delete\", \"create_date\", \"create_id\", \"create_machine\", \"update_date\", \"update_id\", \"update_machine\", \"main_hoken_pid\", \"limit_cons_flg\", \"renraku_name2\", \"renraku_tel2\") VALUES({patientInsert.HpId}, {patientInsert.PtNum}, '{patientInsert.KanaName}', '{patientInsert.Name}', {patientInsert.Sex}, {patientInsert.Birthday}, {patientInsert.IsDead}, {patientInsert.DeathDate}, '{patientInsert.HomePost}', '{patientInsert.HomeAddress1}', '{patientInsert.HomeAddress2}', '{patientInsert.Tel1}', '{patientInsert.Tel2}', '{patientInsert.Mail}', '{patientInsert.Setanusi}', '{patientInsert.Zokugara}', '{patientInsert.Job}', '{patientInsert.RenrakuName}', '{patientInsert.RenrakuPost}', '{patientInsert.RenrakuAddress1}', '{patientInsert.RenrakuAddress2}', '{patientInsert.RenrakuTel}', '{patientInsert.RenrakuMemo}', '{patientInsert.OfficeName}', '{patientInsert.OfficePost}', '{patientInsert.OfficeAddress1}', '{patientInsert.OfficeAddress2}', '{patientInsert.OfficeTel}', '{patientInsert.OfficeMemo}', {patientInsert.PrimaryDoctor}, {patientInsert.IsTester}, {patientInsert.IsDelete}, '{patientInsert.CreateDate.ToString("yyyy-MM-dd HH:mm:ss.fffZ")}', {patientInsert.CreateId}, '', '{patientInsert.UpdateDate.ToString("yyyy-MM-dd HH:mm:ss.fffZ")}', {patientInsert.UpdateId}, '', {patientInsert.MainHokenPid}, {patientInsert.LimitConsFlg}, '{patientInsert.RenrakuName2}', '{patientInsert.RenrakuTel2}') ON CONFLICT DO NOTHING;";
                TrackingDataContext.Database.SetCommandTimeout(1200);
                bool resultCreatePatient = TrackingDataContext.Database.ExecuteSqlRaw(querySql) > 0;

                if (!resultCreatePatient)
                {
                    return (false, 0, new(), raiinNo);
                }
                else
                {
                    patientInsert.PtId = NoTrackingDataContext.PtInfs.FirstOrDefault(p => p.HpId == hpId && p.PtNum == patientInsert.PtNum)?.PtId ?? 0;
                    // add patientMemo
                    var patientMemo = new PatientMemo();
                    patientMemo.HpId = hpId;
                    patientMemo.PtId = patientInsert.PtId;
                    patientMemo.MemoHtml = ptMemo;
                    patientMemo.CreatedAt = date;
                    patientMemo.CreatedBy = userId.ToString();
                    patientMemo.UpdatedAt = date;
                    patientMemo.UpdatedBy = userId.ToString();
                    TrackingDataContext.PatientMemos.Add(patientMemo);

                    var jihi = CreateJihi(hpId, patientInsert.PtId, userId);
                    var hokenPattern = CreateHokenPatternForJihi(hpId, patientInsert.PtId, jihi.HokenId, userId);
                    TrackingDataContext.PtHokenPatterns.Add(hokenPattern);

                    if (onlineConfirmationId > 0)
                    {
                        var onlineConfirmationHis = TrackingDataContext.OnlineConfirmationHistories.FirstOrDefault(e => e.HpId == hpId && e.ID == onlineConfirmationId);
                        int confirmationType = 0;
                        string infoConsFlg = string.Empty;
                        int prescriptionIssueType = 0;
                        if (onlineConfirmationHis is not null)
                        {
                            onlineConfirmationHis.PtId = patientInsert.PtId;
                            confirmationType = onlineConfirmationHis.ConfirmationType;
                            infoConsFlg = onlineConfirmationHis.InfoConsFlg ?? string.Empty;
                            prescriptionIssueType = onlineConfirmationHis.PrescriptionIssueType;
                        }
                        var receptionModel = new ReceptionModel(hpId, patientInsert.PtId, sinDate, RaiinState.Confirmation, userId, onlineConfirmationId, hokenPattern.HokenPid, confirmationType, infoConsFlg, prescriptionIssueType);
                        raiinInf = CreateNewRaiinInf(receptionModel, hpId, userId);

                        TrackingDataContext.Add(raiinInf);
                    }
                    var isSaveChange = TrackingDataContext.SaveChanges() > 0;
                    raiinNo = raiinInf.RaiinNo;
                    return (isSaveChange, patientInsert.PtId, new(), raiinNo);
                }
            }
            else
            {
                PtInf? patientInfo = TrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptInf.PtId);
                if (patientInfo == null) return (false, 0, new(), raiinNo);

                Mapper.Map(ptInf, patientInfo, (source, dest) =>
                {
                    dest.UpdateDate = date;
                    dest.UpdateId = userId;
                    return dest;
                });

                if (patientInfo.PtNum <= 0)
                {
                    patientInfo.PtNum = GetAutoPtNum(hpId);

                    var isExistJihi = NoTrackingDataContext.PtHokenInfs.Any(e =>
                        e.HpId == hpId &&
                        e.PtId == patientInfo.PtId &&
                        e.Houbetu == HokenConstant.HOUBETU_JIHI_108 &&
                        e.HokenNo == 108 &&
                        e.IsDeleted == DeleteTypes.None);

                    if (!isExistJihi)
                    {
                        var jihi = CreateJihi(hpId, patientInfo.PtId, userId);
                        var hokenPattern = CreateHokenPatternForJihi(hpId, patientInfo.PtId, jihi.HokenId, userId);
                        TrackingDataContext.PtHokenPatterns.Add(hokenPattern);
                    }
                }

                var patientMemo = TrackingDataContext.PatientMemos.FirstOrDefault(e => e.HpId == hpId && e.PtId == ptInf.PtId && e.IsDeleted == DeleteTypes.None);
                if (patientMemo != null)
                {
                    patientMemo.MemoHtml = ptMemo;
                    patientMemo.UpdatedAt = date;
                    patientMemo.UpdatedBy = userId.ToString();
                }
                else
                {
                    patientMemo = new PatientMemo();
                    patientMemo.HpId = hpId;
                    patientMemo.PtId = patientInfo.PtId;
                    patientMemo.MemoHtml = ptMemo;
                    patientMemo.CreatedAt = date;
                    patientMemo.CreatedBy = userId.ToString();
                    patientMemo.UpdatedAt = date;
                    patientMemo.UpdatedBy = userId.ToString();
                    TrackingDataContext.PatientMemos.Add(patientMemo);
                }

                return (TrackingDataContext.SaveChanges() > 0, patientInfo.PtId, new(), raiinNo);
            }
        }

        public (bool resultSave, long ptId, int hokenId, long onlineConfirmHistoryId) SaveInsuranceInfo(
            int hpId,
            int ptId,
            HokenInfModel hokenInf,
            int userId,
            int sinDate,
            List<PatientInforConfirmOnlineDto> patientInfoFields,
            PtKyuseiModel? ptKyuseiModel,
            OnlineConfirmationHistoryModel onlineCmfHisModel,
            bool isConfirmOnline,
            EndDateModel? endDateModel)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();

                int defaultMaxDate = 99999999;
                int hokenId = 0;
                PtInf? patientInfo = TrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId);
                if (patientInfo is null) return (false, ptId, hokenId, 0);

                var japanDate = CIUtil.GetJapanDateTimeNow();

                #region online confirm
                long onlineConfirmHistoryId = 0;
                if (isConfirmOnline)
                {

                    if (endDateModel != null)
                    {
                        var oldHokenInf = TrackingDataContext.PtHokenInfs.FirstOrDefault(e => e.HpId == hpId && e.PtId == ptId && e.HokenId == endDateModel.HokenId && e.SeqNo == endDateModel.SeqNo && e.IsDeleted == DeleteTypes.None);
                        if (oldHokenInf != null)
                        {
                            oldHokenInf.EndDate = endDateModel.EndDate;
                            oldHokenInf.UpdateDate = japanDate;
                            oldHokenInf.UpdateId = userId;
                        }
                    }

                    if (patientInfoFields.Any())
                    {
                        patientInfo.CreateDate = DateTime.SpecifyKind(patientInfo.CreateDate, DateTimeKind.Utc);
                        patientInfo.UpdateDate = japanDate;
                        patientInfo.UpdateId = userId;

                        if (ptKyuseiModel != null)
                        {
                            var ptKyusei = new PtKyusei()
                            {
                                HpId = hpId,
                                PtId = ptId,
                                KanaName = ptKyuseiModel.KanaName,
                                Name = ptKyuseiModel.Name,
                                EndDate = ptKyuseiModel.EndDate,
                                CreateDate = japanDate,
                                CreateId = userId,
                                UpdateDate = japanDate,
                                UpdateId = userId
                            };
                            TrackingDataContext.PtKyuseis.Add(ptKyusei);
                        }
                    }

                    foreach (var item in patientInfoFields)
                    {
                        string pascalCaseCellName = FirstCharToUpper(item.FieldName);

                        switch (pascalCaseCellName)
                        {
                            case nameof(PtInf.Name):
                                patientInfo.Name = item.FieldValue;
                                break;
                            case nameof(PtInf.KanaName):
                                patientInfo.KanaName = item.FieldValue;
                                break;
                            case nameof(PtInf.Birthday):
                                patientInfo.Birthday = int.Parse(item.FieldValue);
                                break;
                            case nameof(PtInf.Sex):
                                patientInfo.Sex = int.Parse(item.FieldValue);
                                break;
                            case nameof(PtInf.HomePost):
                                patientInfo.HomePost = item.FieldValue;
                                break;
                            case nameof(PtInf.HomeAddress1):
                                patientInfo.HomeAddress1 = item.FieldValue;
                                break;
                            case nameof(PtInf.HomeAddress2):
                                patientInfo.HomeAddress2 = item.FieldValue;
                                break;
                            case nameof(PtInf.Setanusi):
                                patientInfo.Setanusi = item.FieldValue;
                                break;
                        }
                    }

                    var onlineConfirmationHistory = new OnlineConfirmationHistory()
                    {
                        HpId = hpId,
                        PtId = ptId,
                        OnlineConfirmationDate = onlineCmfHisModel.OnlineConfirmationDate,
                        ConfirmationType = onlineCmfHisModel.ConfirmationType,
                        ConfirmationResult = onlineCmfHisModel.ConfirmationResult,
                        CreateDate = japanDate,
                        CreateId = userId,
                        InfoConsFlg = onlineCmfHisModel.InfoConsFlg,
                        PrescriptionIssueType = onlineCmfHisModel.PrescriptionIssueType,
                        UketukeStatus = onlineCmfHisModel.UketukeStatus,
                        UpdateDate = japanDate,
                        UpdateId = userId
                    };

                    TrackingDataContext.OnlineConfirmationHistories.Add(onlineConfirmationHistory);
                    TrackingDataContext.SaveChanges();

                    onlineConfirmHistoryId = onlineConfirmationHistory.ID;
                    if (onlineConfirmHistoryId <= 0)
                    {
                        transaction.Rollback();
                        return (false, 0, 0, 0);
                    }

                    //patientInfo.ReferenceNo = patientInfo.PtNum;
                }
                #endregion

                #region HokenInf
                //Add New
                if (hokenInf.SeqNo == 0)
                {
                    var ptHokenInf = new PtHokenInf();
                    Mapper.Map<HokenInfModel, PtHokenInf>(hokenInf, ptHokenInf, (src, dest) =>
                    {
                        dest.CreateId = userId;
                        dest.CreateDate = japanDate;
                        dest.UpdateId = userId;
                        dest.UpdateDate = japanDate;
                        dest.PtId = ptId;
                        dest.HpId = hpId;
                        dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                        dest.HokenId = GenHokenId(hpId, ptId);

                        #region PtRousaiTenki
                        TrackingDataContext.PtRousaiTenkis.AddRange(Mapper.Map<RousaiTenkiModel, PtRousaiTenki>(src.ListRousaiTenki, (srcR, destR) =>
                        {
                            destR.CreateId = userId;
                            destR.PtId = patientInfo.PtId;
                            destR.HpId = hpId;
                            destR.Tenki = srcR.RousaiTenkiTenki;
                            destR.Sinkei = srcR.RousaiTenkiSinkei;
                            destR.EndDate = srcR.RousaiTenkiEndDate;
                            destR.HokenId = dest.HokenId;
                            destR.UpdateId = userId;
                            destR.CreateDate = japanDate;
                            destR.UpdateDate = japanDate;
                            return destR;
                        }));
                        #endregion

                        #region PtHokenCheck
                        var confirmDates = Mapper.Map<ConfirmDateModel, PtHokenCheck>(src.ConfirmDateList, (srcCf, destCf) =>
                        {
                            var checkDate = srcCf.ConfirmDate == 0 ? sinDate : srcCf.ConfirmDate;
                            destCf.CreateId = userId;
                            destCf.UpdateId = userId;
                            destCf.CreateDate = japanDate;
                            destCf.UpdateDate = japanDate;
                            destCf.CheckDate = DateTime.SpecifyKind(CIUtil.IntToDate(checkDate), DateTimeKind.Utc);
                            destCf.CheckCmt = srcCf.CheckComment;
                            destCf.HokenId = dest.HokenId;
                            destCf.CheckId = userId;
                            destCf.PtID = patientInfo.PtId;
                            destCf.HpId = hpId;
                            destCf.HokenGrp = 1;
                            destCf.OnlineConfirmationId = onlineConfirmHistoryId == 0 ? srcCf.OnlineConfirmationId : onlineConfirmHistoryId.AsInteger();
                            return destCf;
                        }).DistinctBy(e => e.CheckDate).ToList();

                        TrackingDataContext.PtHokenChecks.AddRange(confirmDates);
                        #endregion

                        return dest;
                    });
                    TrackingDataContext.PtHokenInfs.Add(ptHokenInf);
                    hokenId = ptHokenInf.HokenId;
                }
                else
                {
                    //Update
                    var updateHokenInf = TrackingDataContext.PtHokenInfs.FirstOrDefault(x => x.PtId == patientInfo.PtId && x.HpId == patientInfo.HpId && x.SeqNo == hokenInf.SeqNo && x.IsDeleted == DeleteTypes.None);

                    if (updateHokenInf != null)
                    {
                        if (updateHokenInf.HokenId == 1 && updateHokenInf.HokenKbn == 0 && updateHokenInf.Houbetu == HokenConstant.HOUBETU_JIHI_108) return (false, ptId, hokenId, 0);
                        var databaseHokenChecks = TrackingDataContext.PtHokenChecks.Where(c => c.PtID == patientInfo.PtId && c.HpId == patientInfo.HpId && c.IsDeleted == DeleteTypes.None).ToList();
                        //Info inf
                        Mapper.Map(hokenInf, updateHokenInf, (src, dest) =>
                        {
                            dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                            dest.UpdateDate = japanDate;
                            dest.UpdateId = userId;
                            return dest;
                        });

                        //ConfirmDate
                        UpdateHokenCheck(databaseHokenChecks, hokenInf.ConfirmDateList, patientInfo.HpId, patientInfo.PtId, updateHokenInf.HokenId, userId, false, (int)onlineConfirmHistoryId);

                        //RousaiTenkik
                        var listAddTenki = Mapper.Map<RousaiTenkiModel, PtRousaiTenki>(hokenInf.ListRousaiTenki.Where(x => x.SeqNo == 0), (src, dest) =>
                        {
                            dest.Sinkei = src.RousaiTenkiSinkei;
                            dest.Tenki = src.RousaiTenkiTenki;
                            dest.EndDate = src.RousaiTenkiEndDate;
                            dest.PtId = patientInfo.PtId;
                            dest.HpId = hpId;
                            dest.HokenId = updateHokenInf.HokenId;
                            dest.CreateId = userId;
                            dest.CreateDate = japanDate;
                            dest.UpdateId = userId;
                            dest.UpdateDate = japanDate;
                            return dest;
                        });
                        TrackingDataContext.PtRousaiTenkis.AddRange(listAddTenki);


                        if (hokenInf.ListRousaiTenki.Count > 0)
                        {
                            var listRousaiTenki = hokenInf.ListRousaiTenki.Where(x => x.SeqNo != 0).ToList();
                            var databasePtRousaiTenkis = TrackingDataContext.PtRousaiTenkis.Where(c => c.PtId == patientInfo.PtId && c.HpId == patientInfo.HpId && c.HokenId == updateHokenInf.HokenId && c.IsDeleted == DeleteTypes.None).ToList();

                            foreach (var rsTkUpdate in listRousaiTenki)
                            {
                                var updateItem = databasePtRousaiTenkis.FirstOrDefault(x => x.SeqNo == rsTkUpdate.SeqNo);
                                if (updateItem != null)
                                {
                                    if ((updateItem.Sinkei != rsTkUpdate.RousaiTenkiSinkei ||
                                         updateItem.Tenki != rsTkUpdate.RousaiTenkiTenki ||
                                         updateItem.EndDate != rsTkUpdate.RousaiTenkiEndDate) &&
                                         updateItem.IsDeleted == DeleteTypes.None)
                                    {
                                        updateItem.Sinkei = rsTkUpdate.RousaiTenkiSinkei;
                                        updateItem.Tenki = rsTkUpdate.RousaiTenkiTenki;
                                        updateItem.EndDate = rsTkUpdate.RousaiTenkiEndDate;
                                        updateItem.UpdateDate = japanDate;
                                        updateItem.UpdateId = userId;
                                    }

                                }
                            }

                            var listDatabaseByHokenInf = databasePtRousaiTenkis.Where(x => x.HokenId == updateHokenInf.HokenId);
                            var listRemoves = listDatabaseByHokenInf.Where(x => !hokenInf.ListRousaiTenki.Any(m => m.SeqNo == x.SeqNo)).ToList();

                            listRemoves.ForEach(x =>
                            {
                                x.IsDeleted = 1;
                                x.UpdateId = userId;
                                x.UpdateDate = japanDate;
                            });
                        }

                        hokenId = updateHokenInf.HokenId;
                    }
                }

                var isSuccess = TrackingDataContext.SaveChanges();
                if (isSuccess > 0) transaction.Commit();
                else transaction.Rollback();

                return (isSuccess > 0, patientInfo.PtId, hokenId, onlineConfirmHistoryId);
            });

            #endregion HokenInf

        }

        #endregion
        public CheckDrawerLedgerDataExistedModel CheckDrawerLedgerDataExisted(int hpId, long ptId, long raiinNo, int sinDate)
        {
            var result = new CheckDrawerLedgerDataExistedModel();
            var odrKouiKbn = new List<int> { 21, 22, 23, 28 };
            var order = TrackingDataContext.OdrInfs.Where(x => x.HpId == hpId &&
                                                                        x.PtId == ptId &&
                                                                        x.IsDeleted == DeleteTypes.None &&
                                                                        odrKouiKbn.Contains(x.OdrKouiKbn) &&
                                                                        x.RaiinNo == raiinNo &&
                                                                        x.SinDate == sinDate).ToList();

            var inOrder = order.FirstOrDefault(o => o.InoutKbn == 0);

            if (inOrder != null)
            {
                result.ExistedInOrder = true;
                result.ExistedDrugInfo = true;
            }

            var outOrder = order.FirstOrDefault(o => o.InoutKbn == 1);
            if (outOrder != null)
            {
                result.ExistedOutOrder = true;
            }

            var raiinInf = TrackingDataContext.RaiinInfs.FirstOrDefault(x => x.HpId == hpId &&
                                                                             x.PtId == ptId &&
                                                                             x.IsDeleted == DeleteTypes.None &&
                                                                             x.RaiinNo == raiinNo &&
                                                                             x.SinDate == sinDate &&
                                                                             (x.Status == RaiinState.Paid || x.Status == RaiinState.FcoWaiting));

            if (raiinInf != null)
            {
                result.ExistedReceiptData = true;
                result.ExistedDetailData = true;
            }

            var karteEdition = NoTrackingDataContext.KarteEditions.FirstOrDefault(e => e.HpId == hpId && e.PtId == ptId && e.RaiinNo == raiinNo && e.SinDate == sinDate && e.IsDeleted == DeleteTypes.None);
            if (karteEdition != null)
            {
                var odrInf = NoTrackingDataContext.OdrInfs.Any(e => e.HpId == hpId && e.PtId == ptId && e.RaiinNo == raiinNo && e.SinDate == sinDate && e.Edition == karteEdition.Edition && e.OdrKouiKbn != 10 && e.IsDeleted == DeleteTypes.None);
                result.ExistedSijisenCo = odrInf;
            }

            return result;
        }

        private int GenHokenId(int hpId, long ptId)
        {
            var hokenInf = NoTrackingDataContext.PtHokenInfs.OrderByDescending(x => x.HokenId).FirstOrDefault(e => e.HpId == hpId && e.PtId == ptId);
            int hokenId = 1;
            if (hokenInf != null) hokenId += hokenInf.HokenId;
            return hokenId;
        }

        private PtHokenInf CreateJihi(int hpId, long ptId, int userId)
        {
            var date = CIUtil.GetJapanDateTimeNow();
            var jihi = new PtHokenInf();
            jihi.HpId = hpId;
            jihi.PtId = ptId;
            jihi.HokenId = GenHokenId(hpId, ptId);
            jihi.CreateDate = date;
            jihi.CreateId = userId;
            jihi.UpdateDate = date;
            jihi.UpdateId = userId;
            jihi.HokenNo = int.Parse(HokenConstant.HOUBETU_JIHI_108);
            jihi.Houbetu = HokenConstant.HOUBETU_JIHI_108;
            jihi.StartDate = 0;
            jihi.EndDate = 99999999;
            TrackingDataContext.PtHokenInfs.Add(jihi);
            return jihi;
        }

        private PtHokenPattern CreateHokenPatternForJihi(int hpId, long ptId, int hokenId, int userId)
        {
            var date = CIUtil.GetJapanDateTimeNow();
            var hokenPids = TrackingDataContext.PtHokenPatterns
                .Where(p => p.HpId == hpId && p.PtId == ptId)
                .Select(p => p.HokenPid);

            int maxHokenPid = hokenPids.Any() ? hokenPids.Max() : 0;
            int hokenPid = maxHokenPid + 1;
            var hokenPattern = new PtHokenPattern()
            {
                HpId = hpId,
                PtId = ptId,
                HokenPid = hokenPid,
                HokenId = hokenId,
                CreateDate = date,
                CreateId = userId,
                UpdateDate = date,
                UpdateId = userId,
                StartDate = 0,
                EndDate = 99999999,
            };
            return hokenPattern;
        }
        public CheckPatientHokenInfoDifferenceDto CheckPatientHokenInfoDifference(int hpid, long ptId, int age, int sinDate, CheckPatientHokenInfoDifferenceModel checkPatientHokenInfoDifferenceModel)
        {

            checkPatientHokenInfoDifferenceModel = new CheckPatientHokenInfoDifferenceModel(
                checkPatientHokenInfoDifferenceModel.InsurerNumber.Trim(),
                checkPatientHokenInfoDifferenceModel.InsuredCardSymbol?.Trim(),
                checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber?.Trim(),
                checkPatientHokenInfoDifferenceModel.InsuredBranchNumber?.Trim(),
                checkPatientHokenInfoDifferenceModel.PersonalFamilyClassification,
                checkPatientHokenInfoDifferenceModel.InsuredCardValidDate,
                checkPatientHokenInfoDifferenceModel.InsuredCardExpirationDate == 0 ? 99999999 : checkPatientHokenInfoDifferenceModel.InsuredCardExpirationDate,
                checkPatientHokenInfoDifferenceModel.InsuredCertificateIssuanceDate,
                checkPatientHokenInfoDifferenceModel.LimitApplicationCertificateClassificationFlag.Trim(),
                checkPatientHokenInfoDifferenceModel.InsuredCardClassification
            );

            var hokenInf = NoTrackingDataContext.PtHokenInfs
              .Where(c => c.HpId == hpid
                  && c.PtId == ptId
                  && (c.HokensyaNo == HenkanJ.ToFullsize(checkPatientHokenInfoDifferenceModel.InsurerNumber)
                    || c.HokensyaNo == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsurerNumber)
                    || c.HokensyaNo == checkPatientHokenInfoDifferenceModel.InsurerNumber
                    || (c.HokensyaNo ?? string.Empty) == (checkPatientHokenInfoDifferenceModel.InsurerNumber ?? string.Empty))
                  && (c.Kigo == HenkanJ.ToFullsize(checkPatientHokenInfoDifferenceModel.InsuredCardSymbol)
                    || c.Kigo == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsuredCardSymbol)
                    || c.Kigo == checkPatientHokenInfoDifferenceModel.InsuredCardSymbol
                    || (c.Kigo ?? string.Empty) == (checkPatientHokenInfoDifferenceModel.InsuredCardSymbol ?? string.Empty))
                  && (c.Bango == HenkanJ.ToFullsize(checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber)
                    || c.Bango == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber)
                    || c.Bango == checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber
                    || (c.Bango ?? string.Empty) == (checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber ?? string.Empty))
                  && (string.IsNullOrEmpty(checkPatientHokenInfoDifferenceModel.InsuredBranchNumber)
                    || (c.EdaNo == HenkanJ.ToFullsize(checkPatientHokenInfoDifferenceModel.InsuredBranchNumber)
                    || c.EdaNo == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsuredBranchNumber))
                    || c.EdaNo == checkPatientHokenInfoDifferenceModel.InsuredBranchNumber)
                  && (checkPatientHokenInfoDifferenceModel.PersonalFamilyClassification == 0 || c.HonkeKbn == checkPatientHokenInfoDifferenceModel.PersonalFamilyClassification)
                  && (checkPatientHokenInfoDifferenceModel.InsuredCertificateIssuanceDate == 0 || c.KofuDate == checkPatientHokenInfoDifferenceModel.InsuredCertificateIssuanceDate)
                  && (checkPatientHokenInfoDifferenceModel.InsuredCardValidDate == 0 || c.StartDate == checkPatientHokenInfoDifferenceModel.InsuredCardValidDate)
                  && ((checkPatientHokenInfoDifferenceModel.InsuredCardClassification == 5 && c.HokenNo == 68 && c.HokenEdaNo == 0) || (checkPatientHokenInfoDifferenceModel.InsuredCardClassification != 5 && !(c.HokenNo == 68 && c.HokenEdaNo == 0)))
                  && (c.EndDate == checkPatientHokenInfoDifferenceModel.InsuredCardExpirationDate || c.EndDate == 0 || checkPatientHokenInfoDifferenceModel.InsuredCardExpirationDate == 99999999)
                  && c.IsDeleted == DeleteTypes.None)
              .AsEnumerable()
              .Where(c => NormalizeKogakuValue(GetKogakuValue(age, c.HokensyaNo, sinDate, Convert.ToString(c.KogakuKbn)))
                       == NormalizeKogakuValue(GetKogakuValue(age, checkPatientHokenInfoDifferenceModel.InsurerNumber, sinDate, MapLimitApplicationCertificateFlag(checkPatientHokenInfoDifferenceModel.LimitApplicationCertificateClassificationFlag, age))))
              .OrderByDescending(c => c.HokenId)
              .FirstOrDefault();

            if (hokenInf != null)
            {
                return new CheckPatientHokenInfoDifferenceDto(
                    new HokensyaNo() { Value = string.Empty, XmlValue = string.Empty, IsMap = true },
                    new Kigo() { Value = string.Empty, XmlValue = string.Empty, IsMap = true },
                    new Bango() { Value = string.Empty, XmlValue = string.Empty, IsMap = true },
                    new HokenEdaNo() { Value = string.Empty, XmlValue = string.Empty, IsMap = true },
                    new HonkeKbn() { Value = 0, XmlValue = 0, IsMap = true },
                    new KofuDate() { Value = 0, XmlValue = 0, IsMap = true },
                    new StartDate() { Value = 0, XmlValue = 0, IsMap = true },
                    new EndDate() { Value = 0, XmlValue = 0, IsMap = true },
                    new KogakuKbn() { Value = string.Empty, XmlValue = string.Empty, IsMap = true },
                    new HokenInfo() { Value = string.Empty, XmlValue = string.Empty, IsMap = true },
                    isMapAll: true,
                    seqNo: hokenInf.SeqNo,
                    hokenId: hokenInf.HokenId,
                    hokenName: FormatHokenInfoName(hokenInf.HokenId, hokenInf.Houbetu)
                );
            }

            hokenInf = NoTrackingDataContext.PtHokenInfs
                .Where(c => c.HpId == hpid
                    && c.PtId == ptId
                    && (c.HokensyaNo == HenkanJ.ToFullsize(checkPatientHokenInfoDifferenceModel.InsurerNumber)
                    || c.HokensyaNo == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsurerNumber)
                    || c.HokensyaNo == checkPatientHokenInfoDifferenceModel.InsurerNumber
                    || (c.HokensyaNo ?? string.Empty) == (checkPatientHokenInfoDifferenceModel.InsurerNumber ?? string.Empty))
                  && (c.Kigo == HenkanJ.ToFullsize(checkPatientHokenInfoDifferenceModel.InsuredCardSymbol)
                    || c.Kigo == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsuredCardSymbol)
                    || c.Kigo == checkPatientHokenInfoDifferenceModel.InsuredCardSymbol
                    || (c.Kigo ?? string.Empty) == (checkPatientHokenInfoDifferenceModel.InsuredCardSymbol ?? string.Empty))
                  && (c.Bango == HenkanJ.ToFullsize(checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber)
                    || c.Bango == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber)
                    || c.Bango == checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber
                    || (c.Bango ?? string.Empty) == (checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber ?? string.Empty))
                    && c.IsDeleted == DeleteTypes.None)
                .OrderByDescending(c => c.HokenId)
                .FirstOrDefault();

            if (hokenInf == null)
            {
                return new CheckPatientHokenInfoDifferenceDto();
            }

            return new CheckPatientHokenInfoDifferenceDto(
                new HokensyaNo() { Value = hokenInf.HokensyaNo, XmlValue = checkPatientHokenInfoDifferenceModel.InsurerNumber, IsMap = HenkanJ.ToHalfsize(hokenInf.HokensyaNo) == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsurerNumber) },
                new Kigo() { Value = hokenInf.Kigo, XmlValue = checkPatientHokenInfoDifferenceModel.InsuredCardSymbol, IsMap = HenkanJ.ToHalfsize(hokenInf.Kigo) == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsuredCardSymbol) },
                new Bango() { Value = hokenInf.Bango, XmlValue = checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber, IsMap = HenkanJ.ToHalfsize(hokenInf.Bango) == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsuredIdentificationNumber) },
                new HokenEdaNo() {
                    Value = hokenInf.EdaNo,
                    XmlValue = checkPatientHokenInfoDifferenceModel.InsuredBranchNumber,
                    IsMap = HenkanJ.ToHalfsize(hokenInf.EdaNo) == HenkanJ.ToHalfsize(checkPatientHokenInfoDifferenceModel.InsuredBranchNumber)
                     || string.IsNullOrEmpty(checkPatientHokenInfoDifferenceModel.InsuredBranchNumber)
                },
                new HonkeKbn() {
                    Value = hokenInf.HonkeKbn,
                    XmlValue = checkPatientHokenInfoDifferenceModel.PersonalFamilyClassification,
                    IsMap = hokenInf.HonkeKbn == checkPatientHokenInfoDifferenceModel.PersonalFamilyClassification || checkPatientHokenInfoDifferenceModel.PersonalFamilyClassification == 0
                },
                new KofuDate() {
                    Value = hokenInf.KofuDate,
                    XmlValue = checkPatientHokenInfoDifferenceModel.InsuredCertificateIssuanceDate,
                    IsMap = hokenInf.KofuDate == checkPatientHokenInfoDifferenceModel.InsuredCertificateIssuanceDate || checkPatientHokenInfoDifferenceModel.InsuredCertificateIssuanceDate == 0
                },
                new StartDate() {
                    Value = hokenInf.StartDate,
                    XmlValue = checkPatientHokenInfoDifferenceModel.InsuredCardValidDate,
                    IsMap = hokenInf.StartDate == checkPatientHokenInfoDifferenceModel.InsuredCardValidDate || checkPatientHokenInfoDifferenceModel.InsuredCardValidDate == 0
                },
                new EndDate() { Value = hokenInf.EndDate, XmlValue = checkPatientHokenInfoDifferenceModel.InsuredCardExpirationDate, IsMap = hokenInf.EndDate == checkPatientHokenInfoDifferenceModel.InsuredCardExpirationDate || (hokenInf.EndDate == 0 && checkPatientHokenInfoDifferenceModel.InsuredCardExpirationDate == 99999999) },
                new KogakuKbn() {
                    Value = GetKogakuValue(age, hokenInf.HokensyaNo, sinDate, hokenInf.KogakuKbn.ToString()) ,
                    XmlValue = GetKogakuValue(age, checkPatientHokenInfoDifferenceModel.InsurerNumber, sinDate, MapLimitApplicationCertificateFlag(checkPatientHokenInfoDifferenceModel.LimitApplicationCertificateClassificationFlag, age)),
                    IsMap = NormalizeKogakuValue(GetKogakuValue(age, hokenInf.HokensyaNo, sinDate, hokenInf.KogakuKbn.ToString()))
                            == NormalizeKogakuValue(GetKogakuValue(age, checkPatientHokenInfoDifferenceModel.InsurerNumber, sinDate,
                            MapLimitApplicationCertificateFlag(checkPatientHokenInfoDifferenceModel.LimitApplicationCertificateClassificationFlag, age)))
                },
                new HokenInfo()
                {
                    Value = (hokenInf.HokenNo == 68 && hokenInf.HokenEdaNo == 0) ? "該当" : string.Empty,
                    XmlValue = checkPatientHokenInfoDifferenceModel.InsuredCardClassification == 5 ? "該当" : string.Empty,
                    IsMap = (hokenInf.HokenNo == 68 && hokenInf.HokenEdaNo == 0) && checkPatientHokenInfoDifferenceModel.InsuredCardClassification == 5 || (checkPatientHokenInfoDifferenceModel.InsuredCardClassification != 5 && !(hokenInf.HokenNo == 68 && hokenInf.HokenEdaNo == 0))
                },
                isMapAll: false,
                seqNo: hokenInf.SeqNo,
                hokenId: hokenInf.HokenId,
                hokenName: FormatHokenInfoName(hokenInf.HokenId, hokenInf.Houbetu)
            );
        }
        private string NormalizeKogakuValue(string value)
        {
            return string.IsNullOrEmpty(value) || value == "（情報なし）" ? string.Empty : value;
        }

        public string MapLimitApplicationCertificateFlag(string flag, int patientAge)
        {
            if (string.IsNullOrWhiteSpace(flag))
            {
                return "（情報なし）";
            }

            flag = HenkanJ.ToHalfsize(flag.Trim()).ToUpper();
            if (patientAge < 70 && new[] { "B01", "B02", "B03", "B04", "B05", "B06", "B07", "B08" }.Contains(flag))
            {
                return "（情報なし）";
            }

            if (patientAge >= 70 && new[] { "A01", "A02", "A03", "A04", "A05", "A06" }.Contains(flag))
            {
                return "（情報なし）";
            }

            if (patientAge < 70)
            {
                switch (flag)
                {
                    case "A01": return "26";
                    case "A02": return "27";
                    case "A03": return "28";
                    case "A04": return "29";
                    case "A05": return "30";
                    case "A06": return "30";
                    default: return flag;
                }
            }
            else
            {
                switch (flag)
                {
                    case "B01": return "26";
                    case "B02": return "27";
                    case "B03": return "28";
                    case "B04": return "一般";
                    case "B05": return "4";
                    case "B06":
                    case "B07":
                    case "B08": return "5";
                    default: return flag;
                }
            }
        }

        public string GetKogakuValue(int age, string hokensyaNo, int sindate, string? kogakuKbn)
        {
            bool isOver70 = age >= 70;
            bool isSpecialInsurance = hokensyaNo.Length == 8 && HenkanJ.ToHalfsize(hokensyaNo).StartsWith("39");
            if (kogakuKbn == "0")
            {
                return string.Empty;
            }

            if (isOver70 || isSpecialInsurance)
            {
                if (sindate < 20180801)
                {
                    return "3 上位";
                }
                return kogakuKbn switch
                {
                    "4" => "4 低所Ⅱ",
                    "5" => "5 低所Ⅰ",
                    _ when sindate < 20090101 => "6 特定収入",
                    "26" => "26 現役Ⅲ",
                    "27" => "27 現役Ⅱ",
                    "28" => "28 現役Ⅰ",
                    _ when isSpecialInsurance => "41 一般Ⅱ",
                    "3" => "3 上位",
                    "6" => "6 特定収入",
                    "17" => "17 上位[A]",
                    "18" => "18 一般[B]",
                    "19" => "19 低所[C]",
                    "29" => "29 区エ",
                    "30" => "30 区オ",
                    "41" => "41 一般Ⅱ",
                    _ => kogakuKbn?.ToString() ?? string.Empty

                };
            }
            else
            {
                if (sindate < 20150101)
                {
                    return kogakuKbn switch
                    {
                        "17" => "17 上位[A]",
                        "18" => "18 一般[B]",
                        "19" => "19 低所[C]",
                        _ => kogakuKbn?.ToString() ?? string.Empty
                    };
                }
                return kogakuKbn switch
                {
                    "3" => "3 上位",
                    "4" => "4 低所Ⅱ",
                    "5" => "5 低所Ⅰ",
                    "6" => "6 特定収入",
                    "17" => "17 上位[A]",
                    "18" => "18 一般[B]",
                    "19" => "19 低所[C]",
                    "26" => "26 区ア",
                    "27" => "27 区イ",
                    "28" => "28 区ウ",
                    "29" => "29 区エ",
                    "30" => "30 区オ",
                    "41" => "41 一般Ⅱ",
                    _ => kogakuKbn?.ToString() ?? string.Empty
                };
            }
        }

        public static string FormatHokenInfoName(int hokenId, string houbetu)
        {
            var hokenMap = new Dictionary<int, string>
            {
                { 67, "退職" },
                { 100, "国保" },
                { 39, "後期" }
            };

            if (!string.IsNullOrEmpty(houbetu))
            {
                houbetu = hokenMap.TryGetValue(houbetu.AsInteger(), out string value) ? value : "社保";
            }

            return $"{hokenId:D3} {houbetu}";
        }

        public (bool isExist, HokenInfModel hokenModel) GetHokenInfByInfo(int hpId, int hokenId, long ptId)
        {

            var hokenInf = NoTrackingDataContext.PtHokenInfs.FirstOrDefault(e => e.IsDeleted == DeleteTypes.None && e.HpId == hpId && e.HokenId == hokenId && e.PtId == ptId);
            if (hokenInf is null)
                return (false, new HokenInfModel());

            return (true, Mapper.Map(hokenInf, new HokenInfModel(), (src, dest) =>
            {
                return dest;
            }));
        }


        public (bool isExist, KohiInfModel kohiModel) GetKohiInfByInf(int hpId, int hokenId, long ptId)
        {
            var ptKohi = NoTrackingDataContext.PtKohis.FirstOrDefault(e => e.IsDeleted == DeleteTypes.None && e.HpId == hpId && e.HokenId == hokenId && e.PtId == ptId);
            if (ptKohi is null) return (false, new KohiInfModel());
            return (true, Mapper.Map(ptKohi, new KohiInfModel(), (src, dest) =>
            {
                return dest;
            }));
        }

        public List<ConfirmDateModel> GetCheckDateByInfo(int hpId, int hokenId, long ptId, int hokenGrp)
        {
            var hokenChecks = NoTrackingDataContext.PtHokenChecks.Where(e => e.IsDeleted == DeleteTypes.None && e.HpId == hpId && e.PtID == ptId && e.HokenId == hokenId && e.HokenGrp == hokenGrp).ToList();
            var cfDateList = new List<ConfirmDateModel>();
            foreach (var item in hokenChecks)
            {
                var confirmdate = ConvertHokenCheck(item);
                cfDateList.Add(confirmdate);
            }

            return cfDateList;
        }

        private ConfirmDateModel ConvertHokenCheck(PtHokenCheck hokencheck)
        {
            return new ConfirmDateModel(hokencheck.PtID, hokencheck.HokenGrp, hokencheck.HokenId, hokencheck.SeqNo, hokencheck.CheckId, hokencheck.CheckCmt ?? string.Empty, hokencheck.CheckDate, hokencheck.CheckMachine ?? string.Empty, hokencheck.IsDeleted);
        }

        public bool IsValidDuplicateHoken(int hpId, int ptId, string hokensyaNo, int startDate, int endDate, int hokenId)
        {
            var isDuplicate = NoTrackingDataContext.PtHokenInfs.Any(e => e.HpId == hpId
                                                                    && e.PtId == ptId
                                                                    && e.StartDate == startDate
                                                                    && e.EndDate == endDate
                                                                    && e.HokensyaNo != null
                                                                    && e.HokenId != hokenId
                                                                    && e.HokensyaNo == hokensyaNo
                                                                    && ((e.HokenKbn == 1 && e.Houbetu != HokenConstant.HOUBETU_NASHI)
                                                                        || e.HokenKbn == 2));
            return isDuplicate;
        }

        private HokenMstModel ConvertHokenMst(HokenMst hokenMst)
        {
            return new HokenMstModel(
                hokenMst.FutanKbn,
                hokenMst.FutanRate,
                hokenMst.StartDate,
                hokenMst.EndDate,
                hokenMst.HokenNo,
                hokenMst.HokenEdaNo,
                hokenMst.HokenSname ?? string.Empty,
                hokenMst.Houbetu ?? string.Empty,
                hokenMst.HokenSbtKbn,
                hokenMst.CheckDigit,
                hokenMst.AgeStart,
                hokenMst.AgeEnd,
                hokenMst.IsFutansyaNoCheck,
                hokenMst.IsJyukyusyaNoCheck,
                hokenMst.JyukyuCheckDigit,
                hokenMst.IsTokusyuNoCheck,
                hokenMst.HokenName ?? string.Empty,
                hokenMst.HokenNameCd ?? string.Empty,
                hokenMst.HokenKohiKbn,
                hokenMst.IsOtherPrefValid,
                hokenMst.ReceKisai,
                hokenMst.IsLimitList,
                hokenMst.IsLimitListSum,
                hokenMst.EnTen,
                hokenMst.KaiLimitFutan,
                hokenMst.DayLimitFutan,
                hokenMst.MonthLimitFutan,
                hokenMst.MonthLimitCount,
                hokenMst.LimitKbn,
                hokenMst.CountKbn,
                hokenMst.FutanYusen,
                hokenMst.CalcSpKbn,
                hokenMst.MonthSpLimit,
                hokenMst.KogakuTekiyo,
                hokenMst.KogakuTotalKbn,
                hokenMst.KogakuHairyoKbn,
                hokenMst.ReceSeikyuKbn,
                hokenMst.ReceKisaiKokho,
                hokenMst.ReceKisai2,
                hokenMst.ReceTenKisai,
                hokenMst.ReceFutanRound,
                hokenMst.ReceZeroKisai,
                hokenMst.ReceSpKbn,
                string.Empty,
                hokenMst.PrefNo,
                hokenMst.SortNo,
                hokenMst.SeikyuYm,
                hokenMst.ReceFutanHide,
                hokenMst.ReceFutanKbn,
                hokenMst.KogakuTotalAll,
                false,
                hokenMst.DayLimitCount,
                new()
                );
        }

        public HokenMstModel GetHokenMst(int hpId, int hokenNo, int hokenEdaNo, int sinDate)
        {
            // Get all hoken follow hpInf
            var hpInf = NoTrackingDataContext.HpInfs.Where(h => h.HpId == hpId && h.StartDate <= sinDate).OrderByDescending(x => x.StartDate).FirstOrDefault();
            var prefCd = hpInf?.PrefNo;
            //Validate get all hokenMst
            var hokenMst = TrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && x.HokenNo == hokenNo
                                                                        && x.HokenEdaNo == hokenEdaNo
                                                                        && x.StartDate <= sinDate
                                                                        && x.EndDate >= sinDate
                                   && (x.PrefNo == prefCd
                            || x.PrefNo == 0
                            || x.IsOtherPrefValid == 1))
                                .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .FirstOrDefault();
            if (hokenMst is null)
            {
                return GetHokenMstByInfor(hpId, hokenNo, hokenEdaNo, sinDate);
            }

            return ConvertHokenMst(hokenMst);
        }

        public (int minStartdate, int maxEnddate) GetExpiredHokenMst(int hpId, int hokenNo, int hokenEdaNo, int sinDate)
        {
            // Get all hoken follow hpInf
            // Get all hoken follow hpInf
            var hpInf = NoTrackingDataContext.HpInfs.Where(h => h.HpId == hpId && h.StartDate <= sinDate).OrderByDescending(x => x.StartDate).FirstOrDefault();
            var prefCd = hpInf?.PrefNo;
            //Validate get all hokenMst
            var listHokenMst = TrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && x.HokenNo == hokenNo
                                                                        && x.HokenEdaNo == hokenEdaNo

                                   && (x.PrefNo == prefCd
                            || x.PrefNo == 0
                            || x.IsOtherPrefValid == 1))
                                .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .ToList();

            if (listHokenMst != null && listHokenMst.Count > 0)
            {
                var minStartdate = listHokenMst.Min(e => e.StartDate);
                var maxEnddate = listHokenMst.Max(e => e.EndDate);
                return (minStartdate, maxEnddate);
            }
            return (0, 0);
        }

        public BasicPatientInfoModel GetBasicPatientInfo(long ptId, int hpId)
        {
            var patientInfo = NoTrackingDataContext.PtInfs.FirstOrDefault(x => x.PtId == ptId && x.HpId == hpId && x.IsDelete == DeleteTypes.None);

            if (patientInfo == null)
            {
                return null;
            }

            var portalCustomer = patientInfo.PortalCustomerId > 0 ?
                NoTrackingDataContext.PortalCustomers.FirstOrDefault(x => x.CustomerId == patientInfo.PortalCustomerId && x.IsDeleted == DeleteTypes.None) : null;

            var portalCustomerDeliveryAddress = portalCustomer != null ?
                NoTrackingDataContext.PortalCustomerDeliveryAddresses.FirstOrDefault(x => x.CustomerId == portalCustomer.CustomerId && x.IsDeleted == DeleteTypes.None) : null;

            var portalCustomerLogin = portalCustomer != null ?
                NoTrackingDataContext.PortalCustomerLogins.FirstOrDefault(x => x.CustomerId == portalCustomer.CustomerId && x.IsDeleted == DeleteTypes.None) : null;

            return new BasicPatientInfoModel
            {
                PatientInforModel = Mapper.Map(patientInfo, new PatientInforModel(), (source, dest) => { return dest; }),
                PortalCustomerModel = Mapper.Map(portalCustomer, new PortalCustomerModel(), (source, dest) => { return dest; }),
                PortalCustomerDeliveryAddressModel = Mapper.Map(portalCustomerDeliveryAddress, new PortalCustomerDeliveryAddressModel(), (source, dest) => { return dest; }),
                PortalCustomerLoginModel = Mapper.Map(portalCustomerLogin, new PortalCustomerLoginModel(), (source, dest) => { return dest; })
            };
        }

        public bool EditBasicInfoOverwrite(int hpId, long ptId, int userId,
                    string name, bool isNameUpdated, string kaneName, bool isKanaNameUpdated,
                    int birthday, bool isBirthdayUpdated, string address1, bool isAddressUpdated,
                    string homePost, bool isHomePostUpdated, string tel, bool isTelUpdated, int sex,
                    bool isSexUpdate, string mail, bool isMailUpdated, string address2)
        {
            var patientInfo = TrackingDataContext.PtInfs.FirstOrDefault(x => x.PtId == ptId && x.HpId == hpId && x.IsDelete == DeleteTypes.None);

            if (patientInfo is null)
                return false;

            if (isNameUpdated) patientInfo.Name = name;
            if (isKanaNameUpdated) patientInfo.KanaName = kaneName;
            if (isSexUpdate) patientInfo.Sex = sex;
            if (isBirthdayUpdated) patientInfo.Birthday = birthday;
            if (isHomePostUpdated) patientInfo.HomePost = homePost;
            if (isAddressUpdated)
            {
                patientInfo.HomeAddress1 = address1;
                patientInfo.HomeAddress2 = address2;
            }
            if (isTelUpdated) patientInfo.Tel1 = tel;
            if (isMailUpdated) patientInfo.Mail = mail;

            if (!isNameUpdated && !isKanaNameUpdated && !isSexUpdate && !isBirthdayUpdated && !isHomePostUpdated && !isAddressUpdated
                && !isTelUpdated && !isMailUpdated)
            {
                return false;
            }

            patientInfo.UpdateDate = CIUtil.GetJapanDateTimeNow();
            patientInfo.UpdateId = userId;

            return TrackingDataContext.SaveChanges() > 0;
        }

        public List<PortalCustomerFileModel> GetInsuranceImageCard(long ptId, int hpId)
        {
            var patientInfo = NoTrackingDataContext.PtInfs.FirstOrDefault(x => x.PtId == ptId && x.HpId == hpId && x.IsDelete == DeleteTypes.None);
            if (patientInfo == null)
            {
                return null;
            }

            var portalCustomerFile = patientInfo.PortalCustomerId > 0 ? NoTrackingDataContext.PortalCustomerFiles.
                Where(x => x.CustomerId == patientInfo.PortalCustomerId && x.IsDeleted == DeleteTypes.None).ToList()
             : null;

            if (portalCustomerFile == null || !portalCustomerFile.Any())
            {
                return new List<PortalCustomerFileModel>();
            }

            List<PortalCustomerFileModel> portalCustomerFileModels = new List<PortalCustomerFileModel>();

            foreach (var item in portalCustomerFile)
            {
                PortalCustomerFileModel portalCustomerFileModel = new PortalCustomerFileModel();
                portalCustomerFileModel.CustomerId = item.CustomerId;
                portalCustomerFileModel.OriginalFileName = item.OriginalFileName;
                portalCustomerFileModel.Type = item.Type;
                portalCustomerFileModel.PathFile = item.S3Key;
                portalCustomerFileModels.Add(portalCustomerFileModel);
            }
            return portalCustomerFileModels;
        }
        public bool SaveHokenCheck(int hpId, int userId, long ptId, List<SaveHokenCheckDto> hokenCheckDtos)
        {
            List<PtHokenCheck> listHokenCheckAddNew = new();

            var hokenChecks = NoTrackingDataContext.PtHokenChecks.Where(e => e.HpId == hpId && e.PtID == ptId && e.IsDeleted == DeleteTypes.None).ToList();
            var date = CIUtil.GetJapanDateTimeNow();
            foreach (var hokeCheck in hokenCheckDtos)
            {
                var checkDatetimeInput = DateTime.ParseExact(hokeCheck.ConfirmDate.ToString(), "yyyyMMdd", CultureInfo.InvariantCulture);
                var utcCheckDateTime = DateTime.SpecifyKind(checkDatetimeInput, DateTimeKind.Utc);
                var hokenGrp = hokeCheck.IsHokenGroupKohi ? HokenGroupConstant.HokenGroupKohi : HokenGroupConstant.HokenGroupHokenPattern;

                var checkDuplicateDate = hokenChecks.Any(item => item.CheckDate.ToString("yyyyMMdd") == utcCheckDateTime.ToString("yyyyMMdd") && item.HokenGrp == hokenGrp && item.HokenId == hokeCheck.HokenId);

                if (!checkDuplicateDate)
                {
                    listHokenCheckAddNew.Add(new PtHokenCheck
                    {
                        HpId = hpId,
                        PtID = ptId,
                        HokenGrp = hokenGrp,
                        HokenId = hokeCheck.HokenId,
                        CheckDate = utcCheckDateTime,
                        CheckId = userId,
                        CreateDate = date,
                        CreateId = userId,
                        UpdateDate = date,
                        UpdateId = userId
                    });
                }
            }
            if (listHokenCheckAddNew.Count == 0) return false;

            TrackingDataContext.PtHokenChecks.AddRange(listHokenCheckAddNew);

            return TrackingDataContext.SaveChanges() > 0;
        }

        public CheckKohiInfoDifferenceDto CheckKohiInfoDifference(int hpid,
            long ptId, bool isMarucho, CheckKohiInfoDifferenceModel checkKohiInfoDifferenceModel)
        {
            if (isMarucho) return CheckMaruchoKohiInfoDifference(hpid, ptId, checkKohiInfoDifferenceModel);
            return CheckKohiInfoDifference(hpid, ptId, checkKohiInfoDifferenceModel);
        }

        private CheckKohiInfoDifferenceDto CheckKohiInfoDifference(int hpid,
            long ptId, CheckKohiInfoDifferenceModel checkKohiInfoDifferenceModel)
        {
            checkKohiInfoDifferenceModel = new CheckKohiInfoDifferenceModel(
                checkKohiInfoDifferenceModel.InsurerNumber?.Trim(),
                checkKohiInfoDifferenceModel.InsuredIdentificationNumber?.Trim(),
                checkKohiInfoDifferenceModel.Birthdate,
                checkKohiInfoDifferenceModel.MedicalTicketValidDate,
                checkKohiInfoDifferenceModel.MedicalTicketExpirationDate == 0 ? 99999999 : checkKohiInfoDifferenceModel.MedicalTicketExpirationDate,
                checkKohiInfoDifferenceModel.SelfPayAmount
            );

            var ptKohiInf = NoTrackingDataContext.PtKohis
             .Where(c => c.HpId == hpid
                 && c.PtId == ptId
                 && c.PrefNo == 0
                 && c.HokenNo == 12
                 && (c.FutansyaNo == HenkanJ.ToFullsize(checkKohiInfoDifferenceModel.InsurerNumber)
                    || c.FutansyaNo == HenkanJ.ToHalfsize(checkKohiInfoDifferenceModel.InsurerNumber)
                    || c.FutansyaNo == checkKohiInfoDifferenceModel.InsurerNumber
                    || (c.FutansyaNo ?? string.Empty) == (checkKohiInfoDifferenceModel.InsurerNumber ?? string.Empty))
                 && (c.JyukyusyaNo == HenkanJ.ToFullsize(checkKohiInfoDifferenceModel.InsuredIdentificationNumber)
                    || c.JyukyusyaNo == HenkanJ.ToHalfsize(checkKohiInfoDifferenceModel.InsuredIdentificationNumber)
                    || c.JyukyusyaNo == checkKohiInfoDifferenceModel.InsuredIdentificationNumber
                    || (c.JyukyusyaNo ?? string.Empty) == (checkKohiInfoDifferenceModel.InsuredIdentificationNumber ?? string.Empty))
                 && c.StartDate == checkKohiInfoDifferenceModel.MedicalTicketValidDate
                 && c.EndDate == checkKohiInfoDifferenceModel.MedicalTicketExpirationDate || c.EndDate == 0 && checkKohiInfoDifferenceModel.MedicalTicketExpirationDate == 99999999
                 && c.Birthday == checkKohiInfoDifferenceModel.Birthdate
                 && c.IsDeleted == DeleteTypes.None
                 && (checkKohiInfoDifferenceModel.SelfPayAmount == 0 ||
                     (c.GendoGaku != 0 && c.GendoGaku == checkKohiInfoDifferenceModel.SelfPayAmount) ||
                     (c.GendoGaku == 0 &&
                         NoTrackingDataContext.HokenMsts
                             .Where(h => h.HpId == hpid
                                 && h.PrefNo == c.PrefNo
                                 && h.HokenNo == c.HokenNo
                                 && h.HokenEdaNo == c.HokenEdaNo)
                             .Select(h => h.MonthLimitFutan)
                             .FirstOrDefault() == checkKohiInfoDifferenceModel.SelfPayAmount
                     )
                 )
             )
             .OrderByDescending(c => c.HokenId)
             .FirstOrDefault();
            if (ptKohiInf != null) { return new CheckKohiInfoDifferenceDto(isMapAll: true, seqNo: ptKohiInf.SeqNo, hokenId: ptKohiInf.HokenId); }

            ptKohiInf = NoTrackingDataContext.PtKohis.Where(
                c => c.HpId == hpid
                && c.PtId == ptId
                && c.PrefNo == 0
                && c.HokenNo == 12
                && (c.FutansyaNo == HenkanJ.ToFullsize(checkKohiInfoDifferenceModel.InsurerNumber)
                    || c.FutansyaNo == HenkanJ.ToHalfsize(checkKohiInfoDifferenceModel.InsurerNumber)
                    || c.FutansyaNo == checkKohiInfoDifferenceModel.InsurerNumber
                    || (c.FutansyaNo ?? string.Empty) == (checkKohiInfoDifferenceModel.InsurerNumber ?? string.Empty))
                && (c.JyukyusyaNo == HenkanJ.ToFullsize(checkKohiInfoDifferenceModel.InsuredIdentificationNumber)
                    || c.JyukyusyaNo == HenkanJ.ToHalfsize(checkKohiInfoDifferenceModel.InsuredIdentificationNumber)
                    || c.JyukyusyaNo == checkKohiInfoDifferenceModel.InsuredIdentificationNumber
                    || (c.JyukyusyaNo ?? string.Empty) == (checkKohiInfoDifferenceModel.InsuredIdentificationNumber ?? string.Empty))
                && c.IsDeleted == DeleteTypes.None
            ).OrderByDescending(c => c.HokenId).FirstOrDefault();
            if (ptKohiInf == null)
            {
                return new CheckKohiInfoDifferenceDto(isMapAll: false);
            }

            var gendogakuValue = ptKohiInf.GendoGaku == 0
               ? NoTrackingDataContext.HokenMsts
                   .Where(h => h.HpId == hpid
                        && h.PrefNo == ptKohiInf.PrefNo
                        && h.HokenNo == ptKohiInf.HokenNo
                        && h.HokenEdaNo == ptKohiInf.HokenEdaNo
                   )
                   .Select(h => h.MonthLimitFutan)
                   .FirstOrDefault()
               : ptKohiInf.GendoGaku;

            return new CheckKohiInfoDifferenceDto(
                funtansyaNo: new FutansyaNoInfor { Value = ptKohiInf.FutansyaNo, XmlValue = checkKohiInfoDifferenceModel.InsurerNumber, IsMap = HenkanJ.ToHalfsize(ptKohiInf.FutansyaNo) == HenkanJ.ToHalfsize(checkKohiInfoDifferenceModel.InsurerNumber) },
                jyukyusyaNo: new JyukyusyaNoInfor { Value = ptKohiInf.JyukyusyaNo, XmlValue = checkKohiInfoDifferenceModel.InsuredIdentificationNumber, IsMap = HenkanJ.ToHalfsize(ptKohiInf.JyukyusyaNo) == HenkanJ.ToHalfsize(checkKohiInfoDifferenceModel.InsuredIdentificationNumber) },
                startDate: new StartDateInfo { Value = ptKohiInf.StartDate, XmlValue = checkKohiInfoDifferenceModel.MedicalTicketValidDate, IsMap = ptKohiInf.StartDate == checkKohiInfoDifferenceModel.MedicalTicketValidDate },
                endDate: new EndDateInfo { Value = ptKohiInf.EndDate, XmlValue = checkKohiInfoDifferenceModel.MedicalTicketExpirationDate, IsMap = ptKohiInf.EndDate == checkKohiInfoDifferenceModel.MedicalTicketExpirationDate || ptKohiInf.EndDate == 0 && checkKohiInfoDifferenceModel.MedicalTicketExpirationDate == 99999999 },
                gendogaku: new GendogakuInfo { Value = gendogakuValue, XmlValue = checkKohiInfoDifferenceModel.SelfPayAmount, IsMap = gendogakuValue == checkKohiInfoDifferenceModel.SelfPayAmount || checkKohiInfoDifferenceModel.SelfPayAmount == 0 },
                birthDay: new BirthDayInformation { Value = ptKohiInf.Birthday, XmlValue = checkKohiInfoDifferenceModel.Birthdate, IsMap = ptKohiInf.Birthday == checkKohiInfoDifferenceModel.Birthdate },
                isMapAll: false,
                seqNo: ptKohiInf.SeqNo,
                hokenId: ptKohiInf.HokenId,
                kohiName: FormatKohiName(ptKohiInf).Trim()
            );
        }

        private CheckKohiInfoDifferenceDto CheckMaruchoKohiInfoDifference(int hpid,
            long ptId, CheckKohiInfoDifferenceModel checkKohiInfoDifferenceModel)
        {
            var hokenEdaNo = 0;
            var prefNo = 0;
            if (checkKohiInfoDifferenceModel.SpecificDiseasesValidEndDate == 0)
            {
                checkKohiInfoDifferenceModel = new CheckKohiInfoDifferenceModel(
                    checkKohiInfoDifferenceModel.SpecificDiseasesSelfPay?.Trim(),
                    checkKohiInfoDifferenceModel.SpecificDiseasesValidStartDate,
                    checkKohiInfoDifferenceModel.SpecificDiseasesValidEndDate == 0 ? 99999999 : checkKohiInfoDifferenceModel.SpecificDiseasesValidEndDate
                );
            }

            if (checkKohiInfoDifferenceModel.SpecificDiseasesSelfPay == "20000円")
            {
                hokenEdaNo = 1;
            }

            var maruchoKohiInf = NoTrackingDataContext.PtKohis.Where(
                c => c.HpId == hpid
                && c.PtId == ptId
                && c.HokenNo == 102
                && c.HokenEdaNo == hokenEdaNo
                && c.PrefNo == 0
                && c.StartDate == checkKohiInfoDifferenceModel.SpecificDiseasesValidStartDate
                && (c.EndDate == checkKohiInfoDifferenceModel.SpecificDiseasesValidEndDate || (c.EndDate == 0 && checkKohiInfoDifferenceModel.SpecificDiseasesValidEndDate == 99999999))
                && c.IsDeleted == DeleteTypes.None
            ).OrderByDescending(c => c.HokenId).FirstOrDefault();
            if (maruchoKohiInf != null)
            {
                return new CheckKohiInfoDifferenceDto(isMapAll: true, seqNo: maruchoKohiInf.SeqNo, hokenId: maruchoKohiInf.HokenId);
            }

            maruchoKohiInf = NoTrackingDataContext.PtKohis.Where(
                c => c.HpId == hpid
                && c.PtId == ptId
                && c.HokenNo == 102
                && (c.HokenEdaNo == 1 || c.HokenEdaNo == 0)
                && c.IsDeleted == DeleteTypes.None
            ).OrderByDescending(c => c.HokenId).FirstOrDefault();
            if (maruchoKohiInf == null)
            {
                return new CheckKohiInfoDifferenceDto(isMapAll: false);
            }

            string selfPayDisplay = maruchoKohiInf.HokenEdaNo == 0 && maruchoKohiInf.PrefNo == 0 ? "10000円" : maruchoKohiInf.HokenEdaNo == 1 && maruchoKohiInf.PrefNo == 0 ? "20000円" : string.Empty;

            return new CheckKohiInfoDifferenceDto(
                   startDate: new StartDateInfo()
                   {
                       Value = maruchoKohiInf.StartDate,
                       XmlValue = checkKohiInfoDifferenceModel.SpecificDiseasesValidStartDate,
                       IsMap = maruchoKohiInf.StartDate.Equals(checkKohiInfoDifferenceModel.SpecificDiseasesValidStartDate)
                   },
                   endDate: new EndDateInfo()
                   {
                       Value = maruchoKohiInf.EndDate,
                       XmlValue = checkKohiInfoDifferenceModel.SpecificDiseasesValidEndDate,
                       IsMap = maruchoKohiInf.EndDate.Equals(checkKohiInfoDifferenceModel.SpecificDiseasesValidEndDate) || (maruchoKohiInf.EndDate == 0 && checkKohiInfoDifferenceModel.SpecificDiseasesValidEndDate == 99999999)
                   },
                   hokenEdaNo: new HokenEdaNo()
                   {
                       Value = selfPayDisplay,
                       XmlValue = checkKohiInfoDifferenceModel.SpecificDiseasesSelfPay,
                       IsMap = selfPayDisplay.Equals(checkKohiInfoDifferenceModel.SpecificDiseasesSelfPay)
                   },
                   isMapAll: false,
                   seqNo: maruchoKohiInf.SeqNo,
                   hokenId: maruchoKohiInf.HokenId,
                   kohiName: FormatKohiName(maruchoKohiInf).Trim()
            );

        }

        private string FormatKohiName(PtKohi ptKohi)
        {
            var hokenMst = NoTrackingDataContext.HokenMsts.FirstOrDefault(c =>
                c.HpId == ptKohi.HpId
                && c.PrefNo == ptKohi.PrefNo
                && c.HokenNo == ptKohi.HokenNo
                && c.HokenEdaNo == ptKohi.HokenEdaNo
            );

            return $"{ptKohi.HokenId:D3} {(hokenMst == null ? string.Empty : hokenMst.HokenNameCd)}";
        }
        private string FirstCharToUpper(string s)
        {
            if (string.IsNullOrEmpty(s))
            {
                return string.Empty;
            }
            return char.ToUpper(s[0]) + s.Substring(1);
        }
        private bool UpdatePatientByFieldName(int hpId, long ptId, int userId, List<PatientInforConfirmOnlineDto> patientInfoFields, PtKyuseiModel? ptKyuseiModel)
        {
            PtInf? patientInfo = TrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId);
            var japanDate = CIUtil.GetJapanDateTimeNow();
            if (patientInfo is null) return (false);
            if (patientInfoFields.Any())
            {
                patientInfo.IsRyosyoDetail = 1; //detault 1
                patientInfo.CreateDate = DateTime.SpecifyKind(patientInfo.CreateDate, DateTimeKind.Utc);
                patientInfo.UpdateDate = japanDate;
                patientInfo.UpdateId = userId;

                if (ptKyuseiModel != null)
                {
                    var ptKyuseiUpdate = TrackingDataContext.PtKyuseis.Where(e => e.HpId == hpId && e.PtId == ptId && e.IsDeleted == DeleteTypes.None).OrderByDescending(e => e.EndDate).FirstOrDefault();
                    if (ptKyuseiUpdate != null)
                    {
                        ptKyuseiUpdate.EndDate = CIUtil.DateTimeToInt(japanDate);
                        ptKyuseiUpdate.UpdateDate = japanDate;
                        ptKyuseiUpdate.UpdateId = userId;
                    }

                    var ptKyusei = new PtKyusei()
                    {
                        HpId = hpId,
                        PtId = ptId,
                        KanaName = ptKyuseiModel.KanaName,
                        Name = ptKyuseiModel.Name,
                        EndDate = ptKyuseiModel.EndDate,
                        CreateDate = japanDate,
                        CreateId = userId,
                        UpdateDate = japanDate,
                        UpdateId = userId
                    };
                    TrackingDataContext.PtKyuseis.Add(ptKyusei);
                }
            }

            foreach (var item in patientInfoFields)
            {
                string pascalCaseCellName = FirstCharToUpper(item.FieldName);

                switch (pascalCaseCellName)
                {
                    case nameof(PtInf.Name):
                        patientInfo.Name = item.FieldValue;
                        break;
                    case nameof(PtInf.KanaName):
                        patientInfo.KanaName = item.FieldValue;
                        break;
                    case nameof(PtInf.Birthday):
                        patientInfo.Birthday = int.Parse(item.FieldValue);
                        break;
                    case nameof(PtInf.Sex):
                        patientInfo.Sex = int.Parse(item.FieldValue);
                        break;
                    case nameof(PtInf.HomePost):
                        patientInfo.HomePost = item.FieldValue;
                        break;
                    case nameof(PtInf.HomeAddress1):
                        patientInfo.HomeAddress1 = item.FieldValue;
                        break;
                    case nameof(PtInf.Setanusi):
                        patientInfo.Setanusi = item.FieldValue;
                        break;
                }
            }

            return true;
        }

        private (bool result, List<int> hokenIds, long ptId) SaveListHokenInfModel(int hpId, int userId, PtInf? patientInfo, int onlineConfirmHistoryId, int sinDate, List<HokenInfModel> hokenInfs, List<EndDateModel>? endDateModels)
        {
            var defaultMaxDate = 99999999;
            var japanDate = CIUtil.GetJapanDateTimeNow();
            int hokenId = 0;
            int loopCount = 0;
            var hokenIds = new List<int>();
            var nextHokenId = GenHokenId(hpId, patientInfo.PtId);
            if (patientInfo is null) return (false, new List<int>(), 0);

            foreach (var hokenInf in hokenInfs)
            {
                if (hokenInf.SeqNo == 0)
                {
                    if (loopCount > 0)
                    {
                        nextHokenId++;
                    }
                    var ptHokenInf = Mapper.Map<HokenInfModel, PtHokenInf>(hokenInf, new PtHokenInf(), (src, dest) =>
                    {
                        dest.CreateId = userId;
                        dest.CreateDate = japanDate;
                        dest.UpdateId = userId;
                        dest.UpdateDate = japanDate;
                        dest.PtId = patientInfo.PtId;
                        dest.HpId = hpId;
                        dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                        dest.HokenId = nextHokenId;
                        return dest;
                    });

                    SaveRousaiTenki(hokenInf.ListRousaiTenki, ptHokenInf.HokenId, hpId, patientInfo.PtId, userId, japanDate);
                    SaveHokenCheck(hokenInf.ConfirmDateList, ptHokenInf.HokenId, hpId, patientInfo.PtId, userId, onlineConfirmHistoryId, sinDate, japanDate);

                    TrackingDataContext.PtHokenInfs.Add(ptHokenInf);
                    hokenId = ptHokenInf.HokenId;
                    hokenIds.Add(hokenId);
                    loopCount++;
                }
                else
                {
                    var updateHokenInf = TrackingDataContext.PtHokenInfs.FirstOrDefault(x => x.PtId == patientInfo.PtId && x.HpId == patientInfo.HpId && x.SeqNo == hokenInf.SeqNo && x.IsDeleted == DeleteTypes.None);
                    if (updateHokenInf != null)
                    {
                        Mapper.Map(hokenInf, updateHokenInf, (src, dest) =>
                        {
                            dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                            dest.UpdateDate = japanDate;
                            dest.UpdateId = userId;
                            return dest;
                        });

                        var databaseHokenChecks = TrackingDataContext.PtHokenChecks.Where(c => c.PtID == patientInfo.PtId && c.HpId == patientInfo.HpId && c.IsDeleted == DeleteTypes.None).ToList();
                        UpdateHokenCheck(databaseHokenChecks, hokenInf.ConfirmDateList, hpId, patientInfo.PtId, updateHokenInf.HokenId, userId, false, onlineConfirmHistoryId);

                        UpdateRousaiTenki(hokenInf.ListRousaiTenki, updateHokenInf.HokenId, hpId, patientInfo.PtId, userId, japanDate);
                        hokenId = updateHokenInf.HokenId;
                        hokenIds.Add(hokenId);
                    }
                }
            }

            if (endDateModels.Any(c => c.HokenId == 0))
            {
                var seqNoList = endDateModels.Select(e => e.SeqNo).ToList();

                var updateHokenInf = TrackingDataContext.PtHokenInfs
                    .Where(x => x.PtId == patientInfo.PtId
                                && x.HpId == patientInfo.HpId
                                && seqNoList.Contains(x.SeqNo)
                                && x.IsDeleted == DeleteTypes.None)
                    .ToList();

                foreach (var endDateModel in endDateModels.Where(c => c.HokenId == 0))
                {
                    var recordToUpdate = updateHokenInf.FirstOrDefault(x => x.SeqNo == endDateModel.SeqNo);
                    if (recordToUpdate == null)
                    {
                        continue;
                    }
                    recordToUpdate.CreateDate = recordToUpdate.CreateDate.ToUniversalTime();
                    recordToUpdate.UpdateDate = japanDate;
                    recordToUpdate.EndDate = endDateModel.EndDate;
                }
            }

            TrackingDataContext.SaveChanges();
            return (true, hokenIds, patientInfo.PtId);
        }

        private void SaveRousaiTenki(IEnumerable<RousaiTenkiModel> list, int hokenId, int hpId, long ptId, int userId, DateTime japanDate)
        {
            var items = list.Select(src => new PtRousaiTenki
            {
                CreateId = userId,
                PtId = ptId,
                HpId = hpId,
                HokenId = hokenId,
                Tenki = src.RousaiTenkiTenki,
                Sinkei = src.RousaiTenkiSinkei,
                EndDate = src.RousaiTenkiEndDate,
                CreateDate = japanDate,
                UpdateId = userId,
                UpdateDate = japanDate
            }).ToList();
            TrackingDataContext.PtRousaiTenkis.AddRange(items);
        }

        private void SaveHokenCheck(IEnumerable<ConfirmDateModel> list, int hokenId, int hpId, long ptId, int userId, int onlineConfirmationId, int sinDate, DateTime japanDate)
        {
            var items = list.Select(src => new PtHokenCheck
            {
                CreateId = userId,
                UpdateId = userId,
                CreateDate = japanDate,
                UpdateDate = japanDate,
                CheckDate = DateTime.SpecifyKind(CIUtil.IntToDate(src.ConfirmDate == 0 ? sinDate : src.ConfirmDate), DateTimeKind.Utc),
                CheckCmt = src.CheckComment,
                HokenId = hokenId,
                CheckId = userId,
                PtID = ptId,
                HpId = hpId,
                HokenGrp = 1,
                OnlineConfirmationId = onlineConfirmationId,
            }).DistinctBy(e => e.CheckDate).ToList();
            TrackingDataContext.PtHokenChecks.AddRange(items);
        }

        private void UpdateRousaiTenki(IEnumerable<RousaiTenkiModel> list, int hokenId, int hpId, long ptId, int userId, DateTime japanDate)
        {
            var databaseItems = TrackingDataContext.PtRousaiTenkis.Where(c => c.PtId == ptId && c.HpId == hpId && c.HokenId == hokenId && c.IsDeleted == DeleteTypes.None).ToList();

            foreach (var item in list)
            {
                var updateItem = databaseItems.FirstOrDefault(x => x.SeqNo == item.SeqNo);
                if (updateItem != null && (updateItem.Sinkei != item.RousaiTenkiSinkei || updateItem.Tenki != item.RousaiTenkiTenki || updateItem.EndDate != item.RousaiTenkiEndDate))
                {
                    updateItem.Sinkei = item.RousaiTenkiSinkei;
                    updateItem.Tenki = item.RousaiTenkiTenki;
                    updateItem.EndDate = item.RousaiTenkiEndDate;
                    updateItem.UpdateDate = japanDate;
                    updateItem.UpdateId = userId;
                }
            }

            var listRemoves = databaseItems.Where(x => !list.Any(m => m.SeqNo == x.SeqNo)).ToList();
            foreach (var removeItem in listRemoves)
            {
                removeItem.IsDeleted = 1;
                removeItem.UpdateId = userId;
                removeItem.UpdateDate = japanDate;
            }
        }

        private (bool result, List<int> kohiIds) SaveListKohi(int hpId, int userId, long ptId, int onlineConfirmHistoryId, List<KohiInfModel> kohiModels, List<EndDateModel>? endDateModels)
        {
            var defaultMaxDate = 99999999;
            var kohiIds = new List<int>();
            var loopCount = 0;
            var newKohiId = 0;
            var existingKohis = TrackingDataContext.PtKohis
                .Where(kohi => kohi.HpId == hpId && kohi.PtId == ptId && kohi.IsDeleted == DeleteTypes.None)
                .ToList();
            if (!kohiModels.Any())
            {
                return (true, kohiIds);
            }

            var allLimitListInfs = TrackingDataContext.LimitListInfs
                .Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None)
                .ToList();

            newKohiId = GenKohiId(hpId, ptId);

            foreach (var kohiModel in kohiModels)
            {
                var kohi = existingKohis.FirstOrDefault(k => k.HokenId == kohiModel.HokenId && k.SeqNo == kohiModel.SeqNo);

                List<LimitListInf> maxMoneyDatabases = allLimitListInfs
                    .Where(x => x.KohiId == kohiModel.HokenId)
                    .ToList();

                if (loopCount > 0)
                {
                    newKohiId++;
                }

                if (kohi == null)
                {
                    kohi = new PtKohi()
                    {
                        UpdateId = userId,
                        UpdateDate = CIUtil.GetJapanDateTimeNow(),
                        CreateId = userId,
                        CreateDate = CIUtil.GetJapanDateTimeNow(),
                        EndDate = kohiModel.EndDate == 0 ? defaultMaxDate : kohiModel.EndDate,
                        Birthday = kohiModel.Birthday,
                        FutansyaNo = kohiModel.FutansyaNo,
                        GendoGaku = kohiModel.GendoGaku,
                        HokenEdaNo = kohiModel.HokenEdaNo,
                        HokenNo = kohiModel.HokenNo,
                        HokenSbtKbn = kohiModel.HokenSbtKbn,
                        Houbetu = kohiModel.Houbetu,
                        IsDeleted = kohiModel.IsDeleted,
                        JyukyusyaNo = kohiModel.JyukyusyaNo,
                        KofuDate = kohiModel.KofuDate,
                        PrefNo = kohiModel.PrefNo,
                        Rate = kohiModel.Rate,
                        SikakuDate = kohiModel.SikakuDate,
                        StartDate = kohiModel.StartDate,
                        TokusyuNo = kohiModel.TokusyuNo,
                        HpId = hpId,
                        PtId = ptId,
                        HokenId = newKohiId
                    };
                    TrackingDataContext.PtKohis.Add(kohi);
                    loopCount++;

                    #region PtHokenCheck
                    TrackingDataContext.PtHokenChecks.AddRange(Mapper.Map<ConfirmDateModel, PtHokenCheck>(kohiModel.ConfirmDateList, (srcCf, destCf) =>
                    {
                        destCf.CreateId = userId;
                        destCf.CreateDate = CIUtil.GetJapanDateTimeNow();
                        destCf.CheckDate = DateTime.SpecifyKind(CIUtil.IntToDate(srcCf.ConfirmDate), DateTimeKind.Utc);
                        destCf.CheckCmt = srcCf.CheckComment;
                        destCf.HokenId = kohi.HokenId;
                        destCf.CheckId = userId;
                        destCf.PtID = ptId;
                        destCf.HokenGrp = HokenGroupConstant.HokenGroupKohi;
                        destCf.HpId = hpId;
                        destCf.OnlineConfirmationId = (int)onlineConfirmHistoryId;
                        return destCf;
                    }));
                    #endregion
                }
                else
                {
                    Mapper.Map(kohiModel, kohi, (src, dest) =>
                    {
                        dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                        dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        dest.CreateDate = DateTime.SpecifyKind(dest.CreateDate, DateTimeKind.Utc);
                        dest.UpdateId = userId;
                        return dest;
                    });

                    TrackingDataContext.PtKohis.Update(kohi);

                    var databaseHokenChecks = TrackingDataContext.PtHokenChecks
                        .Where(c => c.PtID == ptId && c.HpId == hpId && c.IsDeleted == DeleteTypes.None && c.HokenId == kohiModel.HokenId)
                        .ToList();

                    UpdateHokenCheck(databaseHokenChecks, kohiModel.ConfirmDateList, hpId, ptId, kohiModel.HokenId, userId, true, (int)onlineConfirmHistoryId);
                }
                var kohiId = kohi.HokenId;
                kohiIds.Add(kohiId);

                #region Maxmoney

                foreach (var item in maxMoneyDatabases)
                {
                    var exist = kohiModel.LimitListModel.FirstOrDefault(x => x.SeqNo == item.SeqNo && x.Id == item.Id);
                    if (exist != null && CheckLimitListInfChanged(item, exist))
                    {
                        item.SortKey = exist.SortKey;
                        item.FutanGaku = exist.FutanGaku;
                        item.TotalGaku = exist.TotalGaku;
                        item.Biko = exist.Biko;
                        item.SinDate = exist.SinDateY * 10000 + exist.SinDateM * 100 + exist.SinDateD;
                        item.IsDeleted = exist.IsDeleted;
                        item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        item.UpdateId = userId;
                    }
                }

                TrackingDataContext.LimitListInfs.AddRange(Mapper.Map<LimitListModel, LimitListInf>(kohiModel.LimitListModel.Where(x => x.SeqNo == 0 && x.Id == 0 && x.IsDeleted == DeleteStatus.None), (src, dest) =>
                {
                    dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                    dest.UpdateDate = dest.CreateDate;
                    dest.PtId = ptId;
                    dest.HpId = hpId;
                    dest.KohiId = kohiId;
                    dest.SinDate = src.SinDateY * 10000 + src.SinDateM * 100 + src.SinDateD;
                    dest.UpdateId = userId;
                    dest.CreateId = userId;
                    return dest;
                }));

                #endregion
            }

            if (endDateModels.Any(c => c.HokenId > 0))
            {
                var seqNoList = endDateModels.Select(e => e.SeqNo).ToList();
                var hokenId = endDateModels.Select(e => e.HokenId).ToList();

                var updateKohiInf = TrackingDataContext.PtKohis
                    .Where(x => x.PtId == ptId
                                && x.HpId == hpId
                                && seqNoList.Contains(x.SeqNo)
                                && hokenId.Contains(x.HokenId)
                                && x.IsDeleted == DeleteTypes.None)
                    .ToList();

                foreach (var endDateModel in endDateModels.Where(c => c.HokenId > 0).ToList())
                {
                    var recordToUpdate = updateKohiInf.FirstOrDefault(x => x.SeqNo == endDateModel.SeqNo && x.HokenId == endDateModel.HokenId);
                    if (recordToUpdate == null)
                    {
                        continue;
                    }
                    recordToUpdate.CreateDate = recordToUpdate.CreateDate.ToUniversalTime();
                    recordToUpdate.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    recordToUpdate.EndDate = endDateModel.EndDate;
                }
            }

            TrackingDataContext.SaveChanges();
            return (true, kohiIds);
        }

        private int GenKohiId(int hpId, long ptId)
        {
            var kohi = NoTrackingDataContext.PtKohis.Where(item => item.HpId == hpId && item.PtId == ptId).OrderByDescending(item => item.HokenId).FirstOrDefault();
            int kohiId = 1;
            if (kohi != null) kohiId += kohi.HokenId;
            return kohiId;
        }
        private bool CheckLimitListInfChanged(LimitListInf limitListInf, LimitListModel limitListModel)
        {
            if (limitListInf.IsDeleted != limitListModel.IsDeleted ||
                limitListInf.SortKey != limitListModel.SortKey ||
                limitListInf.FutanGaku != limitListModel.FutanGaku ||
                limitListInf.TotalGaku != limitListModel.TotalGaku ||
                limitListInf.Biko != limitListModel.Biko ||
                limitListInf.SinDate != limitListModel.SinDateY * 10000 + limitListModel.SinDateM * 100 + limitListModel.SinDateD)
            {
                return true;
            }

            return false;
        }

        public (bool result, List<int> hokenIds, List<int> kohiIds) SaveOnlineMyCardBeforeReception(int hpId,
            int ptId,
            int onlineConfirmHistoryId,
            int userId,
            int sinDate,
            List<PatientInforConfirmOnlineDto> patientInfoFields,
            PtKyuseiModel? ptKyuseiModel,
            List<HokenInfModel> hokenInfs,
            List<KohiInfModel> kohiModels,
            List<EndDateModel>? endDateModels)
        {
            var hokenIds = new List<int>();
            var kohiIds = new List<int>();

            PtInf? patientInfo = TrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId && x.IsDelete == DeleteTypes.None);
            if (patientInfo == null)
            {
                return (true, new(), new());
            }

            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();

                if (!UpdatePatientByFieldName(hpId, ptId, userId, patientInfoFields, ptKyuseiModel))
                {
                    return (false, new List<int>(), new List<int>());
                }

                if (hokenInfs.Any())
                {
                    var resultSaveHoken = SaveListHokenInfModel(hpId, userId, patientInfo, onlineConfirmHistoryId, sinDate, hokenInfs, endDateModels);
                    if (!resultSaveHoken.result)
                    {
                        return (false, new List<int>(), new List<int>());
                    }
                    hokenIds = resultSaveHoken.hokenIds;
                }

                if (kohiModels.Any())
                {
                    var resultSavekohi = SaveListKohi(hpId, userId, ptId, onlineConfirmHistoryId, kohiModels, endDateModels);
                    if (!resultSavekohi.result)
                    {
                        return (false, new List<int>(), new List<int>());
                    }
                    kohiIds = resultSavekohi.kohiIds;
                }

                TrackingDataContext.SaveChanges();
                transaction.Commit();
                return (true, hokenIds, kohiIds);
            });
        }

        public CheckPatientInfoDifferenceModel CheckPatientInfoDifference(int hpid, long ptId, long onlineConfirmationId)
        {
            var onlineConfirmRecord = NoTrackingDataContext.OnlineConfirmationHistories
                .FirstOrDefault(c => c.HpId == hpid && c.ID == onlineConfirmationId && c.UketukeStatus != 9);

            if (onlineConfirmRecord == null || onlineConfirmRecord.ConfirmationResult == null)
            {
                return new CheckPatientInfoDifferenceModel();
            }

            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(c => c.HpId == hpid && c.PtId == ptId && c.IsDelete == 0);
            if (ptInf == null)
            {
                return new CheckPatientInfoDifferenceModel();
            }

            XElement xml = XElement.Parse(onlineConfirmRecord.ConfirmationResult);
            string CleanString(string input) => string.IsNullOrWhiteSpace(input) ? string.Empty : input.Trim().Replace("　", "").Replace(" ", "");

            string nameKana = xml.Descendants(XmlConstant.NameKana).FirstOrDefault()?.Value;
            string name = xml.Descendants(XmlConstant.Name).FirstOrDefault()?.Value;
            string birthday = xml.Descendants(XmlConstant.Birthdate).FirstOrDefault()?.Value;
            string sex1 = xml.Descendants(XmlConstant.Sex1).FirstOrDefault()?.Value;
            string sex2 = xml.Descendants(XmlConstant.Sex2).FirstOrDefault()?.Value;
            string insuredName = xml.Descendants(XmlConstant.InsuredName).FirstOrDefault()?.Value;
            var postNumberXml = xml.Descendants(XmlConstant.PostNumber).FirstOrDefault()?.Value;
            string address = xml.Descendants(XmlConstant.Address).FirstOrDefault()?.Value;

            string postNumber = string.Empty;
            if (!string.IsNullOrEmpty(postNumberXml))
                postNumber = Regex.Replace(postNumberXml, "[-‐‑‒–—―−]", "");

            int sex = int.TryParse(sex2, out var gender2) ? gender2 : (int.TryParse(sex1, out var gender1) ? gender1 : 0);
            int birthdayInt = int.TryParse(birthday, out var bday) ? bday : 19000101;

            var nameData = new NameInfo { Value = name, IsMap = CleanString(HenkanJ.ToHalfsize(name)) == CleanString(HenkanJ.ToHalfsize(ptInf.Name)) };
            var nameKanaData = new NameInfo { Value = nameKana, IsMap = CleanString(HenkanJ.ToHalfsize(nameKana)) == CleanString(HenkanJ.ToHalfsize(ptInf.KanaName)) };
            var birthDayData = new BirthDayInfo { Value = birthdayInt != 19000101 ? birthdayInt.ToString() : "情報なし", IsMap = birthdayInt != 19000101 ? birthdayInt == ptInf.Birthday : true };
            var sexData = new SexInfo { Value = sex, IsMap = sex == ptInf.Sex };
            var homePostData = new AddressInfo { Value = postNumber, IsMap = CleanString(HenkanJ.ToHalfsize(postNumber)) == CleanString(HenkanJ.ToHalfsize(ptInf.HomePost)) };
            var addressData = new AddressInfo { Value = address, IsMap = CleanString(HenkanJ.ToHalfsize(address)) == CleanString(HenkanJ.ToHalfsize(ptInf.HomeAddress1)) };
            var setainusiData = new AddressInfo { Value = insuredName, IsMap = CleanString(insuredName) == CleanString(ptInf.Setanusi) };

            return new CheckPatientInfoDifferenceModel(nameData, nameKanaData, birthDayData, sexData, homePostData, addressData, setainusiData);
        }

        public bool BulkUpdateHoumonAgreed(List<PatientInforModel> ptInforModels, int hpId, int houmonAgreed)
        {
            var ptIds = ptInforModels.Select(p => p.PtId).ToList();

            var patients = TrackingDataContext.PtInfs
                .Where(x => x.HpId == hpId && ptIds.Contains(x.PtId))
                .ToList();

            if (!patients.Any()) return false;

            patients.ForEach(x =>
            {
                x.UpdateDate = CIUtil.GetJapanDateTimeNow();
                x.CreateDate = x.CreateDate.SetKindUtc();
                x.HoumonAgreed = houmonAgreed;
            });
            TrackingDataContext.UpdateRange(patients);

            return TrackingDataContext.SaveChanges() > 0;
        }

        RaiinInf CreateNewRaiinInf(ReceptionModel model, int hpId, int userId)
        {
            var japanDate = CIUtil.GetJapanDateTimeNow();
            return new RaiinInf
            {
                HpId = hpId,
                PtId = model.PtId,
                SinDate = model.SinDate,
                Status = model.Status,
                IsYoyaku = model.IsYoyaku,
                YoyakuTime = model.YoyakuTime,
                UketukeTime = model.UketukeTime,
                UketukeNo = model.UketukeNo,
                CreateDate = japanDate,
                UpdateDate = japanDate,
                UpdateId = userId,
                CreateId = userId,
                OnlineConfirmationId = model.OnlineConfirmationHistoryId,
                MonshinStatus = 0,
                HokenPid = model.HokenPid,
                ConfirmationType = model.ConfirmationType,
                InfoConsFlg = model.InfoConsFlg,
                PrescriptionIssueType = model.PrescriptionIssueType
            };
        }

        public List<KohiInfModel> GetListPmhKohiInf(int hpId, long ptId, int sinDate, string medicalSubsidyInsurerNumber, string medicalSubsidyRecipientNumber)
        {
            var ptKohiQuery = NoTrackingDataContext.PtKohis.Where(item => item.HpId == hpId &&
                                                                          item.PtId == ptId &&
                                                                          item.IsDeleted == DeleteTypes.None &&
                                                                          item.FutansyaNo == medicalSubsidyInsurerNumber &&
                                                                          (item.JyukyusyaNo == medicalSubsidyRecipientNumber || item.TokusyuNo == medicalSubsidyRecipientNumber));

            var hokenMstQuery = NoTrackingDataContext.HokenMsts.Where(item => item.HpId == hpId &&
                                                                              item.StartDate <= sinDate &&
                                                                              item.EndDate >= sinDate);

            var queryKohi = (from ptKohi in ptKohiQuery
                             join hokenMst in hokenMstQuery on
                                new { ptKohi.HokenNo, ptKohi.HokenEdaNo, ptKohi.PrefNo } equals
                                new { hokenMst.HokenNo, hokenMst.HokenEdaNo, hokenMst.PrefNo }
                             select new
                             {
                                 PtKohi = ptKohi,
                                 HokenMst = hokenMst
                             }).ToList();

            var listKohi = queryKohi.Select(item => new KohiInfModel(item.PtKohi.FutansyaNo ?? string.Empty,
                                                                     item.PtKohi.JyukyusyaNo ?? string.Empty,
                                                                     item.PtKohi.HokenId,
                                                                     item.PtKohi.StartDate,
                                                                     item.PtKohi.EndDate,
                                                                     0,
                                                                     item.PtKohi.Rate,
                                                                     item.PtKohi.GendoGaku,
                                                                     item.PtKohi.SikakuDate,
                                                                     item.PtKohi.KofuDate,
                                                                     item.PtKohi.TokusyuNo ?? string.Empty,
                                                                     item.PtKohi.HokenSbtKbn,
                                                                     item.PtKohi.Houbetu ?? string.Empty,
                                                                     item.PtKohi.HokenNo,
                                                                     item.PtKohi.HokenEdaNo,
                                                                     item.PtKohi.PrefNo,
                                                                     new HokenMstModel(item.HokenMst.FutanKbn,
                                                                                       item.HokenMst.FutanRate,
                                                                                       item.HokenMst.StartDate,
                                                                                       item.HokenMst.EndDate,
                                                                                       item.HokenMst.HokenNo,
                                                                                       item.HokenMst.HokenEdaNo,
                                                                                       item.HokenMst.HokenSname ?? string.Empty,
                                                                                       item.HokenMst.Houbetu ?? string.Empty,
                                                                                       item.HokenMst.HokenSbtKbn,
                                                                                       item.HokenMst.CheckDigit,
                                                                                       item.HokenMst.AgeStart,
                                                                                       item.HokenMst.AgeEnd,
                                                                                       item.HokenMst.IsFutansyaNoCheck,
                                                                                       item.HokenMst.IsJyukyusyaNoCheck,
                                                                                       item.HokenMst.JyukyuCheckDigit,
                                                                                       item.HokenMst.IsTokusyuNoCheck,
                                                                                       item.HokenMst.HokenName ?? string.Empty,
                                                                                       item.HokenMst.HokenNameCd ?? string.Empty,
                                                                                       item.HokenMst.HokenKohiKbn,
                                                                                       item.HokenMst.IsOtherPrefValid,
                                                                                       item.HokenMst.ReceKisai,
                                                                                       item.HokenMst.IsLimitList,
                                                                                       item.HokenMst.IsLimitListSum,
                                                                                       item.HokenMst.EnTen,
                                                                                       item.HokenMst.KaiLimitFutan,
                                                                                       item.HokenMst.DayLimitFutan,
                                                                                       item.HokenMst.MonthLimitFutan,
                                                                                       item.HokenMst.MonthLimitCount,
                                                                                       item.HokenMst.LimitKbn,
                                                                                       item.HokenMst.CountKbn,
                                                                                       item.HokenMst.FutanYusen,
                                                                                       item.HokenMst.CalcSpKbn,
                                                                                       item.HokenMst.MonthSpLimit,
                                                                                       item.HokenMst.KogakuTekiyo,
                                                                                       item.HokenMst.KogakuTotalKbn,
                                                                                       item.HokenMst.KogakuHairyoKbn,
                                                                                       item.HokenMst.ReceSeikyuKbn,
                                                                                       item.HokenMst.ReceKisaiKokho,
                                                                                       item.HokenMst.ReceKisai2,
                                                                                       item.HokenMst.ReceTenKisai,
                                                                                       item.HokenMst.ReceFutanRound,
                                                                                       item.HokenMst.ReceZeroKisai,
                                                                                       item.HokenMst.ReceSpKbn,
                                                                                       string.Empty,
                                                                                       item.HokenMst.PrefNo,
                                                                                       item.HokenMst.SortNo,
                                                                                       item.HokenMst.SeikyuYm,
                                                                                       item.HokenMst.ReceFutanHide,
                                                                                       item.HokenMst.ReceFutanKbn,
                                                                                       item.HokenMst.KogakuTotalAll,
                                                                                       false,
                                                                                       item.HokenMst.DayLimitCount,
                                                                                       new List<ExceptHokensyaModel>()),
                                                                     sinDate,
                                                                     new(),
                                                                     false,
                                                                     item.PtKohi.IsDeleted,
                                                                     false,
                                                                     item.PtKohi.SeqNo)).ToList();

            return listKohi;
        }

        public int GetPrefNo(int hpId)
        {
            var hpInfo = NoTrackingDataContext.HpInfs
                .Where(item => item.HpId == hpId)
                .OrderByDescending(item => item.StartDate)
                .FirstOrDefault();

            int prefNo = 0;
            if (hpInfo != null)
            {
                prefNo = hpInfo.PrefNo;
            }

            return prefNo;
        }

        public List<HokenMstModel> GetHokenMstForPmhKohi(int hpId, string houbetu, int sinDate)
        {
            int prefNo = GetPrefNo(hpId);

            var listHokenMst = NoTrackingDataContext.HokenMsts.Where(item => item.HpId == hpId &&
                                                                            (item.HokenSbtKbn == 6 || item.HokenSbtKbn == 7) &&
                                                                             item.Houbetu == houbetu &&
                                                                            (item.PrefNo == 0 || item.PrefNo == prefNo) &&
                                                                             item.StartDate <= sinDate &&
                                                                             item.EndDate >= sinDate)
                                                              .ToList();

            var listHokenMstModel = listHokenMst.Select(item => new HokenMstModel(item.FutanKbn,
                                                                                  item.FutanRate,
                                                                                  item.StartDate,
                                                                                  item.EndDate,
                                                                                  item.HokenNo,
                                                                                  item.HokenEdaNo,
                                                                                  item.HokenSname ?? string.Empty,
                                                                                  item.Houbetu ?? string.Empty,
                                                                                  item.HokenSbtKbn,
                                                                                  item.CheckDigit,
                                                                                  item.AgeStart,
                                                                                  item.AgeEnd,
                                                                                  item.IsFutansyaNoCheck,
                                                                                  item.IsJyukyusyaNoCheck,
                                                                                  item.JyukyuCheckDigit,
                                                                                  item.IsTokusyuNoCheck,
                                                                                  item.HokenName ?? string.Empty,
                                                                                  item.HokenNameCd ?? string.Empty,
                                                                                  item.HokenKohiKbn,
                                                                                  item.IsOtherPrefValid,
                                                                                  item.ReceKisai,
                                                                                  item.IsLimitList,
                                                                                  item.IsLimitListSum,
                                                                                  item.EnTen,
                                                                                  item.KaiLimitFutan,
                                                                                  item.DayLimitFutan,
                                                                                  item.MonthLimitFutan,
                                                                                  item.MonthLimitCount,
                                                                                  item.LimitKbn,
                                                                                  item.CountKbn,
                                                                                  item.FutanYusen,
                                                                                  item.CalcSpKbn,
                                                                                  item.MonthSpLimit,
                                                                                  item.KogakuTekiyo,
                                                                                  item.KogakuTotalKbn,
                                                                                  item.KogakuHairyoKbn,
                                                                                  item.ReceSeikyuKbn,
                                                                                  item.ReceKisaiKokho,
                                                                                  item.ReceKisai2,
                                                                                  item.ReceTenKisai,
                                                                                  item.ReceFutanRound,
                                                                                  item.ReceZeroKisai,
                                                                                  item.ReceSpKbn,
                                                                                  string.Empty,
                                                                                  item.PrefNo,
                                                                                  item.SortNo,
                                                                                  item.SeikyuYm,
                                                                                  item.ReceFutanHide,
                                                                                  item.ReceFutanKbn,
                                                                                  item.KogakuTotalAll,
                                                                                  false,
                                                                                  item.DayLimitCount,
                                                                                  new List<ExceptHokensyaModel>()))
                                                .ToList();

            return listHokenMstModel;
        }
    }
}
