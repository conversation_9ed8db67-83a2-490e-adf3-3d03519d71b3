﻿using System.Globalization;
using System.Runtime.InteropServices;
using System.Text;
using System.Xml.Linq;
using Domain.Constant;
using Domain.Models.CommonModel;
using Domain.Models.ConfirmOnline;
using Domain.Models.Family;
using Domain.Models.Insurance;
using Domain.Models.NextOrder;
using Domain.Models.Online;
using Domain.Models.PatientInfor;
using Domain.Models.PortalCustomerPharmacy;
using Domain.Models.Reception;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Enum;
using Helper.Extension;
using Infrastructure.Base;
using Infrastructure.Constants;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

namespace Infrastructure.Repositories
{
    public class ReceptionRepository : RepositoryBase, IReceptionRepository
    {
        private readonly INextOrderRepository _nextOrderRepository;
        private readonly ICommonRepository _commonRepository;

        public ReceptionRepository(ITenantProvider tenantProvider, INextOrderRepository nextOrderRepository, ICommonRepository commonRepository) : base(tenantProvider)
        {
            _nextOrderRepository = nextOrderRepository;
            _commonRepository = commonRepository;
        }

        public ReceptionModel Get(int hpId, long raiinNo, bool flag = false)
        {
            var receptionEntity = NoTrackingDataContext.RaiinInfs.FirstOrDefault(r => r.HpId == hpId && r.RaiinNo == raiinNo && (!flag || r.IsDeleted == 0));
            var ptId = receptionEntity?.PtId;
            var ptInf = ptId.HasValue
                ? NoTrackingDataContext.PtInfs.FirstOrDefault(r => r.HpId == hpId && r.PtId == ptId)
                : null;
            var kaId = receptionEntity?.KaId;
            var kaInf = kaId.HasValue
                ? NoTrackingDataContext.KaMsts.FirstOrDefault(r => r.HpId == hpId && r.KaId == kaId)
                : null;
            var treatmentDepartmentId = receptionEntity?.TreatmentDepartmentId;
            var treatmentDepartmentInf = treatmentDepartmentId.HasValue
                ? NoTrackingDataContext.TreatmentDepartments.FirstOrDefault(r => r.HospitalId == hpId && r.TreatmentDepartmentId == treatmentDepartmentId)
                : null;
            var tantoId = receptionEntity?.TantoId;
            var tantoInf = tantoId.HasValue
                ? NoTrackingDataContext.UserMsts.FirstOrDefault(u => u.UserId == tantoId && u.HpId == hpId)
                : null;
            var ptMemo = ptId.HasValue
                ? NoTrackingDataContext.PtMemos.FirstOrDefault(r => r.HpId == hpId && r.PtId == ptId)
                : null;
            var hokenPId = receptionEntity?.HokenPid;
            var hoken = hokenPId.HasValue
                ? NoTrackingDataContext.PtHokenPatterns.FirstOrDefault(r => r.PtId == ptId && r.HokenPid == hokenPId && r.HpId == hpId)
                : null;
            var hokenInf = (hokenPId.HasValue && hoken != null)
                ? NoTrackingDataContext.PtHokenInfs.FirstOrDefault(r => r.PtId == ptId && r.HokenId == hoken.HokenId && r.HpId == hpId)
                : null;

            var sinDateInt = CIUtil.IntToDate(receptionEntity?.SinDate ?? 0).ToUniversalTime();
            var onlineAgreedConsent = ptId.HasValue
               ? NoTrackingDataContext.OnlineAgreedConsents
                   .Where(c => c.HpId == hpId
                               && c.PtId == ptId
                               && (c.ConsType == 0 || (c.ConsDate <= sinDateInt && sinDateInt <= c.LimitDate)))
                   .GroupBy(c => c.ConsKbn)
                   .Select(g => g.OrderByDescending(c => c.ProcessTime).First())
                   .ToList()
               : new List<OnlineAgreedConsent>();
            var infoConsFlg = GetInfoConsFlg(onlineAgreedConsent);
            if (infoConsFlg.IsNullOrEmpty() && receptionEntity != null && receptionEntity.OnlineConfirmationId != 0)
            {
                infoConsFlg = NoTrackingDataContext.OnlineConfirmationHistories.FirstOrDefault(r => r.HpId == hpId && r.ID == receptionEntity.OnlineConfirmationId && r.UketukeStatus != 9)?.InfoConsFlg ?? string.Empty;
            }

            CombineStatusEnum combine = CombineStatusEnum.None;
            var countCombine = (receptionEntity != null && receptionEntity.OyaRaiinNo > 0) ? NoTrackingDataContext.RaiinInfs.Where(r => r.HpId == hpId && r.OyaRaiinNo == receptionEntity.OyaRaiinNo).Count() : 0;
            if (countCombine > 1)
            {
                combine = CombineStatusEnum.Combined;
            }
            else if (hokenInf != null && receptionEntity != null)
            {
                var combineReception = (
                                        from r in NoTrackingDataContext.RaiinInfs
                                        join h in NoTrackingDataContext.PtHokenPatterns on new { r.HpId, r.PtId, r.HokenPid } equals new { h.HpId, h.PtId, HokenPid = h.HokenPid }
                                        where r.HpId == hpId &&
                                              r.Status >= RaiinState.Receptionist &&
                                              r.Status < RaiinState.Paid &&
                                              r.PtId == receptionEntity.PtId &&
                                              r.SinDate == receptionEntity.SinDate &&
                                              r.IsDeleted == DeleteTypes.None &&
                                              h.HokenId == hokenInf.HokenId
                                        join rd in NoTrackingDataContext.ReserveDetails
                                            .Where(rd => rd.IsDeleted == 0)
                                            on r.ReserveDetailId equals rd.ReserveDetailId into rdGroup
                                        from rd in rdGroup.DefaultIfEmpty()
                                        where r.ReserveDetailId <= 0 || rd.ReserveType != 1
                                        select r
                                        ).ToList();


                if (combineReception.Count() > 1)
                {
                    combine = CombineStatusEnum.CanCombine;
                }
            }
            var kohiIds = new List<int?> { hoken?.Kohi1Id, hoken?.Kohi2Id, hoken?.Kohi3Id, hoken?.Kohi4Id }
                            .Where(id => id.HasValue && id.Value != 0)
                            .Select(id => id!.Value)
                            .ToList();

            var kohis = NoTrackingDataContext.PtKohis
                .Where(r => kohiIds.Contains(r.HokenId))
                .ToList();

            var kohi1 = kohis.FirstOrDefault(r => r.HokenId == hoken?.Kohi1Id);
            var kohi2 = kohis.FirstOrDefault(r => r.HokenId == hoken?.Kohi2Id);
            var kohi3 = kohis.FirstOrDefault(r => r.HokenId == hoken?.Kohi3Id);
            var kohi4 = kohis.FirstOrDefault(r => r.HokenId == hoken?.Kohi4Id);

            var listKohi = new List<KohiInfModel>
            {
                new KohiInfModel(
                    kohi1?.HokenId ?? 0,
                    kohi1?.Houbetu ?? string.Empty,
                    kohi1 ?.SeqNo ?? 0
                    ),
                new KohiInfModel(
                    kohi2?.HokenId ?? 0,
                    kohi2?.Houbetu ?? string.Empty,
                    kohi2 ?.SeqNo ?? 0
                    ),
                new KohiInfModel(
                    kohi3?.HokenId ?? 0,
                    kohi3?.Houbetu ?? string.Empty,
                    kohi3?.SeqNo ?? 0
                    ),
               new KohiInfModel(
                    kohi4?.HokenId ?? 0,
                    kohi4?.Houbetu ?? string.Empty,
                    kohi4?.SeqNo ?? 0
                    ),
            };
            var hasMessage = receptionEntity != null
                ? NoTrackingDataContext.PatientMessageChannels.Any(r => r.HospitalId == hpId
                                                                    && r.PatientId == receptionEntity.PtId.ToString()
                                                                    && r.IsDeleted == DeleteTypes.None
                                                                    && NoTrackingDataContext.PatientMessageChannelMembers.Any(a => a.IsPatient == 1
                                                                                                                                && a.HasUnread == 1
                                                                                                                                && a.ChannelId == r.ChannelId
                                                                                                                                && a.MemberId == r.PatientId
                                                                                                                                && a.IsDeleted == DeleteTypes.None))
                : false;

            var reserveDetail = receptionEntity != null
                ? NoTrackingDataContext.ReserveDetails.FirstOrDefault(e => e.ReserveDetailId == receptionEntity.ReserveDetailId && e.IsDeleted == DeleteTypes.None)
                : new();
            var raiinKbnMsts = NoTrackingDataContext.RaiinKbnMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None).OrderBy(e => e.SortNo);
            var raiinKbnInfs = NoTrackingDataContext.RaiinKbnInfs.Where(x => x.HpId == hpId && x.IsDelete == DeleteTypes.None);

            var lables = receptionEntity != null
                       ? (from inf in raiinKbnInfs
                          join mst in raiinKbnMsts on
                              new { inf.HpId, inf.GrpId } equals
                              new { mst.HpId, GrpId = mst.GrpCd }
                          where inf.HpId == hpId
                              && inf.PtId == receptionEntity.PtId
                              && inf.SinDate == receptionEntity.SinDate
                              && inf.RaiinNo == receptionEntity.RaiinNo
                          select new Label(inf.GrpId, mst.GrpName ?? string.Empty, mst.ColorCode ?? string.Empty, mst.SortNo, mst.HpId)).ToList()
                       : new List<Label>();

            var karteEdition = receptionEntity != null
                ? NoTrackingDataContext.KarteEditions.FirstOrDefault(r => r.RaiinNo == receptionEntity.RaiinNo && r.IsDeleted == DeleteTypes.None)
                : null;

            var reserve = reserveDetail != null && ptId.HasValue
                ? NoTrackingDataContext.Reserves.FirstOrDefault(e => e.ReserveId == reserveDetail.ReserveId && e.PatientId == ptId && e.IsDeleted == DeleteTypes.None)
                : new();
            var pharmacy = new PortalCustomerPharmacyModel();
            if (reserve != null && reserve.PrescriptionReceiveMethod == 2)
            {
                var pharmacyEntity = NoTrackingDataContext.PortalCustomerPharmacies.FirstOrDefault(e => e.ReserveId == reserve.ReserveId && e.IsDeleted == DeleteTypes.None);
                if (pharmacyEntity != null)
                {
                    pharmacy = new PortalCustomerPharmacyModel(pharmacyEntity.PortalCustomerPharmacyId, pharmacyEntity.ReserveId, pharmacyEntity.CustomerId, pharmacyEntity.PharmacyName, pharmacyEntity.PharmacyStoreName ?? string.Empty, pharmacyEntity.FaxNumber, pharmacyEntity.Address1, pharmacyEntity.Address2 ?? string.Empty, pharmacyEntity.PostCode, pharmacyEntity.PhoneNumber ?? string.Empty, pharmacyEntity.IsDeleted);
                }
            }

            var raiinCmtInf = receptionEntity != null
                ? NoTrackingDataContext.RaiinCmtInfs.FirstOrDefault(x => x.HpId == hpId
                                                                          && x.CmtKbn == CmtKbns.Comment
                                                                          && x.IsDelete == DeleteTypes.None
                                                                          && x.PtId == receptionEntity.PtId
                                                                          && x.SinDate == receptionEntity.SinDate
                                                                          && x.RaiinNo == receptionEntity.RaiinNo)
                : null;
            var onlineConfirmationId = receptionEntity?.OnlineConfirmationId;
            var onlineConfirmationHistory = onlineConfirmationId.HasValue && onlineConfirmationId > 0
                ? NoTrackingDataContext.OnlineConfirmationHistories.FirstOrDefault(e => e.HpId == hpId && e.PtId == ptId && e.ID == onlineConfirmationId)
                : new();

            var renkeiMst = NoTrackingDataContext.RenkeiMsts.FirstOrDefault(e => e.HpId == hpId && e.RenkeiId == 290 && e.IsInvalid == 0);

            var renkeiConf = renkeiMst != null
                ? NoTrackingDataContext.RenkeiConfs.FirstOrDefault(e => e.HpId == hpId && e.RenkeiId == renkeiMst.RenkeiId && e.IsInvalid == 0)
                : null;

            var isLinkCard = renkeiMst != null && renkeiConf != null
                ? NoTrackingDataContext.RenkeiPathConfs.Any(e => e.HpId == hpId && e.RenkeiId == renkeiMst.RenkeiId && e.SeqNo == renkeiConf.SeqNo && e.IsInvalid == 0)
                : false;

            var canEditPrescription = true;
            var epsPrescription = receptionEntity != null
                ? NoTrackingDataContext.EpsPrescriptions.FirstOrDefault(c => c.HpId == hpId && c.PtId == ptId && c.RaiinNo == receptionEntity.RaiinNo && c.Status == 0)
                : null;

            var statusNotEditPrescription = new List<int>() { RaiinState.Reservation, RaiinState.Confirmation, RaiinState.Deleted };
            if (epsPrescription != null || (receptionEntity != null && statusNotEditPrescription.Contains(receptionEntity.Status)))
            {
                canEditPrescription = false;
            }

            var meeting = reserve != null
                ? NoTrackingDataContext.Meetings.FirstOrDefault(e => e.ReserveId == reserve.ReserveId && e.IsDeleted == DeleteTypes.None)
                : null;

            return new ReceptionModel
                (
                    receptionEntity?.HpId ?? 0,
                    receptionEntity?.PtId ?? 0,
                    ptInf?.Name ?? string.Empty,
                    ptInf?.KanaName ?? string.Empty,
                    ptInf?.Sex ?? 0,
                    ptMemo?.Memo ?? string.Empty,
                    receptionEntity?.PrescriptionIssueType ?? 0,
                    receptionEntity?.PrintEpsReference ?? 0,
                    receptionEntity?.KaId ?? 0,
                    receptionEntity?.TantoId ?? 0,
                    receptionEntity?.TreatmentDepartmentId ?? 0,
                    receptionEntity?.SinDate ?? 0,
                    receptionEntity?.YoyakuTime ?? string.Empty,
                    receptionEntity?.YoyakuEndTime ?? string.Empty,
                    receptionEntity?.IsYoyaku ?? 0,
                    reserveDetail?.ReserveType ?? 0,
                    receptionEntity?.HokenPid ?? 0,
                    onlineConfirmationHistory?.ConfirmationType ?? 0,
                    GenerateDescription(infoConsFlg ?? string.Empty),
                    /*receptionEntity?.PrescriptionDeliInfo ?? string.Empty*/ string.Empty,
                    receptionEntity?.Status ?? RaiinState.Reservation,
                    tantoInf?.Sname ?? string.Empty,
                    kaInf?.KaSname ?? string.Empty,
                    treatmentDepartmentInf?.Title ?? string.Empty,
                    hoken?.HokenSbtCd ?? 0,
                    hokenInf?.Houbetu ?? string.Empty,
                    listKohi,
                    combine,
                    raiinNo,
                    hasMessage,
                    lables,
                    karteEdition?.RaiinNo ?? 0,
                    receptionEntity?.ReserveDetailId ?? 0,
                    pharmacy,
                    reserve?.PrescriptionReceiveMethod ?? 0,
                    raiinCmtInf?.Text ?? string.Empty,
                    receptionEntity?.SyosaisinKbn ?? 0,
                    receptionEntity?.JikanKbn ?? 0,
                    new OnlineConfirmationHistoryModel(
                        onlineConfirmationHistory?.ID ?? 0,
                        onlineConfirmationHistory?.PtId ?? 0,
                        onlineConfirmationHistory?.OnlineConfirmationDate ?? DateTime.MinValue,
                        onlineConfirmationHistory?.ConfirmationType ?? 0,
                        onlineConfirmationHistory?.InfoConsFlg ?? string.Empty,
                        onlineConfirmationHistory?.ConfirmationResult ?? string.Empty,
                        onlineConfirmationHistory?.PrescriptionIssueType ?? 0,
                        onlineConfirmationHistory?.UketukeStatus ?? 0,
                        onlineConfirmationHistory?.HpId ?? 0
                    ),
                    isLinkCard,
                    canEditPrescription,
                    hokenInf?.HokenKbn ?? 0,
                    meeting?.MeetingId ?? 0
                );
        }

        private string GenerateDescription(string input)
        {
            string[] descriptions = { ReceiptListConstant.MEDICATION_INFORMATION, ReceiptListConstant.SPECIFIC_EXAMINATION, ReceiptListConstant.MEDICAL_INFORMATION, ReceiptListConstant.SURGICAL_INFORMATION };
            StringBuilder result = new StringBuilder();

            for (int i = 0; i < input.Length; i++)
            {
                char c = input[i];
                if (c == '1') // Agree
                {
                    if (result.Length > 0)
                    {
                        result.Append(", ");
                    }
                    result.Append(descriptions[i]);
                }
            }

            return result.Length > 0 ? result.ToString() : string.Empty;
        }

        public long Insert(ReceptionSaveDto dto, int hpId, int userId)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();

                // Insert RaiinInf
                var raiinInf = CreateNewRaiinInf(new ReceptionModel(dto.Reception), hpId, userId);
                raiinInf.KaId = _commonRepository.GetKaIdByTreatmentDepartmentId(hpId, raiinInf.TreatmentDepartmentId);

                UpdateConfirmationInfo(hpId, raiinInf.SinDate, raiinInf.PtId, ref raiinInf);
                TrackingDataContext.RaiinInfs.Add(raiinInf);
                TrackingDataContext.SaveChanges();

                if (raiinInf.OyaRaiinNo == 0)
                {
                    raiinInf.OyaRaiinNo = raiinInf.RaiinNo;
                }

                // Insert RaiinCmtInf
                if (!string.IsNullOrWhiteSpace(dto.ReceptionComment))
                {
                    var raiinCmtInf = CreateNewRaiinCmtInf(raiinInf, dto.ReceptionComment, hpId, userId);
                    TrackingDataContext.RaiinCmtInfs.Add(raiinCmtInf);
                }

                // Insert RaiinKbnInfs
                var raiinKbnInfs = dto.KubunInfs
                    .Select(dto => CreateNewRaiinKbnInf(dto, raiinInf, hpId, userId));
                if (raiinKbnInfs.Any())
                {
                    TrackingDataContext.RaiinKbnInfs.AddRange(raiinKbnInfs);
                }

                // Update insurances and diseases
                // SaveInsuraceConfirmationHistories(dto.Insurances, raiinInf.PtId, hpId, userId);
                // UpdateDiseaseTenkis(dto.Diseases, raiinInf.PtId, hpId, userId);
                TrackingDataContext.SaveChanges();

                transaction.Commit();
                return raiinInf.RaiinNo;
            });
        }

        public string? GetInfoConsFlg(List<OnlineAgreedConsent> onlineAgreedConsent)
        {
            char[] result = new char[4];

            bool[] flags = new bool[4];
            if (!onlineAgreedConsent.Any())
            {
                return null;
            }

            foreach (var consent in onlineAgreedConsent)
            {
                if (consent.ConsKbn >= 1 && consent.ConsKbn <= 4)
                {
                    if (consent.ConsFlg == 1)
                    {
                        flags[consent.ConsKbn - 1] = true;
                    }
                }
            }

            for (int i = 0; i < 4; i++)
            {
                result[i] = flags[i] ? '1' : '2';
            }

            return new string(result);
        }

        #region Helper methods

        RaiinInf CreateNewRaiinInf(ReceptionModel model, int hpId, int userId)
        {
            return new RaiinInf
            {
                HpId = hpId,
                PtId = model.PtId,
                SinDate = model.SinDate,
                OyaRaiinNo = model.OyaRaiinNo,
                Status = model.Status,
                IsYoyaku = model.IsYoyaku,
                YoyakuTime = model.YoyakuTime,
                YoyakuId = model.YoyakuId,
                UketukeSbt = model.UketukeSbt,
                UketukeTime = model.UketukeTime,
                UketukeId = model.UketukeId,
                UketukeNo = model.UketukeNo,
                SinStartTime = model.SinStartTime,
                SinEndTime = model.SinEndTime,
                KaikeiTime = model.KaikeiTime,
                KaikeiId = model.KaikeiId,
                KaId = model.KaId,
                TantoId = model.TantoId,
                HokenPid = model.HokenPid,
                SanteiKbn = model.SanteiKbn,
                SyosaisinKbn = model.SyosaisinKbn,
                JikanKbn = model.JikanKbn,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateId = userId,
                CreateId = userId,
                TreatmentDepartmentId = model.TreatmentDepartmentId,
                PrintEpsReference = model.PrintEpsReference,
                PrescriptionIssueType = model.PrescriptionIssueType,
                OnlineConfirmationId = model.OnlineConfirmationHistoryId,
                MonshinStatus = 0
            };
        }

        RaiinCmtInf CreateNewRaiinCmtInf(RaiinInf raiinInf, string text, int hpId, int userId)
        {
            return new RaiinCmtInf
            {
                HpId = hpId,
                PtId = raiinInf.PtId,
                SinDate = raiinInf.SinDate,
                RaiinNo = raiinInf.RaiinNo,
                CmtKbn = CmtKbns.Comment,
                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateId = userId,
                Text = text,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId
            };
        }

        RaiinKbnInf CreateNewRaiinKbnInf(RaiinKbnInfDto dto, RaiinInf raiinInf, int hpId, int userId)
        {
            return new RaiinKbnInf
            {
                HpId = hpId,
                PtId = raiinInf.PtId,
                SinDate = raiinInf.SinDate,
                RaiinNo = raiinInf.RaiinNo,
                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateId = userId,
                GrpId = dto.GrpId,
                KbnCd = dto.KbnCd,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId
            };
        }

        void UpdateConfirmationInfo(int hpId, int sinDate, long ptId, ref RaiinInf raiinInf)
        {
            int confirmationType = 0;
            var raiinInfsInSameday = TrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId
                                                                                 && item.SinDate == sinDate
                                                                                 && item.PtId == ptId)
                                                                  .ToList();

            var onlineConfirmationHistoryInSameday = TrackingDataContext.OnlineConfirmationHistories.Where(item => item.PtId == ptId && item.HpId == hpId)
                                                                                                    .AsEnumerable()
                                                                                                    .Where(item => CIUtil.DateTimeToInt(item.OnlineConfirmationDate) == sinDate)
                                                                                                    .ToList();

            #region update ConfirmationType
            if (raiinInfsInSameday.Any())
            {
                var confirmedRaiinInfs = raiinInfsInSameday.Where(x => x.ConfirmationType > 0);
                confirmationType = confirmedRaiinInfs.Any() ? confirmedRaiinInfs.Min(x => x.ConfirmationType) : 0;
                raiinInf.ConfirmationType = confirmationType;
            }
            if (onlineConfirmationHistoryInSameday.Any())
            {
                var confirmedOnlineConfirmationHistorys = onlineConfirmationHistoryInSameday.Where(x => x.ConfirmationType > 0);
                confirmationType = confirmedOnlineConfirmationHistorys.Any() ? confirmedOnlineConfirmationHistorys.Min(x => x.ConfirmationType) : 0;
                raiinInf.ConfirmationType = confirmationType;
            }
            #endregion

            #region update InfConsFlg
            string infoConsFlg = "    ";
            if (raiinInfsInSameday.Any())
            {
                void UpdateFlgValue(int flgIdx)
                {
                    char flgToChar(int flg)
                    {
                        if (flg == 1)
                        {
                            return '1';
                        }
                        else if (flg == 2)
                        {
                            return '2';
                        }
                        return ' ';
                    }

                    var confirmedFlgRaiinInfs = raiinInfsInSameday.Where(x => !string.IsNullOrEmpty(x.InfoConsFlg) && x.InfoConsFlg.Length > flgIdx && x.InfoConsFlg[flgIdx] != ' ');
                    int infConsFlg = !confirmedFlgRaiinInfs.Any() ? 0 : confirmedFlgRaiinInfs.Where(x => x.InfoConsFlg != null).Select(x => x.InfoConsFlg![flgIdx].AsInteger()).DefaultIfEmpty()?.Min() ?? 0;
                    infoConsFlg = ReplaceAt(infoConsFlg, flgIdx, flgToChar(infConsFlg));
                }
                //Update PharmacistsInfoConsFlg
                UpdateFlgValue(0);
                //Update SpecificHealthCheckupsInfoConsFlg
                UpdateFlgValue(1);
                //Update DiagnosisInfoConsFlg
                UpdateFlgValue(2);
                //Update OperationInfoConsFlg
                UpdateFlgValue(3);
            }
            if (onlineConfirmationHistoryInSameday.Any())
            {
                void UpdateFlgValue(int flgIdx)
                {
                    char flgToChar(int flg)
                    {
                        if (flg == 1)
                        {
                            return '1';
                        }
                        else if (flg == 2)
                        {
                            return '2';
                        }
                        return ' ';
                    }

                    var confirmedFlgRaiinInfs = onlineConfirmationHistoryInSameday.Where(x => !string.IsNullOrEmpty(x.InfoConsFlg) && x.InfoConsFlg.Length > flgIdx && x.InfoConsFlg[flgIdx] != ' ');
                    int infConsFlg = !confirmedFlgRaiinInfs.Any() ? 0 : confirmedFlgRaiinInfs.Where(x => x.InfoConsFlg != null).Select(x => x.InfoConsFlg![flgIdx].AsInteger()).DefaultIfEmpty()?.Min() ?? 0;
                    infoConsFlg = ReplaceAt(infoConsFlg, flgIdx, flgToChar(infConsFlg));
                }
                //Update PharmacistsInfoConsFlg
                UpdateFlgValue(0);
                //Update SpecificHealthCheckupsInfoConsFlg
                UpdateFlgValue(1);
                //Update DiagnosisInfoConsFlg
                UpdateFlgValue(2);
                //Update OperationInfoConsFlg
                UpdateFlgValue(3);
            }
            if (string.IsNullOrWhiteSpace(infoConsFlg))
            {
                infoConsFlg = "";
            }
            raiinInf.InfoConsFlg = infoConsFlg;
            #endregion
        }

        string ReplaceAt(string input, int index, char newChar)
        {
            if (input == null)
            {
                return string.Empty;
            }
            StringBuilder builder = new StringBuilder(input);
            builder[index] = newChar;
            return builder.ToString();
        }
        #endregion

        public int GetMaxUketukeNo(int hpId, int sindate)
        {
            var query = NoTrackingDataContext.RaiinInfs.Where
                (
                    p => p.HpId == hpId && p.SinDate == sindate
                ).OrderByDescending(p => p.UketukeNo).FirstOrDefault();
            if (query != null)
            {
                return query.UketukeNo;
            }
            return 0;
        }

        public bool Update(ReceptionSaveDto dto, int hpId, int userId)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            var date = CIUtil.GetJapanDateTimeNow();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();

                var raiinInf = TrackingDataContext.RaiinInfs
                    .FirstOrDefault(r => r.HpId == hpId
                        && r.PtId == dto.Reception.PtId
                        && r.SinDate == dto.Reception.SinDate
                        && r.RaiinNo == dto.Reception.RaiinNo
                        && r.IsDeleted == DeleteTypes.None);

                if (raiinInf is null) return false;

                UpdateRaiinInfIfChanged(raiinInf, new ReceptionModel(dto.Reception));

                if (!string.IsNullOrWhiteSpace(dto.ReceptionComment))
                {
                    UpsertRaiinCmtInf(raiinInf, dto.ReceptionComment);
                }

                SaveRaiinKbnInfs(raiinInf, dto.KubunInfs);

                TrackingDataContext.SaveChanges();

                transaction.Commit();
                return true;

            });

            #region Helper methods

            void UpdateRaiinInfIfChanged(RaiinInf entity, ReceptionModel model)
            {
                // Detect changes
                if (entity.OyaRaiinNo != model.OyaRaiinNo ||
                    entity.KaId != model.KaId ||
                    entity.UketukeSbt != model.UketukeSbt ||
                    entity.UketukeNo != model.UketukeNo ||
                    entity.TantoId != model.TantoId ||
                    entity.SyosaisinKbn != model.SyosaisinKbn ||
                    entity.JikanKbn != model.JikanKbn ||
                    entity.SanteiKbn != model.SanteiKbn ||
                    entity.HokenPid != model.HokenPid ||
                    entity.TreatmentDepartmentId != model.TreatmentDepartmentId)
                {
                    if (model.OyaRaiinNo == 0)
                    {
                        entity.OyaRaiinNo = entity.RaiinNo;
                    }
                    else
                    {
                        entity.OyaRaiinNo = model.OyaRaiinNo;
                    }
                    var kaId = _commonRepository.GetKaIdByTreatmentDepartmentId(model.HpId, model.TreatmentDepartmentId);
                    entity.KaId = kaId;
                    entity.UketukeSbt = model.UketukeSbt;
                    entity.UketukeNo = model.UketukeNo;
                    entity.TantoId = model.TantoId;
                    entity.HokenPid = model.HokenPid;
                    entity.SyosaisinKbn = model.SyosaisinKbn;
                    entity.JikanKbn = model.JikanKbn;
                    entity.SanteiKbn = model.SanteiKbn;
                    entity.UpdateDate = date;
                    entity.UpdateId = userId;
                    entity.TreatmentDepartmentId = model.TreatmentDepartmentId;
                }

                if (entity.Status == RaiinState.Reservation || entity.Status == RaiinState.Confirmation)
                {
                    if (string.IsNullOrEmpty(entity.UketukeTime) || entity.UketukeTime.Equals("0"))
                    {
                        entity.UketukeTime = model.UketukeTime;
                    }
                    entity.Status = model.Status;
                }
            }

            void UpsertRaiinCmtInf(RaiinInf raiinInf, string text)
            {
                var raiinCmtInf = TrackingDataContext.RaiinCmtInfs
                   .FirstOrDefault(x => x.HpId == hpId
                        && x.RaiinNo == raiinInf.RaiinNo
                        && x.CmtKbn == CmtKbns.Comment
                        && x.IsDelete == DeleteTypes.None);
                if (raiinCmtInf is null)
                {
                    TrackingDataContext.RaiinCmtInfs.Add(new RaiinCmtInf
                    {
                        HpId = hpId,
                        PtId = raiinInf.PtId,
                        SinDate = raiinInf.SinDate,
                        RaiinNo = raiinInf.RaiinNo,
                        CmtKbn = CmtKbns.Comment,
                        Text = text,
                        CreateDate = date,
                        CreateId = userId,
                        UpdateDate = date,
                        UpdateId = userId
                    });
                }
                else if (raiinCmtInf.Text != text)
                {
                    raiinCmtInf.Text = text;
                    raiinCmtInf.UpdateDate = date;
                    raiinCmtInf.UpdateId = userId;
                }
            }

            void SaveRaiinKbnInfs(RaiinInf raiinInf, IEnumerable<RaiinKbnInfDto> kbnInfDtos)
            {
                var raiinInfs = TrackingDataContext.RaiinKbnInfs
                    .Where(x => x.HpId == hpId
                        && x.PtId == raiinInf.PtId
                        && x.SinDate == raiinInf.SinDate
                        && x.RaiinNo == raiinInf.RaiinNo
                        && x.IsDelete == DeleteTypes.None)
                    .ToList();
                var grpIds = kbnInfDtos.Select(e => e.GrpId).ToList();
                var raiinDelete = raiinInfs.Where(e => !grpIds.Contains(e.GrpId)).ToList();
                foreach (var raiin in raiinDelete)
                {
                    raiin.IsDelete = DeleteTypes.Deleted;
                    raiin.UpdateDate = date;
                    raiin.UpdateId = userId;
                }
                var grpIdSelect = raiinInfs.Select(e => e.GrpId).ToList();
                var raiinAdd = grpIds.Except(grpIdSelect).ToList();

                foreach (var grpId in raiinAdd)
                {
                    TrackingDataContext.RaiinKbnInfs.Add(new RaiinKbnInf
                    {
                        HpId = hpId,
                        PtId = raiinInf.PtId,
                        SinDate = raiinInf.SinDate,
                        RaiinNo = raiinInf.RaiinNo,
                        GrpId = grpId,
                        CreateDate = date,
                        UpdateDate = date,
                        UpdateId = userId,
                        CreateId = userId
                    });
                }
            }

            #endregion
        }

        private bool Update(int hpId, long raiinNo, Action<RaiinInf> updateEntity)
        {
            var raiinInf = TrackingDataContext.RaiinInfs.Where(r =>
                r.HpId == hpId
                && r.RaiinNo == raiinNo).FirstOrDefault();
            if (raiinInf is null)
            {
                return false;
            }

            updateEntity(raiinInf);
            return TrackingDataContext.SaveChanges() > 0;
        }

        private bool Update(int hpId, long raiinNo, Action<RaiinInf> updateEntity, int userId)
        {
            var raiinInf = TrackingDataContext.RaiinInfs.Where(r =>
                r.HpId == hpId
                && r.RaiinNo == raiinNo
                && r.IsDeleted == DeleteTypes.None).FirstOrDefault();
            if (raiinInf is null)
            {
                return false;
            }

            updateEntity(raiinInf);
            raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
            raiinInf.UpdateId = userId;

            if (raiinInf.Status == RaiinState.Deleted)
            {
                raiinInf.IsDeleted = DeleteTypes.Deleted;
            }

            TrackingDataContext.SaveChanges();
            return true;
        }

        private (bool isValid, int typeInvalid) IsValidated(long raiinNo, int updatedStatus, int hpId)
        {
            var karteEditionExist = NoTrackingDataContext.KarteEditions.Any(r => r.HpId == hpId && r.RaiinNo == raiinNo && r.IsDeleted == DeleteTypes.None && r.KarteStatus == KarteStatusConst.Official);
            if (updatedStatus == RaiinState.Deleted)
            {
                if (karteEditionExist)
                    return (false, 1);
            }

            var existingRaiinInf = NoTrackingDataContext.RaiinInfs.FirstOrDefault(r => r.HpId == hpId && r.RaiinNo == raiinNo);

            if (existingRaiinInf == null)
            {
                return (false, 0);
            }

            if (existingRaiinInf?.Status != updatedStatus)
            {
                switch (existingRaiinInf?.Status)
                {
                    case RaiinState.Reservation:
                    case RaiinState.Confirmation:
                        if (updatedStatus != RaiinState.Deleted)
                            return (false, 0);
                        break;
                    case RaiinState.Paid:
                    case RaiinState.FcoWaiting:
                    case RaiinState.Deleted:
                        return (false, 0);
                    case RaiinState.Receptionist:
                    case RaiinState.Called:
                    case RaiinState.Consulting:
                        if (updatedStatus == RaiinState.Confirmation || updatedStatus == RaiinState.Paid || updatedStatus == RaiinState.FcoWaiting)
                            return (false, 0);
                        if ((updatedStatus == RaiinState.ConsultationCompleted || updatedStatus == RaiinState.AmountConfirmed) && !karteEditionExist)
                            return (false, 0);
                        break;
                    case RaiinState.ConsultationCompleted:
                    case RaiinState.AmountConfirmed:
                        if (updatedStatus == RaiinState.Confirmation || updatedStatus == RaiinState.Paid || updatedStatus == RaiinState.FcoWaiting)
                            return (false, 0);
                        break;
                    default:
                        break;
                }
            }
            return (true, -1);
        }

        private void SaveInsuraceConfirmationHistories(IEnumerable<InsuranceDto> insurances, long ptId, int hpId, int userId)
        {
            if (insurances.Any())
            {
                List<PtHokenCheck> listHokenCheckAddNew = new();

                foreach (var insuranceItem in insurances)
                {
                    var hokenGrp = insuranceItem.IsHokenGroupKohi ? HokenGroupConstant.HokenGroupKohi : HokenGroupConstant.HokenGroupHokenPattern;

                    var listHokenCheckInsertInput = insuranceItem.ConfirmDateList.Where(item => item.SeqNo == 0).ToList();
                    var listHokenCheckUpdateInput = insuranceItem.ConfirmDateList.Where(item => item.SeqNo != 0).ToList();

                    // Update PtHokenCheck
                    var listUpdateItemDB = TrackingDataContext.PtHokenChecks
                                 .Where(item =>
                                             listHokenCheckUpdateInput.Select(item => item.SeqNo).Contains(item.SeqNo)
                                             && item.HpId == hpId
                                             && item.PtID == ptId
                                             && item.HokenId == insuranceItem.HokenId
                                             && item.HokenGrp == hokenGrp
                                             && item.IsDeleted == 0)
                                 .ToList();

                    // Update PtHokenCheck
                    foreach (var update in listHokenCheckUpdateInput)
                    {
                        var checkDatetimeInput = DateTime.ParseExact(update.SinDate.ToString(), "yyyyMMdd", CultureInfo.InvariantCulture);
                        var utcCheckDateTime = DateTime.SpecifyKind(checkDatetimeInput, DateTimeKind.Utc);

                        var hokenCheckItem = listUpdateItemDB.FirstOrDefault(item => item.SeqNo == update.SeqNo);
                        if (hokenCheckItem != null)
                        {
                            hokenCheckItem.UpdateDate = CIUtil.GetJapanDateTimeNow();
                            hokenCheckItem.UpdateId = userId;

                            // update isDelete
                            if (update.IsDelete)
                            {
                                hokenCheckItem.IsDeleted = 1;
                            }
                            else
                            {
                                hokenCheckItem.CheckDate = utcCheckDateTime;
                                hokenCheckItem.CheckCmt = update.Comment;
                                hokenCheckItem.CheckId = userId;
                            }
                        }
                    }

                    // Add new PtHokenCheck
                    foreach (var item in listHokenCheckInsertInput)
                    {
                        var checkDatetime = DateTime.ParseExact(item.SinDate.ToString(), "yyyyMMdd", CultureInfo.InvariantCulture);
                        var utcCheckDateTime = DateTime.SpecifyKind(checkDatetime, DateTimeKind.Utc);

                        listHokenCheckAddNew.Add(new PtHokenCheck
                        {
                            HpId = hpId,
                            PtID = ptId,
                            HokenGrp = hokenGrp,
                            HokenId = insuranceItem.HokenId,
                            CheckDate = utcCheckDateTime,
                            CheckCmt = item.Comment,
                            CheckId = userId,
                            CreateDate = CIUtil.GetJapanDateTimeNow(),
                            CreateId = userId,
                            UpdateDate = CIUtil.GetJapanDateTimeNow(),
                            UpdateId = userId
                        });
                    }
                }
                TrackingDataContext.PtHokenChecks.AddRange(listHokenCheckAddNew);
            }
        }

        private void UpdateDiseaseTenkis(IEnumerable<DiseaseDto> diseases, long ptId, int hpId, int userId)
        {
            var ptByomeiIds = diseases.Select(d => d.Id);
            var ptByomeis = TrackingDataContext.PtByomeis.AsTracking()
                .Where(x => x.HpId == hpId && x.PtId == ptId && ptByomeiIds.Contains(x.Id))
                .ToList();

            foreach (var disease in diseases)
            {
                var ptByomei = ptByomeis.Find(x => x.Id == disease.Id);
                if (ptByomei is not null
                    && (ptByomei.TenkiKbn != disease.TenkiKbn || ptByomei.TenkiDate != disease.TenkiDate))
                {
                    ptByomei.TenkiKbn = disease.TenkiKbn;
                    ptByomei.TenkiDate = disease.TenkiDate;
                    ptByomei.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    ptByomei.UpdateId = userId;
                }
            }
        }

        public List<ReceptionRowModel> GetList(int hpId, int sinDate, long raiinNo, long ptId, [Optional] bool isGetAccountDue, [Optional] bool isGetFamily, int isDeleted = 2, bool searchSameVisit = false)
        {
            return GetReceptionRowModels(hpId, sinDate, raiinNo, ptId, isGetAccountDue, isGetFamily, isDeleted, searchSameVisit);
        }

        public IEnumerable<ReceptionModel> GetList(int hpId, long ptId, int karteDeleteHistory)
        {
            var result = NoTrackingDataContext.RaiinInfs.Where
                                (r =>
                                    r.HpId == hpId && r.PtId == ptId && r.Status >= RaiinState.Called &&
                                 (r.IsDeleted == DeleteTypes.None || karteDeleteHistory == 1 || (r.IsDeleted != DeleteTypes.Confirm && karteDeleteHistory == 2)));
            return result.Select(r => new ReceptionModel(
                        r.HpId,
                        r.PtId,
                        r.SinDate,
                        r.RaiinNo,
                        r.OyaRaiinNo,
                        r.HokenPid,
                        r.SanteiKbn,
                        r.Status,
                        r.IsYoyaku,
                        r.YoyakuTime ?? string.Empty,
                        r.YoyakuId,
                        r.UketukeSbt,
                        r.UketukeTime ?? string.Empty,
                        r.UketukeId,
                        r.UketukeNo,
                        r.SinStartTime ?? string.Empty,
                        r.SinEndTime ?? string.Empty,
                        r.KaikeiTime ?? string.Empty,
                        r.KaikeiId,
                        r.KaId,
                        r.TantoId,
                        r.SyosaisinKbn,
                        r.JikanKbn,
                        string.Empty,
                        0
                   ));

        }

        /// <summary>
        ///  get reception paging list
        /// </summary>
        /// <param name="hpId"></param>
        /// <param name="sinDate"></param>
        /// <param name="raiinNo"></param>
        /// <param name="ptId"></param>
        /// <param name="isGetAccountDue"></param>
        /// <param name="isGetFamily"></param>
        /// <param name="isDeleted"></param>
        /// <param name="searchSameVisit"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public (List<ReceptionRowModel> receptionInfos, int totalItems) GetPagingList(int hpId, int sinDate, long raiinNo, long ptId, bool isGetAccountDue, bool isGetFamily, int isDeleted = 2, bool searchSameVisit = false, int limit = 0, int offset = 0)
        {
            // 1. Prepare all the necessary collections for the join operation
            // Raiin (Reception)
            var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(x => x.HpId == hpId && (isDeleted == 2 || x.IsDeleted == isDeleted));
            var karteEditions = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId && item.IsDeleted == DeleteTypes.None && item.KarteStatus == KarteStatusConst.Official);
            var raiinCmtInfs = NoTrackingDataContext.RaiinCmtInfs.Where(x => x.HpId == hpId && x.IsDelete == DeleteTypes.None);
            var raiinKbnInfs = NoTrackingDataContext.RaiinKbnInfs.Where(x => x.HpId == hpId && x.IsDelete == DeleteTypes.None);
            var raiinKbnDetails = NoTrackingDataContext.RaiinKbnDetails.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // Pt (Patient)
            var ptInfs = NoTrackingDataContext.PtInfs.Where(x => x.HpId == hpId && (isDeleted == 2 || x.IsDelete == isDeleted));
            var ptCmtInfs = NoTrackingDataContext.PtCmtInfs.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            var ptKohis = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // Rsv (Reservation)
            var rsvInfs = NoTrackingDataContext.RsvInfs.Where(i => i.HpId == hpId);
            var rsvFrameMsts = NoTrackingDataContext.RsvFrameMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // User (Doctor)
            var userMsts = NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // Ka (Department)
            var kaMsts = NoTrackingDataContext.KaMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // Lock (Function lock)
            var lockInfs = NoTrackingDataContext.LockInfs.Where(x => x.HpId == hpId &&
                x.FunctionCd == FunctionCode.MedicalExaminationCode || x.FunctionCd == FunctionCode.TeamKarte || x.FunctionCd == FunctionCode.SwitchOrderCode);
            // Uketuke
            var uketukeSbtMsts = NoTrackingDataContext.UketukeSbtMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);

            // 2. Filter collections by parameters
            var filteredRaiinInfs = raiinInfs;
            if (!isGetAccountDue)
            {
                filteredRaiinInfs = filteredRaiinInfs.Where(x => x.HpId == hpId && x.SinDate == sinDate);
            }
            if (raiinNo != CommonConstants.InvalidId && !searchSameVisit)
            {
                filteredRaiinInfs = filteredRaiinInfs.Where(x => x.RaiinNo == raiinNo);
            }
            if (searchSameVisit && ptId != CommonConstants.InvalidId)
            {
                filteredRaiinInfs = filteredRaiinInfs.Where(item => item.PtId == ptId);
            }

            var filteredPtInfs = ptInfs;
            if (ptId != CommonConstants.InvalidId && !isGetFamily)
            {
                filteredPtInfs = filteredPtInfs.Where(x => x.PtId == ptId);
            }
            else if (ptId != CommonConstants.InvalidId && isGetFamily)
            {
                filteredRaiinInfs = filteredRaiinInfs.Where(item => item.Status >= RaiinState.Called);
            }

            // 3. Perform the join operation
            var raiinQuery =
                from raiinInf in filteredRaiinInfs
                join ptInf in filteredPtInfs on
                    new { raiinInf.HpId, raiinInf.PtId } equals
                    new { ptInf.HpId, ptInf.PtId }
                join raiinCmtInfComment in raiinCmtInfs.Where(x => x.CmtKbn == CmtKbns.Comment) on
                    new { raiinInf.HpId, raiinInf.PtId, raiinInf.SinDate, raiinInf.RaiinNo } equals
                    new { raiinCmtInfComment.HpId, raiinCmtInfComment.PtId, raiinCmtInfComment.SinDate, raiinCmtInfComment.RaiinNo } into relatedRaiinCmtInfComments
                from relatedRaiinCmtInfComment in relatedRaiinCmtInfComments.DefaultIfEmpty()
                join raiinCmtInfRemark in raiinCmtInfs.Where(x => x.CmtKbn == CmtKbns.Remark) on
                    new { raiinInf.HpId, raiinInf.PtId, raiinInf.SinDate, raiinInf.RaiinNo } equals
                    new { raiinCmtInfRemark.HpId, raiinCmtInfRemark.PtId, raiinCmtInfRemark.SinDate, raiinCmtInfRemark.RaiinNo } into relatedRaiinCmtInfRemarks
                from relatedRaiinCmtInfRemark in relatedRaiinCmtInfRemarks.DefaultIfEmpty()
                from ptCmtInf in ptCmtInfs.Where(x => x.PtId == ptInf.PtId).OrderByDescending(x => x.UpdateDate).Take(1).DefaultIfEmpty()
                join rsvInf in rsvInfs on raiinInf.RaiinNo equals rsvInf.RaiinNo into relatedRsvInfs
                from relatedRsvInf in relatedRsvInfs.DefaultIfEmpty()
                join rsvFrameMst in rsvFrameMsts on relatedRsvInf.RsvFrameId equals rsvFrameMst.RsvFrameId into relatedRsvFrameMsts
                from relatedRsvFrameMst in relatedRsvFrameMsts.DefaultIfEmpty()
                join lockInf in lockInfs on raiinInf.RaiinNo equals lockInf.RaiinNo into relatedLockInfs
                from relatedLockInf in relatedLockInfs.DefaultIfEmpty()
                join uketukeSbtMst in uketukeSbtMsts on raiinInf.UketukeSbt equals uketukeSbtMst.KbnId into relatedUketukeSbtMsts
                from relatedUketukeSbtMst in relatedUketukeSbtMsts.DefaultIfEmpty()
                join tanto in userMsts on
                    new { raiinInf.HpId, UserId = raiinInf.TantoId } equals
                    new { tanto.HpId, tanto.UserId } into relatedTantos
                from relatedTanto in relatedTantos.DefaultIfEmpty()
                join primaryDoctor in userMsts on
                    new { raiinInf.HpId, UserId = ptInf.PrimaryDoctor } equals
                    new { primaryDoctor.HpId, primaryDoctor.UserId } into relatedPrimaryDoctors
                from relatedPrimaryDoctor in relatedPrimaryDoctors.DefaultIfEmpty()
                join kaMst in kaMsts on
                    new { raiinInf.HpId, raiinInf.KaId } equals
                    new { kaMst.HpId, kaMst.KaId } into relatedKaMsts
                from relatedKaMst in relatedKaMsts.DefaultIfEmpty()
                join ptHokenPattern in ptHokenPatterns on
                    new { raiinInf.HpId, raiinInf.PtId, raiinInf.HokenPid } equals
                    new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.HokenPid } into relatedPtHokenPatterns
                from relatedPtHokenPattern in relatedPtHokenPatterns.DefaultIfEmpty()
                from ptKohi1 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi1Id).Take(1).DefaultIfEmpty()
                from ptKohi2 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi2Id).Take(1).DefaultIfEmpty()
                from ptKohi3 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi3Id).Take(1).DefaultIfEmpty()
                from ptKohi4 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi4Id).Take(1).DefaultIfEmpty()
                select new
                {
                    raiinInf,
                    ptInf,
                    ptCmtInf,
                    primaryDoctorName = relatedPrimaryDoctor.Sname,
                    relatedUketukeSbtMst,
                    relatedTanto,
                    relatedKaMst,
                    relatedRaiinCmtInfComment,
                    relatedRaiinCmtInfRemark,
                    relatedRsvFrameMst,
                    relatedLockInf,
                    relatedPtHokenPattern,
                    ptKohi1,
                    ptKohi2,
                    ptKohi3,
                    ptKohi4,
                    raiinKbnDetails = (
                        from inf in raiinKbnInfs
                        join detail in raiinKbnDetails on
                            new { inf.HpId, inf.GrpId, inf.KbnCd } equals
                            new { detail.HpId, GrpId = detail.GrpCd, detail.KbnCd }
                        where inf.HpId == hpId
                            && inf.PtId == raiinInf.PtId
                            && inf.SinDate == sinDate
                            && inf.RaiinNo == raiinInf.RaiinNo
                        select detail
                    ).ToList(),
                    parentRaiinNo = (
                        from r in raiinInfs
                        where r.HpId == hpId
                            && r.PtId == raiinInf.PtId
                            && r.SinDate == raiinInf.SinDate
                            && r.OyaRaiinNo == raiinInf.OyaRaiinNo
                            && r.RaiinNo != r.OyaRaiinNo
                        select r.OyaRaiinNo
                    ).FirstOrDefault(),
                    lastVisitDate = (
                        from x in raiinInfs
                        where x.HpId == hpId
                            && x.PtId == raiinInf.PtId
                            && x.SinDate < sinDate
                        join k in karteEditions on
                            x.RaiinNo equals k.RaiinNo
                        orderby x.SinDate descending, x.RaiinNo descending
                        select x.SinDate
                    ).FirstOrDefault(),
                    firstVisitDate = (
                        from x in raiinInfs
                        where x.HpId == hpId
                            && x.PtId == raiinInf.PtId
                            && x.SinDate < sinDate
                            && x.SyosaisinKbn == SyosaiConst.Syosin
                        join k in karteEditions on
                            x.RaiinNo equals k.RaiinNo
                        orderby x.SinDate descending
                        select x.SinDate
                    ).FirstOrDefault()
                };

            var raiins = raiinQuery.Skip(offset)
                                   .Take(limit)
                                   .ToList();

            var grpIds = NoTrackingDataContext.RaiinKbnMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None).Select(x => x.GrpCd).ToList();
            var models = raiins.Select(r => new ReceptionRowModel(
                r.raiinInf.RaiinNo,
                r.raiinInf.PtId,
                r.parentRaiinNo,
                r.raiinInf.UketukeNo,
                r.relatedLockInf is not null,
                r.raiinInf.Status,
                r.raiinInf.IsDeleted,
                r.ptInf.PtNum,
                r.ptInf.KanaName ?? string.Empty,
                r.ptInf.Name ?? string.Empty,
                r.ptInf.Sex,
                r.ptInf.Birthday,
                r.raiinInf.YoyakuTime ?? string.Empty,
                r.raiinInf.ConfirmationType,
                r.raiinInf.InfoConsFlg ?? string.Empty,
                r.relatedRsvFrameMst?.RsvFrameName ?? string.Empty,
                r.relatedUketukeSbtMst?.KbnId ?? CommonConstants.InvalidId,
                r.raiinInf.UketukeTime ?? string.Empty,
                r.raiinInf.SinStartTime ?? string.Empty,
                r.raiinInf.SinEndTime ?? string.Empty,
                r.raiinInf.KaikeiTime ?? string.Empty,
                r.relatedRaiinCmtInfComment?.Text ?? string.Empty,
                r.ptCmtInf?.Text ?? string.Empty,
                r.relatedTanto?.UserId ?? CommonConstants.InvalidId,
                string.IsNullOrEmpty(r.relatedTanto?.DrName) ? r.relatedTanto?.Name ?? string.Empty : r.relatedTanto?.DrName ?? string.Empty,
                r.relatedTanto?.KanaName ?? string.Empty,
                r.relatedKaMst?.KaId ?? CommonConstants.InvalidId,
                r.relatedKaMst?.KaName ?? string.Empty,
                r.lastVisitDate,
                r.firstVisitDate,
                r.primaryDoctorName ?? string.Empty,
                r.relatedRaiinCmtInfRemark?.Text ?? string.Empty,
                r.raiinInf.ConfirmationState,
                r.raiinInf.ConfirmationResult ?? string.Empty,
                grpIds,
                dynamicCells: r.raiinKbnDetails.Select(d => new DynamicCell(d.GrpCd, d.KbnCd, d.KbnName ?? string.Empty, d.ColorCd?.Length > 0 ? "#" + d.ColorCd : string.Empty)).ToList(),
                r.raiinInf.SinDate,
                // Fields needed to create Hoken name
                r.relatedPtHokenPattern?.HokenPid ?? CommonConstants.InvalidId,
                r.relatedPtHokenPattern?.StartDate ?? 0,
                r.relatedPtHokenPattern?.EndDate ?? 0,
                r.relatedPtHokenPattern?.HokenSbtCd ?? CommonConstants.InvalidId,
                r.relatedPtHokenPattern?.HokenKbn ?? CommonConstants.InvalidId,
                r.ptKohi1?.HokenSbtKbn ?? CommonConstants.InvalidId,
                r.ptKohi1?.Houbetu ?? string.Empty,
                r.ptKohi2?.HokenSbtKbn ?? CommonConstants.InvalidId,
                r.ptKohi2?.Houbetu ?? string.Empty,
                r.ptKohi3?.HokenSbtKbn ?? CommonConstants.InvalidId,
                r.ptKohi3?.Houbetu ?? string.Empty,
                r.ptKohi4?.HokenSbtKbn ?? CommonConstants.InvalidId,
                r.ptKohi4?.Houbetu ?? string.Empty
            )).ToList();

            foreach (var model in models)
            {
                var kanaName = model.KanaName?.Replace("　", " ") ?? "";
                var list = models
                    .Where(vs => vs.KanaName?.Replace("　", " ") == kanaName && vs.PtId != model.PtId && model.PtNum != vs.PtNum);
                if (!string.IsNullOrWhiteSpace(kanaName) && list != null && list.Any())
                {
                    model.IsNameDuplicate = true;
                }
                else
                {
                    model.IsNameDuplicate = false;
                }
            }

            // Get total items
            int totalItems = (from raiinInf in filteredRaiinInfs
                              join ptInf in filteredPtInfs on
                                  new { raiinInf.HpId, raiinInf.PtId } equals
                                  new { ptInf.HpId, ptInf.PtId }
                              select raiinInf.RaiinNo)
                              .Count();
            return (models, totalItems);
        }

        public ReceptionModel GetYoyakuRaiinInf(int hpId, long ptId, int sinDate)
        {
            var entity = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId
                                                                       && item.SinDate == sinDate
                                                                       && item.PtId == ptId
                                                                       && item.IsDeleted == DeleteTypes.None
                                                                       && item.Status == RaiinState.Reservation)
                                                        .OrderByDescending(p => p.RaiinNo)
                                                        .FirstOrDefault();
            if (entity == null)
            {
                return new();
            }
            return new ReceptionModel(
                        entity.HpId,
                        entity.PtId,
                        entity.SinDate,
                        entity.RaiinNo,
                        entity.OyaRaiinNo,
                        entity.HokenPid,
                        entity.SanteiKbn,
                        entity.Status,
                        entity.IsYoyaku,
                        entity.YoyakuTime ?? string.Empty,
                        entity.YoyakuId,
                        entity.UketukeSbt,
                        entity.UketukeTime ?? string.Empty,
                        entity.UketukeId,
                        entity.UketukeNo,
                        entity.SinStartTime ?? string.Empty,
                        entity.SinEndTime ?? string.Empty,
                        entity.KaikeiTime ?? string.Empty,
                        entity.KaikeiId,
                        entity.KaId,
                        entity.TantoId,
                        entity.SyosaisinKbn,
                        entity.JikanKbn,
                        string.Empty
                        );
        }

        public List<ReceptionModel> GetLastRaiinInfs(int hpId, long ptId, int sinDate, bool isGetSysosaisin = false)
        {
            var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                             item.PtId == ptId &&
                                                                             item.IsDeleted == DeleteTypes.None &&
                                                                             item.SinDate < sinDate && (!isGetSysosaisin || item.SanteiKbn != 2))
                                                              .OrderByDescending(p => p.SinDate)
                                                              .ThenByDescending(p => p.RaiinNo);

            var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                     item.PtId == ptId &&
                                                                                     item.IsDeleted == DeleteTypes.None &&
                                                                                     item.KarteStatus == KarteStatusConst.Official &&
                                                                                     item.SinDate < sinDate);

            var result = (from raiinInf in listRaiinInf
                          join karteEdition in listKarteEdition on
                              raiinInf.RaiinNo equals karteEdition.RaiinNo
                          select raiinInf)
                         .OrderByDescending(item => item.SinDate)
                         .ThenByDescending(item => item.RaiinNo);

            return result.Select(r => new ReceptionModel(
                    r.HpId,
                    r.PtId,
                    r.SinDate,
                    r.RaiinNo,
                    r.OyaRaiinNo,
                    r.HokenPid,
                    r.SanteiKbn,
                    r.Status,
                    r.IsYoyaku,
                    r.YoyakuTime ?? string.Empty,
                    r.YoyakuId,
                    r.UketukeSbt,
                    r.UketukeTime ?? string.Empty,
                    r.UketukeId,
                    r.UketukeNo,
                    r.SinStartTime ?? string.Empty,
                    r.SinEndTime ?? string.Empty,
                    r.KaikeiTime ?? string.Empty,
                    r.KaikeiId,
                    r.KaId,
                    r.TantoId,
                    r.SyosaisinKbn,
                    r.JikanKbn,
                    string.Empty,
                    0
               )).ToList();
        }

        public ReceptionModel GetLastVisit(int hpId, long ptId, int sinDate, bool isGetSysosaisin = false)
        {
            var result = NoTrackingDataContext.RaiinInfs
                            .Join(
                                    NoTrackingDataContext.KarteEditions,
                                    r => new { r.HpId, r.PtId, r.RaiinNo },
                                    k => new { k.HpId, k.PtId, k.RaiinNo },
                                    (r, k) => new { r, k }
                                )
                            .Where(p => p.r.HpId == hpId &&
                                        p.r.PtId == ptId &&
                                        p.r.IsDeleted == DeleteTypes.None &&
                                        // p.Status >= RaiinState.Calculate &&
                                        p.k.KarteStatus == 1 &&
                                        (!isGetSysosaisin || p.r.SanteiKbn != 2) &&
                                        (sinDate <= 0 || p.r.SinDate < sinDate))
                            .OrderByDescending(p => p.r.SinDate)
                            .ThenByDescending(p => p.r.UketukeTime)
                            .ThenByDescending(p => p.r.RaiinNo)
                            .Select(p => p.r)
                            .FirstOrDefault();

            if (result == null)
                return new();

            return new ReceptionModel(
                    result.HpId,
                    result.PtId,
                    result.SinDate,
                    result.RaiinNo,
                    result.OyaRaiinNo,
                    result.HokenPid,
                    result.SanteiKbn,
                    result.Status,
                    result.IsYoyaku,
                    result.YoyakuTime ?? string.Empty,
                    result.YoyakuId,
                    result.UketukeSbt,
                    result.UketukeTime ?? string.Empty,
                    result.UketukeId,
                    result.UketukeNo,
                    result.SinStartTime ?? string.Empty,
                    result.SinEndTime ?? string.Empty,
                    result.KaikeiTime ?? string.Empty,
                    result.KaikeiId,
                    result.KaId,
                    result.TantoId,
                    result.SyosaisinKbn,
                    result.JikanKbn,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    result.ReserveDetailId ?? 0,
                    result.PrintEpsReference,
                    result.PrescriptionIssueType,
                    result.UpdateDate
               );
        }

        public List<SameVisitModel> GetListSameVisit(int hpId, long ptId, int sinDate)
        {
            List<SameVisitModel> result = new();
            var raiinInfList = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId
                                                                             && item.PtId == ptId
                                                                             && (sinDate == 0 || item.SinDate == sinDate)
                                                                             && item.IsDeleted == 0)
                                                              .ToList();

            foreach (var raiinInf in raiinInfList)
            {
                string sameVisit = raiinInfList.FirstOrDefault(item => item.OyaRaiinNo == raiinInf.OyaRaiinNo && item.RaiinNo != raiinInf.RaiinNo)?.OyaRaiinNo.ToString() ?? string.Empty;
                var sameItem = new SameVisitModel(raiinInf.SinDate, raiinInf.PtId, raiinInf.RaiinNo, raiinInf.OyaRaiinNo, sameVisit);
                result.Add(sameItem);
            }
            return result;
        }

        public bool CheckListNo(int hpId, List<long> raininNos)
        {
            var check = NoTrackingDataContext.RaiinInfs.Any(r => r.HpId == hpId && raininNos.Contains(r.RaiinNo) && r.IsDeleted != 1);
            return check;
        }

        public bool CheckNo(int hpId, long raininNo, long ptId, int sinDate)
        {
            var check = NoTrackingDataContext.RaiinInfs.Any(r => r.HpId == hpId && r.RaiinNo == raininNo && r.IsDeleted != 1 && r.PtId == ptId && r.SinDate == sinDate);
            return check;
        }

        public bool CheckExistOfRaiinNos(int hpId, List<long> raininNos)
        {
            raininNos = raininNos.Distinct().ToList();
            var raiinInfCount = NoTrackingDataContext.RaiinInfs.Count(r => r.HpId == hpId && raininNos.Contains(r.RaiinNo) && r.IsDeleted != 1);
            return raininNos.Count == raiinInfCount;
        }

        private List<ReceptionRowModel> GetReceptionRowModels(int hpId, int sinDate, long raiinNo, long ptId, bool isGetAccountDue, bool isGetFamily, int isDeleted, bool searchSameVisit)
        {
            // 1. Prepare all the necessary collections for the join operation
            // Raiin (Reception)
            var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(x => x.HpId == hpId && (isDeleted == 2 || x.IsDeleted == isDeleted));
            var karteEditions = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId && item.IsDeleted == DeleteTypes.None && item.KarteStatus == KarteStatusConst.Official);
            var raiinCmtInfs = NoTrackingDataContext.RaiinCmtInfs.Where(x => x.HpId == hpId && x.IsDelete == DeleteTypes.None);
            var raiinKbnInfs = NoTrackingDataContext.RaiinKbnInfs.Where(x => x.HpId == hpId && x.IsDelete == DeleteTypes.None);
            var raiinKbnDetails = NoTrackingDataContext.RaiinKbnDetails.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // Pt (Patient)
            var ptInfs = NoTrackingDataContext.PtInfs.Where(x => x.HpId == hpId && (isDeleted == 2 || x.IsDelete == isDeleted));
            var ptCmtInfs = NoTrackingDataContext.PtCmtInfs.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            var ptKohis = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // Rsv (Reservation)
            var rsvInfs = NoTrackingDataContext.RsvInfs.Where(i => i.HpId == hpId);
            var rsvFrameMsts = NoTrackingDataContext.RsvFrameMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // User (Doctor)
            var userMsts = NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // Ka (Department)
            var kaMsts = NoTrackingDataContext.KaMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // Lock (Function lock)
            var lockInfs = NoTrackingDataContext.LockInfs.Where(x => x.HpId == hpId &&
                x.FunctionCd == FunctionCode.MedicalExaminationCode || x.FunctionCd == FunctionCode.TeamKarte || x.FunctionCd == FunctionCode.SwitchOrderCode);
            // Uketuke
            var uketukeSbtMsts = NoTrackingDataContext.UketukeSbtMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);

            // 2. Filter collections by parameters
            var filteredRaiinInfs = raiinInfs;
            if (!isGetAccountDue)
            {
                filteredRaiinInfs = filteredRaiinInfs.Where(x => x.HpId == hpId && x.SinDate == sinDate);
            }
            if (raiinNo != CommonConstants.InvalidId && !searchSameVisit)
            {
                filteredRaiinInfs = filteredRaiinInfs.Where(x => x.RaiinNo == raiinNo);
            }
            if (searchSameVisit && ptId != CommonConstants.InvalidId)
            {
                filteredRaiinInfs = filteredRaiinInfs.Where(item => item.PtId == ptId);
            }

            var filteredPtInfs = ptInfs;
            if (ptId != CommonConstants.InvalidId && !isGetFamily)
            {
                filteredPtInfs = filteredPtInfs.Where(x => x.PtId == ptId);
            }
            else if (ptId != CommonConstants.InvalidId && isGetFamily)
            {
                filteredRaiinInfs = filteredRaiinInfs.Where(item => item.Status >= RaiinState.Called);
            }

            // 3. Perform the join operation
            var raiinQuery =
                from raiinInf in filteredRaiinInfs
                join ptInf in filteredPtInfs on
                    new { raiinInf.HpId, raiinInf.PtId } equals
                    new { ptInf.HpId, ptInf.PtId }
                join raiinCmtInfComment in raiinCmtInfs.Where(x => x.CmtKbn == CmtKbns.Comment) on
                    new { raiinInf.HpId, raiinInf.PtId, raiinInf.SinDate, raiinInf.RaiinNo } equals
                    new { raiinCmtInfComment.HpId, raiinCmtInfComment.PtId, raiinCmtInfComment.SinDate, raiinCmtInfComment.RaiinNo } into relatedRaiinCmtInfComments
                from relatedRaiinCmtInfComment in relatedRaiinCmtInfComments.DefaultIfEmpty()
                join raiinCmtInfRemark in raiinCmtInfs.Where(x => x.CmtKbn == CmtKbns.Remark) on
                    new { raiinInf.HpId, raiinInf.PtId, raiinInf.SinDate, raiinInf.RaiinNo } equals
                    new { raiinCmtInfRemark.HpId, raiinCmtInfRemark.PtId, raiinCmtInfRemark.SinDate, raiinCmtInfRemark.RaiinNo } into relatedRaiinCmtInfRemarks
                from relatedRaiinCmtInfRemark in relatedRaiinCmtInfRemarks.DefaultIfEmpty()
                from ptCmtInf in ptCmtInfs.Where(x => x.PtId == ptInf.PtId).OrderByDescending(x => x.UpdateDate).Take(1).DefaultIfEmpty()
                join rsvInf in rsvInfs on raiinInf.RaiinNo equals rsvInf.RaiinNo into relatedRsvInfs
                from relatedRsvInf in relatedRsvInfs.DefaultIfEmpty()
                join rsvFrameMst in rsvFrameMsts on relatedRsvInf.RsvFrameId equals rsvFrameMst.RsvFrameId into relatedRsvFrameMsts
                from relatedRsvFrameMst in relatedRsvFrameMsts.DefaultIfEmpty()
                join lockInf in lockInfs on raiinInf.RaiinNo equals lockInf.RaiinNo into relatedLockInfs
                from relatedLockInf in relatedLockInfs.DefaultIfEmpty()
                join uketukeSbtMst in uketukeSbtMsts on raiinInf.UketukeSbt equals uketukeSbtMst.KbnId into relatedUketukeSbtMsts
                from relatedUketukeSbtMst in relatedUketukeSbtMsts.DefaultIfEmpty()
                join tanto in userMsts on
                    new { raiinInf.HpId, UserId = raiinInf.TantoId } equals
                    new { tanto.HpId, tanto.UserId } into relatedTantos
                from relatedTanto in relatedTantos.DefaultIfEmpty()
                join primaryDoctor in userMsts on
                    new { raiinInf.HpId, UserId = ptInf.PrimaryDoctor } equals
                    new { primaryDoctor.HpId, primaryDoctor.UserId } into relatedPrimaryDoctors
                from relatedPrimaryDoctor in relatedPrimaryDoctors.DefaultIfEmpty()
                join kaMst in kaMsts on
                    new { raiinInf.HpId, raiinInf.KaId } equals
                    new { kaMst.HpId, kaMst.KaId } into relatedKaMsts
                from relatedKaMst in relatedKaMsts.DefaultIfEmpty()
                join ptHokenPattern in ptHokenPatterns on
                    new { raiinInf.HpId, raiinInf.PtId, raiinInf.HokenPid } equals
                    new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.HokenPid } into relatedPtHokenPatterns
                from relatedPtHokenPattern in relatedPtHokenPatterns.DefaultIfEmpty()
                from ptKohi1 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi1Id).Take(1).DefaultIfEmpty()
                from ptKohi2 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi2Id).Take(1).DefaultIfEmpty()
                from ptKohi3 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi3Id).Take(1).DefaultIfEmpty()
                from ptKohi4 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi4Id).Take(1).DefaultIfEmpty()
                select new
                {
                    raiinInf,
                    ptInf,
                    ptCmtInf,
                    primaryDoctorName = relatedPrimaryDoctor.Sname,
                    relatedUketukeSbtMst,
                    relatedTanto,
                    relatedKaMst,
                    relatedRaiinCmtInfComment,
                    relatedRaiinCmtInfRemark,
                    relatedRsvFrameMst,
                    relatedLockInf,
                    relatedPtHokenPattern,
                    ptKohi1,
                    ptKohi2,
                    ptKohi3,
                    ptKohi4,
                    raiinKbnDetails = (
                        from inf in raiinKbnInfs
                        join detail in raiinKbnDetails on
                            new { inf.HpId, inf.GrpId, inf.KbnCd } equals
                            new { detail.HpId, GrpId = detail.GrpCd, detail.KbnCd }
                        where inf.HpId == hpId
                            && inf.PtId == raiinInf.PtId
                            && inf.SinDate == sinDate
                            && inf.RaiinNo == raiinInf.RaiinNo
                        select detail
                    ).ToList(),
                    parentRaiinNo = (
                        from r in raiinInfs
                        where r.HpId == hpId
                            && r.PtId == raiinInf.PtId
                            && r.SinDate == raiinInf.SinDate
                            && r.OyaRaiinNo == raiinInf.OyaRaiinNo
                            && r.RaiinNo != r.OyaRaiinNo
                        select r.OyaRaiinNo
                    ).FirstOrDefault(),
                    lastVisitDate = (
                        from x in raiinInfs
                        where x.HpId == hpId
                            && x.PtId == raiinInf.PtId
                            && x.SinDate < sinDate
                        join k in karteEditions on
                            x.RaiinNo equals k.RaiinNo
                        orderby x.SinDate descending, x.RaiinNo descending
                        select x.SinDate
                    ).FirstOrDefault(),
                    firstVisitDate = (
                        from x in raiinInfs
                        where x.HpId == hpId
                            && x.PtId == raiinInf.PtId
                            && x.SinDate < sinDate
                            && x.SyosaisinKbn == SyosaiConst.Syosin
                        join k in karteEditions on
                            x.RaiinNo equals k.RaiinNo
                        orderby x.SinDate descending
                        select x.SinDate
                    ).FirstOrDefault()
                };

            var raiins = raiinQuery.AsEnumerable().DistinctBy(r => r.raiinInf.RaiinNo).ToList();
            var grpIds = NoTrackingDataContext.RaiinKbnMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None).Select(x => x.GrpCd).ToList();
            var models = raiins.Select(r => new ReceptionRowModel(
                r.raiinInf.HpId,
                r.raiinInf.RaiinNo,
                r.raiinInf.PtId,
                r.parentRaiinNo,
                r.raiinInf.UketukeNo,
                r.relatedLockInf is not null,
                r.raiinInf.Status,
                r.raiinInf.IsDeleted,
                r.ptInf.PtNum,
                r.ptInf.KanaName ?? string.Empty,
                r.ptInf.Name ?? string.Empty,
                r.ptInf.Sex,
                r.ptInf.Birthday,
                r.raiinInf.YoyakuTime ?? string.Empty,
                r.raiinInf.ConfirmationType,
                r.raiinInf.InfoConsFlg ?? string.Empty,
                r.relatedRsvFrameMst?.RsvFrameName ?? string.Empty,
                r.relatedUketukeSbtMst?.KbnId ?? CommonConstants.InvalidId,
                r.raiinInf.UketukeTime ?? string.Empty,
                r.raiinInf.SinStartTime ?? string.Empty,
                r.raiinInf.SinEndTime ?? string.Empty,
                r.raiinInf.KaikeiTime ?? string.Empty,
                r.relatedRaiinCmtInfComment?.Text ?? string.Empty,
                r.ptCmtInf?.Text ?? string.Empty,
                r.relatedTanto?.UserId ?? CommonConstants.InvalidId,
                string.IsNullOrEmpty(r.relatedTanto?.DrName) ? r.relatedTanto?.Name ?? string.Empty : r.relatedTanto?.DrName ?? string.Empty,
                r.relatedTanto?.KanaName ?? string.Empty,
                r.relatedKaMst?.KaId ?? CommonConstants.InvalidId,
                r.relatedKaMst?.KaName ?? string.Empty,
                r.lastVisitDate,
                r.firstVisitDate,
                r.primaryDoctorName ?? string.Empty,
                r.relatedRaiinCmtInfRemark?.Text ?? string.Empty,
                r.raiinInf.ConfirmationState,
                r.raiinInf.ConfirmationResult ?? string.Empty,
                grpIds,
                dynamicCells: r.raiinKbnDetails.Select(d => new DynamicCell(d.GrpCd, d.KbnCd, d.KbnName ?? string.Empty, d.ColorCd?.Length > 0 ? "#" + d.ColorCd : string.Empty)).ToList(),
                r.raiinInf.SinDate,
                // Fields needed to create Hoken name
                r.relatedPtHokenPattern?.HokenPid ?? CommonConstants.InvalidId,
                r.relatedPtHokenPattern?.StartDate ?? 0,
                r.relatedPtHokenPattern?.EndDate ?? 0,
                r.relatedPtHokenPattern?.HokenSbtCd ?? CommonConstants.InvalidId,
                r.relatedPtHokenPattern?.HokenKbn ?? CommonConstants.InvalidId,
                r.ptKohi1?.HokenSbtKbn ?? CommonConstants.InvalidId,
                r.ptKohi1?.Houbetu ?? string.Empty,
                r.ptKohi2?.HokenSbtKbn ?? CommonConstants.InvalidId,
                r.ptKohi2?.Houbetu ?? string.Empty,
                r.ptKohi3?.HokenSbtKbn ?? CommonConstants.InvalidId,
                r.ptKohi3?.Houbetu ?? string.Empty,
                r.ptKohi4?.HokenSbtKbn ?? CommonConstants.InvalidId,
                r.ptKohi4?.Houbetu ?? string.Empty
            )).ToList();

            foreach (var model in models)
            {
                var kanaName = model.KanaName?.Replace("　", " ") ?? "";
                var list = models
                    .Where(vs => vs.KanaName?.Replace("　", " ") == kanaName && vs.PtId != model.PtId && model.PtNum != vs.PtNum);
                if (!string.IsNullOrWhiteSpace(kanaName) && list != null && list.Any())
                {
                    model.IsNameDuplicate = true;
                }
                else
                {
                    model.IsNameDuplicate = false;
                }
            }

            return models;
        }

        public (bool resultSave, int oldStatus, int typeInvalid) UpdateStatus(int hpId, long raiinNo, int status, int userId, long ptId)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(
                () =>
                {
                    using var transaction = TrackingDataContext.Database.BeginTransaction();
                    try
                    {
                        int oldStatus = -1;
                        var validate = IsValidated(raiinNo, status, hpId);
                        if (!validate.isValid)
                        {
                            return (false, oldStatus, validate.typeInvalid);
                        }
                        var raiinInf = TrackingDataContext.RaiinInfs.Where(r =>
                            r.HpId == hpId
                            && r.RaiinNo == raiinNo
                            && r.IsDeleted == DeleteTypes.None).FirstOrDefault();
                        if (raiinInf is null)
                        {
                            return (false, oldStatus, 0);
                        }
                        oldStatus = raiinInf.Status;

                        if (status == RaiinState.Reservation)
                        {
                            switch (raiinInf.Status)
                            {
                                case RaiinState.Receptionist:
                                case RaiinState.Called:
                                case RaiinState.Consulting:
                                case RaiinState.ConsultationCompleted:
                                case RaiinState.AmountConfirmed:
                                    var isOdr = NoTrackingDataContext.KarteEditions.Any(e => e.HpId == hpId && e.RaiinNo == raiinNo && e.PtId == raiinInf.PtId && e.SinDate == raiinInf.SinDate && e.IsDeleted == DeleteTypes.None);
                                    raiinInf.UketukeNo = 0;
                                    raiinInf.UketukeTime = string.Empty;
                                    raiinInf.UketukeId = 0;
                                    raiinInf.SinStartTime = string.Empty;
                                    raiinInf.SinEndTime = string.Empty;
                                    if (!isOdr)
                                    {
                                        raiinInf.SyosaisinKbn = 2;
                                        raiinInf.JikanKbn = -1;
                                        raiinInf.HokenPid = 0;
                                    }
                                    DeleteCombineBill(raiinInf, userId);
                                    break;
                            }
                        }
                        raiinInf.Status = status;
                        raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        raiinInf.UpdateId = userId;

                        if (raiinInf.Status == RaiinState.Deleted)
                        {
                            _nextOrderRepository.ConvertTodayOrderToNextOrder(hpId, ptId, raiinNo, userId);
                            raiinInf.IsDeleted = DeleteTypes.Deleted;
                            raiinInf.OyaRaiinNo = raiinInf.RaiinNo;
                            DeleteCombineBill(raiinInf, userId);
                        }

                        TrackingDataContext.SaveChanges();

                        transaction.Commit();
                        return (true, oldStatus, 0);
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                });
        }

        private void DeleteCombineBill(RaiinInf raiinInf, int userId)
        {
            if (raiinInf.RaiinNo != raiinInf.OyaRaiinNo)
            {
                raiinInf.OyaRaiinNo = raiinInf.RaiinNo;
            }
            else
            {
                var raiinInfs = TrackingDataContext.RaiinInfs.Where(e => e.PtId == raiinInf.PtId && e.SinDate == raiinInf.SinDate && e.OyaRaiinNo == raiinInf.OyaRaiinNo && e.RaiinNo != raiinInf.RaiinNo).OrderBy(e => e.RaiinNo).ToList();
                if (raiinInfs.Count > 0)
                {
                    var firstRaiinNo = raiinInfs[0].RaiinNo;
                    foreach (var item in raiinInfs)
                    {
                        item.OyaRaiinNo = firstRaiinNo;
                        item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        item.UpdateId = userId;
                    }
                }
            }
        }
        public bool UpdateTreatmentDepartment(int hpId, long raiinNo, int treatmentDepartmentId, int userId)
        {
            var isValid = ValidateByStatus(hpId, raiinNo, userId);
            if (!isValid)
            {
                return false;
            }

            var kaId = _commonRepository.GetKaIdByTreatmentDepartmentId(hpId, treatmentDepartmentId);
            var resultUpdateTreatmentDepartment = Update(hpId, raiinNo, r => r.TreatmentDepartmentId = treatmentDepartmentId, userId);
            var resultUpdateKaId = Update(hpId, raiinNo, r => r.KaId = kaId, userId);

            return resultUpdateTreatmentDepartment && resultUpdateKaId;
        }
        public bool UpdateMonshinStatus(int hpId, long raiinNo, int monshinStatus, int userId)
        {
            return Update(hpId, raiinNo, r => r.MonshinStatus = monshinStatus, userId);
        }
        public bool UpdateUketukeNo(int hpId, long raiinNo, int uketukeNo, int userId)
        {
            return Update(hpId, raiinNo, r => r.UketukeNo = uketukeNo, userId);
        }

        public bool UpdateUketukeTime(int hpId, long raiinNo, string uketukeTime, int userId)
        {
            return Update(hpId, raiinNo, r => r.UketukeTime = uketukeTime, userId);
        }

        public bool UpdateSinStartTime(int hpId, long raiinNo, string sinStartTime, int userId)
        {
            return Update(hpId, raiinNo, r => r.SinStartTime = sinStartTime, userId);
        }

        public bool UpdateUketukeSbt(int hpId, long raiinNo, int uketukeSbt, int userId)
        {
            return Update(hpId, raiinNo, r => r.UketukeSbt = uketukeSbt, userId);
        }

        public bool UpdateTantoId(int hpId, long raiinNo, int tantoId, int userId)
        {
            var isValid = ValidateByStatus(hpId, raiinNo, userId);
            return isValid ? Update(hpId, raiinNo, r => r.TantoId = tantoId, userId) : isValid;
        }

        public bool UpdateKaId(int hpId, long raiinNo, int kaId, int userId)
        {
            return Update(hpId, raiinNo, r => r.KaId = kaId, userId);
        }

        public bool UpdateIsDeleted(int hpId, long raiinNo)
        {
            var resultValidate = IsValidated(raiinNo, 9, hpId);
            if (!resultValidate.isValid)
            {
                return false;
            }
            return Update(hpId, raiinNo, r => r.IsDeleted = 0);
        }

        public ReceptionModel GetReceptionComments(int hpId, long raiinNo)
        {
            var receptionComment = NoTrackingDataContext.RaiinCmtInfs
                .FirstOrDefault(x => x.HpId == hpId && x.RaiinNo == raiinNo && x.IsDelete == 0 && x.CmtKbn == 1);
            if (receptionComment is null)
                return new ReceptionModel();
            return new ReceptionModel(
                receptionComment.HpId,
                receptionComment.PtId,
                receptionComment.RaiinNo,
                receptionComment.Text ?? string.Empty
                );
        }

        public ReceptionModel GetReceptionVisiting(int hpId, long raiinNo)
        {
            var DataRaiinInf = NoTrackingDataContext.RaiinInfs
                .FirstOrDefault(x => x.HpId == hpId && x.RaiinNo == raiinNo);
            if (DataRaiinInf is null)
                return new ReceptionModel();
            return new ReceptionModel(
                DataRaiinInf.RaiinNo,
                DataRaiinInf.UketukeId,
                DataRaiinInf.KaId,
                DataRaiinInf.UketukeTime ?? string.Empty,
                DataRaiinInf.SinStartTime ?? string.Empty,
                DataRaiinInf.Status,
                DataRaiinInf.YoyakuId,
                DataRaiinInf.TantoId);
        }

        public ReceptionModel GetDataDefaultReception(int hpId, long ptId, int sinDate, int defaultSettingDoctor)
        {
            var tantoId = 0;
            var kaId = 0;
            // Tanto Id
            var mainDoctor = NoTrackingDataContext.PtInfs.FirstOrDefault(p => p.HpId == hpId && p.PtId == ptId && p.IsDelete != 1);
            if (mainDoctor != null)
            {
                var userMst = NoTrackingDataContext.UserMsts.FirstOrDefault(u => u.HpId == hpId && u.UserId == mainDoctor.PrimaryDoctor && (sinDate <= 0 || u.StartDate <= sinDate && u.EndDate >= sinDate));
                if (userMst?.JobCd == 1)
                {
                    tantoId = mainDoctor.PrimaryDoctor;
                }

                // if DefaultDoctorSetting = 1 get doctor from last visit
                if (defaultSettingDoctor == 1)
                {
                    var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                                     item.PtId == ptId &&
                                                                                     item.IsDeleted == DeleteTypes.None &&
                                                                                     (sinDate <= 0 || item.SinDate < sinDate))
                                                                      .OrderByDescending(item => item.SinDate)
                                                                      .ThenByDescending(item => item.RaiinNo);

                    var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                             item.PtId == ptId &&
                                                                                             item.IsDeleted == DeleteTypes.None &&
                                                                                             item.KarteStatus == KarteStatusConst.Official &&
                                                                                             item.SinDate < sinDate);

                    var lastRaiinInf = (from raiinInf in listRaiinInf
                                        join karteEdition in listKarteEdition on
                                            raiinInf.RaiinNo equals karteEdition.RaiinNo
                                        select raiinInf)
                                       .OrderByDescending(item => item.SinDate)
                                       .ThenByDescending(item => item.RaiinNo)
                                       .FirstOrDefault();

                    if (lastRaiinInf != null && lastRaiinInf.TantoId > 0)
                    {
                        tantoId = lastRaiinInf.TantoId;
                    }
                }

                // if DefaultDoctorSetting = 2 get doctor from last reception
                if (defaultSettingDoctor == 2)
                {
                    var lastRaiinInf = NoTrackingDataContext.RaiinInfs.Where(p => p.HpId == hpId &&
                                                           p.IsDeleted == DeleteTypes.None &&
                                                           p.SinDate <= sinDate)
                                                            .OrderByDescending(p => p.SinDate)
                                                            .ThenByDescending(p => p.RaiinNo).FirstOrDefault();
                    if (lastRaiinInf != null && lastRaiinInf.TantoId > 0)
                    {
                        tantoId = lastRaiinInf.TantoId;
                    }
                }
            }

            // KaId
            var getKaIdDefault = NoTrackingDataContext.UserMsts.FirstOrDefault(u => u.HpId == hpId && u.UserId == tantoId && u.IsDeleted == 0);
            if (getKaIdDefault != null)
            {
                kaId = getKaIdDefault.KaId;
            }
            return new ReceptionModel(tantoId, kaId);
        }

        public long InitDoctorCombobox(int userId, int tantoId, long ptId, int hpId, int sinDate)
        {
            var isDoctor = NoTrackingDataContext.UserMsts.Any(u => u.HpId == hpId && u.UserId == userId && u.IsDeleted == DeleteTypes.None && u.JobCd == 1);
            var doctors = NoTrackingDataContext.UserMsts.Where(p => p.HpId == hpId && p.StartDate <= sinDate && p.EndDate >= sinDate && p.JobCd == 1 && p.IsDeleted == DeleteTypes.None).OrderBy(p => p.SortNo).ToList();
            // if have only 1 doctor in user list
            if (doctors.Count == 1)
            {
                return doctors[0].UserId;
            }

            if (isDoctor)
            {
                return userId;
            }
            else
            {
                var mainDoctor = NoTrackingDataContext.PtInfs.FirstOrDefault(p => p.HpId == hpId && p.PtId == ptId && p.IsDelete != 1);

                if (mainDoctor != null)
                {
                    var userMst = NoTrackingDataContext.UserMsts.FirstOrDefault(u => u.HpId == hpId && u.UserId == mainDoctor.PrimaryDoctor && (sinDate <= 0 || u.StartDate <= sinDate && u.EndDate >= sinDate));
                    if (userMst?.JobCd == 1)
                    {
                        return mainDoctor.PrimaryDoctor;
                    }
                }
                var defaultDoctorSetting = NoTrackingDataContext.SystemConfs.FirstOrDefault(p =>
                        p.HpId == hpId && p.GrpCd == 1009 && p.GrpEdaNo == 0)?.Val ?? 0;

                // if DefaultDoctorSetting = 1 get doctor from last visit
                if (defaultDoctorSetting == 1)
                {
                    var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                                     item.PtId == ptId &&
                                                                                     item.IsDeleted == DeleteTypes.None &&
                                                                                     (sinDate <= 0 || item.SinDate < sinDate))
                                                                      .OrderByDescending(item => item.SinDate)
                                                                      .ThenByDescending(item => item.RaiinNo);

                    var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                             item.PtId == ptId &&
                                                                                             item.IsDeleted == DeleteTypes.None &&
                                                                                             item.KarteStatus == KarteStatusConst.Official &&
                                                                                             item.SinDate < sinDate);

                    var lastRaiinInf = (from raiinInf in listRaiinInf
                                        join karteEdition in listKarteEdition on
                                            raiinInf.RaiinNo equals karteEdition.RaiinNo
                                        select raiinInf)
                                       .OrderByDescending(item => item.SinDate)
                                       .ThenByDescending(item => item.RaiinNo)
                                       .FirstOrDefault();

                    if (lastRaiinInf != null && lastRaiinInf.TantoId > 0)
                    {
                        return lastRaiinInf.TantoId;
                    }
                }

                // if DefaultDoctorSetting = 2 get doctor from last reception
                if (defaultDoctorSetting == 2)
                {
                    var lastRaiinInf = NoTrackingDataContext.RaiinInfs
                            .Where(p => p.HpId == hpId &&
                                        p.PtId == ptId &&
                                        p.IsDeleted == DeleteTypes.None &&
                                        p.SinDate <= sinDate)
                            .OrderByDescending(p => p.SinDate)
                            .ThenByDescending(p => p.RaiinNo)
                            .FirstOrDefault();

                    if (lastRaiinInf != null && lastRaiinInf.TantoId > 0)
                    {
                        return lastRaiinInf.TantoId;
                    }
                }
            }

            return doctors.Count > 0 ? doctors[0].UserId : 0;
        }

        public int GetFirstVisitWithSyosin(int hpId, long ptId, int sinDate)
        {
            int firstDate = 0;

            var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                             item.PtId == ptId &&
                                                                             item.SinDate < sinDate &&
                                                                             item.SyosaisinKbn == SyosaiConst.Syosin &&
                                                                             item.IsDeleted == DeleteTypes.None)
                                                              .OrderByDescending(item => item.SinDate);

            var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                     item.PtId == ptId &&
                                                                                     item.IsDeleted == DeleteTypes.None &&
                                                                                     item.KarteStatus == KarteStatusConst.Official &&
                                                                                     item.SinDate < sinDate);

            var syosinBi = (from raiinInf in listRaiinInf
                            join karteEdition in listKarteEdition on
                                raiinInf.RaiinNo equals karteEdition.RaiinNo
                            select raiinInf)
                           .OrderByDescending(item => item.SinDate)
                           .FirstOrDefault();

            if (syosinBi != null)
            {
                firstDate = syosinBi.SinDate;
            }

            return firstDate;
        }

        public bool CheckExistRaiinNo(int hpId, long ptId, long raiinNo)
        {
            return NoTrackingDataContext.RaiinInfs.Any(item => item.HpId == hpId && item.PtId == ptId && item.RaiinNo == raiinNo);
        }

        /// <summary>
        /// Delete reception
        /// </summary>
        /// <param name="flag"></param>
        /// <param name="hpId"></param>
        /// <param name="ptId"></param>
        /// <param name="userId"></param>
        /// <param name="sinDate"></param>
        /// <param name="receptions"></param>
        /// Item1: RaiinNo
        /// Item2: OyaRaiinNo
        /// Item3: Status
        /// <returns></returns>
        /// Item1: SinDate
        /// Item2: RaiinNo
        /// Item3: ptId
        public List<Tuple<int, long, long>> Delete(bool flag, int hpId, long ptId, int userId, int sinDate, List<Tuple<long, long, int>> receptions)
        {
            if (flag)
            {
                var raiinNos = receptions.Select(r => r.Item1);
                raiinNos = raiinNos.Distinct().ToList();
                var raiinInfs = TrackingDataContext.RaiinInfs.Where(r => r.HpId == hpId && raiinNos.Contains(r.RaiinNo)).ToList();
                foreach (var raiinInf in raiinInfs)
                {
                    raiinInf.IsDeleted = DeleteTypes.Deleted;
                    raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    raiinInf.UpdateId = userId;
                }

                TrackingDataContext.SaveChanges();

                var result = raiinInfs.Select(r => new Tuple<int, long, long>(r.SinDate, r.RaiinNo, r.PtId)).ToList();

                return result;
            }
            else
            {
                List<Tuple<int, long, long>> result = new();
                foreach (var raiinNoAndOya in receptions)
                {
                    var deletedItem = DeleteKarute(hpId, ptId, raiinNoAndOya.Item1, raiinNoAndOya.Item2, raiinNoAndOya.Item3, sinDate, userId);
                    if (deletedItem.Item1 != 0 && deletedItem.Item2 != 0 && deletedItem.Item3 != 0)
                        result.Add(deletedItem);
                }

                return result;
            }
        }

        private Tuple<int, long, long> DeleteKarute(int hpId, long ptId, long raiinNo, long oyaRaiinNo, int status, int sinDate, int userId)
        {
            //delete raiinInf
            var raiinInf = TrackingDataContext.RaiinInfs.FirstOrDefault(r => r.HpId == hpId && r.PtId == ptId && r.RaiinNo == raiinNo && r.SinDate == sinDate);
            if (raiinInf == null) return new(0, 0, 0);

            var karteEdition = TrackingDataContext.KarteEditions
                .FirstOrDefault(item => item.HpId == hpId && item.PtId == ptId && item.RaiinNo == raiinNo && item.SinDate == sinDate &&
                                        item.KarteStatus == KarteStatusConst.Draft && item.IsDeleted == DeleteTypes.None);

            int deleteFlag = 1;
            if (status == RaiinState.Reservation || status == RaiinState.Receptionist || karteEdition != null)
            {
                deleteFlag = 2;
            }
            else if (status == RaiinState.ConsultationCompleted || status == RaiinState.AmountConfirmed || status == RaiinState.Paid || status == RaiinState.FcoWaiting)
            {
                deleteFlag = 1;
            }

            raiinInf.UpdateId = userId;
            raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
            raiinInf.IsDeleted = deleteFlag;

            // Update oyaRaiinNo of other raiinInf
            var listRaiinInf = TrackingDataContext.RaiinInfs.Where(r => r.HpId == hpId && r.OyaRaiinNo == raiinNo && r.RaiinNo != raiinNo && r.IsDeleted == DeleteTypes.None).ToList();
            if (listRaiinInf.Count > 0)
            {
                long minRaiinNo = listRaiinInf.Min(r => r.RaiinNo);
                listRaiinInf.ForEach((r) =>
                {
                    r.OyaRaiinNo = minRaiinNo;
                });
            }
            // No.6512 commit raiininf first for trigger
            TrackingDataContext.SaveChanges();

            // Delete reservation info
            var rsvInf = TrackingDataContext.RsvInfs.FirstOrDefault(r => r.HpId == hpId && r.RaiinNo == raiinNo && r.SinDate == sinDate && r.PtId == ptId);
            if (rsvInf != null) TrackingDataContext.RsvInfs.Remove(rsvInf);

            var rsvFrameInf = TrackingDataContext.RsvFrameInfs.FirstOrDefault(r => r.HpId == hpId && r.Number == raiinNo);
            if (rsvFrameInf != null) TrackingDataContext.RsvFrameInfs.Remove(rsvFrameInf);

            //delete order
            var odrInfs = TrackingDataContext.OdrInfs.Where(odr => odr.HpId == hpId
                                                                   && odr.PtId == ptId
                                                                   && odr.RaiinNo == raiinNo
                                                                   && odr.SinDate == sinDate);
            if (odrInfs != null)
            {
                var updateId = userId;
                var updateDate = CIUtil.GetJapanDateTimeNow();

                foreach (var odrInf in odrInfs)
                {
                    odrInf.IsDeleted = deleteFlag;
                    odrInf.UpdateId = updateId;
                    odrInf.UpdateDate = updateDate;
                }
            }

            //delete karte
            var karteInfs = NoTrackingDataContext.KarteInfs.Where(k => k.HpId == hpId
                                                                       && k.PtId == ptId
                                                                       && k.RaiinNo == raiinNo
                                                                       && k.SinDate == sinDate);
            if (karteInfs != null)
            {
                var updateId = userId;
                var updateDate = CIUtil.GetJapanDateTimeNow();

                foreach (var karteInf in karteInfs)
                {
                    karteInf.IsDeleted = deleteFlag;
                    karteInf.UpdateId = updateId;
                    karteInf.UpdateDate = updateDate;
                }
            }

            // Delete KENSA_INF,KENSA_INF_DETAIL
            var listKendaInf = TrackingDataContext.KensaInfs.Where(k => k.HpId == hpId
                                                                        && k.PtId == ptId
                                                                        && k.RaiinNo == raiinNo)
                                                            .ToList();
            listKendaInf.ForEach((k) =>
            {
                k.IsDeleted = DeleteTypes.Deleted;
                k.UpdateDate = CIUtil.GetJapanDateTimeNow();
                k.UpdateId = userId;
            });

            var listKendaInfDetail = TrackingDataContext.KensaInfs.Where(k => k.HpId == hpId
                                                                              && k.PtId == ptId
                                                                              && k.RaiinNo == raiinNo)
                                                                  .ToList();
            listKendaInfDetail.ForEach((k) =>
            {
                k.IsDeleted = DeleteTypes.Deleted;
                k.UpdateDate = CIUtil.GetJapanDateTimeNow();
                k.UpdateId = userId;
            });

            // Delete LIMIT_LIST_INF、LIMIT_CNT_LIST_INF
            var listLimitListInf = TrackingDataContext.LimitListInfs.Where(k => k.HpId == hpId
                                                                                && k.PtId == ptId
                                                                                && k.RaiinNo == raiinNo)
                                                                    .ToList();
            listLimitListInf.ForEach((k) =>
            {
                k.IsDeleted = DeleteTypes.Deleted;
                k.UpdateDate = CIUtil.GetJapanDateTimeNow();
                k.UpdateId = userId;
            });

            var listLimitCntListInf = TrackingDataContext.LimitCntListInfs.Where(k => k.HpId == hpId
                                                                                      && k.PtId == ptId
                                                                                      && k.OyaRaiinNo == oyaRaiinNo)
                                                                          .ToList();
            listLimitCntListInf.ForEach((k) =>
            {
                k.IsDeleted = DeleteTypes.Deleted;
                k.UpdateDate = CIUtil.GetJapanDateTimeNow();
                k.UpdateId = userId;
            });

            // Delete Monshin
            var listMonshinInf = TrackingDataContext.MonshinInfo.Where(m => m.HpId == hpId
                                                                            && m.PtId == ptId
                                                                            && m.RaiinNo == raiinNo
                                                                            && m.SinDate == sinDate)
                                                                .ToList();
            listMonshinInf.ForEach((m) =>
            {
                m.IsDeleted = DeleteTypes.Deleted;
                m.UpdateDate = CIUtil.GetJapanDateTimeNow();
                m.UpdateId = userId;
            });
            var result = new Tuple<int, long, long>(raiinInf.SinDate, raiinInf.RaiinNo, raiinInf.PtId);

            TrackingDataContext.SaveChanges();

            return result;
        }

        public ReceptionModel? GetLastKarute(int hpId, long ptNum)
        {
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(p => p.HpId == hpId && p.PtNum == ptNum && p.IsDelete == DeleteTypes.None);

            if (ptInf != null)
            {
                var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                                 item.PtId == ptInf.PtId &&
                                                                                 item.IsDeleted == DeleteTypes.None)
                                                                  .OrderByDescending(item => item.SinDate);

                var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                         item.PtId == ptInf.PtId &&
                                                                                         item.IsDeleted == DeleteTypes.None &&
                                                                                         item.KarteStatus == KarteStatusConst.Official);

                var raiinInf = (from raiinInfItem in listRaiinInf
                                join karteEdition in listKarteEdition on
                                    raiinInfItem.RaiinNo equals karteEdition.RaiinNo
                                select raiinInfItem)
                               .OrderByDescending(item => item.SinDate)
                               .FirstOrDefault();

                if (raiinInf != null)
                {
                    return new ReceptionModel(raiinInf.HpId,
                                              raiinInf.PtId,
                                              raiinInf.RaiinNo,
                                              raiinInf.SinDate);
                }
            }

            return null;
        }

        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        public List<ReceptionModel> GetListRaiinInf(int hpId, long ptId, int pageIndex, int pageSize, int isDeleted, bool isAll = false)
        {
            List<ReceptionModel> result = new();
            List<RaiinInf> raiinInfs;
            if (isAll)
            {
                raiinInfs = NoTrackingDataContext.RaiinInfs.Where(x => x.HpId == hpId &&
                                                        x.PtId == ptId && (x.IsDeleted == DeleteTypes.None || isDeleted == 1 || (x.IsDeleted != DeleteTypes.Confirm && isDeleted == 2)))
                                            .OrderByDescending(x => x.SinDate)
                                            .ToList();
            }
            else
            {
                raiinInfs = NoTrackingDataContext.RaiinInfs.Where(x => x.HpId == hpId &&
                                                           x.PtId == ptId && (x.IsDeleted == DeleteTypes.None || isDeleted == 1 || (x.IsDeleted != DeleteTypes.Confirm && isDeleted == 2)))
                                               .OrderByDescending(x => x.SinDate)
                                               .Skip((pageIndex - 1) * pageSize)
                                               .Take(pageSize)
                                               .ToList();
            }

            var tantoIdList = raiinInfs.Select(item => item.TantoId).Distinct().ToList();
            var kaIdIdList = raiinInfs.Select(item => item.KaId).Distinct().ToList();

            var userMsts = NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId &&
                                                                     x.IsDeleted == 0 &&
                                                                     tantoIdList.Contains(x.UserId))
                                                         .ToList();

            var kaMsts = NoTrackingDataContext.KaMsts.Where(x => x.HpId == hpId &&
                                                                x.IsDeleted == 0 &&
                                                                kaIdIdList.Contains(x.KaId))
                                                     .ToList();

            var ptHokenInfs = NoTrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId &&
                                                                           x.IsDeleted == 0 &&
                                                                           x.PtId == ptId)
                                                                .ToList();
            var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(x => x.HpId == hpId &&
                                                                                   x.IsDeleted == 0 &&
                                                                                   x.PtId == ptId)
                                                                       .ToList();

            foreach (var raiinInf in raiinInfs)
            {
                var kaMst = kaMsts.FirstOrDefault(item => item.KaId == raiinInf.KaId);
                var userMst = userMsts.FirstOrDefault(item => item.UserId == raiinInf.TantoId);
                var ptHokenPattern = ptHokenPatterns.FirstOrDefault(item => item.HokenPid == raiinInf.HokenPid);
                var ptHokenInf = ptHokenInfs.FirstOrDefault(item => ptHokenPattern != null && item.HokenId == ptHokenPattern.HokenId);
                var item = new ReceptionModel(
                            raiinInf.HpId,
                            raiinInf.PtId,
                            raiinInf.SinDate,
                            raiinInf.UketukeNo,
                            raiinInf.Status,
                            kaMst?.KaSname ?? string.Empty,
                            userMst?.Sname ?? string.Empty,
                            ptHokenInf?.Houbetu ?? string.Empty,
                            ptHokenInf?.HokensyaNo ?? string.Empty,
                            ptHokenInf?.HokenKbn ?? 0,
                            ptHokenInf?.HokenId ?? 0,
                            raiinInf.HokenPid,
                            raiinInf.RaiinNo,
                            raiinInf.IsDeleted == 1);
                result.Add(item);
            }

            return result;
        }

        public List<ReceptionModel> GetRaiinListWithKanInf(int hpId, long ptId)
        {
            List<ReceptionModel> result = new();
            var raiinInfList = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                             item.IsDeleted == DeleteTypes.None &&
                                                                             item.PtId == ptId
                                                               ).OrderByDescending(p => p.SinDate)
                                                               .ToList();

            var kaIdList = raiinInfList.Select(item => item.KaId).Distinct().ToList();
            var tantoIdList = raiinInfList.Select(item => item.TantoId).Distinct().ToList();
            var hokenPIdList = raiinInfList.Select(item => item.HokenPid).Distinct().ToList();

            var kaMstList = NoTrackingDataContext.KaMsts.Where(item => item.HpId == hpId &&
                                                                       item.IsDeleted == 0 &&
                                                                       kaIdList.Contains(item.KaId)
                                                        ).ToList();

            var userMstList = NoTrackingDataContext.UserMsts.Where(item => item.HpId == hpId &&
                                                                           item.IsDeleted == 0 &&
                                                                           tantoIdList.Contains(item.UserId)
                                                            ).ToList();

            var ptHokenPatternList = NoTrackingDataContext.PtHokenPatterns.Where(item => item.HpId == hpId &&
                                                                                         item.IsDeleted == 0 &&
                                                                                         item.PtId == ptId &&
                                                                                         hokenPIdList.Contains(item.HokenPid)
                                                                          ).ToList();

            var hokenIdList = ptHokenPatternList.Select(item => item.HokenId).Distinct().ToList();

            var ptHokenInfList = NoTrackingDataContext.PtHokenInfs.Where(item => item.HpId == hpId &&
                                                                                 item.IsDeleted == 0 &&
                                                                                 item.PtId == ptId &&
                                                                                 hokenIdList.Contains(item.HokenId)
                                                                  ).ToList();

            foreach (var raiinInf in raiinInfList)
            {
                var kaSName = kaMstList.FirstOrDefault(item => item.KaId == raiinInf.KaId)?.KaSname ?? string.Empty;
                var sName = userMstList.FirstOrDefault(item => item.UserId == raiinInf.TantoId)?.Sname ?? string.Empty;
                var ptHokenPattern = ptHokenPatternList.FirstOrDefault(item => item.HokenPid == raiinInf.HokenPid);
                var ptHokenInf = ptHokenInfList.FirstOrDefault(item => item.HokenId == ptHokenPattern?.HokenId);
                var hokenKbnName = GetHokenKbnName(ptHokenPattern, ptHokenInf);
                result.Add(new ReceptionModel(
                               raiinInf.PtId,
                               raiinInf.SinDate,
                               raiinInf.RaiinNo,
                               raiinInf.TantoId,
                               raiinInf.KaId,
                               sName,
                               kaSName,
                               hokenKbnName));
            }
            return result;
        }

        private string GetHokenKbnName(PtHokenPattern? ptHokenPattern, PtHokenInf? ptHokenInf)
        {
            string result = string.Empty;
            if (ptHokenPattern == null || ptHokenPattern.PtId == 0 && ptHokenPattern.HokenPid == 0 && ptHokenPattern.HpId == 0)
            {
                return string.Empty;
            }

            if (ptHokenInf == null)
            {
                result = "公費";
                return result;
            }

            if (ptHokenInf.Houbetu == HokenConstant.HOUBETU_NASHI)
            {
                result = "公費";
                return result;
            }

            string hokensyaNo = ptHokenInf.HokensyaNo ?? string.Empty;
            switch (ptHokenInf.HokenKbn)
            {
                case 0:
                    result = "自費";
                    break;
                case 1:
                    result = "社保";
                    break;
                case 2:
                    if (hokensyaNo.Length == 8 &&
                        hokensyaNo.StartsWith("39"))
                    {
                        result = "後期";
                    }
                    else if (hokensyaNo.Length == 8 &&
                        hokensyaNo.StartsWith("67"))
                    {
                        result = "退職";
                    }
                    else
                    {
                        result = "国保";
                    }
                    break;
                case 11:
                case 12:
                case 13:
                    result = "労災";
                    break;
                case 14:
                    result = "自賠";
                    break;
            }
            return result;
        }

        public List<RaiinInfToPrintModel> GetOutDrugOrderList(int hpId, int fromDate, int toDate)
        {
            var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                             item.IsDeleted == DeleteTypes.None &&
                                                                             item.SinDate >= fromDate &&
                                                                             item.SinDate <= toDate);

            var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                     item.IsDeleted == DeleteTypes.None &&
                                                                                     item.KarteStatus == KarteStatusConst.Official &&
                                                                                     item.SinDate >= fromDate &&
                                                                                     item.SinDate <= toDate);

            var raiinInfList = (from raiinInf in listRaiinInf
                                join karteEdition in listKarteEdition on
                                    raiinInf.RaiinNo equals karteEdition.RaiinNo
                                select raiinInf)
                               .ToList();

            var raiinNoList = raiinInfList.Select(item => item.RaiinNo).Distinct().ToList();
            var ptIdList = raiinInfList.Select(item => item.PtId).Distinct().ToList();
            var kaIdList = raiinInfList.Select(item => item.KaId).Distinct().ToList();
            var tantoIdList = raiinInfList.Select(item => item.TantoId).Distinct().ToList();
            var uketukeSbtList = raiinInfList.Select(item => item.UketukeSbt).Distinct().ToList();
            var hokenPidList = raiinInfList.Select(item => item.HokenPid).Distinct().ToList();

            var ordInfList = NoTrackingDataContext.OdrInfs.Where(item => item.HpId == hpId
                                                                         && item.SinDate >= fromDate
                                                                         && item.SinDate <= toDate
                                                                         && item.IsDeleted == 0
                                                                         && raiinNoList.Contains(item.RaiinNo)
                                                                         && item.InoutKbn == 1// コメント（処方箋備考）
                                                                         && ((item.OdrKouiKbn >= 20 && item.OdrKouiKbn <= 29) // 処方
                                                                              || item.OdrKouiKbn == 100 // コメント（処方箋）
                                                                              || item.OdrKouiKbn == 101))
                                                          .GroupBy(item => new { item.RaiinNo })
                                                          .Select(item => item.FirstOrDefault())
                                                          .ToList();

            var ptInfList = NoTrackingDataContext.PtInfs.Where(item => item.HpId == hpId
                                                                       && item.IsDelete == 0
                                                                       && ptIdList.Contains(item.PtId))
                                                        .ToList();

            var kaMstList = NoTrackingDataContext.KaMsts.Where(item => item.HpId == hpId
                                                                       && item.IsDeleted == 0
                                                                       && kaIdList.Contains(item.KaId))
                                                        .ToList();

            var userMstList = NoTrackingDataContext.UserMsts.Where(item => item.HpId == hpId
                                                                           && item.IsDeleted == 0
                                                                           && item.StartDate <= fromDate
                                                                           && toDate <= item.EndDate
                                                                           && tantoIdList.Contains(item.UserId))
                                                            .ToList();

            var uketsukeSbtMstList = NoTrackingDataContext.UketukeSbtMsts.Where(item => item.HpId == hpId
                                                                                        && item.IsDeleted == 0
                                                                                        && uketukeSbtList.Contains(item.KbnId));

            #region Get HokenPatternName
            var ptHokenPatternList = NoTrackingDataContext.PtHokenPatterns.Where(item => item.HpId == hpId
                                                                                         && item.IsDeleted == 0
                                                                                         && hokenPidList.Contains(item.HokenPid)
                                                                                         && ptIdList.Contains(item.PtId))
                                                                          .ToList();

            var hokenIdList = ptHokenPatternList.Select(item => item.HokenId).Distinct().ToList();

            var ptHokenInfList = NoTrackingDataContext.PtHokenInfs.Where(item => item.HpId == hpId
                                                                                 && item.IsDeleted == 0
                                                                                 && ptIdList.Contains(item.PtId)
                                                                                 && hokenIdList.Contains(item.HokenId))
                                                                  .ToList();

            var ptHokenPatternResult = (from ptHokenPattern in ptHokenPatternList
                                        join ptHokenInf in ptHokenInfList on
                                            new { ptHokenPattern.PtId, ptHokenPattern.HokenId } equals
                                            new { ptHokenInf.PtId, ptHokenInf.HokenId } into ptHokenInf1List
                                        from ptHokenInfItem in ptHokenInf1List.DefaultIfEmpty()
                                        select new
                                        {
                                            ptHokenPattern.HokenPid,
                                            ptHokenPattern.PtId,
                                            HokenHobetu = ptHokenInfItem == null ? "" : ptHokenInfItem.Houbetu,
                                            HokensyaNo = ptHokenInfItem == null ? "" : ptHokenInfItem.HokensyaNo,
                                            PtHokenPattern = ptHokenPattern,
                                            HokenInfHokenId = ptHokenInfItem == null ? 0 : ptHokenInfItem.HokenId,
                                            HokenInfStartDate = ptHokenInfItem == null ? 0 : ptHokenInfItem.StartDate,
                                            HokenInfEndDate = ptHokenInfItem == null ? 0 : ptHokenInfItem.EndDate,
                                        }).ToList();

            #endregion

            var query = from odr in ordInfList
                        join raiin in raiinInfList
                            on new { odr.HpId, odr.PtId, odr.SinDate, odr.RaiinNo }
                            equals new { raiin.HpId, raiin.PtId, raiin.SinDate, raiin.RaiinNo }
                        join pt in ptInfList
                            on new { raiin.PtId }
                            equals new { pt.PtId } into ptLeft
                        from pt in ptLeft
                        join ka in kaMstList
                            on new { raiin.KaId }
                            equals new { ka.KaId } into kaLeft
                        from ka in kaLeft
                        join user in userMstList
                             on new { raiin.TantoId }
                             equals new { TantoId = user.UserId } into userLeft
                        from user in userLeft.DefaultIfEmpty()
                        join uketsuke in uketsukeSbtMstList
                            on new { raiin.UketukeSbt }
                            equals new { UketukeSbt = uketsuke.KbnId } into uketsukeLeft
                        from uketsuke in uketsukeLeft.DefaultIfEmpty()
                        join hokenPattern in ptHokenPatternResult
                            on new { raiin.PtId, raiin.HokenPid, }
                            equals new { hokenPattern.PtId, hokenPattern.HokenPid } into PtHokenPatternLeft
                        from hokenPattern in PtHokenPatternLeft.DefaultIfEmpty()
                        select new
                        {
                            Raiin = raiin,
                            Pt = pt,
                            Ka = ka,
                            User = user,
                            Uketsuke = uketsuke,
                            PtHokenPatternItem = hokenPattern
                        };
            var result = query.Select(data => new RaiinInfToPrintModel(PrintMode.PrintPrescription,
                                                                       data.Pt?.Name ?? string.Empty,
                                                                       data.User?.Name ?? string.Empty,
                                                                       0,
                                                                       data.Raiin?.KaId ?? 0,
                                                                       data.Pt?.PtId ?? 0,
                                                                       data.Pt?.PtNum ?? 0,
                                                                       data.PtHokenPatternItem?.HokenHobetu ?? string.Empty,
                                                                       data.PtHokenPatternItem?.PtHokenPattern.HokenKbn ?? 0,
                                                                       string.Empty,
                                                                       data.PtHokenPatternItem?.HokensyaNo ?? string.Empty,
                                                                       data.Raiin?.UketukeNo ?? 0,
                                                                       0,
                                                                        data.Raiin?.SinDate ?? 0,
                                                                       0,
                                                                       data.Raiin?.TantoId ?? 0,
                                                                       data.Ka?.KaName ?? string.Empty,
                                                                       data.Raiin?.UketukeSbt ?? 0,
                                                                       data.Uketsuke?.KbnName ?? string.Empty,
                                                                       0,
                                                                       data.Raiin?.HokenPid ?? 0,
                                                                       0,
                                                                       -1,
                                                                       -1,
                                                                       string.Empty,
                                                                       string.Empty,
                                                                       0, 0, 0, 0, 0, 0, 0, 0, string.Empty, string.Empty, string.Empty, string.Empty,
                                                                       data.Raiin?.Status ?? RaiinState.Reservation, data.Raiin?.RaiinNo ?? 0))
                          .ToList();

            return result;
        }

        public int GetStatusRaiinInf(int hpId, long raiinNo, long ptId)
        {
            var raiinInf = NoTrackingDataContext.RaiinInfs.FirstOrDefault(item => item.HpId == hpId
                                                                                  && item.PtId == ptId
                                                                                  && item.RaiinNo == raiinNo);
            if (raiinInf == null)
            {
                return 0;
            }
            return raiinInf.Status;
        }

        public ReceptionModel GetRaiinInfBySinDate(int hpId, long ptId, int sinDate)
        {
            var raiinInfList = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId
                                                                             && item.SinDate == sinDate
                                                                             && item.PtId == ptId
                                                                             && item.IsDeleted == DeleteTypes.None)
                                                              .ToList();
            if (!raiinInfList.Any())
            {
                return new();
            }

            RaiinInf raiinInf;

            if (raiinInfList.Any(item => item.Status == RaiinState.Reservation))
            {
                raiinInf = raiinInfList.OrderByDescending(item => item.IsYoyaku).ThenBy(item => item.YoyakuTime).ThenBy(item => item.RaiinNo).First();
            }
            else
            {
                raiinInf = raiinInfList.OrderBy(item => item.UketukeTime).ThenBy(item => item.RaiinNo).First();
            }

            return new ReceptionModel(raiinInf.HpId,
                                      raiinInf.PtId,
                                      raiinInf.SinDate,
                                      raiinInf.RaiinNo,
                                      raiinInf.OyaRaiinNo,
                                      raiinInf.HokenPid,
                                      raiinInf.SanteiKbn,
                                      raiinInf.Status,
                                      raiinInf.IsYoyaku,
                                      raiinInf.YoyakuTime ?? string.Empty,
                                      raiinInf.YoyakuId,
                                      raiinInf.UketukeSbt,
                                      raiinInf.UketukeTime ?? string.Empty,
                                      raiinInf.UketukeId,
                                      raiinInf.UketukeNo,
                                      raiinInf.SinStartTime ?? string.Empty,
                                      raiinInf.SinEndTime ?? string.Empty,
                                      raiinInf.KaikeiTime ?? string.Empty,
                                      raiinInf.KaikeiId,
                                      raiinInf.KaId,
                                      raiinInf.TantoId,
                                      raiinInf.SyosaisinKbn,
                                      raiinInf.JikanKbn,
                                      string.Empty
                               );
        }

        public int GetNextUketukeNoBySetting(int hpId, int sindate, int infKbn, int kaId, int uketukeMode, int defaultUkeNo)
        {
            var raiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId
                                                                         && item.SinDate == sindate
                                                                         && (!(uketukeMode == 1 || uketukeMode == 3) || item.UketukeSbt == infKbn)
                                                                         && (!(uketukeMode == 2 || uketukeMode == 3) || item.KaId == kaId)
                                                                         && item.IsDeleted == DeleteTypes.None
                                                         ).OrderByDescending(p => p.UketukeNo)
                                                          .FirstOrDefault();
            if (raiinInf != null)
            {
                return raiinInf.UketukeNo + 1 < defaultUkeNo ? defaultUkeNo : raiinInf.UketukeNo + 1;
            }
            return defaultUkeNo > 0 ? defaultUkeNo : 1;
        }

        public RaiinInfModel? GetRaiinInf(int hpId, long ptId, int sinDate, long raiinNo)
        {
            var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate &&
                p.RaiinNo == raiinNo &&
                p.IsDeleted == DeleteTypes.None
            );

            var kaMsts = NoTrackingDataContext.KaMsts.Where(p =>
                p.HpId == hpId &&
                p.IsDeleted == DeleteStatus.None
            );

            var userMsts = NoTrackingDataContext.UserMsts.Where(p =>
                p.HpId == hpId &&
                p.IsDeleted == DeleteStatus.None
            );

            var join = (
                from raiinInf in raiinInfs
                join kaMst in kaMsts on
                    new { raiinInf.HpId, raiinInf.KaId } equals
                    new { kaMst.HpId, kaMst.KaId } into joinKaMsts
                from joinKaMst in joinKaMsts.DefaultIfEmpty()
                join userMst in userMsts on
                    new { raiinInf.HpId, raiinInf.TantoId } equals
                    new { userMst.HpId, TantoId = userMst.UserId } into joinUserMsts
                from joinUserMst in joinUserMsts.DefaultIfEmpty()
                select new
                {
                    raiinInf,
                    joinKaMst,
                    joinUserMst
                }
                ).ToList();

            RaiinInfModel? result = null;

            if (join != null && join.Any())
            {
                //return RaiinInf with User and Ka info
                result = new RaiinInfModel(join.First().raiinInf.PtId,
                                           join.First().raiinInf.SinDate,
                                           join.First().raiinInf.RaiinNo,
                                           join.First().raiinInf.KaId,
                                           join.First().joinKaMst != null ? join.First().joinKaMst.KaName ?? string.Empty : string.Empty,
                                           join.First().raiinInf.TantoId,
                                           join.First().joinUserMst != null ? join.First().joinUserMst.DrName ?? string.Empty : string.Empty,
                                           join.First().joinUserMst != null ? join.First().joinUserMst.Name ?? string.Empty : string.Empty,
                                           join.First().joinUserMst != null ? join.First().joinUserMst.KanaName ?? string.Empty : string.Empty,
                                           join.First().joinKaMst != null ? join.First().joinKaMst.KaSname ?? string.Empty : string.Empty
                                        );
            }

            return result;
        }

        #region Reception New

        public List<ReceptionForViewDto> GetRecptionList(int hpId, int sinDate, long raiinNo = CommonConstants.InvalidId, int limit = RaiinInfConst.DefaultLimit, int offset = RaiinInfConst.DefaultOffset, string sortField = RaiinInfConst.DefaultSortField, int? filterId = null, List<int> kaIds = null, List<int> treatmentDepartmentIds = null, List<int> userIds = null, List<int> treatmentStatusIds = null, List<int> labels = null, long ptId = CommonConstants.InvalidId)
        {
            var listRaiinNo = new List<long> { raiinNo };
            return GetReceptionRowModels(hpId, sinDate, filterId, kaIds, treatmentDepartmentIds, userIds, treatmentStatusIds, limit, offset, sortField, labels, listRaiinNo, ptId);
        }

        public List<ReceptionForViewDto> GetReceptionListByRaiinNoes(int hpId, int sinDate, List<long> listRaiinNo, int limit = RaiinInfConst.DefaultLimit, int offset = RaiinInfConst.DefaultOffset, string sortField = RaiinInfConst.DefaultSortField, int? filterId = null, List<int> kaIds = null, List<int> treatmentDepartmentIds = null, List<int> userIds = null, List<int> treatmentStatusIds = null, List<int> labels = null, long ptId = CommonConstants.InvalidId)
        {
            return GetReceptionRowModels(hpId, sinDate, filterId, kaIds, treatmentDepartmentIds, userIds, treatmentStatusIds, limit, offset, sortField, labels, listRaiinNo, ptId);
        }

        public List<ReceptionForViewDto> GetReceptionRowModels(int hpId, int sinDate, int? filterId, List<int> kaIds, List<int> treatmentDepartmentIds, List<int> userIds, List<int> treatmentStatusIds, int limit, int offset, string sortField, List<int> labels, List<long> listRaiinNo, long ptId = CommonConstants.InvalidId)
        {

            if (filterId != null)
            {
                var kaFilter = NoTrackingDataContext.RaiinFilterKas.Where(e => e.FilterId == filterId && e.HpId == hpId && e.IsDeleted == DeleteTypes.None).Select(b => b.KaId).ToList();
                var departmentFilter = NoTrackingDataContext.RaiinFilterTreatmentDepartments.Where(e => e.FilterId == filterId && e.IsDeleted == DeleteTypes.None).Select(b => b.TreatmentDepartmentId).ToList();
                var userFilter = NoTrackingDataContext.RaiinFilterUsers.Where(e => e.FilterId == filterId && e.HpId == hpId && e.IsDeleted == DeleteTypes.None).Select(b => b.UserId).ToList();
                kaIds = kaIds ?? new List<int>();
                treatmentDepartmentIds = treatmentDepartmentIds ?? new List<int>();
                userIds = userIds ?? new List<int>();
                kaIds.AddRange(kaFilter);
                treatmentDepartmentIds.AddRange(departmentFilter);
                userIds.AddRange(userFilter);
            }

            // 1. Prepare all the necessary collections for the join operation
            // Raiin (Reception)
            var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(x => x.HpId == hpId &&
                                                                       (sinDate == 0 || x.SinDate == sinDate) &&
                                                                       (kaIds == null || kaIds.Any(b => b == x.KaId)) &&
                                                                       (treatmentDepartmentIds == null || treatmentDepartmentIds.Any(b => b == x.TreatmentDepartmentId)) &&
                                                                       (userIds == null || userIds.Any(b => b == x.TantoId)) &&
                                                                       (treatmentStatusIds == null || treatmentStatusIds.Any(b => b == x.StatusMstKbn)) &&
                                                                       (ptId == CommonConstants.InvalidId || x.PtId == ptId)
                                                                  );
            var raiinCmtInfs = NoTrackingDataContext.RaiinCmtInfs.Where(x => x.HpId == hpId && x.IsDelete == DeleteTypes.None);
            var raiinKbnMsts = NoTrackingDataContext.RaiinKbnMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None).OrderBy(e => e.SortNo);
            var raiinKbnInfs = NoTrackingDataContext.RaiinKbnInfs.Where(x => x.HpId == hpId && x.IsDelete == DeleteTypes.None);

            var ptInfs = NoTrackingDataContext.PtInfs.Where(x => x.HpId == hpId);
            var ptCmtInfs = NoTrackingDataContext.PatientMemos.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);

            var raiinStatusMsts = NoTrackingDataContext.RaiinStatusMsts.Where(e => e.HpId == hpId && e.IsDeleted == DeleteTypes.None);
            var onlineConfirmHistories = NoTrackingDataContext.OnlineConfirmationHistories.Where(x => x.HpId == hpId);
            var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            var ptKohis = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            var ptHokenInf = NoTrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);

            var orderInfs = NoTrackingDataContext.OdrInfs.Where(x => x.HpId == hpId && x.SinDate == sinDate && x.IsDeleted == DeleteTypes.None);
            // User (Doctor)
            var userMsts = NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // Ka (Department)
            var kaMsts = NoTrackingDataContext.KaMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None);
            // Lock (Function lock)
            //var lockInfs = NoTrackingDataContext.LockInfs.Where(x => x.HpId == hpId &&
            //    x.FunctionCd == FunctionCode.MedicalExaminationCode || x.FunctionCd == FunctionCode.TeamKarte || x.FunctionCd == FunctionCode.SwitchOrderCode);

            var reserveDetails = NoTrackingDataContext.ReserveDetails.Where(e => e.IsDeleted == DeleteTypes.None);
            var meetings = NoTrackingDataContext.Meetings.Where(e => e.IsDeleted == DeleteTypes.None && e.HospitalId == hpId);
            var karteEdition = NoTrackingDataContext.KarteEditions.Where(r => r.IsDeleted == DeleteTypes.None);

            var raiinFilter = raiinInfs;
            var raiinKbnInfsFilter = raiinKbnInfs;
            if (labels != null)
            {
                raiinKbnInfsFilter = raiinKbnInfs.Where(x => labels.Any(b => b == x.GrpId));
                raiinFilter = raiinInfs.Where(e => raiinKbnInfsFilter.Any(x => x.RaiinNo == e.RaiinNo));
            }
            if (listRaiinNo != null && listRaiinNo.Count > 0 && !listRaiinNo.Contains(CommonConstants.InvalidId))
            {
                raiinFilter = raiinFilter.Where(e => listRaiinNo.Contains(e.RaiinNo));
            }

            var date = DateTime.MinValue;
            // 2. Perform the join operation
            var raiinQuery =
                from raiinInf in raiinFilter
                join ptInf in ptInfs on
                    new { raiinInf.HpId, raiinInf.PtId } equals
                    new { ptInf.HpId, ptInf.PtId }
                join raiinCmtInfRemark in raiinCmtInfs.Where(x => x.CmtKbn == CmtKbns.Comment) on
                   new { raiinInf.HpId, raiinInf.PtId, raiinInf.SinDate, raiinInf.RaiinNo } equals
                   new { raiinCmtInfRemark.HpId, raiinCmtInfRemark.PtId, raiinCmtInfRemark.SinDate, raiinCmtInfRemark.RaiinNo } into relatedRaiinCmtInfRemarks
                from relatedRaiinCmtInfRemark in relatedRaiinCmtInfRemarks.DefaultIfEmpty()
                join tanto in userMsts on
                    new { raiinInf.HpId, UserId = raiinInf.TantoId } equals
                    new { tanto.HpId, tanto.UserId } into relatedTantos
                from relatedTanto in relatedTantos.DefaultIfEmpty()

                join ka in kaMsts on
                    new { raiinInf.HpId, raiinInf.KaId } equals
                    new { ka.HpId, ka.KaId } into relatedKas
                from relatedKa in relatedKas.DefaultIfEmpty()

                join reserveDetail in reserveDetails
                on raiinInf.ReserveDetailId equals reserveDetail.ReserveDetailId into relatedReserveDetails
                from relatedReserveDetail in relatedReserveDetails.DefaultIfEmpty()

                join onlineConfirmHistory in onlineConfirmHistories
                on (long)raiinInf.OnlineConfirmationId equals onlineConfirmHistory.ID
                into relatedOnlineConfirmHistories
                from relatedOnlineConfirmHistory in relatedOnlineConfirmHistories.DefaultIfEmpty()
                join ptMemo in ptCmtInfs on
                    new { ptInf.HpId, ptInf.PtId } equals
                    new { ptMemo.HpId, ptMemo.PtId } into relatedCmtLeftjoin
                from ptMemo in relatedCmtLeftjoin.DefaultIfEmpty()
                join ptHokenPattern in ptHokenPatterns on
                    new { raiinInf.HpId, raiinInf.PtId, raiinInf.HokenPid } equals
                    new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.HokenPid } into relatedPtHokenPatterns
                from relatedPtHokenPattern in relatedPtHokenPatterns.DefaultIfEmpty()
                from ptKohi1 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi1Id).Take(1).DefaultIfEmpty()
                from ptKohi2 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi2Id).Take(1).DefaultIfEmpty()
                from ptKohi3 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi3Id).Take(1).DefaultIfEmpty()
                from ptKohi4 in ptKohis.Where(x => x.PtId == ptInf.PtId && x.HokenId == relatedPtHokenPattern.Kohi4Id).Take(1).DefaultIfEmpty()
                from raiinStatusMst in raiinStatusMsts.Where(x => x.HpId == raiinInf.HpId && x.StatusKbn == raiinInf.Status).Take(1).DefaultIfEmpty()
                from meeting in meetings.Where(x => x.ReserveId == relatedReserveDetail.ReserveId && x.PatientId == relatedReserveDetail.PatientId && relatedReserveDetail.ReserveType == 1).Take(1).DefaultIfEmpty()

                select new
                {
                    raiinInf.HpId,
                    raiinInf.UketukeTime,
                    UketukeNo = raiinInf.UketukeNo,
                    raiinInf.YoyakuTime,
                    ptInf.PtNum,
                    ptInf.KanaName,
                    ptInf.Name,
                    raiinInf.Status,
                    raiinInf.PtId,
                    raiinInf.SinDate,
                    raiinInf.IsDeleted,
                    raiinInf.RaiinNo,
                    raiinInf.YoyakuEndTime,
                    ptInf.Sex,
                    ptInf.Birthday,
                    ptInf.PortalCustomerId,
                    raiinInf.TreatmentDepartmentId,
                    raiinInf.KaId,
                    relatedKa.KaName,
                    raiinInf.TantoId,
                    tantoKana = relatedTanto.KanaName,
                    raiinInf.MonshinStatus,
                    relatedRaiinCmtInfRemark.Text,
                    ptMemo.MemoHtml,
                    PortalPtId = 1,
                    raiinInf.StatusMstKbn,
                    raiinInf.StatusKbnUpdateTime,
                    SortNoStatusMst = raiinStatusMst.SortNo,
                    reserveType = relatedReserveDetail == null ? 0 : relatedReserveDetail.ReserveType,
                    reverseId = relatedReserveDetail == null ? 0 : relatedReserveDetail.ReserveId,
                    reverseDetailId = relatedReserveDetail == null ? 0 : relatedReserveDetail.ReserveDetailId,
                    meetingStatus = meeting == null ? 0 : meeting.Status,
                    hasUnread = 1,
                    labels = (from inf in raiinKbnInfs
                              join mst in raiinKbnMsts on
                                  new { inf.HpId, inf.GrpId } equals
                                  new { mst.HpId, GrpId = mst.GrpCd }
                              where inf.HpId == hpId
                                  && inf.PtId == raiinInf.PtId
                                  && inf.SinDate == sinDate
                                  && inf.RaiinNo == raiinInf.RaiinNo
                              select new Label(inf.GrpId, mst.GrpName, mst.ColorCode, mst.SortNo, mst.HpId)
                                ).ToList(),
                    raiinInf.OnlineConfirmationId,
                    OnlineConfirmationDate = relatedOnlineConfirmHistory == null ? date : relatedOnlineConfirmHistory.OnlineConfirmationDate,
                    //HokenName = "国保 54+21",
                    OdrId = orderInfs.FirstOrDefault(e => e.RaiinNo == raiinInf.RaiinNo && e.PtId == raiinInf.PtId) != null ? orderInfs.FirstOrDefault(e => e.RaiinNo == raiinInf.RaiinNo && e.PtId == raiinInf.PtId)!.Id : 0,
                    karteEditionRaiinNo = karteEdition.FirstOrDefault(r => r.RaiinNo == raiinInf.RaiinNo && r.KarteStatus == 1) != null ? karteEdition.FirstOrDefault(r => r.RaiinNo == raiinInf.RaiinNo && r.KarteStatus == 1)!.RaiinNo : 0,
                    ptKohi1,
                    ptKohi2,
                    ptKohi3,
                    ptKohi4,
                    HokenSbtCd = relatedPtHokenPattern != null ? relatedPtHokenPattern.HokenSbtCd : 0,
                    Houbetu = ptHokenInf.FirstOrDefault(e => e.PtId == relatedPtHokenPattern.PtId && e.HokenId == relatedPtHokenPattern.HokenId) != null ? ptHokenInf.FirstOrDefault(e => e.PtId == relatedPtHokenPattern.PtId && e.HokenId == relatedPtHokenPattern.HokenId)!.Houbetu : string.Empty,
                    ptInf.ReferenceNo,
                    HokenId = relatedPtHokenPattern != null ? relatedPtHokenPattern.HokenId : 0,
                    meetingId = meeting != null ? meeting.MeetingId : 0
                };

            var raiins = raiinQuery.AsEnumerable().ToList();
            var labelConst = typeof(LabelMstConst);

            var colorLabels = raiinKbnMsts.Select(e => new Label(e.GrpCd, e.GrpName, e.ColorCode, e.SortNo, e.HpId)).ToList();

            var models = raiins.Select(r =>
            {
                var ptHoken = ptHokenInf.FirstOrDefault(e => e.PtId == r.PtId && e.HokenId == r.HokenId);
                return new ReceptionForViewDto(
                            r.HpId,
                            r.PtId,
                            r.SinDate,
                            r.IsDeleted,
                            r.RaiinNo,
                            r.UketukeNo,
                            r.UketukeTime,
                            r.YoyakuTime,
                            r.YoyakuEndTime,
                            r.Status,
                            r.StatusMstKbn,
                            r.StatusKbnUpdateTime,
                            r.PtNum,
                            r.PortalPtId,
                            r.KanaName,
                            r.Name,
                            r.Sex,
                            r.Birthday,
                            r.TreatmentDepartmentId,
                            r.KaId,
                            r.KaName,
                            r.TantoId,
                            r.tantoKana,
                            r.MonshinStatus,
                            r.Text,
                            r.MemoHtml,
                            r.reserveType ?? 0,
                            r.meetingStatus,
                            r.hasUnread,
                            r.labels,
                            colorLabels,
                            r.OnlineConfirmationId,
                            new List<KohiInfModel>
                            {
                                new KohiInfModel(
                                    r.ptKohi1?.HokenId ?? 0,
                                    r.ptKohi1?.Houbetu ?? string.Empty,
                                    r.ptKohi1 ?.SeqNo ?? 0
                                    ),
                                new KohiInfModel(
                                    r.ptKohi2?.HokenId ?? 0,
                                    r.ptKohi2?.Houbetu ?? string.Empty,
                                    r.ptKohi2?.SeqNo ?? 0
                                    ),
                                new KohiInfModel(
                                    r.ptKohi3?.HokenId ?? 0,
                                    r.ptKohi3?.Houbetu ?? string.Empty,
                                    r.ptKohi3?.SeqNo ?? 0
                                    ),
                               new KohiInfModel(
                                    r.ptKohi4?.HokenId ?? 0,
                                    r.ptKohi4?.Houbetu ?? string.Empty,
                                    r.ptKohi4?.SeqNo ?? 0
                                    ),
                                },
                            r.Houbetu,
                            r.HokenSbtCd,
                            r.OdrId,
                            r.karteEditionRaiinNo,
                            r.SortNoStatusMst,
                            r.OnlineConfirmationDate,
                            r.PortalCustomerId,
                            GetTypeAlert(hpId, r.PtId, r.PtNum, r.PortalCustomerId, r.Status, r.Birthday, r.Sex, r.Name, r.KanaName, r.SinDate),
                            r.reverseId,
                            r.reverseDetailId,
                            r.ReferenceNo,
                            r.meetingId,
                            ptHoken != null ? ptHoken.HokenKbn : 0
                );
            }).ToList();

            var onlinePatientData = GetOnlinePatientData(sinDate, hpId, 0);

            var combinedModels = models
                .Concat(onlinePatientData)
                .ToList();

            var alertModels = combinedModels
                .Where(r => r.TypeAlert != 0)
                .OrderByDescending(r => r.SortNoStatusMst)
                .ThenBy(r => r.Status == 0 ? (int.TryParse(r.YoyakuTime, out var yoyakuTime) ? yoyakuTime : int.MaxValue) : int.MaxValue)
                .ThenBy(r => r.Status == 1 && r.OnlineConfirmationDate > 0 ? r.OnlineConfirmationDate : int.MaxValue)
                .ToList();

            var nonAlertModels = combinedModels
                .Where(r => r.TypeAlert == 0)
                .OrderBy(c => c.UketukeNo)
                .ToList();

            return nonAlertModels.Concat(alertModels).ToList();
        }

        private int GetTypeAlert(int hpId, long ptId, long ptNum, int? portalCustomerId, int status, int birthDay, int sex, string name, string nameKana, int sinDate)
        {
            var modalNum = GetModelNum(ptId, ptNum, portalCustomerId ?? 0, status);
            var mappingMembers = GetMappingMeber(hpId, ptId, ptNum, 1, portalCustomerId ?? 0, birthDay, sex, name, nameKana, sinDate, modalNum);

            if (ptId > 0 && ptNum == 0 && portalCustomerId > 0 && status == 1 && mappingMembers.Any())
            {
                return 1;
            }
            if (ptId > 0 && ptNum > 0 && (portalCustomerId == 0 || portalCustomerId == null) && status == 1 && mappingMembers.Any())
            {
                return 1;
            }
            if (ptId > 0 && ptNum == 0 && portalCustomerId > 0 && status == 0 && mappingMembers.Any())
            {
                return 1;
            }
            return 0;
        }

        private bool ValidateByStatus(int hpId, long raiinNo, int userId)
        {
            var raiinInf = NoTrackingDataContext.RaiinInfs.AsTracking().Where(r =>
                            r.HpId == hpId
                            && r.RaiinNo == raiinNo).FirstOrDefault();
            if (raiinInf is null)
            {
                return false;
            }
            if (raiinInf.Status == RaiinState.Reservation || raiinInf.Status == RaiinState.Confirmation || raiinInf.Status == RaiinState.Deleted)
            {
                return false;
            }

            return true;
        }

        public List<ReceptionForViewDto> GetOnlinePatientData(int sinDate, int hpid, long onlineConfirmationId)
        {
            var sinDateInt = sinDate;
            int typeAlert = 0;
            bool flag = false;
            string? errorMess = null;
            string? errorCode = null;
            string? processingResultMess = null;

            var confirmationHistories = NoTrackingDataContext.OnlineConfirmationHistories
            .Where(c => c.HpId == hpid
                && c.PtId == 0 &&
                (onlineConfirmationId == 0 || c.ID == onlineConfirmationId) &&
                (sinDateInt == 0 ||
                (c.OnlineConfirmationDate.Year * 10000 +
                    c.OnlineConfirmationDate.Month * 100 +
                    c.OnlineConfirmationDate.Day) == sinDateInt)
            ).ToList();

            var receptionDtosList = new List<ReceptionForViewDto>();

            foreach (var item in confirmationHistories)
            {
                typeAlert = 0;
                flag = false;
                errorMess = null;
                errorCode = null;
                processingResultMess = null;
                var sinDateConf = CIUtil.DateTimeToInt(item.OnlineConfirmationDate);

                XElement xml = XElement.Parse(item.ConfirmationResult);

                string segmentOfResult = xml.Descendants(XmlConstant.SegmentOfResult).FirstOrDefault()?.Value;
                string processingResultStatus = xml.Descendants(XmlConstant.ProcessingResultStatus).FirstOrDefault()?.Value;
                if (Convert.ToInt32(segmentOfResult) == XmlConstant.UnexpectedEnding || Convert.ToInt32(processingResultStatus) == XmlConstant.Error)
                {
                    flag = true;
                    errorCode = xml.Descendants(XmlConstant.ErrorCode).FirstOrDefault()?.Value ?? xml.Descendants(XmlConstant.ProcessingResultCode).FirstOrDefault()?.Value;
                    errorMess = xml.Descendants(XmlConstant.ErrorMessage).FirstOrDefault()?.Value ?? xml.Descendants(XmlConstant.ProcessingResultMessage).FirstOrDefault()?.Value;
                }

                string nameKana = xml.Descendants(XmlConstant.NameKana).FirstOrDefault()?.Value;
                string name = xml.Descendants(XmlConstant.Name).FirstOrDefault()?.Value;
                string birthdate = xml.Descendants(XmlConstant.Birthdate).FirstOrDefault()?.Value;
                string sex1 = xml.Descendants(XmlConstant.Sex1).FirstOrDefault()?.Value;
                string sex2 = xml.Descendants(XmlConstant.Sex2).FirstOrDefault()?.Value;

                int sex = sex2 != null && int.TryParse(sex2, out var gender2)
                    ? gender2
                    : (sex1 != null && int.TryParse(sex1, out var gender1) ? gender1 : 0);

                int birthdateInt = birthdate != null && int.TryParse(birthdate, out var bd) ? bd : 0;

                var modalNum = GetModelNum(0, 0, 0, 1);
                var mappingMembers = GetMappingMeber(hpid, 0, 0, 1, 0, birthdateInt, sex, name, nameKana, sinDateConf, modalNum);
                if (modalNum == 1 && item.PtId == 0 && !mappingMembers.Any())
                {
                    mappingMembers = GetMappingMeber(hpid, 0, 0, 1, 0, birthdateInt, sex, name, nameKana, sinDateConf, 2);
                }

                if (mappingMembers.Any() && !flag)
                {
                    typeAlert = 1;
                }
                else
                {
                    typeAlert = flag ? 2 : 3;
                }

                var receptionDto = new ReceptionForViewDto(
                    hpId: hpid,
                    ptId: 0,
                    sinDate: sinDateConf,
                    isDeleted: 0,
                    raiinNo: 0,
                    uketukeNo: 0,
                    uketukeTime: string.Empty,
                    yoyakuTime: string.Empty,
                    yoyakuEndTime: string.Empty,
                    status: item.UketukeStatus == 9 ? 9 : 1,
                    statusMstKbn: 0,
                    statusKbnUpdateTime: null,
                    ptNum: 0,
                    portalPtId: 0,
                    kanaName: nameKana,
                    name: name,
                    sex: sex,
                    birthday: birthdateInt,
                    treatmentDepartmentId: 0,
                    kaId: 0,
                    kaName: string.Empty,
                    tantoId: 0,
                    tantoName: string.Empty,
                    monshinStatus: null,
                    text: string.Empty,
                    memo: string.Empty,
                    reserveType: 0,
                    meetingStatus: 0,
                    hasUnread: 0,
                    labels: new List<Label>(),
                    colorLabels: new List<Label>(),
                    onlineConfirmationId: item.ID,
                    kohi: new List<KohiInfModel>(),
                    houbetu: string.Empty,
                    hokenSbtCd: 0,
                    odrId: 0,
                    karteEditionRaiinNo: 0,
                    sortNoStatusMst: 0,
                    onlineConfirmationDate: item.OnlineConfirmationDate,
                    portalCustomerId: 0,
                    typeAlert: typeAlert,
                    reverseId: 0,
                    reverseDetailId: 0,
                    referenceNo: 0,
                    errorCode: errorCode,
                    errorMessage: errorMess
                );

                receptionDtosList.Add(receptionDto);
            }

            return receptionDtosList.ToList();
        }

        #endregion

        public List<ReceptionCombineModel> GetReceptionCombine(int hpId, int sinDate, long raiinNo, int hokenPid, long ptId, bool isCombined)
        {
            var treatments = NoTrackingDataContext.TreatmentDepartments.Where(r => r.IsDeleted == DeleteTypes.None && r.HospitalId == hpId);
            var tantos = NoTrackingDataContext.UserMsts.Where(r => r.HpId == hpId && r.IsDeleted == DeleteTypes.None);
            var kas = NoTrackingDataContext.KaMsts.Where(r => r.IsDeleted == DeleteTypes.None && r.HpId == hpId);

            var receptionEntity = NoTrackingDataContext.RaiinInfs.FirstOrDefault(r => r.RaiinNo == raiinNo && r.HpId == hpId);
            var hoken = NoTrackingDataContext.PtHokenPatterns.FirstOrDefault(r => r.PtId == ptId && r.HokenPid == hokenPid && r.HpId == hpId);
            var hokenInf = hoken != null
                ? NoTrackingDataContext.PtHokenInfs.FirstOrDefault(r => r.PtId == ptId && r.HokenId == hoken.HokenId && r.HpId == hpId)
                : null;

            var listRaiinInf = new List<RaiinInf>();
            if (isCombined && receptionEntity != null)
            {
                listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(r => r.HpId == hpId && r.OyaRaiinNo == receptionEntity.OyaRaiinNo).ToList();
            }
            else if (receptionEntity != null && hokenInf != null)
            {
                listRaiinInf = NoTrackingDataContext.RaiinInfs
                                                    .Where(r =>
                                                        r.HpId == hpId &&
                                                        r.Status >= RaiinState.Receptionist &&
                                                        r.Status < RaiinState.Paid &&
                                                        r.PtId == receptionEntity.PtId &&
                                                        r.SinDate == receptionEntity.SinDate &&
                                                        r.IsDeleted == DeleteTypes.None &&
                                                        NoTrackingDataContext.PtHokenPatterns.Any(h =>
                                                                h.HpId == hpId &&
                                                                h.PtId == r.PtId &&
                                                                h.HokenPid == r.HokenPid &&
                                                                h.HokenId == hokenInf.HokenId))
                                                    .ToList()
                                                    .Where(r => r.ReserveDetailId <= 0 ||
                                                                NoTrackingDataContext.ReserveDetails.FirstOrDefault(rd =>
                                                                                    rd.IsDeleted == 0 &&
                                                                                    rd.PatientId == r.PtId &&
                                                                                    rd.ReserveDetailId == r.ReserveDetailId)
                                                                            ?.ReserveType != 1)
                                                    .ToList();
            }

            var result = listRaiinInf.Select(r => new ReceptionCombineModel(
                                                        r.HpId,
                                                        r.RaiinNo,
                                                        r.OyaRaiinNo,
                                                        r.PtId,
                                                        NoTrackingDataContext.PtInfs.FirstOrDefault(d => d.HpId == hpId && d.PtId == r.PtId)?.Name ?? string.Empty,
                                                        r.TreatmentDepartmentId,
                                                        treatments.FirstOrDefault(d => d.TreatmentDepartmentId == r.TreatmentDepartmentId)?.Title ?? string.Empty,
                                                        r.KaId,
                                                        kas.FirstOrDefault(d => d.KaId == r.KaId)?.KaName ?? string.Empty,
                                                        r.TantoId,
                                                        tantos.FirstOrDefault(d => d.UserId == r.TantoId)?.Name ?? string.Empty,
                                                        r.UketukeNo))
                                     .ToList();

            return result;
        }

        public int SaveCombineBill(List<ReceptionCombineModel> combineBills, int userId, bool isSplited, long raiinNo)
        {
            var raiinoList = combineBills.Select(r => r.RaiinNo).ToList();

            var hasDeletedRaiino = TrackingDataContext.RaiinInfs.Any(r => raiinoList.Contains(r.RaiinNo) && (r.IsDeleted == DeleteTypes.Deleted || r.Status == RaiinState.Deleted));
            if (hasDeletedRaiino)
                return 2;

            var receptionToUpdate = TrackingDataContext.RaiinInfs.Where(r => raiinoList.Contains(r.RaiinNo));
            if (!receptionToUpdate.Any())
                return 1;

            var lstStatusValidate = new List<int>
            {
                RaiinState.Reservation,
                RaiinState.Confirmation,
                RaiinState.Paid,
                RaiinState.FcoWaiting
            };
            if (receptionToUpdate.Any(r => lstStatusValidate.Contains(r.Status)))
                return 3;

            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                if (isSplited)
                {
                    foreach (var item in receptionToUpdate)
                    {
                        item.OyaRaiinNo = item.RaiinNo;
                        item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        item.UpdateId = userId;
                    }
                }
                else
                {
                    foreach (var item in receptionToUpdate)
                    {
                        item.OyaRaiinNo = raiinNo;
                        item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        item.UpdateId = userId;
                    }
                }
                TrackingDataContext.SaveChanges();
                transaction.Commit();
                return 0;
            });
        }

        public int GetModelNum(long ptId, long ptNum, int portalCustomerId, int status)
        {
            if ((ptId == 0 && ptNum == 0 && portalCustomerId == 0 && status == 1) || (ptId > 0 && ptNum > 0 && portalCustomerId == 0 && status == 1))
            {
                return 1;
            }
            if (ptId > 0 && ptNum == 0 && portalCustomerId > 0 && status == 1)
            {
                return 2;
            }
            if (ptId > 0 && ptNum == 0 && portalCustomerId > 0 && status == 0)
            {
                return 3;
            }
            return 0;
        }
        public bool DeletePatientFile(long ptId, int hpId, int categoryCd, int fileId, int userId)
        {
            var patientFileToDelete = TrackingDataContext.FilingInf.FirstOrDefault(r => r.HpId == hpId
                                                                                       && r.PtId == ptId
                                                                                       && r.CategoryCd == categoryCd
                                                                                       && r.FileId == fileId
                                                                                       && r.IsDeleted == DeleteTypes.None);
            if (patientFileToDelete == null)
            {
                return false;
            }

            patientFileToDelete.IsDeleted = DeleteTypes.Deleted;
            patientFileToDelete.UpdateDate = CIUtil.GetJapanDateTimeNow();
            patientFileToDelete.UpdateId = userId;
            TrackingDataContext.SaveChanges();
            return true;
        }
        public List<GetPatientFileModel> GetPatientFiles(int hpId, long ptId, int categoryCd, string fileName)
        {
            var listFile = NoTrackingDataContext.FilingInf.Where(r => r.HpId == hpId
                                                                    && r.PtId == ptId
                                                                    && r.CategoryCd == categoryCd
                                                                    && r.IsDeleted == DeleteTypes.None)
                                                          .OrderByDescending(r => r.GetDate)
                                                          .Select(r => new GetPatientFileModel(r.HpId,
                                                                                                r.PtId,
                                                                                                r.CategoryCd,
                                                                                                r.DspFileName ?? string.Empty,
                                                                                                r.FileName ?? string.Empty,
                                                                                                r.GetDate,
                                                                                                r.SinDate,
                                                                                                r.Memo ?? string.Empty,
                                                                                                r.S3FileName ?? string.Empty,
                                                                                                r.FileId
                                                                                                ))
                                                          .ToList();

            if (!string.IsNullOrEmpty(fileName))
            {
                var fileNameSearch = HenkanJ.ToHalfsize(RomajiString.RomajiToKana(fileName));
                return listFile.Where(item => item.FileNameSearch.Contains(fileNameSearch))
                               .OrderByDescending(item => item.GetDate)
                               .ToList();
            }
            else
            {
                return listFile;
            }
        }

        public bool AddPatientFile(List<AddPatientFileModel> listItem, int hpId, int userId, int getDate)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                int fileNo = 0;
                foreach (var item in listItem)
                {
                    fileNo++;
                    var newFiling = new FilingInf
                    {
                        HpId = hpId,
                        PtId = item.PtId,
                        CategoryCd = item.CategoryCd,
                        GetDate = getDate,
                        FileNo = fileNo,
                        DspFileName = item.DspFileName,
                        FileName = item.FileName,
                        S3FileName = item.S3FileName,
                        Memo = item.Memo,
                        CreateDate = CIUtil.GetJapanDateTimeNow(),
                        CreateId = userId,
                        UpdateDate = CIUtil.GetJapanDateTimeNow(),
                        UpdateId = userId,
                    };
                    TrackingDataContext.FilingInf.AddRange(newFiling);
                }
                TrackingDataContext.SaveChanges();
                transaction.Commit();
                return true;
            });
        }

        public bool SubmitConfirmation(int hpId, long ptId, long onlineConfiemId, long raiino, int userId)
        {
            var ptInf = TrackingDataContext.PtInfs.FirstOrDefault(c => c.HpId == hpId && c.PtId == ptId && c.IsDelete == 0);
            if (ptInf == null)
            {
                return false;
            }
            var onlineConfirmHis = TrackingDataContext.OnlineConfirmationHistories.FirstOrDefault(c => c.HpId == hpId && c.ID == onlineConfiemId && c.UketukeStatus != 9 && onlineConfiemId > 0);
            if (onlineConfirmHis == null && onlineConfiemId > 0)
            {
                return false;
            }
            var japanNow = CIUtil.GetJapanDateTimeNow();

            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                //ptInf.ReferenceNo = ptInf.PtNum;
                ptInf.UpdateDate = japanNow;
                ptInf.UpdateId = userId;

                if (onlineConfirmHis != null)
                {
                    onlineConfirmHis.PtId = ptInf.PtId;
                    onlineConfirmHis.UpdateDate = japanNow;
                    onlineConfirmHis.UpdateId = userId;
                }

                if (raiino != 0)
                {
                    var raiinf = TrackingDataContext.RaiinInfs.FirstOrDefault(c => c.HpId == hpId && c.RaiinNo == raiino && c.IsDeleted == 0);
                    if (raiinf == null)
                    {
                        return false;
                    }
                    raiinf.OnlineConfirmationId = Convert.ToInt32(onlineConfiemId);
                    raiinf.UpdateDate = japanNow;
                    raiinf.UpdateId = userId;
                }

                TrackingDataContext.SaveChanges();
                transaction.Commit();
                return true;
            });
        }

        public long InsertFromCalendarOrPortal(InsertFromCalendarOrPortalDto dto, string raiinComment)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            var date = CIUtil.GetJapanDateTimeNow();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                var kaId = _commonRepository.GetKaIdByTreatmentDepartmentId(dto.HpId, dto.TreatmentDepartmentId);

                // Insert RaiinInf
                var raiinInf = new RaiinInf
                {
                    HpId = dto.HpId,
                    PtId = dto.PtId,
                    SinDate = dto.SinDate,
                    Status = RaiinState.Reservation,
                    IsYoyaku = 1,
                    YoyakuTime = dto.YoyakuTime,
                    YoyakuId = dto.YoyakuId,
                    UketukeSbt = 0,
                    UketukeTime = null,
                    UketukeId = 0,
                    UketukeNo = 0,
                    SinStartTime = null,
                    SinEndTime = null,
                    KaikeiTime = null,
                    KaikeiId = 0,
                    KaId = kaId,
                    TantoId = dto.TantoId,
                    HokenPid = 0,
                    SyosaisinKbn = 2,
                    JikanKbn = -1,
                    IsDeleted = 0,
                    CreateDate = date,
                    CreateId = dto.YoyakuId,
                    CreateMachine = null,
                    UpdateDate = date,
                    UpdateId = dto.YoyakuId,
                    UpdateMachine = null,
                    SanteiKbn = 0,
                    ConfirmationResult = null,
                    ConfirmationState = 0,
                    ConfirmationType = 0,
                    InfoConsFlg = null,
                    PrescriptionIssueType = 0,
                    PrintEpsReference = 0,
                    TreatmentDepartmentId = dto.TreatmentDepartmentId,
                    OnlineConfirmationId = 0,
                    YoyakuEndTime = dto.YoyakuEndTime,
                    ReserveDetailId = dto.ReserveDetailId
                };
                TrackingDataContext.RaiinInfs.Add(raiinInf);
                TrackingDataContext.SaveChanges();

                if (raiinInf.RaiinNo <= 0)
                {
                    transaction.Rollback();
                    return 0;
                }

                raiinInf.OyaRaiinNo = raiinInf.RaiinNo;

                // Insert RaiinCmtInf
                if (!string.IsNullOrWhiteSpace(raiinComment))
                {
                    var raiinCmtInf = new RaiinCmtInf
                    {
                        HpId = raiinInf.HpId,
                        RaiinNo = raiinInf.RaiinNo,
                        CmtKbn = CmtKbns.Comment,
                        PtId = raiinInf.PtId,
                        SinDate = raiinInf.SinDate,
                        Text = raiinComment,
                        IsDelete = 0,
                        CreateDate = date,
                        CreateId = raiinInf.YoyakuId,
                        CreateMachine = null,
                        UpdateDate = date,
                        UpdateId = raiinInf.YoyakuId,
                        UpdateMachine = null

                    };
                    TrackingDataContext.RaiinCmtInfs.Add(raiinCmtInf);
                }

                TrackingDataContext.SaveChanges();

                transaction.Commit();

                return raiinInf.RaiinNo;
            });
        }

        public IQueryable<PtInf> GetFilteredPtInfs(int hpId, long ptId, long ptNum, int portalCustomerId, int inputBirthday, string name, string kanaName, int modalNum)

        {
            if (modalNum == 1)
            {
                return NoTrackingDataContext.PtInfs.Where(ptInf =>
                    (inputBirthday == 19000101 && !string.IsNullOrEmpty(kanaName) &&
                     ((ptId == 0 && ptNum == 0 && portalCustomerId == 0 && ptInf.HpId == hpId && ptInf.PortalCustomerId > 0 && (ptInf.KanaName.Replace("　", "").Replace(" ", "") == kanaName.Replace("　", "").Replace(" ", "") || (!name.IsNullOrEmpty() && ptInf.Name.Replace("　", "").Replace(" ", "") == name.Replace("　", "").Replace(" ", ""))) && ptInf.IsDelete == 0) ||
                      (ptId > 0 && ptNum > 0 && portalCustomerId == 0 && ptInf.HpId == hpId && ptInf.PtNum == 0 && ptInf.PortalCustomerId > 0 && (ptInf.KanaName.Replace("　", "").Replace(" ", "") == kanaName.Replace("　", "").Replace(" ", "") || (!name.IsNullOrEmpty() && ptInf.Name.Replace("　", "").Replace(" ", "") == name.Replace("　", "").Replace(" ", ""))) && ptInf.IsDelete == 0))) ||

                    (inputBirthday != 19000101 &&
                     ((ptId == 0 && ptNum == 0 && portalCustomerId == 0 && ptInf.HpId == hpId && ptInf.PortalCustomerId > 0 && ptInf.Birthday == inputBirthday && ptInf.IsDelete == 0) ||
                      (ptId > 0 && ptNum > 0 && portalCustomerId == 0 && ptInf.HpId == hpId && ptInf.PtNum == 0 && ptInf.PortalCustomerId > 0 && ptInf.Birthday == inputBirthday && ptInf.IsDelete == 0)))
                );
            }
            else if (modalNum == 2)
            {
                return NoTrackingDataContext.PtInfs.Where(ptInf =>
                    (inputBirthday == 19000101 && !string.IsNullOrEmpty(kanaName) &&
                     ((ptId == 0 && ptNum == 0 && portalCustomerId == 0 && ptInf.HpId == hpId && ptInf.PtId > 0 && ptInf.PtNum > 0 && (ptInf.KanaName.Replace("　", "").Replace(" ", "") == kanaName.Replace("　", "").Replace(" ", "") || (!name.IsNullOrEmpty() && ptInf.Name.Replace("　", "").Replace(" ", "") == name.Replace("　", "").Replace(" ", ""))) && ptInf.IsDelete == 0) ||
                      (ptId > 0 && ptNum == 0 && portalCustomerId > 0 && ptInf.HpId == hpId && (ptInf.PortalCustomerId == 0 || ptInf.PortalCustomerId == null) && ptInf.PtNum > 0 && (ptInf.KanaName.Replace("　", "").Replace(" ", "") == kanaName.Replace("　", "").Replace(" ", "") || (!name.IsNullOrEmpty() && ptInf.Name.Replace("　", "").Replace(" ", "") == name.Replace("　", "").Replace(" ", "")) && ptInf.IsDelete == 0)))) ||

                    (inputBirthday != 19000101 &&
                     ((ptId == 0 && ptNum == 0 && portalCustomerId == 0 && ptInf.HpId == hpId && ptInf.PtId > 0 && ptInf.PtNum > 0 && ptInf.Birthday == inputBirthday && ptInf.IsDelete == 0) ||
                      (ptId > 0 && ptNum == 0 && portalCustomerId > 0 && ptInf.HpId == hpId && (ptInf.PortalCustomerId == 0 || ptInf.PortalCustomerId == null) && ptInf.PtNum > 0 && ptInf.Birthday == inputBirthday && ptInf.IsDelete == 0)))
                );
            }
            else if (modalNum == 3)
            {
                return NoTrackingDataContext.PtInfs.Where(ptInf =>
                    (inputBirthday == 19000101 && !string.IsNullOrEmpty(kanaName) &&
                     (ptId > 0 && ptNum == 0 && portalCustomerId > 0 && ptInf.HpId == hpId && (ptInf.PortalCustomerId == 0 || ptInf.PortalCustomerId == null) && ptInf.PtNum > 0 && (ptInf.KanaName.Replace("　", "").Replace(" ", "") == kanaName.Replace("　", "").Replace(" ", "") || (!name.IsNullOrEmpty() && ptInf.Name.Replace("　", "").Replace(" ", "") == name.Replace("　", "").Replace(" ", ""))) && ptInf.IsDelete == 0)) ||

                    (inputBirthday != 19000101 &&
                     (ptId > 0 && ptNum == 0 && portalCustomerId > 0 && ptInf.HpId == hpId && (ptInf.PortalCustomerId == 0 || ptInf.PortalCustomerId == null) && ptInf.PtNum > 0 && ptInf.Birthday == inputBirthday && ptInf.IsDelete == 0))
                );
            }

            return Enumerable.Empty<PtInf>().AsQueryable();
        }

        public List<MappingMemberModel> GetMappingMeber(int hpId, long ptId, long ptNum, int status, int portalCustomerId, int birthday, int sex, string name, string kanaName, int sinDate, int modelNum)
        {
            if (modelNum != 1 && modelNum != 2 && modelNum != 3)
            {
                return new List<MappingMemberModel>();
            }
            var ptInfData = GetFilteredPtInfs(hpId, ptId, ptNum, portalCustomerId, birthday, name, kanaName, modelNum);
            if (!ptInfData.Any())
            {
                return new List<MappingMemberModel>();
            }

            var ptIds = ptInfData.Select(pt => pt.PtId).ToList();
            var raiinInfData = NoTrackingDataContext.RaiinInfs.Where(c => c.HpId == hpId && ptIds.Contains(c.PtId) && c.SinDate == sinDate && c.Status == RaiinState.Reservation && c.IsDeleted == 0);

            var joindeData = modelNum is 1 or 4
                ? (from ptInf in ptInfData
                   join raiinInf in raiinInfData
                       on new { ptInf.HpId, ptInf.PtId } equals new { raiinInf.HpId, raiinInf.PtId }
                   select new
                   {
                       ptInf.PtId,
                       ptInf.PtNum,
                       ptInf.Name,
                       ptInf.KanaName,
                       ptInf.Sex,
                       ptInf.Birthday,
                       ptInf.PortalCustomerId,
                       RaiinNo = raiinInf != null ? (long?)raiinInf.RaiinNo : null,
                       OnlineConfirmationId = raiinInf != null ? (int?)raiinInf.OnlineConfirmationId : null
                   })
                : ptInfData.Select(ptInf => new
                {
                    ptInf.PtId,
                    ptInf.PtNum,
                    ptInf.Name,
                    ptInf.KanaName,
                    ptInf.Sex,
                    ptInf.Birthday,
                    ptInf.PortalCustomerId,
                    RaiinNo = (long?)null,
                    OnlineConfirmationId = (int?)null
                });

            var result = joindeData.Select(data => new MappingMemberModel(
                data.PtId,
                data.PtNum,
                data.Name,
                data.KanaName,
                data.Sex,
                data.Birthday,
                data.RaiinNo,
                data.PortalCustomerId,
                data.OnlineConfirmationId,
                string.Equals(
                    (data.Name ?? "").Replace("　", "").Replace(" ", ""),
                    (name ?? "").Replace("　", "").Replace(" ", ""),
                    StringComparison.OrdinalIgnoreCase
                ),
                string.Equals(
                    (data.KanaName ?? "").Replace("　", "").Replace(" ", ""),
                    (kanaName ?? "").Replace("　", "").Replace(" ", ""),
                    StringComparison.OrdinalIgnoreCase
                ),
                data.Sex == sex,
                data.Birthday == birthday
            )).ToList().DistinctBy(c => c.PtId);

            var sortedResult = result
                .OrderByDescending(m =>
                    (m.IsNameEqual ? 1 : 0) +
                    (m.IsKanaNameEqual ? 1 : 0) +
                    (m.IsSexEqual ? 1 : 0) +
                    (m.IsBirthDayEqual ? 1 : 0)
                ).ToList();

            return sortedResult;
        }

        public UpdateMappingModelDto UpdatePatientLinking(int hospitalArrivalStatus, UpdateConfirmOnlineModel updateConfirmOnlineModel)
        {
            if (hospitalArrivalStatus == 1)
            {
                return UpdateOnlineConfirmationId
                (
                    updateConfirmOnlineModel.HpId,
                    updateConfirmOnlineModel.AiChartPtId,
                    updateConfirmOnlineModel.OnlineConfirmHisId,
                    updateConfirmOnlineModel.UserId
                );
            }

            if (hospitalArrivalStatus == 2)
            {
                return RegisterRaiinInfForExistingPatient
                (
                    updateConfirmOnlineModel.HpId,
                    updateConfirmOnlineModel.AiChartPtId,
                    updateConfirmOnlineModel.OnlineConfirmHisId,
                    updateConfirmOnlineModel.UserId
                );
            }

            if (hospitalArrivalStatus == 3)
            {
                return UpdatePatientAndVisitRecords
                (
                    updateConfirmOnlineModel.HpId,
                    updateConfirmOnlineModel.AiChartPtId,
                    updateConfirmOnlineModel.PortalPtId,
                    updateConfirmOnlineModel.OnlineConfirmHisId,
                    updateConfirmOnlineModel.UserId,
                    hospitalArrivalStatus
                );
            }

            if (hospitalArrivalStatus == 4)
            {
                return UpdatePatientAndVisitRecords
                (
                    updateConfirmOnlineModel.HpId,
                    updateConfirmOnlineModel.AiChartPtId,
                    updateConfirmOnlineModel.PortalPtId,
                    updateConfirmOnlineModel.OnlineConfirmHisId,
                    updateConfirmOnlineModel.UserId,
                    hospitalArrivalStatus
                );
            }

            if (hospitalArrivalStatus == 5)
            {
                return UpdatePatientAndVisitRecords
                (
                    updateConfirmOnlineModel.HpId,
                    updateConfirmOnlineModel.AiChartPtId,
                    updateConfirmOnlineModel.PortalPtId,
                    updateConfirmOnlineModel.OnlineConfirmHisId,
                    updateConfirmOnlineModel.UserId,
                    hospitalArrivalStatus
                );
            }

            return new UpdateMappingModelDto(false, true);
        }

        public long InsertRaiinInf(ReceptionSaveDto dto, int hpId, int userId)
        {
            // Insert RaiinInf
            var raiinInf = CreateNewRaiinInf(new ReceptionModel(dto.Reception), hpId, userId);
            raiinInf.KaId = _commonRepository.GetKaIdByTreatmentDepartmentId(hpId, raiinInf.TreatmentDepartmentId);

            UpdateConfirmationInfo(hpId, raiinInf.SinDate, raiinInf.PtId, ref raiinInf);
            TrackingDataContext.RaiinInfs.Add(raiinInf);
            TrackingDataContext.SaveChanges();

            if (raiinInf.OyaRaiinNo == 0)
            {
                raiinInf.OyaRaiinNo = raiinInf.RaiinNo;
            }

            // Insert RaiinCmtInf
            if (!string.IsNullOrWhiteSpace(dto.ReceptionComment))
            {
                var raiinCmtInf = CreateNewRaiinCmtInf(raiinInf, dto.ReceptionComment, hpId, userId);
                TrackingDataContext.RaiinCmtInfs.Add(raiinCmtInf);
            }

            // Insert RaiinKbnInfs
            var raiinKbnInfs = dto.KubunInfs
                .Where(model => model.KbnCd != CommonConstants.KbnCdDeleteFlag && model.KbnCd > 0)
                .Select(dto => CreateNewRaiinKbnInf(dto, raiinInf, hpId, userId));
            if (raiinKbnInfs.Any())
            {
                TrackingDataContext.RaiinKbnInfs.AddRange(raiinKbnInfs);
            }

            TrackingDataContext.SaveChanges();
            // Update insurances and diseases
            // SaveInsuraceConfirmationHistories(dto.Insurances, raiinInf.PtId, hpId, userId);
            // UpdateDiseaseTenkis(dto.Diseases, raiinInf.PtId, hpId, userId);
            return raiinInf.RaiinNo;
        }
        private UpdateMappingModelDto UpdatePatientAndVisitRecords(int hpId, long aiChartPtId, long portalPtId, int onlineConfirmationId, int userId, double caseType)
        {
            var deletedRaiinoLst = new List<long>();

            var oldRaiinList = TrackingDataContext.RaiinInfs
               .Where(c => c.HpId == hpId && c.PtId == aiChartPtId && c.IsDeleted == 0)
               .ToList();
            var mappRaiinList = TrackingDataContext.RaiinInfs
                .Where(c => c.HpId == hpId && c.PtId == portalPtId && c.IsDeleted == 0)
                .ToList();
            if (!oldRaiinList.Any() && caseType == 4 || !mappRaiinList.Any()) return new UpdateMappingModelDto(false, false);

            var oldPtInf = TrackingDataContext.PtInfs.FirstOrDefault(c => c.HpId == hpId && c.PtId == aiChartPtId && c.IsDelete == DeleteTypes.None);
            var mappPtInf = TrackingDataContext.PtInfs.FirstOrDefault(c => c.HpId == hpId && c.PtId == portalPtId && c.IsDelete == DeleteTypes.None);
            if (oldPtInf == null || mappPtInf == null) return new UpdateMappingModelDto(false, false);

            var raiinNos = mappRaiinList.Select(r => r.RaiinNo).ToList();
            var onlineConfirmHisIds = mappRaiinList.Select(e => (long)e.OnlineConfirmationId).ToList();
            var raiinCmtInf = TrackingDataContext.RaiinCmtInfs
                .Where(c => c.HpId == hpId && c.PtId == portalPtId && raiinNos.Contains(c.RaiinNo) && c.IsDelete == DeleteTypes.None)
                .ToList();
            var raiinKbnInf = TrackingDataContext.RaiinKbnInfs
                .Where(c => c.HpId == hpId && c.PtId == portalPtId && raiinNos.Contains(c.RaiinNo) && c.IsDelete == DeleteTypes.None)
                .ToList();

            var japanNow = CIUtil.GetJapanDateTimeNow();
            switch (caseType)
            {
                case 3:
                    mappPtInf.IsDelete = 1;
                    //if (oldPtInf.PtNum > 0)
                    //{
                    //    oldPtInf.ReferenceNo = oldPtInf.PtNum;
                    //}
                    var onlineConfirmationHistories = TrackingDataContext.OnlineConfirmationHistories.Where(c => c.HpId == hpId && onlineConfirmHisIds.Contains(c.ID) && c.UketukeStatus != 9).ToList();
                    foreach (var mapRaiin in mappRaiinList)
                    {
                        mapRaiin.PtId = oldPtInf.PtId;
                        mapRaiin.UpdateDate = japanNow;
                        mapRaiin.UpdateId = userId;

                        var onlineConfirmationHistory = onlineConfirmationHistories.FirstOrDefault(e => e.ID == mapRaiin.OnlineConfirmationId);
                        if (onlineConfirmationHistory != null && mapRaiin.OnlineConfirmationId != 0)
                        {
                            onlineConfirmationHistory.PtId = oldPtInf.PtId;
                            onlineConfirmationHistory.UpdateId = userId;
                            onlineConfirmationHistory.UpdateDate = japanNow;
                        }
                    }
                    oldPtInf.PortalCustomerId = mappPtInf.PortalCustomerId;
                    break;
                case 4:
                    mappPtInf.IsDelete = 1;
                    foreach (var oldRaiin in oldRaiinList)
                    {
                        deletedRaiinoLst.Add(oldRaiin.RaiinNo);
                        oldRaiin.IsDeleted = 1;
                        oldRaiin.Status = RaiinState.Deleted;
                    }

                    foreach (var mapRaiin in mappRaiinList)
                    {
                        mapRaiin.PtId = oldPtInf.PtId;
                        mapRaiin.OnlineConfirmationId = oldRaiinList.First().OnlineConfirmationId;
                        mapRaiin.Status = RaiinState.Confirmation;
                        mapRaiin.UpdateDate = japanNow;
                        mapRaiin.UpdateId = userId;
                    }
                    mappPtInf.PortalCustomerId = oldPtInf.PortalCustomerId;
                    break;
                case 5:
                    mappPtInf.IsDelete = 1;
                    foreach (var mappRaiin in mappRaiinList)
                    {
                        mappRaiin.Status = RaiinState.Confirmation;
                    }
                    foreach (var mapRaiin in mappRaiinList)
                    {
                        mapRaiin.PtId = oldPtInf.PtId;
                        mapRaiin.UpdateDate = japanNow;
                        mapRaiin.UpdateId = userId;
                    }
                    oldPtInf.PortalCustomerId = mappPtInf.PortalCustomerId;
                    break;

                default:
                    return new UpdateMappingModelDto(false, true);
            }

            oldPtInf.UpdateDate = japanNow;
            oldPtInf.UpdateId = userId;

            mappPtInf.UpdateDate = japanNow;
            mappPtInf.UpdateId = userId;

            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    if (caseType == 4)
                    {
                        var oldportalPtId = portalPtId;
                        portalPtId = aiChartPtId;
                        aiChartPtId = oldportalPtId;
                    }

                    foreach (var cmt in raiinCmtInf)
                    {
                        cmt.PtId = oldPtInf.PtId;
                        cmt.UpdateDate = japanNow;
                        cmt.UpdateId = userId;
                    }

                    string query = $"UPDATE \"raiin_kbn_inf\" SET \"pt_id\" = {aiChartPtId} WHERE \"pt_id\" = {portalPtId}";
                    TrackingDataContext.Database.ExecuteSqlRaw(query);

                    if (caseType == 4 || caseType == 5 || caseType == 3)
                    {
                        if (!UpdatePortalTable(hpId, aiChartPtId, portalPtId, userId, caseType))
                        {
                            return new UpdateMappingModelDto(false, true);
                        }
                    }

                    TrackingDataContext.SaveChanges();
                    transaction.Commit();

                    return new UpdateMappingModelDto(true, false, deletedRaiinoLst);
                }
                catch
                {
                    transaction.Rollback();
                    return new UpdateMappingModelDto(false, false);
                }
            });
        }

        private UpdateMappingModelDto RegisterRaiinInfForExistingPatient(int hpId, long ptId, int onlineConfirmationId, int userId)
        {
            var onlineConfirmation = TrackingDataContext.OnlineConfirmationHistories.FirstOrDefault(r => r.HpId == hpId && r.ID == onlineConfirmationId && r.UketukeStatus != 9);
            if (onlineConfirmation == null)
            {
                return new UpdateMappingModelDto(false, false);
            }

            var onlineConfirmationDateInt = onlineConfirmation.OnlineConfirmationDate.Year * 10000 +
                                            onlineConfirmation.OnlineConfirmationDate.Month * 100 +
                                            onlineConfirmation.OnlineConfirmationDate.Day;

            var ptInf = TrackingDataContext.PtInfs.FirstOrDefault(c => c.HpId == hpId && c.PtId == ptId && c.IsDelete == DeleteTypes.None);
            if (ptInf == null)
            {
                return new UpdateMappingModelDto(false, false);
            }

            var reception = new ReceptionUpsertItem(
                hpId: hpId,
                ptId: ptId,
                sinDate: onlineConfirmationDateInt,
                raiinNo: 0,
                oyaRaiinNo: 0,
                hokenPid: 0,
                santeiKbn: 0,
                status: 1,
                isYoyaku: 0,
                yoyakuTime: string.Empty,
                yoyakuId: 0,
                uketukeSbt: 1,
                uketukeTime: string.Empty,
                uketukeId: 0,
                uketukeNo: 0,
                sinStartTime: string.Empty,
                sinEndTime: string.Empty,
                kaikeiTime: string.Empty,
                kaikeiId: 0,
                kaId: 0,
                tantoId: 0,
                syosaisinKbn: 0,
                jikanKbn: 0,
                comment: string.Empty,
                treatmentDepartmentId: 0,
                printEpsReference: 0,
                prescriptionIssueType: onlineConfirmation.PrescriptionIssueType,
                onlineConfirmationHistoryId: onlineConfirmationId
            );

            var dto = new ReceptionSaveDto(
                reception: reception,
                receptionComment: null,
                kubunInfs: new List<RaiinKbnInfDto>(),
                insurances: new List<InsuranceDto>(),
                diseases: null
            );

            onlineConfirmation.PtId = ptId;
            onlineConfirmation.UpdateDate = CIUtil.GetJapanDateTimeNow();
            onlineConfirmation.UpdateId = userId;

            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                //if (ptInf.PtNum > 0)
                //{
                //    ptInf.ReferenceNo = ptInf.PtNum;
                //}
                InsertRaiinInf(dto, hpId, userId);
                TrackingDataContext.SaveChanges();
                transaction.Commit();
                return new UpdateMappingModelDto(true, true);
            });
        }

        private UpdateMappingModelDto UpdateOnlineConfirmationId(int hpId, long ptId, int onlineConfirmationId, int userId)
        {
            var ptInf = TrackingDataContext.PtInfs.FirstOrDefault(pt => pt.HpId == hpId && pt.PtId == ptId && pt.IsDelete == DeleteTypes.None);
            if (ptInf == null)
            {
                return new UpdateMappingModelDto(false, true);
            }

            var raiinInfs = TrackingDataContext.RaiinInfs
                .Where(r => r.HpId == hpId && r.PtId == ptInf.PtId && r.IsDeleted == DeleteTypes.None)
                .ToList();

            var onlineConfirmation = TrackingDataContext.OnlineConfirmationHistories
                .FirstOrDefault(r => r.HpId == hpId && r.ID == onlineConfirmationId && r.UketukeStatus != 9);

            if (!raiinInfs.Any() || onlineConfirmation == null)
            {
                return new UpdateMappingModelDto(false, true);
            }

            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    var japanNow = CIUtil.GetJapanDateTimeNow();
                    raiinInfs.ForEach(raiinInf =>
                    {
                        raiinInf.OnlineConfirmationId = onlineConfirmationId;
                        raiinInf.CreateDate = DateTime.SpecifyKind(raiinInf.CreateDate, DateTimeKind.Utc);
                        raiinInf.UpdateDate = japanNow;
                        raiinInf.UpdateId = userId;
                        raiinInf.Status = RaiinState.Confirmation;
                    });

                    onlineConfirmation.PtId = ptInf.PtId;
                    onlineConfirmation.OnlineConfirmationDate = DateTime.SpecifyKind(onlineConfirmation.OnlineConfirmationDate, DateTimeKind.Utc);
                    onlineConfirmation.CreateDate = DateTime.SpecifyKind(onlineConfirmation.CreateDate, DateTimeKind.Utc);
                    onlineConfirmation.UpdateDate = japanNow;
                    onlineConfirmation.UpdateId = userId;

                    //if (ptInf.PtNum > 0)
                    //{
                    //    ptInf.ReferenceNo = ptInf.PtNum;
                    //}

                    TrackingDataContext.SaveChanges();
                    transaction.Commit();

                    return new UpdateMappingModelDto(true, !(ptInf.PtNum > 0));
                }
                catch
                {
                    transaction.Rollback();
                    return new UpdateMappingModelDto(true, false);
                }
            });
        }

        private bool UpdatePortalTable(int hpId, long aiChartPtId, long portalPtId, int userId, double hospitalArrivalStatus)
        {
            //string aiChartPatientMemosHtml;
            var japanNow = CIUtil.GetJapanDateTimeNow();
            var userIdString = userId.ToString();

            if (hospitalArrivalStatus == 4)
            {
                var oldportalPtId = portalPtId;
                portalPtId = aiChartPtId;
                aiChartPtId = oldportalPtId;
            }

            long ptIdInt = aiChartPtId;
            string ptIdString = Convert.ToString(aiChartPtId);

            var meetings = TrackingDataContext.Meetings
                   .Where(c => c.HospitalId == hpId && c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (meetings?.Any() ?? false)
            {
                foreach (var meeting in meetings)
                {
                    meeting.PatientId = ptIdInt;
                    meeting.UpdatedBy = userIdString;
                    meeting.UpdatedAt = japanNow;
                }
            }

            var portalPatientMemos = TrackingDataContext.PatientMemos
                .FirstOrDefault(c => c.HpId == hpId && c.PtId == portalPtId);
            var aiChartPatientMemos = TrackingDataContext.PatientMemos
                .FirstOrDefault(c => c.HpId == hpId && c.PtId == aiChartPtId);
            if (aiChartPatientMemos == null && portalPatientMemos != null)
            {
                string query = $"UPDATE \"patient_memo\" SET \"patient_id\" = {aiChartPtId}, \"updated_by\" = '{userIdString}', \"updated_at\" = '{japanNow}' WHERE \"patient_id\" = {portalPtId}";
                TrackingDataContext.Database.ExecuteSqlRaw(query);
            }

            if (portalPatientMemos != null && aiChartPatientMemos != null)
            {
                if (portalPatientMemos.IsDeleted == DeleteTypes.None &&
                    aiChartPatientMemos.IsDeleted == DeleteTypes.None)
                {
                    var emptyLine = string.IsNullOrWhiteSpace(aiChartPatientMemos.MemoHtml) ? "" : "\n  \n";
                    aiChartPatientMemos.MemoHtml += emptyLine + portalPatientMemos.MemoHtml;

                    portalPatientMemos.UpdatedBy = userIdString;
                    portalPatientMemos.UpdatedAt = japanNow;
                    portalPatientMemos.IsDeleted = DeleteTypes.Deleted;

                    aiChartPatientMemos.UpdatedBy = userIdString;
                    aiChartPatientMemos.UpdatedAt = japanNow;
                }
                else if (aiChartPatientMemos.IsDeleted == DeleteTypes.Deleted && portalPatientMemos.IsDeleted == DeleteTypes.None)
                {
                    string query = $"UPDATE \"patient_memo\" SET \"is_deleted\" = {0} WHERE \"patient_id\" = {aiChartPtId}";
                    TrackingDataContext.Database.ExecuteSqlRaw(query);
                    TrackingDataContext.PatientMemos.Remove(portalPatientMemos);
                    TrackingDataContext.SaveChanges();
                    aiChartPatientMemos.MemoHtml = portalPatientMemos.MemoHtml;
                    aiChartPatientMemos.CreatedBy = portalPatientMemos.CreatedBy;
                    aiChartPatientMemos.CreatedAt = portalPatientMemos.CreatedAt;
                    aiChartPatientMemos.UpdatedBy = userIdString;
                    aiChartPatientMemos.UpdatedAt = japanNow;
                }
            }

            var patientMessChannels = TrackingDataContext.PatientMessageChannels
                .Where(c => c.HospitalId == hpId && c.PatientId == Convert.ToString(portalPtId) && c.IsDeleted == DeleteTypes.None);
            if (patientMessChannels?.Any() ?? false)
            {
                foreach (var channel in patientMessChannels)
                {
                    channel.PatientId = ptIdString;
                    channel.UpdatedBy = userId.ToString();
                    channel.UpdatedAt = japanNow;
                }
            }

            var paymentClinicDetails = TrackingDataContext.PaymentClinicDetails
                .Where(c => c.HospitalId == hpId && c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (paymentClinicDetails?.Any() ?? false)
            {
                foreach (var detail in paymentClinicDetails)
                {
                    detail.PatientId = ptIdInt;
                    detail.UpdatedBy = userId.ToString();
                    detail.UpdatedAt = japanNow;
                }
            }

            var paymentClinicDetailHistories = TrackingDataContext.PaymentClinicDetailHistories
                .Where(c => c.HospitalId == hpId && c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (paymentClinicDetailHistories?.Any() ?? false)
            {
                foreach (var detail in paymentClinicDetailHistories)
                {
                    detail.PatientId = ptIdInt;
                    detail.UpdatedBy = userId.ToString();
                    detail.UpdatedAt = japanNow;
                }
            }

            var paymentPharmacyDetails = TrackingDataContext.PaymentPharmacyDetails
                .Where(c => c.HospitalId == hpId && c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (paymentClinicDetailHistories?.Any() ?? false)
            {
                foreach (var detail in paymentPharmacyDetails)
                {
                    detail.PatientId = ptIdInt;
                    detail.UpdatedBy = userId.ToString();
                    detail.UpdatedAt = japanNow;
                }
            }

            var pharmacyReserves = TrackingDataContext.PharmacyReserves
                .Where(c => c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (pharmacyReserves?.Any() ?? false)
            {
                foreach (var detail in pharmacyReserves)
                {
                    detail.PatientId = ptIdInt;
                    detail.UpdatedBy = userId.ToString();
                    detail.UpdatedAt = japanNow;
                }
            }

            var pharmacyReserveDetails = TrackingDataContext.PharmacyReserveDetails
                .Where(c => c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (pharmacyReserveDetails?.Any() ?? false)
            {
                foreach (var detail in pharmacyReserveDetails)
                {
                    detail.PatientId = ptIdInt;
                    detail.UpdatedBy = userId.ToString();
                    detail.UpdatedAt = japanNow;
                }
            }

            var paymentClinicDetailHis = TrackingDataContext.PaymentPharmacyDetailHistories
                .Where(c => c.HospitalId == hpId && c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (paymentClinicDetailHis?.Any() ?? false)
            {
                foreach (var history in paymentClinicDetailHis)
                {
                    history.PatientId = ptIdInt;
                    history.UpdatedBy = userId.ToString();
                    history.UpdatedAt = japanNow;
                }
            }

            var pharmacyPatientFiles = TrackingDataContext.PharmacyPatientFiles
                .Where(c => c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (pharmacyPatientFiles?.Any() ?? false)
            {
                foreach (var file in pharmacyPatientFiles)
                {
                    file.PatientId = ptIdInt;
                    file.UpdatedBy = userId.ToString();
                    file.UpdatedAt = japanNow;
                }
            }

            var surveyAnswers = TrackingDataContext.SurveyAnswers
                .Where(c => c.HospitalId == hpId && c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (surveyAnswers?.Any() ?? false)
            {
                foreach (var answer in surveyAnswers)
                {
                    answer.PatientId = ptIdInt;
                    answer.UpdatedBy = userId.ToString();
                    answer.UpdatedAt = japanNow;
                }
            }

            var tasks = TrackingDataContext.Tasks
                .Where(c => c.HospitalId == hpId && c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (tasks?.Any() ?? false)
            {
                foreach (var task in tasks)
                {
                    task.PatientId = ptIdInt;
                    task.UpdatedBy = userId.ToString();
                    task.UpdatedAt = japanNow;
                }
            }


            var reserves = TrackingDataContext.Reserves
                .Where(c => c.PatientId == portalPtId && c.IsDeleted == DeleteTypes.None);
            if (reserves?.Any() ?? false)
            {
                foreach (var reserve in reserves)
                {
                    reserve.PatientId = ptIdInt;
                    reserve.UpdatedBy = userId.ToString();
                    reserve.UpdatedAt = japanNow;
                }
            }

            var reserveDetails = TrackingDataContext.ReserveDetails
                .Where(c => c.PatientId == Convert.ToUInt32(portalPtId) && c.IsDeleted == DeleteTypes.None);
            if (reserveDetails?.Any() ?? false)
            {
                foreach (var reserveDetail in reserveDetails)
                {
                    reserveDetail.PatientId = aiChartPtId;
                    reserveDetail.UpdatedBy = userId.ToString();
                    reserveDetail.UpdatedAt = japanNow;
                }
            }

            var patientMessages = TrackingDataContext.PatientMessages
                    .Where(c => c.HospitalId == hpId && c.PostedMember == portalPtId.ToString() && c.IsPatient == 1 && c.IsDeleted == DeleteTypes.None);
            if (patientMessages?.Any() ?? false)
            {
                var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                TimeSpan elapsed = japanNow - unixEpoch;
                foreach (var patientMessage in patientMessages)
                {
                    patientMessage.PostedMember = aiChartPtId.ToString();
                    patientMessage.UpdatedBy = userId.ToString();
                    patientMessage.UpdatedAt = ((long)elapsed.TotalMilliseconds).ToString();
                }
            }

            var newPatientMessageChannelMembers = new List<PatientMessageChannelMember>();
            var patientMessageChannelMembers = NoTrackingDataContext.PatientMessageChannelMembers
                .Where(c => c.MemberId == portalPtId.ToString() && c.IsPatient == 1 && c.IsDeleted == DeleteTypes.None);

            TrackingDataContext.PatientMessageChannelMembers.RemoveRange(patientMessageChannelMembers);
            TrackingDataContext.SaveChanges();

            if (patientMessageChannelMembers?.Any() ?? false)
            {
                foreach (var patientMessageChannelMember in patientMessageChannelMembers)
                {
                    var newPatientChannelMember = new PatientMessageChannelMember();
                    newPatientChannelMember = patientMessageChannelMember;
                    newPatientChannelMember.MemberId = aiChartPtId.ToString();
                    newPatientChannelMember.UpdatedBy = userId.ToString();
                    newPatientChannelMember.UpdatedAt = japanNow;

                    newPatientMessageChannelMembers.Add(newPatientChannelMember);
                }

                TrackingDataContext.PatientMessageChannelMembers.AddRange(newPatientMessageChannelMembers);
            }

            return true;
        }

        public ReceptionModel GetHeader(int hpId, long raiinNo, bool flag = false)
        {
            var receptionEntity = NoTrackingDataContext.RaiinInfs.FirstOrDefault(r => r.HpId == hpId && r.RaiinNo == raiinNo && (!flag || r.IsDeleted == 0));
            var raiinCommentInf = NoTrackingDataContext.RaiinCmtInfs.FirstOrDefault(r => r.HpId == hpId && r.RaiinNo == raiinNo);

            var tantoId = receptionEntity?.TantoId;
            var tantoInf = tantoId.HasValue
                ? NoTrackingDataContext.UserMsts.FirstOrDefault(u => u.UserId == tantoId && u.HpId == hpId)
                : null;

            var treatmentDepartmentId = receptionEntity?.TreatmentDepartmentId;
            var treatmentDepartment = treatmentDepartmentId.HasValue
                ? NoTrackingDataContext.TreatmentDepartments.FirstOrDefault(t => t.TreatmentDepartmentId == treatmentDepartmentId && t.HospitalId == hpId)
                : null;

            return new ReceptionModel
                (
                    receptionEntity?.HpId ?? 0,
                    receptionEntity?.PtId ?? 0,
                    receptionEntity?.SinDate ?? 0,
                    receptionEntity?.RaiinNo ?? 0,
                    receptionEntity?.OyaRaiinNo ?? 0,
                    receptionEntity?.HokenPid ?? 0,
                    receptionEntity?.SanteiKbn ?? 0,
                    receptionEntity?.Status ?? RaiinState.Reservation,
                    receptionEntity?.IsYoyaku ?? 0,
                    receptionEntity?.YoyakuTime ?? string.Empty,
                    receptionEntity?.YoyakuId ?? 0,
                    receptionEntity?.UketukeSbt ?? 0,
                    receptionEntity?.UketukeTime ?? string.Empty,
                    receptionEntity?.UketukeId ?? 0,
                    receptionEntity?.UketukeNo ?? 0,
                    receptionEntity?.SinStartTime ?? string.Empty,
                    receptionEntity?.SinEndTime ?? string.Empty,
                    receptionEntity?.KaikeiTime ?? string.Empty,
                    receptionEntity?.KaikeiId ?? 0,
                    receptionEntity?.TreatmentDepartmentId ?? 0,
                    receptionEntity?.TantoId ?? 0,
                    receptionEntity?.SyosaisinKbn ?? 0,
                    receptionEntity?.JikanKbn ?? 0,
                    raiinCommentInf?.Text ?? string.Empty,
                    tantoInf?.Sname ?? string.Empty,
                    treatmentDepartment?.Title ?? string.Empty,
                    receptionEntity?.ReserveDetailId ?? 0,
                    receptionEntity?.TreatmentDepartmentId ?? 0
                );
        }

        public (long, bool) UpdatePrescription(int hpId, long raiinNo, int userId, int prescriptionIssueType, int printEpsReference, bool? checkStatus = true)
        {
            var raiinInf = TrackingDataContext.RaiinInfs.FirstOrDefault(c => c.HpId == hpId && c.RaiinNo == raiinNo && c.IsDeleted == DeleteTypes.None);
            if (raiinInf == null)
            {
                return (0, false);
            }

            if (checkStatus is not null && checkStatus.Value)
            {
                var epsPrescription = NoTrackingDataContext.EpsPrescriptions.FirstOrDefault(c => c.HpId == hpId && c.PtId == raiinInf.PtId && c.RaiinNo == raiinInf.RaiinNo && c.Status == 0);

                var statusNotEditPrescription = new List<int>() { RaiinState.Reservation, RaiinState.Confirmation, RaiinState.Deleted };
                if (epsPrescription != null || (statusNotEditPrescription.Contains(raiinInf.Status)))
                {
                    return (0, false);
                }

                if (prescriptionIssueType == 2)
                {
                    printEpsReference = 2;
                }
            }

            raiinInf.PrescriptionIssueType = prescriptionIssueType;
            raiinInf.PrintEpsReference = printEpsReference;
            raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
            raiinInf.UpdateId = userId;

            TrackingDataContext.SaveChanges();
            return (raiinInf.RaiinNo, true);
        }

        public List<long> UpdateBookingInfo(int hpId, List<int> reserveDetailIds, int sinDate, string yoyakuTime, string yoyakuEndTime, int treatmentDepartmentId, int userId)
        {
            var raiinInfs = TrackingDataContext.RaiinInfs.Where(e => e.HpId == hpId && reserveDetailIds.Contains(e.ReserveDetailId ?? 0)).ToList();
            if (raiinInfs.Any(e => e.Status != RaiinState.Reservation)) return new();

            var kaId = _commonRepository.GetKaIdByTreatmentDepartmentId(hpId, treatmentDepartmentId);

            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    var japanDate = CIUtil.GetJapanDateTimeNow();
                    raiinInfs.ForEach(e =>
                    {
                        e.SinDate = sinDate;
                        e.YoyakuTime = yoyakuTime;
                        e.YoyakuEndTime = yoyakuEndTime;
                        e.YoyakuId = userId;
                        e.UpdateId = userId;
                        e.UpdateDate = japanDate;
                        e.KaId = kaId;
                        e.TreatmentDepartmentId = treatmentDepartmentId;
                    });

                    var listRaiinNo = raiinInfs.Select(e => e.RaiinNo).ToList();

                    var karteEditons = TrackingDataContext.KarteEditions.Where(e => e.HpId == hpId && listRaiinNo.Contains(e.RaiinNo)).ToList();
                    karteEditons.ForEach(e =>
                    {
                        e.SinDate = sinDate;
                        e.UpdateId = userId;
                        e.UpdateDate = japanDate;
                    });

                    var karteInfs = TrackingDataContext.KarteInfs.Where(e => e.HpId == hpId && listRaiinNo.Contains(e.RaiinNo)).ToList();
                    karteInfs.ForEach(e =>
                    {
                        e.SinDate = sinDate;
                        e.UpdateId = userId;
                        e.UpdateDate = japanDate;
                    });

                    var ordInfs = TrackingDataContext.OdrInfs.Where(e => e.HpId == hpId && listRaiinNo.Contains(e.RaiinNo)).ToList();
                    ordInfs.ForEach(e =>
                    {
                        e.SinDate = sinDate;
                        e.UpdateId = userId;
                        e.UpdateDate = japanDate;
                    });

                    var raiinKbnInfs = TrackingDataContext.RaiinKbnInfs.Where(e => e.HpId == hpId && listRaiinNo.Contains(e.RaiinNo) && e.IsDelete == DeleteTypes.None).ToList();
                    raiinKbnInfs.ForEach(e =>
                    {
                        e.SinDate = sinDate;
                        e.UpdateId = userId;
                        e.UpdateDate = japanDate;
                    });

                    var filingInfs = TrackingDataContext.FilingInf.Where(e => e.HpId == hpId && listRaiinNo.Contains(e.RaiinNo)).ToList();
                    filingInfs.ForEach(e =>
                    {
                        e.SinDate = sinDate;
                        e.UpdateId = userId;
                        e.UpdateDate = japanDate;
                    });

                    var odrInfDetails = TrackingDataContext.OdrInfDetails.Where(e => e.HpId == hpId && listRaiinNo.Contains(e.RaiinNo)).ToList();
                    odrInfDetails.ForEach(e =>
                    {
                        e.SinDate = sinDate;
                    });

                    TrackingDataContext.SaveChanges();
                    transaction.Commit();
                    return listRaiinNo;
                }
                catch
                {
                    transaction.Rollback();
                    return new();
                }

            });

        }

        public bool CheckRaiinInf(int hpId, long raiinNo, bool flag = false)
        {
            var receptionEntity = NoTrackingDataContext.RaiinInfs.FirstOrDefault(r => r.HpId == hpId && r.RaiinNo == raiinNo && (!flag || r.IsDeleted == 0));
            if (receptionEntity == null)
            {
                return false;
            }

            return true;
        }

        public (int prescriptionIssueType, int printEpsReference) GetDefaultPrescription(int hpId, long ptId, int sinDate, long raiinNo, int sysConfVal)
        {
            var prescriptionIssueType = GetDefaultPrescriptionIssueType(hpId, ptId, sinDate);
            var printEpsReference = GetDefaultPrintEPSReference(hpId, ptId, raiinNo, sysConfVal);
            return (prescriptionIssueType, printEpsReference);
        }

        private int GetDefaultPrescriptionIssueType(int hpId, long ptId, int sinDate)
        {
            //(A) 同日来院の発行形態
            var visitingInSameDay = NoTrackingDataContext.RaiinInfs.Where(x => x.PtId == ptId && x.SinDate == sinDate && x.IsDeleted == 0 && x.PrescriptionIssueType != 0)
                                                                .OrderByDescending(x => x.UketukeTime)
                                                                .FirstOrDefault();
            if (visitingInSameDay != null)
            {
                return visitingInSameDay.PrescriptionIssueType;
            }

            //(B) オン資端末で選択された発行形態
            var confirmationHistorysInSameDay = NoTrackingDataContext.OnlineConfirmationHistories.Where(x => x.PtId == ptId && x.PrescriptionIssueType != 0)
                        .AsEnumerable()
                        .Where(x => CIUtil.DateTimeToInt(x.OnlineConfirmationDate) == sinDate)
                        .OrderByDescending(x => x.OnlineConfirmationDate)
                        .FirstOrDefault();
            if (confirmationHistorysInSameDay != null)
            {
                return confirmationHistorysInSameDay.PrescriptionIssueType;
            }

            //(C) オンライン診療のマイナ在宅受付Webで選択された発行形態
            var prescriptionAgreedOnline = NoTrackingDataContext.OnlineAgreedPrescriptions.Where(x => x.PtId == ptId
                                                                                                        && x.HpId == hpId
                                                                                                        && x.ConsType == 1
                                                                                                        && x.PrescriptionIssueType != 0)
                                                                                        .AsEnumerable()
                                                                                        .Where(x => CIUtil.DateTimeToInt(x.ConsDate) <= sinDate
                                                                                                && CIUtil.DateTimeToInt(x.LimitDate) >= sinDate)
                                                                                        .OrderByDescending(x => x.ConsDate)
                                                                                        .FirstOrDefault();
            if (prescriptionAgreedOnline != null)
            {
                return prescriptionAgreedOnline.PrescriptionIssueType;
            }

            //(D) 訪問診療のマイナ在宅受付Webで選択された発行形態
            var patientHoumonAgreed = NoTrackingDataContext.PtInfs.Where(x => x.HoumonAgreed == 1
                                                                                                    && x.PtId == ptId
                                                                                                    && x.HpId == hpId
                                                                                                    && x.IsDelete != 1).FirstOrDefault();
            if (patientHoumonAgreed != null)
            {
                var prescriptionAgreedAtHome = NoTrackingDataContext.OnlineAgreedPrescriptions.Where(x => x.PtId == patientHoumonAgreed.PtId
                                                                                                    && x.ConsType == 0
                                                                                                    && x.PrescriptionIssueType != 0)
                                                                                            .AsEnumerable()
                                                                                            .Where(x => CIUtil.DateTimeToInt(x.ConsDate) <= sinDate
                                                                                                    && CIUtil.DateTimeToInt(x.LimitDate) >= sinDate)
                                                                                            .OrderByDescending(x => x.ConsDate)
                                                                                            .FirstOrDefault();
                if (prescriptionAgreedAtHome != null)
                {
                    return prescriptionAgreedAtHome.PrescriptionIssueType;
                }
            }

            //(E) 前回来院と同じ発行形態
            var lastVisiting = NoTrackingDataContext.RaiinInfs.Where(x => x.PtId == ptId && x.SinDate < sinDate && x.IsDeleted == 0 && x.PrescriptionIssueType != 0)
                                                           .OrderByDescending(x => x.SinDate)
                                                           .ThenByDescending(x => x.UketukeTime)
                                                           .FirstOrDefault();
            if (lastVisiting != null)
            {
                return lastVisiting.PrescriptionIssueType == PrescriptionIssueSelect.Electronic ? PrescriptionIssueSelect.Electronic : PrescriptionIssueSelect.Paper;
            }

            //(F) 紙
            return PrescriptionIssueSelect.Paper;
        }

        private int GetDefaultPrintEPSReference(int hpId, long ptId, long raiinNo, int sysConfVal)
        {
            var defaultPrintEPSReference = 0;
            // (A) 発行形態が電子である直近の来院の処方内容（控え）印刷要否
            RaiinInf? latestElectronicPrescription = NoTrackingDataContext.RaiinInfs
                                                    .Where(item => item.HpId == hpId && item.PtId == ptId && item.PrescriptionIssueType == 1 && item.IsDeleted == 0 && item.RaiinNo != raiinNo)
                                                    .OrderByDescending(p => p.SinDate)
                                                    .FirstOrDefault();
            if (latestElectronicPrescription != null &&
                (latestElectronicPrescription.PrintEpsReference == 1 ||
                latestElectronicPrescription.PrintEpsReference == 2))
            {
                defaultPrintEPSReference = latestElectronicPrescription.PrintEpsReference;
            }
            else
            {
                // (B) システム設定
                defaultPrintEPSReference = sysConfVal;
            }

            return defaultPrintEPSReference;
        }

        public bool CheckLinkCard(int hpId)
        {
            var renkeiMst = NoTrackingDataContext.RenkeiMsts.FirstOrDefault(e => e.HpId == hpId && e.RenkeiId == 290 && e.IsInvalid == 0);

            var renkeiConf = renkeiMst != null
                ? NoTrackingDataContext.RenkeiConfs.FirstOrDefault(e => e.HpId == hpId && e.RenkeiId == renkeiMst.RenkeiId && e.IsInvalid == 0)
                : null;

            var isLinkCard = renkeiMst != null && renkeiConf != null
                ? NoTrackingDataContext.RenkeiPathConfs.Any(e => e.HpId == hpId && e.RenkeiId == renkeiMst.RenkeiId && e.SeqNo == renkeiConf.SeqNo && e.IsInvalid == 0)
                : false;

            return isLinkCard;
        }

        public int ValidateReservation(int hpId, int userId, long ptId, long raiinNo, int sinDate)
        {
            var raiinInf = NoTrackingDataContext.RaiinInfs
                    .FirstOrDefault(r => r.HpId == hpId
                        && r.PtId == ptId
                        && r.SinDate == sinDate
                        && r.RaiinNo == raiinNo);
            if (raiinInf is null) return 1;
            if (raiinInf.IsDeleted == DeleteTypes.Deleted) return 2;
            return 0;
        }

        public (bool Result, int SinDate, List<long> ListRaiinNo) UpdateRaiinCmtByBooking(int hpId, int reserveDetailId, int userId, string raiinCmt)
        {
            var raiinInfs = TrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId && item.ReserveDetailId == reserveDetailId).ToList();
            if (raiinInfs.Count <= 0 || raiinInfs.Any(item => item.Status != RaiinState.Reservation)) return new();

            var japanDate = CIUtil.GetJapanDateTimeNow();
            var listRaiinNo = raiinInfs.Select(item => item.RaiinNo).ToList();

            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    var raiinCmtInfs = TrackingDataContext.RaiinCmtInfs.Where(item => item.HpId == hpId && listRaiinNo.Contains(item.RaiinNo) && item.IsDelete == DeleteTypes.None).ToList();
                    raiinCmtInfs.ForEach(item =>
                    {
                        item.Text = raiinCmt;
                        item.UpdateId = userId;
                        item.UpdateDate = japanDate;
                    });

                    if (!string.IsNullOrWhiteSpace(raiinCmt))
                    {
                        var listRaiinInfInsert = raiinInfs.ExceptBy(raiinCmtInfs.Select(item => item.RaiinNo), item => item.RaiinNo);
                        foreach (var raiinInf in listRaiinInfInsert)
                        {
                            var raiinCmtInf = new RaiinCmtInf()
                            {
                                HpId = hpId,
                                RaiinNo = raiinInf.RaiinNo,
                                UpdateDate = japanDate,
                                CreateDate = japanDate,
                                UpdateId = userId,
                                CreateId = userId,
                                PtId = raiinInf.PtId,
                                SinDate = raiinInf.SinDate,
                                CmtKbn = CmtKbns.Comment,
                                Text = raiinCmt
                            };
                            TrackingDataContext.RaiinCmtInfs.Add(raiinCmtInf);
                        }
                    }

                    TrackingDataContext.SaveChanges();
                    transaction.Commit();
                    return (true, raiinInfs[0].SinDate, listRaiinNo);
                }
                catch
                {
                    transaction.Rollback();
                    return new();
                }
            });
        }
    }
}
