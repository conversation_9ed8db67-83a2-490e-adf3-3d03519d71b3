﻿using System.Data;
using Domain.Enum;
using Domain.Models.FcoLink;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Enum;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;


namespace Infrastructure.Repositories
{
    public class FcoLinkRepository : RepositoryBase, IFcoLinkRepository
    {
        public FcoLinkRepository(ITenantProvider tenantProvider) : base(tenantProvider) { 

        }

        public bool IsExistsFcoApiKeys(int hpId, string apiKey)
        {
            try
            {
                var query = NoTrackingDataContext.FcoApiKeys
                    .Where(f => f.HpId == hpId && f.ApiKey == apiKey && f.IsDeleted == false);
                var result = query.FirstOrDefault() != null;

                return result;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public PatientInfoModel SearchPtInfByPtNum(long ptNum, int hpId)
        {
            var ptInfQuery = NoTrackingDataContext.PtInfs.Where(x => x.HpId == hpId && x.PtNum == ptNum && x.IsDelete == 0);

            var result = ptInfQuery.FirstOrDefault();
            if (result == null)
            {
                return new PatientInfoModel();
            }
            return new PatientInfoModel(result.HpId, result.PtId, result.PtNum, result.KanaName ?? string.Empty, result.Name ?? string.Empty, result.OfficeName ?? string.Empty);
        }

        public List<SeikyuModel> GetSeikyuInfList(int hpId, long ptId, int? sinDate)
        {
            // FCO精算待ちの情報を取得
            var raiinfQuery = NoTrackingDataContext.RaiinInfs.Where(item => 
                item.PtId == ptId &&
                item.HpId == hpId &&
                item.IsDeleted == 0 &&
                item.Status == RaiinState.FcoWaiting
            );

            // sinDate が null でなければ条件を追加
            if (sinDate.HasValue)
            {
                raiinfQuery = raiinfQuery.Where(item => item.SinDate == sinDate.Value);
            }

            var kaikeiInfQuery = NoTrackingDataContext.KaikeiInfs;
            var kaMstsQuery = NoTrackingDataContext.KaMsts.Where(item => item.IsDeleted == 0);

            var query = from raiinInf in raiinfQuery
                        join kaikeiInf in kaikeiInfQuery
                        on new { raiinInf.HpId, raiinInf.PtId, raiinInf.SinDate, raiinInf.RaiinNo }
                        equals new { kaikeiInf.HpId, kaikeiInf.PtId, kaikeiInf.SinDate, kaikeiInf.RaiinNo }
                        join kaMsts in kaMstsQuery.DefaultIfEmpty()
                        on new { raiinInf.HpId, raiinInf.KaId }
                        equals new { kaMsts.HpId, kaMsts.KaId }
                        select new
                        {
                            RaiinInf = raiinInf,
                            KaikeiInf = kaikeiInf,
                            KaMst = kaMsts
                        };

            var listSeikyuInf = query.ToList();

            if (!listSeikyuInf.Any()) 
            {
                return new List<SeikyuModel>();
            }

            List<SeikyuModel> listSeikyuInfModel = new();
            foreach (var model in listSeikyuInf)
            {
                listSeikyuInfModel.Add(ConvertToSeikyuModel(model.RaiinInf, model.KaikeiInf, model.KaMst.KaName ?? string.Empty));
            }   

            return listSeikyuInfModel;
        }

        public List<OrdInfModel> GetOdrInfList(int hpId, long ptId, long raiinNo, int sinDate)
        {

            var odrInfList = NoTrackingDataContext.OdrInfs.Where(odr => odr.HpId == hpId && odr.PtId == ptId 
            && odr.RaiinNo == raiinNo && odr.IsDeleted == 0);

            var odrInfModel = new List<OrdInfModel>();
            foreach (var model in odrInfList)
            {
                var allOdrInfDetails = NoTrackingDataContext.OdrInfDetails.Where(o => o.HpId == hpId && o.PtId == ptId && o.SinDate == sinDate 
                && o.RaiinNo == raiinNo && o.RpNo == model.RpNo && model.RpEdaNo == o.RpEdaNo)?.ToList();
                var odrInfDetailModels = allOdrInfDetails?.Select(od => ConvertToDetailModel(od)).ToList();
                odrInfModel.Add(ConvertToModel(model, odrInfDetailModels ?? new List<OrdInfDetailModel>()));
            }

            return odrInfModel;
        }

        public List<SinKouiDetailModel> GetSinKouiList(int hpId, long ptId, long raiinNo, int sinDate)
        {
            int sinYM = sinDate / 100;
            int sinDay = sinDate - sinYM * 100;
            var sinKouiCountQuery = NoTrackingDataContext.SinKouiCounts
                .Where(s => s.HpId == hpId && s.PtId == ptId && s.RaiinNo == raiinNo && s.SinDay == sinDay);

            // 院外処方除く,消費税,消費税軽減,内税,内税軽減のぞく
            var excludedCdKbns = new[] { "SZ", "SK", "UK", "UZ" };
            var sinKouiQuery = NoTrackingDataContext.SinKouis
                .Where(s => s.HpId == hpId && s.PtId == ptId 
                && s.InoutKbn != (int)InouKbnEnums.OutHospital && !excludedCdKbns.Contains(s.CdKbn));

            var sinRpInfQuery = NoTrackingDataContext.SinRpInfs;

            // "IS_NODSP_RYOSYU"" <> 1" '領収証非表示項目を除く
            var sinKouiDetailQuery = NoTrackingDataContext.SinKouiDetails
                .Where(s => s.HpId == hpId && s.PtId == ptId && s.IsNodspRyosyu != 1 );

            // 点数マスタ
            var TenMstQuery =  NoTrackingDataContext.TenMsts.Where(ten => ten.HpId == hpId && ten.IsDeleted == 0);
            var TenMstMaxQuery = from tmKey in TenMstQuery
                where tmKey.HpId == hpId
                    && tmKey.StartDate <= sinDate
                    && tmKey.EndDate >= sinDate
                group tmKey by new { tmKey.HpId, tmKey.ItemCd } into g
                select new { g.Key.HpId, g.Key.ItemCd, StartDateMax = g.Max(x => x.StartDate) };

            // 請求情報明細作成
            var sinKouiCountDetail = from sinKouiCount in sinKouiCountQuery
                join sinKoui in sinKouiQuery
                on new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.RpNo, sinKouiCount.SinYm }
                equals new { sinKoui.HpId, sinKoui.PtId, sinKoui.RpNo, sinKoui.SinYm }
                
                join sinRpInf in sinRpInfQuery
                on new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.RpNo, sinKouiCount.SinYm }
                equals new { sinRpInf.HpId, sinRpInf.PtId, sinRpInf.RpNo, sinRpInf.SinYm }
                
                join sinKouiDetail in sinKouiDetailQuery
                on new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.RpNo, sinKouiCount.SinYm }
                equals new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.RpNo, sinKouiDetail.SinYm }
                
                join tmKey in TenMstMaxQuery
                on new { sinKouiDetail.HpId, sinKouiDetail.ItemCd }
                equals new { tmKey.HpId, tmKey.ItemCd }

                join tenMst in TenMstQuery
                on new { tmKey.HpId, tmKey.ItemCd, StartDate = tmKey.StartDateMax } 
                equals new { tenMst.HpId, tenMst.ItemCd, tenMst.StartDate } 

                orderby sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.SinYm, sinKouiCount.SinDay, 
                        sinRpInf.SinId, sinRpInf.CdNo, sinRpInf.FirstDay, sinKoui.RpNo, sinKoui.SeqNo, sinKouiDetail.RowNo
                
                select new
                {
                    Sinkouis = sinKoui,
                    SinKouiCount = sinKouiCount,
                    SinKouiDetail = sinKouiDetail,
                    TenMst = tenMst
                };

            var sinKouiModel = new List<SinKouiDetailModel>();
            foreach (var model in sinKouiCountDetail)
            {
                var cmtSbt = TenMstQuery.Where(item => item.ItemCd == model.SinKouiDetail.ItemCd).FirstOrDefault()?.CmtSbt ?? 0;
                sinKouiModel.Add(new SinKouiDetailModel(model.SinKouiCount.RaiinNo, model.SinKouiCount.SinDate, model.Sinkouis.CdKbn ?? string.Empty,
                        model.Sinkouis.HokenId, model.Sinkouis.RpNo, model.SinKouiCount.SeqNo, model.Sinkouis.EntenKbn,
                        model.Sinkouis.Ten, model.SinKouiCount.Count, model.SinKouiDetail.ItemCd ?? string.Empty, model.SinKouiDetail.ItemName ?? string.Empty,
                        model.SinKouiDetail.Suryo, model.SinKouiDetail.UnitName ?? string.Empty, model.SinKouiDetail.RecId ?? string.Empty, model.TenMst.CmtSbt));
            }
            return sinKouiModel;
        }

        public List<SyunoSeikyuModel> GetListSeikyuInf(int hpId, long ptId, List<long> raiinNoList)
        {
            List<RaiinInf> listRaiinInf;

            //来院番号からの取得
            listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(
                item => item.HpId == hpId && item.PtId == ptId && raiinNoList.Contains(item.RaiinNo) && item.IsDeleted == 0).ToList();
            //データ存在チェック
            if (listRaiinInf == null || !listRaiinInf.Any())
            {
                return new List<SyunoSeikyuModel>();
            }
            
            //指定来院番号件数チェック
            if (listRaiinInf.Count != raiinNoList.Count)
            {
                return new List<SyunoSeikyuModel>();
            }

             IEnumerable<SyunoSeikyu> syunoSeikyuRepo;

            syunoSeikyuRepo = NoTrackingDataContext.SyunoSeikyus
                .Where(item =>
                    item.HpId == hpId && item.PtId == ptId && raiinNoList.Contains(item.RaiinNo))
                .OrderBy(item => item.SinDate).ThenBy(item => item.RaiinNo);

            var raiinInfRepo = NoTrackingDataContext.RaiinInfs.Where(item =>
                item.HpId == hpId && item.PtId == ptId && raiinNoList.Contains(item.RaiinNo) && item.IsDeleted == 0);

            var querySyuno = from syunoSeikyu in syunoSeikyuRepo
                             join raiinInf in raiinInfRepo on
                                 new { syunoSeikyu.HpId, syunoSeikyu.PtId, syunoSeikyu.SinDate, syunoSeikyu.RaiinNo } equals
                                 new { raiinInf.HpId, raiinInf.PtId, raiinInf.SinDate, raiinInf.RaiinNo }
                             select new
                             {
                                 SyunoSeikyu = syunoSeikyu,
                                 RaiinInf = raiinInf,
                                 hokenPid = raiinInf.HokenPid
                             };

            var kaikeiInfRepo = NoTrackingDataContext.KaikeiInfs.Where(item =>
                item.HpId == hpId && item.PtId == ptId);

            var query = from syuno in querySyuno
                        join kaikeiInf in kaikeiInfRepo on
                            new { syuno.SyunoSeikyu.HpId, syuno.SyunoSeikyu.PtId, syuno.SyunoSeikyu.SinDate, syuno.SyunoSeikyu.RaiinNo } equals
                            new { kaikeiInf.HpId, kaikeiInf.PtId, kaikeiInf.SinDate, kaikeiInf.RaiinNo } into
                            listKaikeInf
                        select ConvertToSyunoSeikyuModel(syuno.SyunoSeikyu, syuno.RaiinInf, listKaikeInf?.FirstOrDefault()?.TotalPtFutan ?? 0);

            return query.ToList();
        }

        public SyunoSeikyuModel? FcoSaveNyukin(SyunoSeikyuModel syunoSeikyu, int depositMethod)
        {
            // FCO連携時の更新者は0固定
            var userId = 0;
            var now = CIUtil.GetJapanDateTimeNow();
            var dateInt = int.Parse(now.ToString("yyyyMMdd"));
            var kaikeiTime = now.ToString("HHmmss");

            // Syuno_Seikyu更新
            var updateSeikyu = UpdateStatusSyunoSeikyu(syunoSeikyu, userId);
            if(updateSeikyu == null){
                return null;
            }

            // Syuno_Nyukin更新
            var updateNyukin = UpdateSyunoNyukin(syunoSeikyu, userId, dateInt, depositMethod);
            if(updateNyukin == null){
                return null;
            }

            // Raiin_Inf更新
            var updateRaiininf = UpdateStatusRaiinInf(syunoSeikyu.HpId, syunoSeikyu.PtId, syunoSeikyu.RaiinNo, userId, kaikeiTime);
            if(updateRaiininf == null){
                return null;
            }

            syunoSeikyu.NyukinDate = dateInt;
            syunoSeikyu.NyukinGaku = syunoSeikyu.SeikyuGaku;

            return syunoSeikyu;
        }

       private static OrdInfModel ConvertToModel(OdrInf ordInf, List<OrdInfDetailModel> ordInfoDetailList)
        {
            return new OrdInfModel(ordInf.HpId,
                        ordInf.RaiinNo,
                        ordInf.RpNo,
                        ordInf.RpEdaNo,
                        ordInf.PtId,
                        ordInf.SinDate,
                        ordInf.HokenPid,
                        ordInf.OdrKouiKbn,
                        ordInf.RpName ?? string.Empty,
                        ordInf.InoutKbn,
                        ordInf.SikyuKbn,
                        ordInf.SyohoSbt,
                        ordInf.SanteiKbn,
                        ordInf.TosekiKbn,
                        ordInf.DaysCnt,
                        ordInf.SortNo,
                        ordInf.IsDeleted,
                        ordInf.Id,
                        ordInfoDetailList,
                        ordInf.CreateDate,
                        ordInf.CreateId,
                        string.Empty,
                        ordInf.UpdateDate,
                        ordInf.UpdateId,
                        string.Empty,
                        ordInf.CreateMachine ?? string.Empty,
                        ordInf.UpdateMachine ?? string.Empty
                   );
        }

        private OrdInfDetailModel ConvertToDetailModel(OdrInfDetail ordInfDetail)
        {
            return new OrdInfDetailModel(
                            ordInfDetail.HpId,
                            ordInfDetail.RaiinNo,
                            ordInfDetail.RpNo,
                            ordInfDetail.RpEdaNo,
                            ordInfDetail.RowNo,
                            ordInfDetail.PtId,
                            ordInfDetail.SinDate,
                            ordInfDetail.SinKouiKbn,
                            ordInfDetail.ItemCd ?? string.Empty,
                            ordInfDetail.ItemName ?? string.Empty
                );
        }

       private SeikyuModel ConvertToSeikyuModel(RaiinInf raiinInf, KaikeiInf kaikeiInf, string KaName)
        {
            return new SeikyuModel
                (
                    raiinInf.PtId,
                    raiinInf.SinDate,
                    raiinInf.RaiinNo,
                    raiinInf.OyaRaiinNo,
                    raiinInf.KaId,
                    KaName,
                    raiinInf.Status,
                    kaikeiInf.HpId,
                    kaikeiInf.HokenId,
                    kaikeiInf.Kohi1Id,
                    kaikeiInf.Kohi2Id,
                    kaikeiInf.Kohi3Id,
                    kaikeiInf.Kohi4Id,
                    kaikeiInf.HokenKbn,
                    kaikeiInf.HokenSbtCd,
                    kaikeiInf.ReceSbt ?? string.Empty,
                    kaikeiInf.Houbetu ?? string.Empty,
                    kaikeiInf.Kohi1Houbetu ?? string.Empty,
                    kaikeiInf.Kohi2Houbetu ?? string.Empty,
                    kaikeiInf.Kohi3Houbetu ?? string.Empty,
                    kaikeiInf.Kohi4Houbetu ?? string.Empty,
                    kaikeiInf.HonkeKbn,
                    kaikeiInf.HokenRate,
                    kaikeiInf.PtRate,
                    kaikeiInf.DispRate,
                    kaikeiInf.Tensu,
                    kaikeiInf.TotalIryohi,
                    kaikeiInf.PtFutan,
                    kaikeiInf.JihiFutan,
                    kaikeiInf.JihiTax,
                    kaikeiInf.JihiOuttax,
                    kaikeiInf.JihiFutanTaxfree,
                    kaikeiInf.JihiFutanTaxNr,
                    kaikeiInf.JihiFutanTaxGen,
                    kaikeiInf.JihiFutanOuttaxNr,
                    kaikeiInf.JihiFutanOuttaxGen,
                    kaikeiInf.JihiTaxNr,
                    kaikeiInf.JihiTaxGen,
                    kaikeiInf.JihiOuttaxNr,
                    kaikeiInf.JihiOuttaxGen,
                    kaikeiInf.JihiFutanSentei,
                    kaikeiInf.AdjustFutan,
                    kaikeiInf.AdjustRound,
                    kaikeiInf.TotalPtFutan,
                    kaikeiInf.AdjustFutanVal,
                    kaikeiInf.AdjustFutanRange,
                    kaikeiInf.AdjustRateVal,
                    kaikeiInf.AdjustRateRange
                );
        }

       private SyunoSeikyuModel ConvertToSyunoSeikyuModel(SyunoSeikyu syunoSeikyu, RaiinInf raiinInf, int totalPtFutan)
        {
            return new SyunoSeikyuModel
                (
                    raiinInf.HpId,
                    raiinInf.PtId,
                    raiinInf.SinDate,
                    raiinInf.Status,
                    raiinInf.RaiinNo,
                    raiinInf.OyaRaiinNo,
                    syunoSeikyu.NyukinKbn,
                    syunoSeikyu.SeikyuTensu,
                    syunoSeikyu.AdjustFutan,
                    syunoSeikyu.SeikyuGaku,
                    syunoSeikyu.SeikyuDetail ?? string.Empty,
                    syunoSeikyu.NewSeikyuTensu,
                    syunoSeikyu.NewAdjustFutan,
                    syunoSeikyu.NewSeikyuGaku,
                    raiinInf.UketukeSbt,
                    totalPtFutan,
                    syunoSeikyu.NewSeikyuDetail ?? string.Empty
                );
        }

        private RaiinInf? UpdateStatusRaiinInf(int hpId, long ptId, long raiinNo, int userId, string kaikeiTime)
        {
            var raiin = TrackingDataContext.RaiinInfs
                                   .FirstOrDefault(item => item.HpId == hpId
                                                  && item.PtId == ptId
                                                  && item.IsDeleted == DeleteTypes.None
                                                  && item.Status == RaiinState.FcoWaiting
                                                  && item.RaiinNo == raiinNo);
            if (raiin != null)
            {
                raiin.Status = RaiinState.Paid;
                raiin.KaikeiTime = kaikeiTime;
                raiin.UpdateDate = CIUtil.GetJapanDateTimeNow();
                raiin.UpdateId = userId;
                return raiin;
            }
            return null;
        }

        private SyunoSeikyu? UpdateStatusSyunoSeikyu(SyunoSeikyuModel model, int userId)
        {
            var seikyu = TrackingDataContext.SyunoSeikyus
                            .FirstOrDefault(item => item.PtId == model.PtId
                                            && item.SinDate == model.SinDate
                                            && item.RaiinNo == model.RaiinNo);
            if (seikyu != null)
            {
                seikyu.NyukinKbn = (int)NyukinKbnEnums.FullySettled;
                seikyu.NewSeikyuTensu = model.SeikyuTensu;
                seikyu.NewSeikyuGaku = model.SeikyuGaku;
                seikyu.NewSeikyuDetail = model.SeikyuDetail;
                seikyu.UpdateDate = CIUtil.GetJapanDateTimeNow();
                seikyu.UpdateId = userId;

                return seikyu;
            }
            return null;
        }
       private SyunoNyukin? UpdateSyunoNyukin(SyunoSeikyuModel model, int userId, int nyukiDate, int depositMethod)
        {
            var nyukin = TrackingDataContext.SyunoNyukin
                            .FirstOrDefault(item => item.PtId == model.PtId
                                            && item.SinDate == model.SinDate
                                            && item.RaiinNo == model.RaiinNo);
            if (nyukin != null)
            {
                nyukin.SinDate = model.SinDate;
                nyukin.NyukinGaku = model.SeikyuGaku;
                nyukin.PaymentMethodCd = (int)PaymentMethodCdEnum.FcoPayment;
                nyukin.NyukinDate = nyukiDate;
                nyukin.UpdateDate = CIUtil.GetJapanDateTimeNow();
                nyukin.UpdateId = userId;
                nyukin.DepositMethod = depositMethod;

                return nyukin;
            }
            return null;
        }

        public int SaveChanges()
        {
            return TrackingDataContext.SaveChanges();
        }

        public void ReleaseResource()
        {
            DisposeDataContext();
        }
    }
}
