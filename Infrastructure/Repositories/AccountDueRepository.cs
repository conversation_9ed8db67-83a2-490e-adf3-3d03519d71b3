﻿using Domain.Constant;
using Domain.Models.AccountDue;
using Domain.Models.Fincode;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Enum;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using Domain.Models.DenkaruApi;

namespace Infrastructure.Repositories;

public class AccountDueRepository : RepositoryBase, IAccountDueRepository
{

    private readonly IFincodeRepository _fincodeRepository;
    public AccountDueRepository(ITenantProvider tenantProvider, IFincodeRepository fincodeRepository) : base(tenantProvider)
    {
        _fincodeRepository = fincodeRepository;
    }

    public List<AccountDueModel> GetAccountDueList(int hpId, long ptId, int sinDate, bool isUnpaidChecked)
    {
        // Left table
        var seikyuList = NoTrackingDataContext.SyunoSeikyus
                        .Where(item => item.HpId == hpId
                                            && item.PtId == ptId).ToList();

        if (isUnpaidChecked && seikyuList.Count > 0)
        {
            seikyuList = seikyuList?.Where(item => item.NyukinKbn == 1 || !new List<int> { 1, 2, 3 }.Contains(item.NyukinKbn)).ToList();
        }

        // Right table
        var nyukinList = NoTrackingDataContext.SyunoNyukin
                               .Where(item => item.HpId == hpId
                                                   && item.PtId == ptId
                                                   && item.IsDeleted == 0).ToList();

        var raiinList = NoTrackingDataContext.RaiinInfs
                        .Where(item => item.HpId == hpId
                                            && item.PtId == ptId
                                            && item.IsDeleted == DeleteTypes.None
                                            && item.Status >= RaiinState.ConsultationCompleted).ToList();

        var kaMstList = NoTrackingDataContext.KaMsts
                        .Where(item => item.HpId == hpId
                                            && item.IsDeleted == 0).ToList();

        var accountDueList = (from seikyu in seikyuList
                              join nyukin in nyukinList on new { seikyu.HpId, seikyu.PtId, seikyu.SinDate, seikyu.RaiinNo }
                                                          equals new { nyukin.HpId, nyukin.PtId, nyukin.SinDate, nyukin.RaiinNo } into nyukinLeft
                              from nyukinItem in nyukinLeft.DefaultIfEmpty()
                              join raiinItem in raiinList on new { seikyu.HpId, seikyu.PtId, seikyu.RaiinNo }
                                                          equals new { raiinItem.HpId, raiinItem.PtId, raiinItem.RaiinNo }
                              join kaMst in kaMstList on new { raiinItem.KaId }
                                                          equals new { kaMst.KaId } into kaMstLeft
                              from kaMstItem in kaMstLeft.DefaultIfEmpty()
                              select ConvertToAccountDueListModel(hpId, ptId, seikyu, nyukinItem, raiinItem, kaMstItem)
                         )
                         .OrderBy(item => item.SeikyuSinDate)
                         .ThenBy(item => item.RaiinNo)
                         .ThenBy(item => item.SortNo)
                         .ToList();
        return accountDueList;
    }

    private AccountDueModel ConvertToAccountDueListModel(int hpId, long ptId, SyunoSeikyu seikyu, SyunoNyukin nyukin, RaiinInf raiinItem, KaMst kaMst)
    {
        int nyukinGaku = 0;
        if(nyukin != null)
        {
            nyukinGaku = nyukin.NyukinStatus == (int)NyukinStatusEnums.Success ? nyukin.NyukinGaku : nyukin.ExpectedNyukinGaku;
        }
        return new AccountDueModel
            (
                hpId,
                ptId,
                seikyu?.SinDate ?? 0,
                GetMonth(seikyu?.SinDate ?? 0),
                seikyu?.RaiinNo ?? 0,
                raiinItem?.HokenPid ?? 0,
                raiinItem?.OyaRaiinNo ?? 0,
                seikyu?.NyukinKbn ?? 0,
                seikyu?.SeikyuTensu ?? 0,
                seikyu?.SeikyuGaku ?? 0,
                nyukin != null ? nyukin.AdjustFutan : 0,
                nyukinGaku,
                nyukin != null ? nyukin.PaymentMethodCd : 0,
                nyukin != null ? nyukin.NyukinDate : 0,
                nyukin != null ? nyukin.UketukeSbt : 0,
                nyukin != null ? nyukin.NyukinCmt ?? string.Empty : string.Empty,
                seikyu?.NewSeikyuGaku ?? 0,
                seikyu?.NewAdjustFutan ?? 0,
                kaMst?.KaSname ?? string.Empty,
                nyukin != null ? nyukin.SortNo : 0,
                nyukin != null ? nyukin.SeqNo : 0,
                seikyu?.SeikyuDetail ?? string.Empty,
                raiinItem?.Status ?? RaiinState.Reservation,
                seikyu?.AdjustFutan ?? 0,
                seikyu?.NewSeikyuDetail ?? string.Empty,
                seikyu?.NewSeikyuTensu ?? 0,
                nyukin?.NyukinStatus ?? 0,
                raiinItem?.TreatmentDepartmentId ?? 0,
                raiinItem?.ReserveDetailId ?? 0
            );
    }
    private int GetMonth(int date)
    {
        return (date / 100);
    }

    public Dictionary<int, string> GetPaymentMethod(int hpId)
    {
        Dictionary<int, string> result = new();
        var paymentMethodList = NoTrackingDataContext.PaymentMethodMsts.Where(item => item.HpId == hpId).OrderBy(item => item.SortNo).ToList();
        foreach (var paymentMethod in paymentMethodList)
        {
            result.Add(paymentMethod.PaymentMethodCd, paymentMethod.PayName ?? string.Empty);
        }
        return result;
    }

    public Dictionary<int, string> GetUketsukeSbt(int hpId)
    {
        Dictionary<int, string> result = new();
        var uketukeList = NoTrackingDataContext.UketukeSbtMsts.Where(item => item.HpId == hpId && item.IsDeleted == 0).OrderBy(p => p.SortNo).ToList();
        foreach (var uketuke in uketukeList)
        {
            result.Add(uketuke.KbnId, uketuke.KbnName ?? string.Empty);
        }
        return result;
    }

    public List<AccountDueModel> SaveAccountDueList(int hpId, long ptId, int userId, int sinDate, List<AccountDueModel> listAccountDues, Dictionary<long, RaiinInfModel> dictRaiinInfModel, string kaikeiTime)
    {
        var listRaiinNo = listAccountDues.Select(item => item.RaiinNo).ToList();
        var raiinLists = TrackingDataContext.RaiinInfs
                                .Where(item => item.HpId == hpId
                                                    && item.PtId == ptId
                                                    && item.IsDeleted == DeleteTypes.None
                                                    && item.Status >= RaiinState.ConsultationCompleted
                                                    && listRaiinNo.Contains(item.RaiinNo))
                                .ToList();

        // Left table
        var seikyuLists = TrackingDataContext.SyunoSeikyus
                            .Where(item => item.HpId == hpId
                                                && item.PtId == ptId
                                                && listRaiinNo.Contains(item.RaiinNo))
                            .ToList();

        // Right table
        var nyukinLists = TrackingDataContext.SyunoNyukin
                               .Where(item => item.HpId == hpId
                                                   && item.PtId == ptId
                                                   && item.IsDeleted == 0
                                                   && listRaiinNo.Contains(item.RaiinNo))
                               .ToList();

        var dateTimeNow = CIUtil.GetJapanDateTimeNow();

        var originalList = listAccountDues;

        List<long> raiinUpdateList = new();
        List<long> raiinErrorList = new();

        foreach (var model in listAccountDues)
        {

            SyunoNyukin? syunoNyukin = null;
            bool isOnlineTreatment = dictRaiinInfModel.TryGetValue(model.RaiinNo, out var raiinInfModel) &&
                         raiinInfModel.IsOnlineTreatment();
            if (model.SeqNo == 0)
            {
                //オンライン診療の決済
                long? paymentClinicDetailId = null;
                NyukinStatusEnums nyukinStatus = NyukinStatusEnums.Success;
                if (isOnlineTreatment && raiinInfModel != null && raiinInfModel.ReserveDetail != null)
                {
                    var res = RequestPayment(hpId, userId, model, raiinInfModel.ReserveDetail);
                    if (res.PaymentClinicDetailId != 0)
                    {
                        paymentClinicDetailId = res.PaymentClinicDetailId;
                        //クレジットカード原因によるエラーは会計一覧に表示する
                        if (res.ErrorCode != null)
                        {
                            nyukinStatus = NyukinStatusEnums.Failed;
                            model.ErrorCode = res.ErrorCode;
                            model.UserMessage = res.UserMessage;
                        }
                    }
                    else
                    {
                        //クレジットカード以外のエラー
                        model.ErrorCode = res.ErrorCode;
                        model.UserMessage = res.UserMessage;
                        raiinErrorList.Add(model.RaiinNo);
                        continue;
                    }
                }

                syunoNyukin = InsertSyunoNyukin(hpId, ptId, userId, paymentClinicDetailId, nyukinStatus, dateTimeNow, model);
                if (nyukinStatus == NyukinStatusEnums.Failed)
                {
                    raiinErrorList.Add(model.RaiinNo);
                    continue;
                }
            }
            else
            {
                var nyukin = nyukinLists.FirstOrDefault(item => item.SeqNo == model.SeqNo);
                if (nyukin != null)
                {
                    //オンライン診療の決済
                    if (isOnlineTreatment && raiinInfModel != null && raiinInfModel.ReserveDetail != null && nyukin.NyukinStatus != (int)NyukinStatusEnums.Failed)
                    {
                        var res = UpdatePayment(userId, model, nyukinLists, raiinInfModel.ReserveDetail);
                        if (res != null)
                        {
                            model.ErrorCode = res.Extensions?.Code;
                            model.UserMessage = res.Extensions?.UserMessage;
                            raiinErrorList.Add(model.RaiinNo);
                            continue;
                        }
                    }
                    syunoNyukin = UpdateSyunoNyukin(userId, dateTimeNow, model, nyukin);
                }
            }

            if (syunoNyukin != null)
            {
                raiinUpdateList.Add(syunoNyukin.RaiinNo);
            }

            // Update raiin status
            var raiinInf = UpdateStatusRaiin(userId, dateTimeNow, model, raiinLists, kaikeiTime);
            if (raiinInf != null)
            {
                raiinUpdateList.Add(raiinInf.RaiinNo);
            }

            // Update left table SyunoSeikyu
            var syunoSeikyu = UpdateStatusSyunoSeikyu(userId, dateTimeNow, model, seikyuLists);
            if (syunoSeikyu != null)
            {
                raiinUpdateList.Add(syunoSeikyu.RaiinNo);
            }
        }

        raiinUpdateList = raiinUpdateList.Distinct().ToList();
        TrackingDataContext.SaveChanges();

        if (raiinErrorList.Count > 0)
        {
            return CompareResultList(originalList, raiinErrorList);
        }

        var result = CompareResultList(originalList, raiinUpdateList);
        return result;
    }


    private List<AccountDueModel> CompareResultList(List<AccountDueModel> originalList, List<long> raiinNoUpdateList)
    {
        var result = originalList.Where(original => raiinNoUpdateList.Contains(original.RaiinNo)).ToList();
        return result;
    }

    private RaiinInf? UpdateStatusRaiin(int userId, DateTime dateTimeNow, AccountDueModel model, List<RaiinInf> raiinLists, string kaikeiTime)
    {
        var raiin = raiinLists.FirstOrDefault(item => item.RaiinNo == model.RaiinNo);
        int tempStatus = model.NyukinKbn == (int)NyukinKbnEnums.Unsettled ? RaiinState.AmountConfirmed : RaiinState.Paid;
        if (raiin != null)
        {
            if (tempStatus != raiin.Status)
            {
                raiin.UpdateDate = dateTimeNow;
                raiin.UpdateId = userId;
                if (raiin.Status != tempStatus
                    || raiin.KaikeiTime != kaikeiTime)
                {
                    raiin.Status = tempStatus;
                    raiin.KaikeiTime = kaikeiTime;
                    return raiin;
                }
            }

            // update menjo
            if (model.NyukinKbn == (int)NyukinKbnEnums.NoBilling)
            {
                raiin.UpdateDate = dateTimeNow;
                raiin.UpdateId = userId;
            }
        }
        return null;
    }

    private SyunoSeikyu? UpdateStatusSyunoSeikyu(int userId, DateTime dateTimeNow, AccountDueModel model, List<SyunoSeikyu> seikyuLists)
    {
        var seikyu = seikyuLists.FirstOrDefault(item => item.RaiinNo == model.RaiinNo);
        if (seikyu != null)
        {
            seikyu.UpdateDate = dateTimeNow;
            seikyu.UpdateId = userId;
            if (seikyu.NyukinKbn != model.NyukinKbn
                || seikyu.SeikyuGaku != model.SeikyuGaku
                || seikyu.AdjustFutan != model.SeikyuAdjustFutan)
            {
                seikyu.NyukinKbn = model.NyukinKbn;
                seikyu.SeikyuGaku = model.SeikyuGaku;
                seikyu.AdjustFutan = model.SeikyuAdjustFutan;
                return seikyu;
            }
        }
        return null;
    }

    private SyunoNyukin? InsertSyunoNyukin(int hpId, long ptId, int userId, long? paymentClinicDetailId, NyukinStatusEnums nyukinStatus, DateTime dateTimeNow, AccountDueModel model)
    {
        var nyukin = new SyunoNyukin();
        nyukin.HpId = hpId;
        nyukin.PtId = ptId;
        nyukin.IsDeleted = 0;
        nyukin.SinDate = model.SeikyuSinDate;
        nyukin.RaiinNo = model.RaiinNo;
        nyukin.SortNo = model.SortNo;
        nyukin.AdjustFutan = model.AdjustFutan;
        nyukin.NyukinGaku = nyukinStatus == NyukinStatusEnums.Success ? model.NyukinGaku : 0;
        nyukin.PaymentMethodCd = model.PaymentMethodCd;
        nyukin.NyukinDate = model.NyukinDate;
        nyukin.UketukeSbt = model.UketukeSbt;
        nyukin.NyukinCmt = model.NyukinCmt;
        nyukin.NyukinjiSeikyu = model.SeikyuGaku;
        nyukin.NyukinjiTensu = model.SeikyuTensu;
        nyukin.NyukinjiDetail = model.SeikyuDetail;
        nyukin.CreateDate = dateTimeNow;
        nyukin.UpdateDate = dateTimeNow;
        nyukin.CreateId = userId;
        nyukin.UpdateId = userId;
        nyukin.PaymentClinicDetailId = paymentClinicDetailId;
        nyukin.NyukinStatus = (int)nyukinStatus;
        nyukin.ExpectedNyukinGaku = nyukinStatus == NyukinStatusEnums.Success ? 0 : model.NyukinGaku;

        TrackingDataContext.SyunoNyukin.Add(nyukin);
        return nyukin;
    }

    private SyunoNyukin? UpdateSyunoNyukin(int userId, DateTime dateTimeNow, AccountDueModel model, SyunoNyukin nyukin)
    {
        nyukin.UpdateDate = dateTimeNow;
        nyukin.UpdateId = userId;
        if (nyukin.SortNo != model.SortNo
            || nyukin.RaiinNo != model.RaiinNo
            || nyukin.AdjustFutan != model.AdjustFutan
            || nyukin.NyukinGaku != model.NyukinGaku
            || nyukin.PaymentMethodCd != model.PaymentMethodCd
            || nyukin.NyukinDate != model.NyukinDate
            || nyukin.UketukeSbt != model.UketukeSbt
            || nyukin.NyukinCmt != model.NyukinCmt
            || nyukin.NyukinjiSeikyu != model.SeikyuGaku
            || nyukin.NyukinjiTensu != model.SeikyuTensu
            || nyukin.NyukinjiDetail != model.SeikyuDetail
            || model.IsDelete)
        {
            nyukin.SortNo = model.SortNo;
            nyukin.RaiinNo = model.RaiinNo;
            nyukin.AdjustFutan = model.AdjustFutan;
            nyukin.NyukinGaku = model.NyukinGaku;
            nyukin.PaymentMethodCd = model.PaymentMethodCd;
            nyukin.NyukinDate = model.NyukinDate;
            nyukin.UketukeSbt = model.UketukeSbt;
            nyukin.NyukinCmt = model.NyukinCmt;
            nyukin.NyukinjiSeikyu = model.SeikyuGaku;
            nyukin.NyukinjiTensu = model.SeikyuTensu;
            nyukin.NyukinjiDetail = model.SeikyuDetail;
            if (model.IsDelete)
            {
                nyukin.IsDeleted = 1;
            }
            return nyukin;
        }
        return null;
    }

    private RequestReservePayment RequestPayment(int hpId, int userId, AccountDueModel model, ReserveDetailModel reserveDetail)
    {
        var paymentType = ((PaymentMethodCdEnum)model.PaymentMethodCd).ConvertToPaymentType();
        if (model.NyukinKbn == (int)NyukinKbnEnums.NoBilling)
        {
            paymentType = PaymentTypeEnums.NoBilling;
        }
        var task = _fincodeRepository.RequestReservePayment(hpId, reserveDetail.ReserveDetailId, (int)paymentType, model.NyukinGaku, userId);
        if (task.Result.Data != null && task.Result.Data.RequestReservePayment != null)
        {

            return task.Result.Data.RequestReservePayment;
        }

        var res = new RequestReservePayment(0, string.Empty, string.Empty);

        if (task.Result.Errors.Count > 0)
        {
            if (task.Result.Errors[0].Extensions?.Code != null)
            {
                res.ErrorCode = task.Result.Errors[0].Extensions?.Code;
            }
            if (task.Result.Errors[0].Extensions?.UserMessage != null)
            {
                res.UserMessage = task.Result.Errors[0].Extensions?.UserMessage;
            }
            return res;
        }

        return res;
    }

    private GraphqlError? UpdatePayment(int userId, AccountDueModel model, List<SyunoNyukin> nyukinLists, ReserveDetailModel reserveDetail)
    {
        var nyukin = nyukinLists.FirstOrDefault(item => item.SeqNo == model.SeqNo);
        if (nyukin != null)
        {
            if (nyukin.PaymentClinicDetailId.HasValue)
            {
                if (model.IsDelete)
                {
                    var cancelTask = _fincodeRepository.CancelReservePayment(nyukin.PaymentClinicDetailId.Value, userId);
                    if (cancelTask.Result.Errors.Count > 0)
                    {
                        return cancelTask.Result.Errors[0];
                    }
                }
                else
                {
                    var paymentType = ((PaymentMethodCdEnum)nyukin.PaymentMethodCd).ConvertToPaymentType();
                    var updateTask = _fincodeRepository.UpdateReservePayment(nyukin.PaymentClinicDetailId.Value, (int)paymentType, model.NyukinGaku, userId);
                    if (updateTask.Result.Errors.Count > 0)
                    {
                        return updateTask.Result.Errors[0];
                    }
                }

            }

        }
        return null;
    }

    public List<SyunoSeikyuModel> GetListSyunoSeikyuModel(int hpId, List<long> listRaiinNo)
    {
        var result = TrackingDataContext.SyunoSeikyus.Where(item => item.HpId == hpId && listRaiinNo.Contains(item.RaiinNo))
                                                             .Select(item => new SyunoSeikyuModel(
                                                                    item.HpId,
                                                                    item.PtId,
                                                                    item.SinDate,
                                                                    item.RaiinNo,
                                                                    item.NyukinKbn,
                                                                    item.SeikyuTensu,
                                                                    item.AdjustFutan,
                                                                    item.SeikyuGaku,
                                                                    item.SeikyuDetail ?? string.Empty,
                                                                    item.NewSeikyuTensu,
                                                                    item.NewAdjustFutan,
                                                                    item.NewSeikyuGaku,
                                                                    item.NewSeikyuDetail ?? string.Empty
                                                             )).ToList();
        return result;
    }

    public List<SyunoNyukinModel> GetListSyunoNyukinModel(int hpId, List<long> listRaiinNo)
    {
        var result = TrackingDataContext.SyunoNyukin.Where(item => item.HpId == hpId && listRaiinNo.Contains(item.RaiinNo) && item.IsDeleted == 0)
                                                             .Select(item => new SyunoNyukinModel(
                                                                    item.HpId,
                                                                    item.PtId,
                                                                    item.SinDate,
                                                                    item.RaiinNo,
                                                                    item.SeqNo,
                                                                    item.SortNo,
                                                                    item.AdjustFutan,
                                                                    item.NyukinGaku,
                                                                    item.PaymentMethodCd,
                                                                    item.NyukinDate,
                                                                    item.UketukeSbt,
                                                                    item.NyukinCmt ?? string.Empty,
                                                                    item.NyukinjiTensu,
                                                                    item.NyukinjiSeikyu,
                                                                    item.NyukinjiDetail ?? string.Empty
                                                             )).ToList();
        return result;
    }

    public Dictionary<long, RaiinInfModel> GetDictRaiinInfModel(int hpId, List<long> listRaiinNo)
    {
        var raiinList = TrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId && listRaiinNo.Contains(item.RaiinNo) && item.IsDeleted == DeleteStatus.None).ToList();
        var reserveDetailIds = raiinList.Select(raiin => raiin.ReserveDetailId).ToArray();
        var reserveDetailList = TrackingDataContext.ReserveDetails.Where(item => reserveDetailIds.Contains(item.ReserveDetailId) && item.IsDeleted == 0).ToList();
        var result = (from raiin in raiinList
                      join reserveDetail in reserveDetailList
                      on raiin.ReserveDetailId equals reserveDetail.ReserveDetailId into reserveJoin
                      from tempJoin in reserveJoin.DefaultIfEmpty()
                      select new
                      {
                          raiin.RaiinNo,
                          RaiinInfModel = new RaiinInfModel(
                                raiin.HpId,
                                raiin.PtId,
                                raiin.SinDate,
                                raiin.RaiinNo,
                                tempJoin == null ? null : new ReserveDetailModel(
                                    tempJoin.ReserveDetailId,
                                    tempJoin.ReserveType
                                ))
                      }).ToDictionary(r => r.RaiinNo, r => r.RaiinInfModel);
        return result;
    }

    public bool IsNyukinExisted(int hpId, long ptId, long raiinNo, int sinDate)
    {
        var seikyuList = NoTrackingDataContext.SyunoSeikyus
                         .Where(item => item.HpId == hpId
                                        && item.PtId == ptId
                                        && item.RaiinNo == raiinNo
                                        && item.SinDate == sinDate
                                        && item.NyukinKbn != 0);

        var nyukinList = NoTrackingDataContext.SyunoNyukin
                         .Where(item => item.HpId == hpId
                                        && item.PtId == ptId
                                        && item.RaiinNo == raiinNo
                                        && item.SinDate == sinDate
                                        && item.IsDeleted == 0);

        var query = from seikyu in seikyuList
                    join nyukin in nyukinList on new { seikyu.HpId, seikyu.PtId, seikyu.SinDate, seikyu.RaiinNo }
                                              equals new { nyukin.HpId, nyukin.PtId, nyukin.SinDate, nyukin.RaiinNo }
                    select new
                    {
                        Seikyu = seikyu
                    };

        return query.Any();
    }

    public void ReleaseResource()
    {
        DisposeDataContext();
    }
}

