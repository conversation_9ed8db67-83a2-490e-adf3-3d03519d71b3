﻿using Domain.Models.Family;
using Entity.Tenant;
using Helper.Constant;
using Helper.Constants;
using Infrastructure.Base;
using Infrastructure.Interfaces;

namespace Infrastructure.Repositories;

public class FamilyRepository : RepositoryBase, IFamilyRepository
{
    public FamilyRepository(ITenantProvider tenantProvider) : base(tenantProvider) { }

    public List<PtFamilyRekiModel> GetFamilyList(int hpId, long ptId, int sinDate)
    {
        var ptFamilyRekis = NoTrackingDataContext.PtFamilyRekis.Where(u => u.HpId == hpId 
                                                                            && u.PtId == ptId
                                                                            && !string.IsNullOrEmpty(u.Byomei)
                                                                            && u.IsDeleted == DeleteTypes.None)
                                                               .ToList();

        return ConvertToPtFamilyRekiModel(ptFamilyRekis)
                        .OrderBy(item => item.SortNo)
                        .ToList();
    }

    public List<PtFamilyRekiModel> GetFamilyListByPtId(int hpId, long ptId, int sinDate)
    {
        var ptFamilyRekis = NoTrackingDataContext.PtFamilyRekis
            .Where(u => u.HpId == hpId && u.PtId == ptId && !string.IsNullOrEmpty(u.Byomei) && u.IsDeleted == DeleteTypes.None)
            .OrderBy(u => u.SortNo)
            .ToList();
        return ConvertToPtFamilyRekiModel(ptFamilyRekis)
                        .OrderBy(item => item.SortNo)
                        .ToList();
    }

    public List<PtFamilyRekiModel> GetListByPtId(int hpId, long ptId)
    {
        var ptFamilyRekis = NoTrackingDataContext.PtFamilyRekis
            .Where(u => u.HpId == hpId && u.PtId == ptId && u.IsDeleted == DeleteTypes.None)
            .OrderBy(u => u.SortNo)
            .ToList();
        return ConvertToPtFamilyRekiModel(ptFamilyRekis)
                        .OrderBy(item => item.SortNo)
                        .ToList();
    }

    public List<RaiinInfModel> GetRaiinInfListByPtId(int hpId, long ptId)
    {
        var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                         item.PtId == ptId &&
                                                                         item.IsDeleted == DeleteTypes.None);

        var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                 item.IsDeleted == DeleteTypes.None &&
                                                                                 item.KarteStatus == KarteStatusConst.Official);

        var raiinInfList = (from raiinInf in listRaiinInf
                            join karteEdition in listKarteEdition on
                                raiinInf.RaiinNo equals karteEdition.RaiinNo
                            select raiinInf)
                           .ToList();

        var tantoIdList = raiinInfList.Select(item => item.TantoId).ToList();
        var kaIdList = raiinInfList.Select(item => item.KaId).ToList();
        var hokenPIdList = raiinInfList.Select(item => item.HokenPid).ToList();

        var doctorList = NoTrackingDataContext.UserMsts.Where(item => item.IsDeleted == DeleteTypes.None
                                                                    && item.HpId == hpId
                                                                    && item.JobCd == JobCdConstant.Doctor
                                                                    && tantoIdList.Contains(item.UserId))
                                                      .ToList();

        var kaMstList = NoTrackingDataContext.KaMsts.Where(item => item.IsDeleted == DeleteTypes.None
                                                                    && item.HpId == hpId
                                                                    && kaIdList.Contains(item.KaId))
                                                    .ToList();

        var hokenPatternList = NoTrackingDataContext.PtHokenPatterns.Where(item => item.IsDeleted == DeleteTypes.None
                                                                                    && item.HpId == hpId
                                                                                    && hokenPIdList.Contains(item.HokenPid))
                                                                    .ToList();

        var hokenIdList = hokenPatternList.Select(item => item.HokenId).Distinct().ToList();
        var hokenInfList = NoTrackingDataContext.PtHokenInfs.Where(item => item.IsDeleted == DeleteTypes.None
                                                                            && item.HpId == hpId
                                                                            && hokenIdList.Contains(item.HokenId))
                                                            .ToList();

        return raiinInfList.Select(item => ConvertToRaiinInfModel(item, doctorList, kaMstList, hokenPatternList, hokenInfList))
                           .OrderByDescending(item => item.SinDate)
                           .ToList();
    }

    #region private function
    private List<PtFamilyRekiModel> ConvertToPtFamilyRekiModel(List<PtFamilyReki> ptFamilyRekis)
    {
        var ptFamilyRekiFilter = ptFamilyRekis.Select(item => ConvertToPtFamilyRekiModel(item))
                                              .ToList();

        return ptFamilyRekiFilter;
    }

    private PtFamilyRekiModel ConvertToPtFamilyRekiModel(PtFamilyReki ptFamilyReki)
    {
        return new PtFamilyRekiModel(
                                        ptFamilyReki.Id,
                                        ptFamilyReki.ByomeiCd ?? string.Empty,
                                        ptFamilyReki.Byomei ?? string.Empty,
                                        ptFamilyReki.Cmt ?? string.Empty,
                                        ptFamilyReki.SortNo
                                    );
    }

    private RaiinInfModel ConvertToRaiinInfModel(RaiinInf raiinInf, List<UserMst> doctorList, List<KaMst> kaMstList, List<PtHokenPattern> hokenPatternList, List<PtHokenInf> hokenInfList)
    {
        var doctor = doctorList.FirstOrDefault(item => item.UserId == raiinInf.TantoId);
        var kaMst = kaMstList.FirstOrDefault(item => item.KaId == raiinInf.KaId);

        string hokenPatternName = string.Empty;
        var hokenPattern = hokenPatternList.FirstOrDefault(item => item.HokenPid == raiinInf.HokenPid);
        if (hokenPattern != null)
        {
            var hokenInf = hokenInfList.FirstOrDefault(item => item.HokenId == hokenPattern.HokenId);
            hokenPatternName = GetHokenName(hokenPattern.HokenKbn, hokenPattern.HokenSbtCd, hokenInf?.Houbetu ?? string.Empty);
        }

        return new RaiinInfModel(
                raiinInf.PtId,
                raiinInf.SinDate,
                raiinInf.RaiinNo,
                raiinInf.KaId,
                kaMst?.KaName ?? string.Empty,
                raiinInf.TantoId,
                doctor?.Sname ?? string.Empty,
                hokenPattern?.HokenPid ?? CommonConstants.InvalidId,
                hokenPatternName
            );
    }

    private string GetHokenName(int hokenKbn, int hokenSbtCd, string houbetu)
    {
        string result = string.Empty;
        switch (hokenKbn)
        {
            case 0:
                switch (houbetu)
                {
                    case "108":
                        result = "自費";
                        break;
                    case "109":
                        result = "自レ";
                        break;
                }
                break;
            case 11:
            case 12:
            case 13:
                result = "労災";
                break;
            case 14:
                result = "自賠";
                break;
            default:
                if (hokenSbtCd >= 100 && hokenSbtCd <= 199)
                {
                    result = "社保";
                }
                else if (hokenSbtCd >= 200 && hokenSbtCd <= 299)
                {
                    result = "国保";
                }
                else if (hokenSbtCd >= 300 && hokenSbtCd <= 399)
                {
                    result = "後期";
                }
                else if (hokenSbtCd >= 400 && hokenSbtCd <= 499)
                {
                    result = "退職";
                }
                else if (hokenSbtCd >= 500 && hokenSbtCd <= 599)
                {
                    result = "公費";
                }
                break;
        }
        return result;
    }
    #endregion

    public void ReleaseResource()
    {
        DisposeDataContext();
    }
}
