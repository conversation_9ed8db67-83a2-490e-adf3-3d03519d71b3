﻿using System.Diagnostics.CodeAnalysis;
using Domain.Constant;
using Domain.Models.KensaIrai;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Extension;
using Helper.Messaging;
using Helper.Messaging.Data;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Text.Json;
using Infrastructure.Services;
using FileData = Domain.Models.KensaIrai.GetIraiFileDataDummy;

namespace Infrastructure.Repositories;

public class KensaIraiRepository : RepositoryBase, IKensaIraiRepository
{
    public KensaIraiRepository(ITenantProvider tenantProvider) : base(tenantProvider)
    {
    }

    public KensaCenterMstModel GetKensaCenterMst(int hpId, string centerCd)
    {
        var kensaCenterMstModel = NoTrackingDataContext.KensaCenterMsts.FirstOrDefault(item => item.HpId == hpId && item.CenterCd == centerCd);
        if (kensaCenterMstModel == null)
        {
            return new();
        }
        return new KensaCenterMstModel(kensaCenterMstModel.CenterCd ?? string.Empty,
                                       kensaCenterMstModel.CenterName ?? string.Empty,
                                       kensaCenterMstModel.PrimaryKbn,
                                       kensaCenterMstModel.Id);
    }

    public List<KensaInfModel> GetKensaInf(int hpId, long ptId, long raiinNo, string centerCd)
    {
        var kensaInfs = NoTrackingDataContext.KensaInfs.Where(item => item.HpId == hpId
                                                                      && item.PtId == ptId
                                                                      && item.RaiinNo == raiinNo
                                                                      && item.InoutKbn == 1
                                                                      && item.CenterCd == centerCd
                                                                      && item.IsDeleted == DeleteStatus.None
                                                              ).ToList();

        var odrInfDtls = NoTrackingDataContext.OdrInfDetails.Where(item => item.HpId == hpId
                                                                           && item.PtId == ptId
                                                                           && item.RaiinNo == raiinNo
                                                                           && !string.IsNullOrEmpty(item.ReqCd))
                                                            .ToList();

        List<string> reqcds = new();
        if (odrInfDtls != null && odrInfDtls.Any())
        {
            reqcds = odrInfDtls.GroupBy(p => p.ReqCd).Select(p => p.Key ?? string.Empty).ToList();
        }

        List<KensaInfModel> results = new();

        kensaInfs?.ForEach(entity =>
        {
            if (!reqcds.Contains(entity.IraiCd.ToString()))
            {
                results.Add(new KensaInfModel(
                    entity.PtId,
                    entity.IraiDate,
                    entity.RaiinNo,
                    entity.IraiCd,
                    entity.InoutKbn,
                    entity.Status,
                    entity.TosekiKbn,
                    entity.SikyuKbn,
                    entity.ResultCheck,
                    entity.CenterCd ?? string.Empty,
                    entity.Nyubi ?? string.Empty,
                    entity.Yoketu ?? string.Empty,
                    entity.Bilirubin ?? string.Empty,
                    false,
                    entity.CreateId));
            }
        });
        return results;
    }

    public List<KensaInfDetailModel> GetKensaInfDetail(int hpId, long ptId, long raiinNo, string centerCd)
    {
        var kensaInfs = NoTrackingDataContext.KensaInfs.Where(item => item.HpId == hpId
                                                                      && item.PtId == ptId
                                                                      && item.RaiinNo == raiinNo
                                                                      && item.InoutKbn == 1
                                                                      && item.CenterCd == centerCd
                                                                      && item.IsDeleted == DeleteStatus.None
        );

        var kensaInfDtls = NoTrackingDataContext.KensaInfDetails.Where(item => item.HpId == hpId
                                                                               && item.PtId == ptId
                                                                               && item.RaiinNo == raiinNo
                                                                               && item.IsDeleted == DeleteStatus.None
        );

        var result = (
                from kensaInf in kensaInfs
                join kensaInfDtl in kensaInfDtls on
                    new { kensaInf.HpId, kensaInf.PtId, kensaInf.IraiCd } equals
                    new { kensaInfDtl.HpId, kensaInfDtl.PtId, kensaInfDtl.IraiCd }
                select new KensaInfDetailModel(kensaInfDtl.PtId,
                                               kensaInfDtl.IraiDate,
                                               kensaInfDtl.RaiinNo,
                                               kensaInfDtl.IraiCd,
                                               kensaInfDtl.SeqNo,
                                               kensaInfDtl.KensaItemCd ?? string.Empty,
                                               kensaInfDtl.ResultVal ?? string.Empty,
                                               kensaInfDtl.ResultType ?? string.Empty,
                                               kensaInfDtl.AbnormalKbn ?? string.Empty,
                                               kensaInfDtl.IsDeleted,
                                               kensaInfDtl.CmtCd1 ?? string.Empty,
                                               kensaInfDtl.CmtCd2 ?? string.Empty,
                                               new())
                    ).ToList();
        return result;
    }

    public bool SaveKensaInf(int hpId, int userId, List<KensaInfModel> kensaInfModels, List<KensaInfDetailModel> kensaInfDetailModels)
    {
        bool successed = false;
        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(
            () =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    SaveKensaInfAction(hpId, userId, kensaInfModels, kensaInfDetailModels);
                    TrackingDataContext.SaveChanges();
                    transaction.Commit();
                    successed = true;
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            });
        return successed;
    }

    public List<KensaIraiModel> GetKensaIraiModels(int hpId, long ptId, int startDate, int endDate, string kensaCenterMstCenterCd, int kensaCenterMstPrimaryKbn)
    {
        List<KensaIraiModel> result = new();
        var odrInfList = NoTrackingDataContext.OdrInfs.Where(item => item.HpId == hpId
                                                                     && item.IsDeleted == 0
                                                                     && (ptId == 0 || item.PtId == ptId)
                                                                     && item.OdrKouiKbn >= 60
                                                                     && item.OdrKouiKbn <= 69
                                                                     && item.InoutKbn == 1
                                                                     && item.SinDate >= startDate
                                                                     && item.SinDate <= endDate)
                                                      .ToList();

        var ptIdList = odrInfList.Select(item => item.PtId).Distinct().ToList();
        var raiinNoList = odrInfList.Select(item => item.RaiinNo).Distinct().ToList();
        var ptInfList = NoTrackingDataContext.PtInfs.Where(item => item.HpId == hpId
                                                                   && item.IsDelete == 0
                                                                   && ptIdList.Contains(item.PtId))
                                                    .ToList();

        var odrInfDetailList = NoTrackingDataContext.OdrInfDetails.Where(item => item.HpId == hpId
                                                                                 && (ptId == 0 || item.PtId == ptId)
                                                                                 && !string.IsNullOrEmpty(item.ItemCd)
                                                                                 && string.IsNullOrEmpty(item.ReqCd)
                                                                                 && raiinNoList.Contains(item.RaiinNo))
                                                                  .ToList();
        var itemCdList = odrInfDetailList.Select(item => item.ItemCd).Distinct().ToList();

        var raiinInfList = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId
                                                                         && item.IsDeleted == DeleteTypes.None
                                                                         && (ptId == 0 || item.PtId == ptId)
                                                                         && raiinNoList.Contains(item.RaiinNo))
                                                          .ToList();

        var tenMstList = NoTrackingDataContext.TenMsts.Where(item => item.HpId == hpId
                                                                     && item.MasterSbt != "C"
                                                                     && item.SinKouiKbn >= 60
                                                                     && item.SinKouiKbn <= 69
                                                                     && item.IsDeleted == DeleteTypes.None
                                                                     && itemCdList.Contains(item.ItemCd))
                                                       .ToList();

        var kensaItemCdList = tenMstList.Select(item => item.KensaItemCd).Distinct().ToList();
        var kensaItemSeqNoList = tenMstList.Select(item => item.KensaItemSeqNo).Distinct().ToList();

        var kensaMstList = NoTrackingDataContext.KensaMsts.Where(item => item.HpId == hpId
                                                                         && (item.CenterCd == kensaCenterMstCenterCd || (kensaCenterMstPrimaryKbn == 1 && string.IsNullOrEmpty(item.CenterCd)))
                                                                         && kensaItemSeqNoList.Contains(item.KensaItemSeqNo)
                                                                         && kensaItemCdList.Contains(item.KensaItemCd))
                                                          .ToList();
        if (kensaMstList.Any())
        {
            kensaMstList = kensaMstList.GroupBy(item => item.KensaItemCd)
                                           .Select(item => item.OrderBy(sort => sort.KensaItemSeqNo).First())
                                           .ToList();
        }

        var todayOdrInfList = (from odrInfEntity in odrInfList
                               join ptInfEntity in ptInfList on
                               new { odrInfEntity.HpId, odrInfEntity.PtId } equals
                               new { ptInfEntity.HpId, ptInfEntity.PtId }
                               join raiinInfEntity in raiinInfList on
                               new { odrInfEntity.HpId, odrInfEntity.PtId, odrInfEntity.RaiinNo } equals
                               new { raiinInfEntity.HpId, raiinInfEntity.PtId, raiinInfEntity.RaiinNo }
                               select new
                               {
                                   odrInfEntity.SikyuKbn,
                                   odrInfEntity.TosekiKbn,
                                   odrInfEntity.SortNo,
                                   PtInf = ptInfEntity,
                                   RaiinInf = raiinInfEntity,
                                   OdrInfDetails = from odrInfDetailEntity in odrInfDetailList
                                                   where odrInfDetailEntity.HpId == odrInfEntity.HpId &&
                                                         odrInfDetailEntity.PtId == odrInfEntity.PtId &&
                                                         odrInfDetailEntity.RaiinNo == odrInfEntity.RaiinNo &&
                                                         odrInfDetailEntity.RpNo == odrInfEntity.RpNo &&
                                                         odrInfDetailEntity.RpEdaNo == odrInfEntity.RpEdaNo
                                                   join tenMstEntity in tenMstList on
                                                   new { odrInfDetailEntity.HpId, odrInfDetailEntity.ItemCd } equals
                                                   new { tenMstEntity.HpId, tenMstEntity.ItemCd }
                                                   join kensaMstEntity in kensaMstList on
                                                   new { tenMstEntity.HpId, tenMstEntity.KensaItemCd, tenMstEntity.KensaItemSeqNo } equals
                                                   new { kensaMstEntity.HpId, kensaMstEntity.KensaItemCd, kensaMstEntity.KensaItemSeqNo } into tenMstKensaMstList
                                                   from tenMstKensaMst in tenMstKensaMstList.DefaultIfEmpty()
                                                   where tenMstEntity.StartDate <= odrInfEntity.SinDate && tenMstEntity.EndDate >= odrInfEntity.SinDate
                                                   select new
                                                   {
                                                       odrInfDetailEntity,
                                                       tenMstEntity,
                                                       KensaMst = tenMstKensaMst,
                                                   }
                               }).ToList();
        var groupTodayOdrInfs = todayOdrInfList
                                .GroupBy(x => new
                                {
                                    x.RaiinInf.RaiinNo,
                                    x.SikyuKbn,
                                    x.TosekiKbn
                                })
                                .ToList();
        foreach (var groupTodayOdrInf in groupTodayOdrInfs)
        {
            List<KensaIraiDetailModel> kensaIraiDetailList = new();
            var groupTodayOdrInfList = groupTodayOdrInf.ToList();
            var firstTodayOdr = groupTodayOdrInfList.FirstOrDefault();
            if (firstTodayOdr == null)
            {
                continue;
            }
            foreach (var todayOdr in groupTodayOdrInfList)
            {
                var todayOdrList = todayOdr.OdrInfDetails
                                           .Select((item) => new KensaIraiDetailModel(
                                                                 item.tenMstEntity?.KensaItemCd ?? string.Empty,
                                                                 item.tenMstEntity?.ItemCd ?? string.Empty,
                                                                 item.tenMstEntity?.Name ?? string.Empty,
                                                                 item.tenMstEntity?.KanaName1 ?? string.Empty,
                                                                 item.KensaMst?.CenterCd ?? string.Empty,
                                                                 item.KensaMst?.KensaItemCd ?? string.Empty,
                                                                 item.KensaMst?.CenterItemCd1 ?? string.Empty,
                                                                 item.KensaMst?.KensaKana ?? string.Empty,
                                                                 item.KensaMst?.KensaName ?? string.Empty,
                                                                 item.KensaMst?.ContainerCd ?? 0,
                                                                 item.odrInfDetailEntity.RpNo,
                                                                 item.odrInfDetailEntity.RpEdaNo,
                                                                 item.odrInfDetailEntity.RowNo,
                                                                 0))
                                          .ToList();
                kensaIraiDetailList.AddRange(todayOdrList);
            }
            result.Add(new KensaIraiModel(
                            firstTodayOdr.RaiinInf.SinDate,
                            firstTodayOdr.RaiinInf.RaiinNo,
                            0,
                            firstTodayOdr.PtInf.PtId,
                            firstTodayOdr.PtInf.PtNum,
                            firstTodayOdr.PtInf.Name ?? string.Empty,
                            firstTodayOdr.PtInf.KanaName ?? string.Empty,
                            firstTodayOdr.PtInf.Sex,
                            firstTodayOdr.PtInf.Birthday,
                            firstTodayOdr.TosekiKbn,
                            firstTodayOdr.SikyuKbn,
                            firstTodayOdr.RaiinInf.KaId,
                            kensaIraiDetailList
                ));
        }

        // Filter irai done item
        result = result.Where(item => item.KensaIraiDetails.Any(item => !string.IsNullOrEmpty(item.KensaItemCd)))
                       .OrderBy(item => item.SinDate)
                       .ThenBy(item => item.PtNum)
                       .ThenBy(item => item.SikyuKbn)
                       .ToList();
        return result;
    }

    [SuppressMessage("ReSharper.DPA", "DPA0000: DPA issues")]
    public List<KensaIraiModel> GetKensaIraiModels(int hpId, int startDate, int endDate, string kensaCenterMstCenterCd, bool isRequestConfirmation)
    {
        List<KensaIraiModel> result = new();
        var odrInfList = NoTrackingDataContext.OdrInfs.Where(item => item.HpId == hpId
                                                                     && item.IsDeleted == DeleteTypes.None
                                                                     && item.OdrKouiKbn >=
                                                                     ReportOdrKouiKbnConst.KensaMin
                                                                     && item.OdrKouiKbn <=
                                                                     ReportOdrKouiKbnConst.KensaMax &&
                                                                     (!isRequestConfirmation ? item.SinDate >= startDate
                                                                     && item.SinDate <= endDate : true))
            .Select(x => new
            {
                x.HpId,
                x.PtId,
                x.RaiinNo,
                x.SinDate,
                x.SikyuKbn,
                x.TosekiKbn,
                x.RpNo,
                x.RpEdaNo,
                x.Id,
                x.UpdateDate
            })
            .ToList();

        var odrInfDetailList = NoTrackingDataContext.OdrInfDetails
            .Where(item => item.HpId == hpId
                           && !string.IsNullOrEmpty(item.ItemCd)
                           && (isRequestConfirmation
                ? true
                : item.JissiKbn == 0))
            .Select(x => new
            {
                x.HpId,
                x.PtId,
                x.RaiinNo,
                x.RpNo,
                x.RpEdaNo,
                x.ItemCd,
                x.ReqCd,
                x.RowNo
            })
            .ToList();

        var itemCds = odrInfDetailList.Select(x => x.ItemCd).Distinct().ToList();

        var tenMstList = NoTrackingDataContext.TenMsts
            .Where(item => item.HpId == hpId
                           && item.MasterSbt != "C"
                           && itemCds.Contains(item.ItemCd)
                           && (!string.IsNullOrEmpty(item.CenterCd)))
            .Select(x => new
            {
                x.HpId,
                x.ItemCd,
                x.CenterCd,
                x.KensaItemCd
            })
            .ToList();

        var centerCds = tenMstList.Select(x => x.CenterCd).Distinct().ToList();

        var comonKensaMsts = NoTrackingDataContext.CommonKensaCenterMst
            .Where(item => (string.IsNullOrEmpty(kensaCenterMstCenterCd) || item.CenterCd == kensaCenterMstCenterCd)
                           && !string.IsNullOrEmpty(item.CenterCd)
                           && centerCds.Contains(item.CenterCd))
            .ToList();

        var commonCenterKensaMsts = NoTrackingDataContext.CommonCenterKensaMst
            .Where(x => centerCds.Contains(x.CenterCd))
            .ToList();
        var commonKensaCenterMst = NoTrackingDataContext.CommonKensaCenterMst
            .ToList();
        var userMstList = NoTrackingDataContext.UserMsts.Where(item => item.HpId == hpId).ToList();

        var kensaInfList = NoTrackingDataContext.KensaInfs.Where(item => item.HpId == hpId).ToList();

        var baseList = !isRequestConfirmation
            ? (from odrInfEntity in odrInfList
               join kensaInfEntity in kensaInfList on
                   new { odrInfEntity.HpId, odrInfEntity.PtId, odrInfEntity.RaiinNo } equals
                   new { kensaInfEntity.HpId, kensaInfEntity.PtId, kensaInfEntity.RaiinNo } into odrKensaList
               from odrKensa in odrKensaList.DefaultIfEmpty()
               select new
               {
                   OdrInf = odrInfEntity,
                   KensaInf = odrKensa,
                   HpId = odrInfEntity.HpId,
                   PtId = odrInfEntity.PtId,
                   RaiinNo = odrInfEntity.RaiinNo,
                   SikyuKbn = odrInfEntity.SikyuKbn,
                   TosekiKbn = odrInfEntity.TosekiKbn
               })
            : (from kensaInfEntity in kensaInfList
               join odrInfEntity in odrInfList on
                   new { kensaInfEntity.HpId, kensaInfEntity.PtId, kensaInfEntity.RaiinNo } equals
                   new { odrInfEntity.HpId, odrInfEntity.PtId, odrInfEntity.RaiinNo } into kensaOdrList
               from kensaOdr in kensaOdrList.DefaultIfEmpty()
               where string.IsNullOrEmpty(kensaCenterMstCenterCd) || kensaInfEntity.CenterCd == kensaCenterMstCenterCd
               select new
               {
                   OdrInf = kensaOdr,
                   KensaInf = kensaInfEntity,
                   HpId = kensaInfEntity.HpId,
                   PtId = kensaInfEntity.PtId,
                   RaiinNo = kensaInfEntity.RaiinNo,
                   SikyuKbn = kensaInfEntity.SikyuKbn,
                   TosekiKbn = kensaInfEntity.TosekiKbn
               });

        var todayOdrInfList = (from baseItem in baseList
                               join ptInfEntity in NoTrackingDataContext.PtInfs on
                                   new { baseItem.HpId, baseItem.PtId } equals
                                   new { ptInfEntity.HpId, ptInfEntity.PtId }
                               join raiinInfEntity in NoTrackingDataContext.RaiinInfs on
                                   new { baseItem.HpId, baseItem.PtId, baseItem.RaiinNo } equals
                                   new { raiinInfEntity.HpId, raiinInfEntity.PtId, raiinInfEntity.RaiinNo }
                               join kaikeiInfEntity in NoTrackingDataContext.KaMsts on
                                   new { raiinInfEntity.HpId, raiinInfEntity.KaId } equals
                                   new { kaikeiInfEntity.HpId, kaikeiInfEntity.KaId } into raiinInfMstKaMstJoin
                               from kaikeiInfMst in raiinInfMstKaMstJoin.DefaultIfEmpty()
                               join userMstEntity in userMstList on
                                   new { raiinInfEntity.HpId, UserId = raiinInfEntity.TantoId } equals
                                   new { userMstEntity.HpId, userMstEntity.UserId } into raiinInfUserMstList
                               from userMstEntityKensaMst in raiinInfUserMstList.DefaultIfEmpty()
                               select new
                               {
                                   baseItem.SikyuKbn,
                                   baseItem.TosekiKbn,
                                   baseItem.HpId,
                                   baseItem.PtId,
                                   baseItem.OdrInf?.Id,
                                   baseItem.OdrInf?.UpdateDate,
                                   SinDate = !isRequestConfirmation ? baseItem.OdrInf.SinDate : raiinInfEntity.SinDate,
                                   baseItem.KensaInf?.IraiDate,
                                   baseItem.KensaInf?.KensaTime,
                                   baseItem.KensaInf?.Status,
                                   baseItem.KensaInf?.IraiCd,
                                   baseItem.KensaInf?.IsDeleted,
                                   baseItem.KensaInf?.CenterCd,
                                   PtInf = ptInfEntity,
                                   RaiinNo = raiinInfEntity.RaiinNo,
                                   KaId = raiinInfEntity.KaId,
                                   kaikeiInfMst?.KaName,
                                   UserName = userMstEntityKensaMst?.Name,
                                   OdrInfDetails = from odrInfDetailEntity in odrInfDetailList
                                                   where odrInfDetailEntity.HpId == baseItem.HpId &&
                                                         odrInfDetailEntity.PtId == baseItem.PtId &&
                                                         odrInfDetailEntity.RaiinNo == baseItem.RaiinNo &&
                                                         (!isRequestConfirmation ? (odrInfDetailEntity.RpNo == baseItem.OdrInf?.RpNo &&
                                                         odrInfDetailEntity.RpEdaNo == baseItem.OdrInf?.RpEdaNo && string.IsNullOrEmpty(odrInfDetailEntity.ReqCd)) :
                                                         (odrInfDetailEntity.RpNo == baseItem.OdrInf?.RpNo &&
                                                         odrInfDetailEntity.RpEdaNo == baseItem.OdrInf?.RpEdaNo))
                                                   join tenMstEntity in tenMstList on
                                                       new { odrInfDetailEntity.HpId, odrInfDetailEntity.ItemCd } equals
                                                       new { tenMstEntity.HpId, tenMstEntity.ItemCd }
                                                   join commonCenterKensaMstEntity in commonCenterKensaMsts on
                                                       new { tenMstEntity.CenterCd, tenMstEntity.KensaItemCd } equals
                                                       new { commonCenterKensaMstEntity.CenterCd, commonCenterKensaMstEntity.KensaItemCd } into
                                                       tenMstEntityList
                                                   from commonCenterKensaMst in tenMstEntityList.DefaultIfEmpty()
                                                   join comonKensaMstEntity in comonKensaMsts on
                                                       commonCenterKensaMst?.CenterCd equals comonKensaMstEntity.CenterCd into comonCenterKensaMstList
                                                   from commonKensaMst in comonCenterKensaMstList.DefaultIfEmpty()
                                                   select new
                                                   {
                                                       odrInfDetailEntity,
                                                       tenMstEntity,
                                                       commonCenterKensaMst,
                                                       commonKensaMst
                                                   }
                               }).Where(item => isRequestConfirmation || item.OdrInfDetails.Any()).ToList();

        if (!isRequestConfirmation)
        {
            var groupTodayOdrInfs = todayOdrInfList
                .GroupBy(x => new
                {
                    x.RaiinNo,
                    x.SikyuKbn,
                    x.TosekiKbn,
                    x.HpId,
                    x.PtId
                });
            foreach (var groupTodayOdrInf in groupTodayOdrInfs)
            {
                List<KensaIraiDetailModel> kensaIraiDetailList = new();
                var groupTodayOdrInfList = groupTodayOdrInf.ToList();
                var firstTodayOdr = groupTodayOdrInfList.FirstOrDefault();

                var dspCenterName = groupTodayOdrInfList
                    .SelectMany(odr => odr.OdrInfDetails)
                    .Where(item => !string.IsNullOrEmpty(item.commonKensaMst?.DspCenterName))
                    .Select(item => (item.commonKensaMst.DspCenterName))
                    .FirstOrDefault();

                foreach (var todayOdr in groupTodayOdrInfList)
                {
                    var todayOdrList = todayOdr.OdrInfDetails.Where(item =>
                            string.IsNullOrEmpty(kensaCenterMstCenterCd) ||
                            item.commonKensaMst?.CenterCd == kensaCenterMstCenterCd)
                        .Select((item) => new KensaIraiDetailModel(
                            item.tenMstEntity?.KensaItemCd ?? string.Empty,
                            item.tenMstEntity?.ItemCd ?? string.Empty,
                            string.Empty,
                            string.Empty,
                            item.commonCenterKensaMst?.CenterCd ?? string.Empty,
                            item.commonCenterKensaMst?.KensaItemCd ?? string.Empty,
                            item.commonCenterKensaMst?.CenterItemCd ?? string.Empty,
                            item.commonCenterKensaMst?.KensaKana ?? string.Empty,
                            item.commonCenterKensaMst?.KensaName ?? string.Empty,
                            0,
                            item.odrInfDetailEntity.RpNo,
                            item.odrInfDetailEntity.RpEdaNo,
                            0,
                            0))
                        .ToList();
                    kensaIraiDetailList.AddRange(todayOdrList);
                }

                kensaIraiDetailList = kensaIraiDetailList.DistinctBy(x => new { x.ItemCd, x.CenterCd, x.KensaItemCd })
                    .ToList();
                result.Add(new KensaIraiModel(
                    firstTodayOdr.SinDate,
                    dspCenterName ?? string.Empty,
                    firstTodayOdr.RaiinNo,
                    firstTodayOdr.IraiCd ?? 0,
                    firstTodayOdr.PtInf.PtId,
                    firstTodayOdr.PtInf.PtNum,
                    firstTodayOdr.PtInf.Name ?? string.Empty,
                    firstTodayOdr.KaName ?? string.Empty,
                    firstTodayOdr.UserName ?? string.Empty,
                    firstTodayOdr.PtInf.KanaName ?? string.Empty,
                    firstTodayOdr.PtInf.Sex,
                    firstTodayOdr.PtInf.Birthday,
                    firstTodayOdr.TosekiKbn,
                    firstTodayOdr.SikyuKbn,
                    firstTodayOdr.KaId,
                    kensaIraiDetailList,
                    firstTodayOdr.IraiDate ?? 0,
                    firstTodayOdr.KensaTime ?? String.Empty,
                    firstTodayOdr.Status ?? 0,
                    firstTodayOdr.IsDeleted ?? 0,
                    new List<string>() { },
                    firstTodayOdr.UpdateDate ?? new(),
                    firstTodayOdr?.Id ?? 0
                ));
            }
        }
        else
        {
            var groupTodayOdrInfs = todayOdrInfList.Where(x => x.IraiDate >= startDate && x.IraiDate <= endDate)
                .GroupBy(x => new
                {
                    x.IraiCd
                });

            foreach (var groupTodayOdrInf in groupTodayOdrInfs)
            {
                var groupTodayOdrInfList = groupTodayOdrInf.ToList();
                var firstTodayOdr = groupTodayOdrInfList.FirstOrDefault();
                var listIrais = groupTodayOdrInfList.SelectMany(x => x.OdrInfDetails).ToList();
                var keyItem = listIrais
                    .Where(x => x.odrInfDetailEntity.ReqCd == firstTodayOdr?.IraiCd.ToString())
                    .DistinctBy(x => new
                    {
                        x.odrInfDetailEntity?.PtId,
                        x.odrInfDetailEntity?.RaiinNo,
                        x.odrInfDetailEntity?.RpEdaNo,
                        x.odrInfDetailEntity?.RpNo
                    })
                    .Select(x => new
                    {
                        x.odrInfDetailEntity?.PtId,
                        x.odrInfDetailEntity?.RaiinNo,
                        x.odrInfDetailEntity?.RpEdaNo,
                        x.odrInfDetailEntity?.RpNo
                    })
                    .ToList();


                List<KensaIraiDetailModel> kensaIraiDetailList = new();
                Dictionary<string, string> kensaNamesDict = new();

                foreach (var todayOdr in groupTodayOdrInfList)
                {
                    var todayOdrList = todayOdr.OdrInfDetails
                        .Where(item =>
                            item.commonKensaMst?.CenterCd == firstTodayOdr?.CenterCd &&
                            keyItem.Any(x => x.PtId == item.odrInfDetailEntity?.PtId &&
                                             x.RaiinNo == item.odrInfDetailEntity?.RaiinNo &&
                                             x.RpEdaNo == item.odrInfDetailEntity?.RpEdaNo &&
                                             x.RpNo == item.odrInfDetailEntity?.RpNo))
                        .Select(item => new KensaIraiDetailModel(
                            item.tenMstEntity?.KensaItemCd ?? string.Empty,
                            item.tenMstEntity?.ItemCd ?? string.Empty,
                            string.Empty,
                            string.Empty,
                            item.commonCenterKensaMst?.CenterCd ?? string.Empty,
                            item.commonCenterKensaMst?.KensaItemCd ?? string.Empty,
                            item.commonCenterKensaMst?.CenterItemCd ?? string.Empty,
                            item.commonCenterKensaMst?.KensaKana ?? string.Empty,
                            item.commonCenterKensaMst?.KensaName ?? string.Empty,
                            0,
                            item.odrInfDetailEntity.RpNo,
                            item.odrInfDetailEntity.RpEdaNo,
                            item.odrInfDetailEntity.RowNo,
                            0))
                        .ToList();
                    kensaIraiDetailList.AddRange(todayOdrList);

                    var kensaInfDetails = NoTrackingDataContext.KensaInfDetails
                        .Where(x => x.IraiCd == todayOdr.IraiCd && x.IsDeleted == DeleteTypes.None)
                        .Select(x => x.KensaItemCd)
                        .Distinct()
                        .ToList();

                    foreach (var kensaItemCd in kensaInfDetails)
                    {
                        if (!kensaNamesDict.ContainsKey(kensaItemCd))
                        {
                            var kensaName = commonCenterKensaMsts?
                                .FirstOrDefault(x => x.CenterCd == todayOdr.CenterCd && x.KensaItemCd == kensaItemCd)
                                ?.KensaName ?? string.Empty;

                            kensaNamesDict[kensaItemCd] = kensaName;
                        }
                    }
                }

                List<string> kensaNames = kensaNamesDict.Values.ToList();

                kensaIraiDetailList = kensaIraiDetailList
                    .DistinctBy(x => new { x.ItemCd, x.CenterCd, x.KensaItemCd }).ToList();

                if (kensaNames.Count > 0)
                    result.Add(new KensaIraiModel(
                        firstTodayOdr.SinDate,
                        commonKensaCenterMst.FirstOrDefault(x => x.CenterCd == firstTodayOdr.CenterCd)
                            ?.DspCenterName ?? string.Empty,
                        firstTodayOdr.RaiinNo,
                        firstTodayOdr.IraiCd ?? 0,
                        firstTodayOdr.PtInf.PtId,
                        firstTodayOdr.PtInf.PtNum,
                        firstTodayOdr.PtInf.Name ?? string.Empty,
                        firstTodayOdr.KaName ?? string.Empty,
                        firstTodayOdr.UserName ?? string.Empty,
                        firstTodayOdr.PtInf.KanaName ?? string.Empty,
                        firstTodayOdr.PtInf.Sex,
                        firstTodayOdr.PtInf.Birthday,
                        firstTodayOdr.TosekiKbn,
                        firstTodayOdr.SikyuKbn,
                        firstTodayOdr.KaId,
                        kensaIraiDetailList,
                        firstTodayOdr.IraiDate ?? 0,
                        firstTodayOdr.KensaTime ?? String.Empty,
                        firstTodayOdr.Status ?? 0,
                        firstTodayOdr.IsDeleted ?? 0,
                        kensaNames.ToList(),
                        firstTodayOdr.UpdateDate ?? new(),
                        firstTodayOdr.Id ?? 0
                    ));
            }
        }


        // Filter irai done item
        result = result.Where(item => !string.IsNullOrEmpty(item.DspCenterName))
            .OrderByDescending(x => !isRequestConfirmation ? x.SinDate : long.Parse((long)(x.IraiDate) + x.KensaTime))
            .ThenByDescending(item => item.SinDate)
            .ThenBy(item => item.PtNum)
            .ThenBy(item => item.RaiinNo)
            .ThenByDescending(item => item.SikyuKbn != 1 && item.TosekiKbn == 0)
            .ThenByDescending(item => item.SikyuKbn == 1 && item.TosekiKbn == 0)
            .ThenByDescending(item => item.SikyuKbn != 1 && item.TosekiKbn == 1)
            .ThenByDescending(item => item.SikyuKbn != 1 && item.TosekiKbn == 2)
            .ThenByDescending(item => item.SikyuKbn == 1 && item.TosekiKbn == 1)
            .ThenByDescending(item => item.SikyuKbn == 1 && item.TosekiKbn == 2)
            .ToList();
        return result;
    }

    public List<KensaIraiModel> GetKensaIraiModels(int hpId, List<KensaInfModel> kensaInfModelList)
    {
        string centerCd = kensaInfModelList.FirstOrDefault()?.CenterCd ?? string.Empty;
        int primaryKbn = kensaInfModelList.FirstOrDefault()?.PrimaryKbn ?? 0;

        var ptIdList = kensaInfModelList.Select(item => item.PtId).Distinct().ToList();
        var raiinNoList = kensaInfModelList.Select(item => item.RaiinNo).Distinct().ToList();

        var odrInfDBList = NoTrackingDataContext.OdrInfs.Where(item => item.HpId == hpId
                                                                       && item.IsDeleted == 0
                                                                       && ptIdList.Contains(item.PtId)
                                                                       && item.OdrKouiKbn >= 60
                                                                       && item.OdrKouiKbn <= 69
                                                                       && item.InoutKbn == 1
                                                                       && raiinNoList.Contains(item.RaiinNo))
                                                         .ToList();

        var ptInfDBList = NoTrackingDataContext.PtInfs.Where(item => item.HpId == hpId
                                                                     && item.IsDelete == 0
                                                                     && ptIdList.Contains(item.PtId))
                                                      .ToList();

        var odrInfDetailDBList = NoTrackingDataContext.OdrInfDetails.Where(item => item.HpId == hpId
                                                                                   && !string.IsNullOrEmpty(item.ItemCd)
                                                                                   && ptIdList.Contains(item.PtId)
                                                                                   && raiinNoList.Contains(item.RaiinNo))
                                                                    .ToList();
        var itemCdList = odrInfDetailDBList.Select(item => item.ItemCd).Distinct().ToList();

        var raiinInfDBList = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId
                                                                           && item.IsDeleted == DeleteTypes.None
                                                                           && ptIdList.Contains(item.PtId)
                                                                           && raiinNoList.Contains(item.RaiinNo))
                                                            .ToList();

        var tenMstDBList = NoTrackingDataContext.TenMsts.Where(item => item.HpId == hpId
                                                                       && item.MasterSbt != "C"
                                                                       && item.SinKouiKbn >= 60
                                                                       && item.SinKouiKbn <= 69
                                                                       && item.IsDeleted == DeleteTypes.None
                                                                       && itemCdList.Contains(item.ItemCd))
                                                        .ToList();

        var kensaItemCdList = tenMstDBList.Select(item => item.KensaItemCd).Distinct().ToList();
        var kensaItemSeqNoList = tenMstDBList.Select(item => item.KensaItemSeqNo).Distinct().ToList();

        var kensaMstDBList = NoTrackingDataContext.KensaMsts.Where(item => item.HpId == hpId
                                                                           && kensaItemSeqNoList.Contains(item.KensaItemSeqNo)
                                                                           && kensaItemCdList.Contains(item.KensaItemCd))
                                                            .ToList();

        if (kensaMstDBList.Any())
        {
            kensaMstDBList = kensaMstDBList.GroupBy(item => item.KensaItemCd)
                                           .Select(item => item.OrderBy(sort => sort.KensaItemSeqNo).First())
                                           .ToList();
        }

        var kensaMstEntities = kensaMstDBList.Where(item => (item.CenterCd == centerCd || (primaryKbn == 1 && string.IsNullOrEmpty(item.CenterCd)))).ToList();

        var kensaInfDBList = NoTrackingDataContext.KensaInfs.Where(item => item.HpId == hpId
                                                                           && raiinNoList.Contains(item.RaiinNo)
                                                                           && item.CenterCd == centerCd
                                                                           && item.IsDeleted == 0)
                                              .ToList();

        var kensaInfDetailDBList = NoTrackingDataContext.KensaInfDetails.Where(item => item.HpId == hpId
                                                                                       && raiinNoList.Contains(item.RaiinNo)
                                                                                       && item.IsDeleted == 0)
                                                          .ToList();

        #region Get old kensaInf list
        List<KensaIraiModel> oldKensaIraiList = new();
        foreach (var model in kensaInfModelList)
        {
            var kensaInf = kensaInfDBList.FirstOrDefault(item => item.IraiCd == model.IraiCd);
            if (kensaInf == null)
            {
                continue;
            }
            var raiinInf = raiinInfDBList.FirstOrDefault(item => item.RaiinNo == model.RaiinNo);
            if (raiinInf == null)
            {
                continue;
            }
            var ptInf = ptInfDBList.FirstOrDefault(item => item.PtId == model.PtId);
            if (ptInf == null)
            {
                continue;
            }
            List<KensaIraiDetailModel> kensaIraiDetailList = new();
            var kensaInfDetailItemList = kensaInfDetailDBList.Where(item => item.IraiCd == kensaInf.IraiCd).ToList();

            var odrInfItemList = odrInfDBList.Where(odrInf => model.RaiinNo == odrInf.RaiinNo
                                                              && odrInf.SikyuKbn == kensaInf.SikyuKbn
                                                              && odrInf.TosekiKbn == kensaInf.TosekiKbn)
                                             .ToList();
            if (!odrInfItemList.Any())
            {
                continue;
            }

            foreach (var detail in kensaInfDetailItemList)
            {
                var kensaMst = kensaMstEntities.Where(item => item.KensaItemCd == detail.KensaItemCd)
                                               .OrderBy(item => item.KensaItemSeqNo)
                                               .FirstOrDefault();
                if (kensaMst == null)
                {
                    continue;
                }
                var tenMstItem = tenMstDBList.FirstOrDefault(item => kensaMst.KensaItemCd == item.KensaItemCd);
                if (tenMstItem == null)
                {
                    continue;
                }
                var odrInfDetailItem = odrInfDetailDBList.FirstOrDefault(item => odrInfItemList.Any(odr => item.RaiinNo == odr.RaiinNo
                                                                                                           && item.RpNo == odr.RpNo
                                                                                                           && item.RpEdaNo == odr.RpEdaNo)
                                                                                 && item.ItemCd == tenMstItem.ItemCd
                                                                                 && item.ReqCd == detail.IraiCd.ToString());
                if (odrInfDetailItem == null)
                {
                    continue;
                }
                odrInfDetailDBList.Remove(odrInfDetailItem);
                var detailModel = new KensaIraiDetailModel(
                                      tenMstItem.KensaItemCd ?? string.Empty,
                                      tenMstItem.ItemCd ?? string.Empty,
                                      tenMstItem.Name ?? string.Empty,
                                      tenMstItem.KanaName1 ?? string.Empty,
                                      kensaMst?.CenterCd ?? string.Empty,
                                      kensaMst?.KensaItemCd ?? string.Empty,
                                      kensaMst?.CenterItemCd1 ?? string.Empty,
                                      kensaMst?.KensaKana ?? string.Empty,
                                      kensaMst?.KensaName ?? string.Empty,
                                      kensaMst?.ContainerCd ?? 0,
                                      odrInfDetailItem?.RpNo ?? 0,
                                      odrInfDetailItem?.RpEdaNo ?? 0,
                                      odrInfDetailItem?.RowNo ?? 0,
                                      0);
                kensaIraiDetailList.Add(detailModel);
            }

            var newDetailModelList = odrInfDetailDBList.Where(item => odrInfItemList.Any(odr => item.RaiinNo == odr.RaiinNo
                                                                                                && item.RpNo == odr.RpNo
                                                                                                && item.RpEdaNo == odr.RpEdaNo))
                                                       .ToList();

            foreach (var odrDetail in newDetailModelList)
            {
                var tenMstItem = tenMstDBList.FirstOrDefault(item => odrDetail.ItemCd == item.ItemCd);
                if (tenMstItem == null)
                {
                    continue;
                }

                var kensaMst = kensaMstEntities.Where(item => item.KensaItemCd == tenMstItem.KensaItemCd)
                                               .OrderBy(item => item.KensaItemSeqNo)
                                               .FirstOrDefault();
                if (kensaMst == null)
                {
                    continue;
                }
                var odrInfDetailItem = odrInfDetailDBList.FirstOrDefault(item => odrInfItemList.Any(odr => item.RaiinNo == odr.RaiinNo
                                                                                                           && item.RpNo == odr.RpNo
                                                                                                           && item.RpEdaNo == odr.RpEdaNo)
                                                                                 && item.ItemCd == tenMstItem.ItemCd
                                                                                 && string.IsNullOrEmpty(item.ReqCd));
                if (odrInfDetailItem == null)
                {
                    continue;
                }
                odrInfDetailDBList.Remove(odrInfDetailItem);
                var detailModel = new KensaIraiDetailModel(
                                      tenMstItem.KensaItemCd ?? string.Empty,
                                      tenMstItem.ItemCd ?? string.Empty,
                                      tenMstItem.Name ?? string.Empty,
                                      tenMstItem.KanaName1 ?? string.Empty,
                                      kensaMst?.CenterCd ?? string.Empty,
                                      kensaMst?.KensaItemCd ?? string.Empty,
                                      kensaMst?.CenterItemCd1 ?? string.Empty,
                                      kensaMst?.KensaKana ?? string.Empty,
                                      kensaMst?.KensaName ?? string.Empty,
                                      kensaMst?.ContainerCd ?? 0,
                                      odrInfDetailItem?.RpNo ?? 0,
                                      odrInfDetailItem?.RpEdaNo ?? 0,
                                      odrInfDetailItem?.RowNo ?? 0,
                                      0);
                kensaIraiDetailList.Add(detailModel);
            }

            oldKensaIraiList.Add(new KensaIraiModel(
                                     raiinInf.SinDate,
                                     kensaInf.RaiinNo,
                                     kensaInf.IraiCd,
                                     kensaInf.PtId,
                                     ptInf.PtNum,
                                     ptInf.Name ?? string.Empty,
                                     ptInf.KanaName ?? string.Empty,
                                     ptInf.Sex,
                                     ptInf.Birthday,
                                     kensaInf.TosekiKbn,
                                     kensaInf.SikyuKbn,
                                     raiinInf.KaId,
                                     kensaInf.UpdateDate,
                                     kensaIraiDetailList
                                ));
        }
        #endregion

        #region Get new kensaInf list
        List<KensaIraiModel> newKensaIraiList = new();
        var groupOdrInf = odrInfDBList.GroupBy(item => new
        {
            item.RaiinNo,
            item.SikyuKbn,
            item.TosekiKbn
        }).ToList();
        foreach (var item in groupOdrInf)
        {
            var odrInfItemList = item.ToList();
            foreach (var odrInf in odrInfItemList)
            {
                List<KensaIraiDetailModel> kensaIraiDetailList = new();
                var raiinInf = raiinInfDBList.FirstOrDefault(item => item.RaiinNo == odrInf.RaiinNo);
                if (raiinInf == null)
                {
                    continue;
                }
                var ptInf = ptInfDBList.FirstOrDefault(item => item.PtId == odrInf.PtId);
                if (ptInf == null)
                {
                    continue;
                }

                var newDetailModelList = odrInfDetailDBList.Where(item => odrInfItemList.Any(odr => item.RaiinNo == odr.RaiinNo
                                                                                                    && item.RpNo == odr.RpNo
                                                                                                    && item.RpEdaNo == odr.RpEdaNo))
                                                      .ToList();

                foreach (var odrDetail in newDetailModelList)
                {
                    var tenMstItem = tenMstDBList.FirstOrDefault(item => odrDetail.ItemCd == item.ItemCd);
                    if (tenMstItem == null)
                    {
                        continue;
                    }

                    var kensaMst = kensaMstEntities.Where(item => item.KensaItemCd == tenMstItem.KensaItemCd)
                                                   .OrderBy(item => item.KensaItemSeqNo)
                                                   .FirstOrDefault();
                    if (kensaMst == null)
                    {
                        continue;
                    }
                    var odrInfDetailItem = odrInfDetailDBList.FirstOrDefault(item => odrInfItemList.Any(odr => item.RaiinNo == odr.RaiinNo
                                                                                                               && item.RpNo == odr.RpNo
                                                                                                               && item.RpEdaNo == odr.RpEdaNo)
                                                                                     && item.ItemCd == tenMstItem.ItemCd
                                                                                     && string.IsNullOrEmpty(item.ReqCd));
                    if (odrInfDetailItem == null)
                    {
                        continue;
                    }
                    odrInfDetailDBList.Remove(odrInfDetailItem);
                    var detailModel = new KensaIraiDetailModel(
                                          tenMstItem.KensaItemCd ?? string.Empty,
                                          tenMstItem.ItemCd ?? string.Empty,
                                          tenMstItem.Name ?? string.Empty,
                                          tenMstItem.KanaName1 ?? string.Empty,
                                          kensaMst?.CenterCd ?? string.Empty,
                                          kensaMst?.KensaItemCd ?? string.Empty,
                                          kensaMst?.CenterItemCd1 ?? string.Empty,
                                          kensaMst?.KensaKana ?? string.Empty,
                                          kensaMst?.KensaName ?? string.Empty,
                                          kensaMst?.ContainerCd ?? 0,
                                          odrInfDetailItem?.RpNo ?? 0,
                                          odrInfDetailItem?.RpEdaNo ?? 0,
                                          odrInfDetailItem?.RowNo ?? 0,
                                          0);
                    kensaIraiDetailList.Add(detailModel);
                }
                if (kensaIraiDetailList.Any())
                {
                    newKensaIraiList.Add(new KensaIraiModel(
                                             raiinInf.SinDate,
                                             raiinInf.RaiinNo,
                                             0,
                                             ptInf.PtId,
                                             ptInf.PtNum,
                                             ptInf.Name ?? string.Empty,
                                             ptInf.KanaName ?? string.Empty,
                                             ptInf.Sex,
                                             ptInf.Birthday,
                                             odrInf.TosekiKbn,
                                             odrInf.SikyuKbn,
                                             raiinInf.KaId,
                                             odrInf.UpdateDate,
                                             kensaIraiDetailList));
                }
            }
        }
        #endregion

        oldKensaIraiList.AddRange(newKensaIraiList);
        oldKensaIraiList = oldKensaIraiList.OrderBy(item => item.SinDate)
                                           .ThenBy(item => item.PtNum)
                                           .ThenBy(item => item.SikyuKbn)
                                           .ToList();
        return oldKensaIraiList;
    }

    public List<KensaIraiModel> CreateDataKensaIraiRenkei(int hpId, int userId, List<KensaIraiModel> kensaIraiList, string centerCd, int systemDate)
    {
        List<KensaInf> kensaInfs = new();
        List<(KensaInf kensaInf, List<KensaInfDetail> kensaInfDetailList, List<OdrInfDetail> odrInfDetailList)> modelRelationList = new();
        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(
            () =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    foreach (var kensaIrai in kensaIraiList)
                    {
                        KensaInf kensaInf = new()
                        {
                            HpId = hpId,
                            PtId = kensaIrai.PtId,
                            RaiinNo = kensaIrai.RaiinNo,
                            IraiDate = systemDate,
                            InoutKbn = 1,
                            Status = 0,
                            TosekiKbn = kensaIrai.TosekiKbn,
                            SikyuKbn = kensaIrai.SikyuKbn >= 1 ? 1 : kensaIrai.SikyuKbn,
                            ResultCheck = 0,
                            CenterCd = centerCd,
                            Nyubi = string.Empty,
                            Yoketu = string.Empty,
                            Bilirubin = string.Empty,
                            IsDeleted = 0,
                            CreateDate = CIUtil.GetJapanDateTimeNow(),
                            CreateId = userId,
                            UpdateDate = CIUtil.GetJapanDateTimeNow(),
                            UpdateId = userId,
                        };
                        kensaInfs.Add(kensaInf);

                        var ptIdList = kensaIraiList.Select(item => item.PtId).Distinct().ToList();
                        var raiinNoList = kensaIraiList.Select(item => item.RaiinNo).Distinct().ToList();
                        List<long> rpNoList = new();
                        List<long> rpEdaNoList = new();
                        List<int> rowNoList = new();

                        foreach (var detailList in from KensaIraiModel item in kensaIraiList
                                                   let detailList = item.KensaIraiDetails
                                                   select detailList)
                        {
                            rpNoList.AddRange(detailList.Select(detail => detail.RpNo).Distinct().ToList());
                            rpEdaNoList.AddRange(detailList.Select(detail => detail.RpEdaNo).Distinct().ToList());
                            rowNoList.AddRange(detailList.Select(detail => detail.RowNo).Distinct().ToList());
                        }
                        rpNoList = rpNoList.Distinct().ToList();
                        rpEdaNoList = rpEdaNoList.Distinct().ToList();
                        rowNoList = rowNoList.Distinct().ToList();

                        var odrInfDetailDBList = TrackingDataContext.OdrInfDetails.Where(item => item.HpId == hpId
                                                                                                 && ptIdList.Contains(item.PtId)
                                                                                                 && raiinNoList.Contains(item.RaiinNo)
                                                                                                 && rpNoList.Contains(item.RpNo)
                                                                                                 && rpEdaNoList.Contains(item.RpEdaNo)
                                                                                                 && rowNoList.Contains(item.RowNo))
                                                                                   .ToList();

                        List<OdrInfDetail> odrInfDetailList = new();
                        List<KensaInfDetail> kensaInfDetailList = new();
                        foreach (var kensaIraiDetail in kensaIrai.KensaIraiDetails)
                        {
                            kensaInfDetailList.Add(new KensaInfDetail()
                            {
                                HpId = hpId,
                                PtId = kensaIrai.PtId,
                                RaiinNo = kensaIrai.RaiinNo,
                                IraiDate = systemDate,
                                KensaItemCd = kensaIraiDetail.KensaItemCd,
                                ResultVal = string.Empty,
                                ResultType = string.Empty,
                                AbnormalKbn = string.Empty,
                                IsDeleted = 0,
                                CmtCd1 = string.Empty,
                                CmtCd2 = string.Empty,
                                CreateDate = CIUtil.GetJapanDateTimeNow(),
                                CreateId = userId,
                                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                                UpdateId = userId,
                            });

                            if (!string.IsNullOrEmpty(kensaIraiDetail.KensaItemCd))
                            {
                                var odrInfDetail = odrInfDetailDBList.FirstOrDefault(item => item.HpId == hpId
                                                                                             && item.PtId == kensaIrai.PtId
                                                                                             && item.RaiinNo == kensaIrai.RaiinNo
                                                                                             && item.RpNo == kensaIraiDetail.RpNo
                                                                                             && item.RpEdaNo == kensaIraiDetail.RpEdaNo
                                                                                             && item.RowNo == kensaIraiDetail.RowNo);
                                if (odrInfDetail != null)
                                {
                                    odrInfDetailList.Add(odrInfDetail);
                                }
                            }
                        }
                        modelRelationList.Add((kensaInf, kensaInfDetailList, odrInfDetailList));
                    }

                    TrackingDataContext.KensaInfs.AddRange(kensaInfs);
                    TrackingDataContext.SaveChanges();
                    List<KensaInfDetail> kensaDetails = new();

                    foreach (var modelRelation in modelRelationList)
                    {
                        foreach (var detail in modelRelation.kensaInfDetailList)
                        {
                            detail.IraiCd = modelRelation.kensaInf.IraiCd;
                        }

                        kensaDetails.AddRange(modelRelation.kensaInfDetailList);

                        foreach (var odrDetail in modelRelation.odrInfDetailList)
                        {
                            odrDetail.JissiKbn = 1;
                            odrDetail.JissiDate = CIUtil.GetJapanDateTimeNow();
                            odrDetail.JissiId = userId;
                            odrDetail.ReqCd = modelRelation.kensaInf.IraiCd.AsString();
                        }
                    }
                    TrackingDataContext.KensaInfDetails.AddRange(kensaDetails);
                    TrackingDataContext.SaveChanges();
                    transaction.Commit();
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            });

        var raiinNoList = kensaIraiList.Select(item => item.RaiinNo).Distinct().ToList();
        var raiinInfList = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId
                                                                         && raiinNoList.Contains(item.RaiinNo)
                                                                         && item.IsDeleted == 0);
        foreach (var model in kensaIraiList)
        {
            var kensaInf = kensaInfs.FirstOrDefault(item => item.PtId == model.PtId
                                                            && item.RaiinNo == model.RaiinNo
                                                            && item.SikyuKbn == model.SikyuKbn
                                                            && item.TosekiKbn == model.TosekiKbn);
            if (kensaInf == null)
            {
                continue;
            }
            var raiinInf = raiinInfList.FirstOrDefault(item => item.RaiinNo == model.RaiinNo);
            model.UpdateIraiCd(kensaInf.IraiCd, raiinInf?.KaId ?? 0);
        }
        return kensaIraiList;
    }

    public List<KensaIraiModel> ReCreateDataKensaIraiRenkei(int hpId, int userId, List<KensaIraiModel> kensaIraiList, int systemDate)
    {
        List<(KensaInf kensaInf, List<KensaInfDetail> kensaInfDetailList, List<OdrInfDetail> odrInfDetailList)> modelRelationList = new();
        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(
            () =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    kensaIraiList = kensaIraiList.OrderByDescending(x => x.IraiCd).ToList();
                    var iraiCdList = kensaIraiList.Select(item => item.IraiCd).Distinct().ToList();
                    var kensaIraiDBList = TrackingDataContext.KensaInfs.Where(item => item.HpId == hpId
                                                                                      && item.IsDeleted == 0
                                                                                      && iraiCdList.Contains(item.IraiCd))
                                                                       .ToList();

                    string centerCd = kensaIraiDBList.FirstOrDefault()?.CenterCd ?? string.Empty;
                    var ptIdList = kensaIraiList.Select(item => item.PtId).Distinct().ToList();
                    var raiinNoList = kensaIraiList.Select(item => item.RaiinNo).Distinct().ToList();

                    var odrInfDetailDBList = TrackingDataContext.OdrInfDetails.Where(item => item.HpId == hpId
                                                                                             && ptIdList.Contains(item.PtId)
                                                                                             && raiinNoList.Contains(item.RaiinNo))
                                                                              .ToList();

                    var kensaInfDetailDBList = TrackingDataContext.KensaInfDetails.Where(item => item.HpId == hpId
                                                                                                 && item.IsDeleted == 0
                                                                                                 && iraiCdList.Contains(item.IraiCd))
                                                                                  .ToList();

                    var listKey = new List<string>();

                    foreach (var kensaIrai in kensaIraiList)
                    {
                        if (!kensaIrai.KensaIraiDetails.Any())
                        {
                            continue;
                        }
                        var key = kensaIrai.PtId + "," + kensaIrai.RaiinNo + "," + kensaIrai.KensaIraiDetails.FirstOrDefault()?.RpEdaNo + "," + kensaIrai.KensaIraiDetails.FirstOrDefault()?.RpNo;
                        if (listKey.Contains(key))
                        {
                            kensaIrai.KensaIraiDetails = new List<KensaIraiDetailModel>();
                        }
                        listKey.Add(key);

                        var firstDetail = kensaIrai.KensaIraiDetails.FirstOrDefault();
                        if (firstDetail == null) continue;
                        var odr = TrackingDataContext.OdrInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == kensaIrai.PtId && x.RaiinNo == kensaIrai.RaiinNo && x.RpNo == firstDetail.RpNo && x.RpEdaNo == firstDetail.RpEdaNo);
                        if (odr == null)
                        {
                            continue;
                        }
                        var kensaInf = kensaIraiDBList.FirstOrDefault(item => item.IraiCd == kensaIrai.IraiCd);
                        if (kensaInf == null && kensaIrai.IraiCd == 0)
                        {
                            kensaInf = new KensaInf();
                            kensaInf.HpId = hpId;
                            kensaInf.IsDeleted = 0;
                            kensaInf.RaiinNo = kensaIrai.RaiinNo;
                            kensaInf.PtId = kensaIrai.PtId;
                            kensaInf.CenterCd = centerCd;
                            kensaInf.IraiCd = 0;
                            kensaInf.CreateDate = CIUtil.GetJapanDateTimeNow();
                            kensaInf.CreateId = userId;
                            kensaInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                            kensaInf.UpdateId = userId;
                        }
                        if (kensaInf != null)
                        {
                            kensaInf.Status = 0;
                            kensaInf.TosekiKbn = odr.TosekiKbn;
                            kensaInf.SikyuKbn = odr.SikyuKbn >= 1 ? 1 : odr.SikyuKbn;
                            kensaInf.ResultCheck = 0;
                            kensaInf.Nyubi = string.Empty;
                            kensaInf.Yoketu = string.Empty;
                            kensaInf.Bilirubin = string.Empty;
                            kensaInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                            kensaInf.UpdateId = userId;

                            List<KensaInfDetail> newKensaInfDetailList = new();
                            List<OdrInfDetail> newOdrInfList = new();
                            foreach (var kensa in kensaIrai.KensaIraiDetails)
                            {
                                newKensaInfDetailList.Add(new KensaInfDetail()
                                {
                                    HpId = hpId,
                                    PtId = kensaIrai.PtId,
                                    RaiinNo = kensaIrai.RaiinNo,
                                    IraiDate = kensaInf.IraiDate,
                                    KensaItemCd = kensa.KensaItemCd,
                                    ResultVal = string.Empty,
                                    ResultType = string.Empty,
                                    AbnormalKbn = string.Empty,
                                    IsDeleted = 0,
                                    CmtCd1 = string.Empty,
                                    CmtCd2 = string.Empty,
                                    CreateDate = CIUtil.GetJapanDateTimeNow(),
                                    CreateId = userId,
                                    UpdateId = userId,
                                    UpdateDate = CIUtil.GetJapanDateTimeNow()
                                });

                                if (!string.IsNullOrEmpty(kensa.KensaItemCd))
                                {
                                    var odrInfDetail = odrInfDetailDBList.FirstOrDefault(item => item.PtId == kensaIrai.PtId
                                                                                                 && item.RaiinNo == kensaIrai.RaiinNo
                                                                                                 && item.RpNo == kensa.RpNo
                                                                                                 && item.RpEdaNo == kensa.RpEdaNo
                                                                                                 && item.RowNo == kensa.RowNo);
                                    if (odrInfDetail != null)
                                    {
                                        newOdrInfList.Add(odrInfDetail);
                                    }
                                }
                            }
                            if (kensaInf.IraiCd == 0)
                            {
                                TrackingDataContext.KensaInfs.Add(kensaInf);
                                TrackingDataContext.SaveChanges();
                            }
                            modelRelationList.Add((kensaInf, newKensaInfDetailList, newOdrInfList));
                        }

                        var kensaInfDetailList = kensaInfDetailDBList.Where(item => item.IraiCd == kensaIrai.IraiCd);
                        foreach (var detail in kensaInfDetailList)
                        {
                            detail.IsDeleted = 1;
                            detail.UpdateDate = CIUtil.GetJapanDateTimeNow();
                            detail.UpdateId = userId;
                        }

                        if (kensaInf != null)
                        {
                            var odrInfDetails = odrInfDetailDBList.Where(item => item.PtId == kensaInf.PtId
                                                                                 && item.RaiinNo == kensaInf.RaiinNo
                                                                                 && item.ReqCd == kensaInf.IraiCd.ToString())
                                                                  .ToList();
                            foreach (var odrInfDetail in odrInfDetails)
                            {
                                odrInfDetail.JissiKbn = 0;
                                odrInfDetail.JissiDate = null;
                                odrInfDetail.JissiId = 0;
                                odrInfDetail.JissiMachine = string.Empty;
                                odrInfDetail.ReqCd = string.Empty;
                            }
                        }
                    }

                    List<KensaInfDetail> kensaDetailList = new();
                    foreach (var modelRelation in modelRelationList)
                    {
                        foreach (var detail in modelRelation.kensaInfDetailList)
                        {
                            detail.IraiCd = modelRelation.kensaInf.IraiCd;
                        }

                        kensaDetailList.AddRange(modelRelation.kensaInfDetailList);

                        foreach (var odrDetail in modelRelation.odrInfDetailList)
                        {
                            odrDetail.JissiKbn = 1;
                            odrDetail.JissiDate = CIUtil.GetJapanDateTimeNow();
                            odrDetail.JissiId = userId;
                            odrDetail.ReqCd = modelRelation.kensaInf.IraiCd.AsString();
                        }
                    }
                    TrackingDataContext.KensaInfDetails.AddRange(kensaDetailList);
                    TrackingDataContext.SaveChanges();
                    transaction.Commit();
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            });

        var raiinNoList = kensaIraiList.Select(item => item.RaiinNo).Distinct().ToList();
        var raiinInfList = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId
                                                                         && raiinNoList.Contains(item.RaiinNo)
                                                                         && item.IsDeleted == 0);
        foreach (var model in kensaIraiList)
        {
            var mode = modelRelationList.FirstOrDefault(item => item.kensaInf.PtId == model.PtId
                                                                     && item.kensaInf.RaiinNo == model.RaiinNo
                                                                     && item.kensaInf.SikyuKbn == model.SikyuKbn
                                                                     && item.kensaInf.TosekiKbn == model.TosekiKbn);
            if (mode.kensaInf == null)
            {
                continue;
            }
            var raiinInf = raiinInfList.FirstOrDefault(item => item.RaiinNo == model.RaiinNo);
            model.UpdateIraiCd(mode.kensaInf.IraiCd, raiinInf?.KaId ?? 0);
        }
        return kensaIraiList;
    }

    public bool CheckExistCenterCd(int hpId, string centerCd)
    {
        return NoTrackingDataContext.KensaCenterMsts.Any(item => item.HpId == hpId && item.CenterCd == centerCd);
    }

    public bool CheckExistCenterCd(int hpId, List<string> centerCdList)
    {
        centerCdList = centerCdList.Distinct().ToList();
        return NoTrackingDataContext.KensaCenterMsts.Count(item => item.HpId == hpId && item.CenterCd != null && centerCdList.Contains(item.CenterCd)) != centerCdList.Count;
    }

    public bool CheckExistIraiCd(int hpId, List<long> iraiCdList)
    {
        iraiCdList = iraiCdList.Distinct().ToList();
        return NoTrackingDataContext.KensaInfDetails.Count(item => item.HpId == hpId && iraiCdList.Contains(item.IraiCd)) != iraiCdList.Count;
    }

    public List<KensaInfModel> GetKensaInfModels(int hpId, int startDate, int endDate, string centerCd = "")
    {
        var kensaInfList = NoTrackingDataContext.KensaInfs.Where(item => item.HpId == hpId
                                                                         && item.IraiDate >= startDate
                                                                         && item.IraiDate <= endDate
                                                                         && item.InoutKbn != 0)
                                                          .ToList();

        var ptIdList = kensaInfList.Select(item => item.PtId).Distinct().ToList();
        var raiinNoList = kensaInfList.Select(item => item.RaiinNo).Distinct().ToList();
        var iraiDateList = kensaInfList.Select(item => item.IraiDate).Distinct().ToList();
        var iraiCdList = kensaInfList.Select(item => item.IraiCd).Distinct().ToList();
        var centerCdList = kensaInfList.Select(item => item.CenterCd).Distinct().ToList();

        var kensaInfDetailList = NoTrackingDataContext.KensaInfDetails.Where(item => item.HpId == hpId
                                                                                     && item.IraiDate >= startDate
                                                                                     && item.IraiDate <= endDate
                                                                                     && ptIdList.Contains(item.PtId)
                                                                                     && raiinNoList.Contains(item.RaiinNo)
                                                                                     && iraiDateList.Contains(item.IraiDate)
                                                                                     && iraiCdList.Contains(item.IraiCd)
                                                                                     && item.IsDeleted == 0)
                                                                      .ToList();
        var ptInfList = NoTrackingDataContext.PtInfs.Where(item => item.HpId == hpId
                                                                   && item.IsDelete == 0
                                                                   && ptIdList.Contains(item.PtId))
                                                    .ToList();
        var kensaCenterMstEntity = NoTrackingDataContext.KensaCenterMsts.Where(item => item.HpId == hpId
                                                                                       && centerCdList.Contains(item.CenterCd));

        if (!string.IsNullOrEmpty(centerCd))
        {
            kensaCenterMstEntity = kensaCenterMstEntity.Where(x => x.CenterCd == centerCd);
        }
        var kensaCenterMstList = kensaCenterMstEntity.ToList();

        var query = from kensaInf in kensaInfList
                    join ptInf in ptInfList on
                    new { kensaInf.HpId, kensaInf.PtId } equals
                    new { ptInf.HpId, ptInf.PtId }
                    join kensaCenterMst in kensaCenterMstList on
                    new { kensaInf.HpId, kensaInf.CenterCd } equals
                    new { kensaCenterMst.HpId, kensaCenterMst.CenterCd } into KensaInfCenterMsts
                    from KensaInfCenterMst in KensaInfCenterMsts.DefaultIfEmpty()
                    select new
                    {
                        kensaInf,
                        ptInf.Name,
                        ptInf.PtNum,
                        CenterName = KensaInfCenterMst != null ? KensaInfCenterMst.CenterName : string.Empty,
                        PrimaryKbn = KensaInfCenterMst == null ? 0 : KensaInfCenterMst.PrimaryKbn,
                        KensaInfDetails = from kensaInfDetail in kensaInfDetailList
                                          where kensaInf.HpId == kensaInfDetail.HpId &&
                                                kensaInf.IraiDate == kensaInfDetail.IraiDate &&
                                                kensaInf.IraiCd == kensaInfDetail.IraiCd &&
                                                kensaInf.PtId == kensaInfDetail.PtId &&
                                                kensaInf.RaiinNo == kensaInfDetail.RaiinNo &&
                                                kensaInfDetail.IsDeleted == 0
                                          select kensaInfDetail
                    };
        var result = query.Select(item => new KensaInfModel(
                                          item.kensaInf.PtId,
                                          item.kensaInf.IraiDate,
                                          item.kensaInf.RaiinNo,
                                          item.kensaInf.IraiCd,
                                          item.kensaInf.InoutKbn,
                                          item.kensaInf.Status,
                                          item.kensaInf.TosekiKbn,
                                          item.kensaInf.SikyuKbn,
                                          item.kensaInf.ResultCheck,
                                          item.kensaInf.CenterCd ?? string.Empty,
                                          item.kensaInf.Nyubi ?? string.Empty,
                                          item.kensaInf.Yoketu ?? string.Empty,
                                          item.kensaInf.Bilirubin ?? string.Empty,
                                          item.kensaInf.IsDeleted == 1,
                                          item.kensaInf.CreateId,
                                          item.PrimaryKbn,
                                          item.PtNum,
                                          item.Name,
                                          item.CenterName,
                                          item.kensaInf.UpdateDate,
                                          item.kensaInf.CreateDate,
                                          item.KensaInfDetails.Select(detail => new KensaInfDetailModel(
                                                                                detail.PtId,
                                                                                detail.IraiDate,
                                                                                detail.RaiinNo,
                                                                                detail.IraiCd,
                                                                                detail.SeqNo,
                                                                                detail.KensaItemCd ?? string.Empty,
                                                                                detail.ResultVal ?? string.Empty,
                                                                                detail.ResultType ?? string.Empty,
                                                                                detail.AbnormalKbn ?? string.Empty,
                                                                                detail.IsDeleted,
                                                                                detail.CmtCd1 ?? string.Empty,
                                                                                detail.CmtCd2 ?? string.Empty,
                                                                                new())).ToList()))
                          .OrderByDescending(x => x.IraiDate)
                          .ThenByDescending(x => x.UpdateDate)
                          .ToList();
        return result;
    }

    public bool DeleteKensaInfModel(int hpId, int userId, List<KensaInfModel> kensaInfList)
    {
        var iraiCdList = kensaInfList.Select(item => item.IraiCd).Distinct().ToList();
        var iraiCdStringList = kensaInfList.Select(item => item.IraiCd.ToString()).Distinct().ToList();
        var raiinNoList = kensaInfList.Select(item => item.RaiinNo).Distinct().ToList();
        List<long> ptIdList = new();
        List<long> seqNoList = new();

        foreach (var item in kensaInfList.Select(item => item.KensaInfDetailModelList).ToList())
        {
            ptIdList.AddRange(item.Select(detail => detail.PtId).ToList());
            seqNoList.AddRange(item.Select(detail => detail.SeqNo).ToList());
        }
        ptIdList = ptIdList.Distinct().ToList();
        seqNoList = seqNoList.Distinct().ToList();

        var kensaInfDBList = TrackingDataContext.KensaInfs.Where(item => item.HpId == hpId
                                                                         && iraiCdList.Contains(item.IraiCd))
                                                          .ToList();

        var kensaInfDetailDBList = TrackingDataContext.KensaInfDetails.Where(item => item.HpId == hpId
                                                                                     && iraiCdList.Contains(item.IraiCd)
                                                                                     && ptIdList.Contains(item.PtId)
                                                                                     && seqNoList.Contains(item.SeqNo))
                                                                      .ToList();
        var orderInfDetailDBList = TrackingDataContext.OdrInfDetails.Where(item => item.HpId == hpId
                                                                                   && raiinNoList.Contains(item.RaiinNo)
                                                                                   && iraiCdStringList.Contains(item.ReqCd ?? string.Empty))
                                                                    .ToList();
        foreach (var kensaInf in kensaInfList)
        {
            var deleteKensaInf = kensaInfDBList.FirstOrDefault(item => item.IraiCd == kensaInf.IraiCd);
            if (deleteKensaInf != null)
            {
                deleteKensaInf.IsDeleted = 1;
                deleteKensaInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                deleteKensaInf.UpdateId = userId;
            }

            foreach (var detail in kensaInf.KensaInfDetailModelList)
            {
                var deleteKensaInfDetail = kensaInfDetailDBList.FirstOrDefault(item => item.IraiCd == detail.IraiCd
                                                                                       && item.PtId == detail.PtId
                                                                                       && item.SeqNo == detail.SeqNo);
                if (deleteKensaInfDetail != null)
                {
                    deleteKensaInfDetail.IsDeleted = 1;
                    deleteKensaInfDetail.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    deleteKensaInfDetail.UpdateId = userId;
                }
            }
            var odrInfDetails = orderInfDetailDBList.Where(item => item.PtId == kensaInf.PtId
                                                                   && item.RaiinNo == kensaInf.RaiinNo
                                                                   && item.ReqCd == kensaInf.IraiCd.ToString());
            foreach (var odrInfDetail in odrInfDetails)
            {
                odrInfDetail.JissiKbn = 0;
                odrInfDetail.JissiDate = null;
                odrInfDetail.JissiId = 0;
                odrInfDetail.JissiMachine = string.Empty;
                odrInfDetail.ReqCd = string.Empty;
            }
        }
        return TrackingDataContext.SaveChanges() > 0;
    }

    public bool CheckExistIraiCdList(int hpId, List<long> iraiCdList)
    {
        iraiCdList = iraiCdList.Distinct().ToList();
        var kensaInfCount = TrackingDataContext.KensaInfs.Count(item => item.HpId == hpId
                                                                        && iraiCdList.Contains(item.IraiCd));
        return iraiCdList.Count == kensaInfCount;
    }

    public List<long> GetIraiCdNotExistList(int hpId, List<long> iraiCdList)
    {
        iraiCdList = iraiCdList.Distinct().ToList();
        var kensaInfExist = TrackingDataContext.KensaInfs.Where(item => item.HpId == hpId
                                                                           && iraiCdList.Contains(item.IraiCd))
                                                         .Select(item => item.IraiCd)
                                                         .Distinct()
                                                         .ToList();
        var kensaInfNotExist = iraiCdList.Where(item => !kensaInfExist.Contains(item)).ToList();
        return kensaInfNotExist;
    }

    public List<KensaIraiLogModel> GetKensaIraiLogModels(int hpId, int startDate, int endDate)
    {
        var kensaIraiLogEntities = NoTrackingDataContext.KensaIraiLogs.Where(item => item.HpId == hpId
                                                                                     && item.IraiDate >= startDate
                                                                                     && item.IraiDate <= endDate);
        var kencenterMstEntities = NoTrackingDataContext.CommonKensaCenterMst;
        var query = from kensaIraiLog in kensaIraiLogEntities
                    join kensaCenterMst in kencenterMstEntities on
                        kensaIraiLog.CenterCd equals kensaCenterMst.CenterCd into kensaInfList
                    from kensaInf in kensaInfList.DefaultIfEmpty()
                    select new
                    {
                        kensaIraiLog,
                        CenterName = kensaInf == null ? string.Empty : kensaInf.CenterName,
                    };
        var result = query.AsEnumerable()
                          .Select(item => new KensaIraiLogModel(
                                              item.kensaIraiLog.IraiDate,
                                              item.kensaIraiLog.CenterCd,
                                              item.CenterName,
                                              item.kensaIraiLog.FromDate,
                                              item.kensaIraiLog.ToDate,
                                              item.kensaIraiLog.CreateDate))
                          .OrderByDescending(item => item.CreateDate)
                          .ToList();
        return result;
    }

    public KensaIraiLogModel DownloadKensaIraiLogModels(int hpId, string centerCd, DateTime createdDate)
    {
        var kensaIraiLogEntity = NoTrackingDataContext.KensaIraiLogs.FirstOrDefault(item => item.HpId == hpId
            && item.CreateDate == createdDate
            && item.CenterCd == centerCd);

        if (kensaIraiLogEntity == null)
        {
            return null!;
        }
        return new KensaIraiLogModel(
                kensaIraiLogEntity.CenterCd,
                kensaIraiLogEntity.IraiFile ?? string.Empty,
                kensaIraiLogEntity.IraiList ?? new byte[] { },
                kensaIraiLogEntity.CreateDate);
    }

    public bool SaveKensaIraiLog(int hpId, int userId, KensaIraiLogModel model)
    {
        KensaIraiLog kensaIraiLog = new();
        kensaIraiLog.HpId = hpId;
        kensaIraiLog.CenterCd = model.CenterCd;
        kensaIraiLog.IraiDate = model.IraiDate;
        kensaIraiLog.FromDate = model.FromDate;
        kensaIraiLog.ToDate = model.ToDate;
        kensaIraiLog.IraiList = model.IraiList;
        kensaIraiLog.IraiFile = model.IraiFile;
        kensaIraiLog.CreateDate = CIUtil.GetJapanDateTimeNow();
        kensaIraiLog.CreateId = userId;
        kensaIraiLog.UpdateDate = CIUtil.GetJapanDateTimeNow();
        kensaIraiLog.UpdateId = userId;
        TrackingDataContext.Add(kensaIraiLog);
        return TrackingDataContext.SaveChanges() > 0;
    }

    public ImportKensaInfMessageModel SaveKensaIraiImport(int hpId, int userId, List<KensaInfDetailModel> kensaInfDetailList, string contentString, CancellationToken cancellationToken)
    {
        var iraiCdList = kensaInfDetailList.Select(item => item.IraiCd).Distinct().ToList();
        var kensaItemCdList = kensaInfDetailList.Select(item => item.KensaItemCd).Distinct().ToList();
        var kensaInfDBList = TrackingDataContext.KensaInfs.Where(item => item.HpId == hpId
                                                                         && iraiCdList.Contains(item.IraiCd))
                                                          .ToList();

        var allKensaInfDetailDbList = TrackingDataContext.KensaInfDetails.Where(item => item.HpId == hpId
                && iraiCdList.Contains(item.IraiCd)
                && !string.IsNullOrEmpty(item.KensaItemCd)
                && item.IsDeleted == 0)
            .ToList();

        var kensaMstDBList = NoTrackingDataContext.CommonCenterKensaMst.Where(item => kensaItemCdList.Contains(item.KensaItemCd))
            .ToList();

        bool succeed = true;
        string errorMessage = string.Empty;
        int succeedCount = 0;

        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    SaveKensaResultLog(hpId, userId, contentString);
                    foreach (var iraiCd in iraiCdList)
                    {
                        var kensaInfDetailModelList = kensaInfDetailList.Where(item => item.IraiCd == iraiCd).ToList();
                        var kensaInfDetailDbList = allKensaInfDetailDbList.Where(item => item.IraiCd == iraiCd).ToList();
                        var resultSave = SaveKensaInfAction(hpId, userId, iraiCd, kensaInfDetailModelList, kensaInfDBList, kensaMstDBList, kensaInfDetailDbList);
                        if (!resultSave)
                        {
                            transaction.Rollback();
                            succeed = false;
                            errorMessage = "Save Kensa Info Action Failed! ";
                            break;
                        }
                        succeedCount++;
                    }
                    if (succeed)
                    {
                        // Check for cancellation before starting any operation
                        if (cancellationToken.IsCancellationRequested)
                        {
                            transaction.Rollback();  // Rollback if cancellation is requested
                            succeed = false;
                            succeedCount = 0;
                            errorMessage = "Canceled by client! ";
                            return new ImportKensaInfMessageModel(succeed, succeedCount, errorMessage);
                        }
                        TrackingDataContext.SaveChanges();
                        transaction.Commit();
                    }
                }
                catch (Exception e)
                {
                    transaction.Rollback();
                    Console.WriteLine(e);
                    errorMessage = e.GetBaseException().Message;
                    succeed = false;
                    succeedCount = 0;
                }

                return new ImportKensaInfMessageModel(succeed, succeedCount, errorMessage);
            });

        return new ImportKensaInfMessageModel(succeed, succeedCount, errorMessage);
    }

    private void SaveKensaResultLog(int hpId, int userId, string KekaFile)
    {
        var kensaResultLog = new KensaResultLog()
        {
            HpId = hpId,
            OpId = 0,
            ImpDate = CIUtil.DateTimeToInt(CIUtil.GetJapanDateTimeNow()),
            KekaFile = KekaFile ?? string.Empty,
            CreateId = userId,
            CreateDate = CIUtil.GetJapanDateTimeNow()
        };
        TrackingDataContext.KensaResultLogs.Add(kensaResultLog);
    }

    public void ReleaseResource()
    {
        DisposeDataContext();
    }

    public List<FileData.PathConfModel> GetOutputFilePath(int hpId)
    {
        var pathConfs = NoTrackingDataContext.PathConfs.Where(p =>
            p.HpId == hpId &&
            p.GrpCd == 54 &&
            p.IsInvalid == 0
        ).ToList();

        List<FileData.PathConfModel> results = new List<FileData.PathConfModel>();

        pathConfs?.ForEach(entity =>
        {
            results.Add(new FileData.PathConfModel(entity));
        }
        );

        return results;
    }

    public FileData.PtInfModel GetPtInf(int hpId, long ptId, int sinDate)
    {
        var ptInfs = NoTrackingDataContext.PtInfs.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptId &&
            p.IsDelete == DeleteStatus.None
        ).ToList();

        FileData.PtInfModel result = null;

        if (ptInfs != null && ptInfs.Any())
        {
            result = new FileData.PtInfModel(ptInfs.First(), sinDate);
        }

        return result;
    }

    public FileData.RaiinInfModel GetRaiinInf(int hpId, long ptId, int sinDate, long raiinNo)
    {
        var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptId &&
            p.SinDate == sinDate &&
            p.RaiinNo == raiinNo &&
            p.IsDeleted == DeleteTypes.None
        );

        var kaMsts = NoTrackingDataContext.KaMsts.Where(p =>
            p.HpId == hpId &&
            p.IsDeleted == DeleteStatus.None
        );

        var userMsts = NoTrackingDataContext.UserMsts.Where(p =>
            p.HpId == hpId &&
            p.IsDeleted == DeleteStatus.None
        );

        var join = (
            from raiinInf in raiinInfs
            join kaMst in kaMsts on
                new { raiinInf.HpId, raiinInf.KaId } equals
                new { kaMst.HpId, kaMst.KaId } into joinKaMsts
            from joinKaMst in joinKaMsts.DefaultIfEmpty()
            join userMst in userMsts on
                new { raiinInf.HpId, raiinInf.TantoId } equals
                new { userMst.HpId, TantoId = userMst.UserId } into joinUserMsts
            from joinUserMst in joinUserMsts.DefaultIfEmpty()
            select new
            {
                raiinInf,
                joinKaMst,
                joinUserMst
            }
            ).ToList();

        FileData.RaiinInfModel result = null;

        if (join != null && join.Any())
        {
            result = new FileData.RaiinInfModel(join.First().raiinInf, join.First().joinKaMst, join.First().joinUserMst);
        }

        return result;
    }

    public List<FileData.OdrInfModel> GetIngaiKensaOdrInf(int hpId, long ptId, int sinDate, long raiinNo)
    {
        var odrInfs = NoTrackingDataContext.OdrInfs.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptId &&
            p.SinDate == sinDate &&
            p.RaiinNo == raiinNo &&
            p.OdrKouiKbn >= 60 &&
            p.OdrKouiKbn <= 69 &&
            p.IsDeleted == DeleteStatus.None
        )
            .OrderBy(p => p.SortNo)
            .ToList();

        List<FileData.OdrInfModel> results = new List<FileData.OdrInfModel>();

        odrInfs?.ForEach(entity =>
        {
            results.Add(new FileData.OdrInfModel(entity));
        }
        );

        return results;
    }

    public List<FileData.OdrInfDetailModel> GetIngaiKensaOdrInfDetail(int hpId, long ptId, int sinDate, long raiinNo, List<string> centerCds)
    {
        //int primaryKbn = 0;
        //var kensaCenterMst = TrackingDataContext.KensaCenterMsts.Where(item => item.HpId == hpId && centerCds.Contains(item.CenterCd)).ToList();
        //primaryKbn = kensaCenterMst.Any(e => e.PrimaryKbn == 1) ? 1 : 0;

        var odrInfs = TrackingDataContext.OdrInfs.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptId &&
            p.SinDate == sinDate &&
            p.RaiinNo == raiinNo &&
            p.OdrKouiKbn >= 60 &&
            p.OdrKouiKbn <= 69 &&
            p.IsDeleted == DeleteStatus.None
        );

        var odrDtls = TrackingDataContext.OdrInfDetails.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptId &&
            p.SinDate == sinDate &&
            p.RaiinNo == raiinNo
        );

        var tenMsts = TrackingDataContext.TenMsts.Where(p =>
            p.HpId == hpId &&
            p.StartDate <= sinDate &&
            p.EndDate >= sinDate &&
            centerCds.Contains(p.CenterCd) &&
            p.IsDeleted == DeleteTypes.None
        );

        var commonCenterKensaMsts = TrackingDataContext.CommonCenterKensaMst.Where(p =>
            p.StartDate <= sinDate &&
            p.EndDate >= sinDate &&
            centerCds.Contains(p.CenterCd)
        );

        var join = (
                from odrInf in odrInfs
                join odrDtl in odrDtls on
                    new { odrInf.HpId, odrInf.PtId, odrInf.RaiinNo, odrInf.RpNo, odrInf.RpEdaNo } equals
                    new { odrDtl.HpId, odrDtl.PtId, odrDtl.RaiinNo, odrDtl.RpNo, odrDtl.RpEdaNo }
                join tenMst in tenMsts on
                    new { odrDtl.HpId, odrDtl.ItemCd } equals
                    new { tenMst.HpId, tenMst.ItemCd }
                join commonCenterKensaMst in commonCenterKensaMsts on
                    new { tenMst.CenterCd, tenMst.KensaItemCd} equals
                    new { commonCenterKensaMst.CenterCd, commonCenterKensaMst.KensaItemCd }
                orderby
                    odrInf.SortNo, odrDtl.RowNo
                select new
                {
                    odrDtl,
                    tenMst,
                    commonCenterKensaMst
                }
            ).ToList();

        List<FileData.OdrInfDetailModel> results = new List<FileData.OdrInfDetailModel>();

        join?.ForEach(entity =>
        {
            results.Add(new FileData.OdrInfDetailModel(entity.odrDtl, entity.tenMst, entity.commonCenterKensaMst));
        });
        return results;
    }

    public string GetDrName(int hpId, int docId)
    {
        string ret = string.Empty;

        var userMsts = NoTrackingDataContext.UserMsts.Where(p =>
            p.HpId == hpId &&
            p.UserId == docId
        );

        if (userMsts != null && userMsts.Any())
        {
            ret = userMsts.First().Name ?? string.Empty;

            if (string.IsNullOrEmpty(userMsts.First().DrName) == false)
            {
                ret = userMsts.First().DrName ?? string.Empty;
            }
        }

        return ret;
    }

    public bool ExistPtGroup(int hpId, long ptid, int grpid, string grpcd)
    {
        bool ret = false;

        var ptgrps = NoTrackingDataContext.PtGrpInfs.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptid &&
            p.GroupId == grpid &&
            p.GroupCode == grpcd &&
            p.IsDeleted == DeleteStatus.None
        );

        if (ptgrps != null && ptgrps.Any())
        {
            ret = true;
        }

        return ret;
    }

    #region private function
    private KensaInf ConvertToNewKensaInf(int hpId, int userId, KensaInfModel model)
    {
        KensaInf kensaInf = new();
        kensaInf.HpId = hpId;
        kensaInf.PtId = model.PtId;
        kensaInf.IraiDate = model.IraiDate;
        kensaInf.RaiinNo = model.RaiinNo;
        kensaInf.IraiCd = model.IraiCd;
        kensaInf.InoutKbn = model.InoutKbn;
        kensaInf.Status = model.Status;
        kensaInf.TosekiKbn = model.TosekiKbn;
        kensaInf.SikyuKbn = model.SikyuKbn;
        kensaInf.ResultCheck = model.ResultCheck;
        kensaInf.CenterCd = model.CenterCd;
        kensaInf.Nyubi = model.Nyubi;
        kensaInf.Yoketu = model.Yoketu;
        kensaInf.Bilirubin = model.Bilirubin;
        kensaInf.IsDeleted = model.IsDeleted ? 1 : 0;
        kensaInf.CreateDate = CIUtil.GetJapanDateTimeNow();
        kensaInf.CreateId = userId;
        kensaInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
        kensaInf.UpdateId = userId;
        return kensaInf;
    }

    private KensaInfDetail ConvertToNewKensaInfDetail(int hpId, int userId, KensaInfDetailModel model)
    {
        KensaInfDetail kensaInfDetail = new();
        kensaInfDetail.HpId = hpId;
        kensaInfDetail.SeqNo = 0;
        kensaInfDetail.PtId = model.PtId;
        kensaInfDetail.IraiCd = model.IraiCd;
        kensaInfDetail.RaiinNo = model.RaiinNo;
        kensaInfDetail.IraiDate = model.IraiDate;
        kensaInfDetail.KensaItemCd = model.KensaItemCd;
        kensaInfDetail.ResultVal = model.ResultVal;
        kensaInfDetail.ResultType = model.ResultType;
        kensaInfDetail.AbnormalKbn = model.AbnormalKbn;
        kensaInfDetail.IsDeleted = model.IsDeleted;
        kensaInfDetail.CmtCd1 = model.CmtCd1;
        kensaInfDetail.CmtCd2 = model.CmtCd2;
        kensaInfDetail.CreateDate = CIUtil.GetJapanDateTimeNow();
        kensaInfDetail.UpdateDate = CIUtil.GetJapanDateTimeNow();
        kensaInfDetail.CreateId = userId;
        kensaInfDetail.UpdateId = userId;
        return kensaInfDetail;
    }

    private void SaveKensaInfAction(int hpId, int userId, List<KensaInfModel> kensaInfModels, List<KensaInfDetailModel> kensaInfDetailModels)
    {
        DateTime dt = CIUtil.GetJapanDateTimeNow();

        // 削除
        if (kensaInfDetailModels != null && kensaInfDetailModels.Any(p => p.IsDeleted == 1))
        {
            List<KensaInfDetailModel> delKensaDtls = kensaInfDetailModels.FindAll(p => p.IsDeleted == 1);
            var ptIdList = delKensaDtls.Select(item => item.PtId).Distinct().ToList();
            var seqNoList = delKensaDtls.Select(item => item.SeqNo).Distinct().ToList();
            var iraiCdList = delKensaDtls.Select(item => item.IraiCd).Distinct().ToList();
            var kensaInfDetailDeleteList = TrackingDataContext.KensaInfDetails.Where(item => item.HpId == hpId
                                                                                             && ptIdList.Contains(item.PtId)
                                                                                             && seqNoList.Contains(item.SeqNo)
                                                                                             && iraiCdList.Contains(item.IraiCd))
                                                                              .ToList();
            foreach (var item in kensaInfDetailDeleteList)
            {
                item.IsDeleted = 1;
                item.UpdateDate = dt;
                item.UpdateId = userId;
            }
            TrackingDataContext.KensaInfDetails.RemoveRange(kensaInfDetailDeleteList);
        }

        // 追加
        if (kensaInfModels.Any(p => p.IsAddNew))
        {
            List<KensaInfModel> addKensaInfs = kensaInfModels.FindAll(p => p.IsAddNew);
            foreach (KensaInfModel addKensaInf in addKensaInfs)
            {
                KensaInf newKensaInf = ConvertToNewKensaInf(hpId, userId, addKensaInf);
                newKensaInf.CreateDate = dt;
                newKensaInf.CreateId = addKensaInf.CreateId;
                newKensaInf.UpdateDate = dt;
                newKensaInf.UpdateId = userId;
                TrackingDataContext.KensaInfs.Add(newKensaInf);
                TrackingDataContext.SaveChanges();
                addKensaInf.ChangeIraiCd(newKensaInf.IraiCd);
            }

            // detailにiraicdを反映
            if (kensaInfDetailModels != null && kensaInfDetailModels.Any())
            {
                foreach (KensaInfModel addKensaInf in addKensaInfs)
                {
                    foreach (KensaInfDetailModel updKensaDtl in kensaInfDetailModels.FindAll(p => p.KeyNo == addKensaInf.KeyNo))
                    {
                        updKensaDtl.ChangeIraiCd(addKensaInf.IraiCd);
                    }
                }
            }
        }

        if (kensaInfDetailModels != null && kensaInfDetailModels.Any(p => p.IsAddNew))
        {
            List<KensaInfDetailModel> addKensaDtls = kensaInfDetailModels.FindAll(p => p.IsAddNew);
            foreach (KensaInfDetailModel addKensaDtl in addKensaDtls)
            {
                KensaInfDetail newKensaInfDetail = ConvertToNewKensaInfDetail(hpId, userId, addKensaDtl);
                newKensaInfDetail.CreateDate = dt;
                newKensaInfDetail.UpdateDate = dt;
                TrackingDataContext.KensaInfDetails.Add(newKensaInfDetail);
            }
        }

        if (kensaInfModels.Any(p => p.IsUpdate))
        {
            List<KensaInfModel> updKensaInfs = kensaInfModels.FindAll(p => p.IsUpdate);
            var ptIdList = updKensaInfs.Select(item => item.PtId).Distinct().ToList();
            var iraiCdList = updKensaInfs.Select(item => item.IraiCd).Distinct().ToList();

            var updKensaInfDB = TrackingDataContext.KensaInfs.Where(item => item.HpId == hpId
                                                                            && ptIdList.Contains(item.PtId)
                                                                            && iraiCdList.Contains(item.IraiCd))
                                                             .ToList();

            foreach (KensaInfModel model in updKensaInfs)
            {
                var kensaInf = updKensaInfDB.FirstOrDefault(item => item.IraiCd == model.IraiCd && item.PtId == model.PtId);
                if (kensaInf != null)
                {
                    kensaInf.IraiDate = model.IraiDate;
                    kensaInf.RaiinNo = model.RaiinNo;
                    kensaInf.IraiCd = model.IraiCd;
                    kensaInf.InoutKbn = model.InoutKbn;
                    kensaInf.Status = model.Status;
                    kensaInf.TosekiKbn = model.TosekiKbn;
                    kensaInf.SikyuKbn = model.SikyuKbn;
                    kensaInf.ResultCheck = model.ResultCheck;
                    kensaInf.CenterCd = model.CenterCd;
                    kensaInf.Nyubi = model.Nyubi;
                    kensaInf.Yoketu = model.Yoketu;
                    kensaInf.Bilirubin = model.Bilirubin;
                    kensaInf.IsDeleted = model.IsDeleted ? 1 : 0;
                    kensaInf.UpdateDate = dt;
                    kensaInf.UpdateId = userId;
                }
            }
        }
        TrackingDataContext.SaveChanges();
    }

    private bool SaveKensaInfAction(int hpId, int userId, long iraiCd, List<KensaInfDetailModel> kensaInfDetailModelList, List<KensaInf> kensaInfDBList, List<CommonCenterKensaMst> kensaMstDBList, List<KensaInfDetail> kensaInfDetailDbList)
    {
        List<KensaInfDetailMessageModel> kensaInfDetailMessageList = new();

        // update kensaInf
        var kensaInf = kensaInfDBList.FirstOrDefault(item => item.IraiCd == iraiCd);
        if (kensaInf == null)
        {
            // 分析物コードが未設定の場合は、読み飛ばす。 skip if iraiCd is not match
            return true;
        }

        if (kensaInfDetailModelList.Any())
        {
            // update kensaInf
            kensaInf.CenterCd = kensaInfDetailModelList[0].CenterCd;
            kensaInf.Nyubi = kensaInfDetailModelList[0].Nyubi;
            kensaInf.Yoketu = kensaInfDetailModelList[0].Yoketu;
            kensaInf.Bilirubin = kensaInfDetailModelList[0].Bilirubin;
            kensaInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
            kensaInf.UpdateId = userId;
            kensaInf.InoutKbn = 1;
            kensaInf.Status = 2;
        }

        // update kensaInfDetails match with .dat file
        var kensaItemCdList = kensaInfDetailModelList.Select(item => item.KensaItemCd).Distinct().ToList();

        var existKensaItemCdList = kensaInfDetailDbList.Select(item => item.KensaItemCd).Distinct().ToList();
        var addKensaInfList = new List<KensaInfDetail>();
        try
        {
            foreach (var detailModel in kensaInfDetailModelList)
            {
                var kensaMst = kensaMstDBList.FirstOrDefault(item => item.KensaItemCd == detailModel.KensaItemCd);
                if (kensaMst == null)
                {
                    continue;
                }

                if (!existKensaItemCdList.Contains(detailModel.KensaItemCd))
                {
                    var kensaInfDetail = new KensaInfDetail();
                    kensaInfDetail.HpId = hpId;
                    kensaInfDetail.CreateDate = CIUtil.GetJapanDateTimeNow();
                    kensaInfDetail.CreateId = userId;
                    kensaInfDetail.IsDeleted = 0;
                    kensaInfDetail.IraiCd = iraiCd;
                    kensaInfDetail.PtId = kensaInf.PtId;
                    kensaInfDetail.IraiDate = kensaInf.IraiDate;
                    kensaInfDetail.RaiinNo = kensaInf.RaiinNo;
                    kensaInfDetail.KensaItemCd = detailModel.KensaItemCd;
                    kensaInfDetail.SeqNo = 0;
                    kensaInfDetail.ResultVal = detailModel.ResultVal;
                    kensaInfDetail.AbnormalKbn = detailModel.AbnormalKbn;
                    kensaInfDetail.CmtCd1 = detailModel.CmtCd1;
                    kensaInfDetail.CmtCd2 = detailModel.CmtCd2;
                    kensaInfDetail.ResultType = detailModel.ResultType == "B" ? string.Empty : detailModel.ResultType;
                    kensaInfDetail.UpdateId = userId;
                    kensaInfDetail.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    addKensaInfList.Add(kensaInfDetail);
                }
                else
                {
                    var existKensaInfoDetail = kensaInfDetailDbList.FirstOrDefault(item =>
                        item.IraiCd == iraiCd && item.KensaItemCd == detailModel.KensaItemCd);

                    existKensaInfoDetail.ResultVal = detailModel.ResultVal;
                    existKensaInfoDetail.AbnormalKbn = detailModel.AbnormalKbn;
                    existKensaInfoDetail.CmtCd1 = detailModel.CmtCd1;
                    existKensaInfoDetail.CmtCd2 = detailModel.CmtCd2;
                    existKensaInfoDetail.ResultType = detailModel.ResultType == "B" ? string.Empty : detailModel.ResultType;
                    existKensaInfoDetail.UpdateId = userId;
                    existKensaInfoDetail.UpdateDate = CIUtil.GetJapanDateTimeNow();
                }

                kensaInfDetailMessageList.Add(new KensaInfDetailMessageModel(detailModel.KensaItemCd, kensaMst.KensaName));
            }

            // update kensaInfDetails don't include in .dat file . => set result value = ""
            var itemNoSendList = kensaInfDetailDbList.Where(k => !kensaItemCdList.Contains(k?.KensaItemCd ?? string.Empty));
            foreach (var itemNoSend in itemNoSendList)
            {
                itemNoSend.ResultVal = string.Empty;
                itemNoSend.UpdateDate = CIUtil.GetJapanDateTimeNow();
                itemNoSend.UpdateId = userId;
            }

            if (addKensaInfList.Any())
            {
                TrackingDataContext.KensaInfDetails.AddRange(addKensaInfList);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return false;
        }
        return true;
    }
    #endregion
}
