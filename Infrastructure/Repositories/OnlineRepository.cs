﻿using Domain.Constant;
using Domain.Converter;
using Domain.Models.Insurance;
using Domain.Models.Online;
using Domain.Models.Online.QualificationConfirmation;
using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using Domain.Models.SystemConf;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Extension;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Globalization;
using System.Resources;
using System.Text;
using System.Xml;
using System.Xml.Linq;
using Microsoft.EntityFrameworkCore.Migrations.Operations;
using System.Xml.Linq;
using System.Xml.Serialization;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.IdentityModel.Tokens;

using Domain.Models.Online.ViewResult;
using System;
using System.Text.RegularExpressions;
using System.Reactive.Joins;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
namespace Infrastructure.Repositories;

public class OnlineRepository : RepositoryBase, IOnlineRepository
{
    private readonly IInsuranceRepository _insuranceRepository;
    private readonly IReceptionRepository _receptionRepository;
    private readonly ISystemConfRepository _systemConfig;

    public OnlineRepository(ITenantProvider tenantProvider, IInsuranceRepository insuranceRepository, IReceptionRepository receptionRepository, ISystemConfRepository systemConfig) : base(tenantProvider)
    {
        _insuranceRepository = insuranceRepository;
        _receptionRepository = receptionRepository;
        _systemConfig = systemConfig;
    }

    public List<long> InsertOnlineConfirmHistory(int userId, List<OnlineConfirmationHistoryModel> onlineList, int hpId)
    {
        List<long> idList = new();
        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(
            () =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    var onlineInsertList = onlineList.Select(item => new OnlineConfirmationHistory()
                    {
                        ID = 0,
                        PtId = item.PtId,
                        OnlineConfirmationDate = item.OnlineConfirmationDate,
                        ConfirmationType = item.ConfirmationType,
                        ConfirmationResult = item.ConfirmationResult,
                        UketukeStatus = item.UketukeStatus,
                        CreateDate = CIUtil.GetJapanDateTimeNow(),
                        UpdateDate = CIUtil.GetJapanDateTimeNow(),
                        CreateId = userId,
                        UpdateId = userId,
                        HpId = hpId,
                    }).ToList();
                    foreach (var online in onlineInsertList)
                    {
                        TrackingDataContext.AddRange(online);
                        TrackingDataContext.SaveChanges();
                        idList.Add(online.ID);
                    }
                    transaction.Commit();
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            });
        return idList;
    }

    public List<OnlineConfirmationHistoryModel> GetRegisterdPatientsFromOnline(int confirmDate, int hpId, int id = 0, int confirmType = 1)
    {
        List<OnlineConfirmationHistory> onlineList;
        if (id == 0)
        {
            onlineList = NoTrackingDataContext.OnlineConfirmationHistories.Where(item => item.ConfirmationType == confirmType && item.HpId == hpId)
                                                                          .ToList();
            onlineList = onlineList.Where(item => CIUtil.DateTimeToInt(item.OnlineConfirmationDate) == confirmDate).ToList();
        }
        else
        {
            onlineList = NoTrackingDataContext.OnlineConfirmationHistories.Where(item => item.ID == id && item.HpId == hpId).ToList();
        }

        var result = onlineList.OrderBy(item => item.OnlineConfirmationDate)
                               .Select(item => ConvertToModel(item))
                               .ToList();
        return result;
    }

    public bool UpdateOnlineConfirmationHistory(int uketukeStatus, int id, int userId)
    {
        string updateDate = CIUtil.GetJapanDateTimeNow().ToString("yyyy-MM-dd HH:mm:ss.fffZ");

        string updateQuery = $"UPDATE \"online_confirmation_history\" SET \"uketuke_status\" = {uketukeStatus}, \"update_date\" = '{updateDate}'"
                             + $", \"update_id\" = {userId}"
                             + $" WHERE \"id\" = {id} AND \"uketuke_status\" = 0";

        return TrackingDataContext.Database.ExecuteSqlRaw(updateQuery) > 0;
    }

    public long UpdateRefNo(int hpId, long ptId, int userId)
    {
        var ptInf = TrackingDataContext.PtInfs.FirstOrDefault(e => e.HpId == hpId && e.PtId == ptId && e.IsDelete == DeleteTypes.None);
        if(ptInf != null)
        {
            ptInf.ReferenceNo = ptInf.PtNum;
            ptInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
            ptInf.UpdateId = userId;
            TrackingDataContext.SaveChanges();
            return ptInf.ReferenceNo;
        } 
        return 0;
    }

    public bool UpdateOnlineHistoryById(int userId, long id, long ptId, int uketukeStatus, int confirmationType, int hpId)
    {
        var onlineHistory = TrackingDataContext.OnlineConfirmationHistories.FirstOrDefault(item => item.ID == id && item.HpId == hpId);
        if (onlineHistory == null)
        {
            return false;
        }
        onlineHistory.UpdateDate = CIUtil.GetJapanDateTimeNow();
        onlineHistory.UpdateId = userId;
        if (uketukeStatus != -1)
        {
            onlineHistory.UketukeStatus = uketukeStatus;
        }
        if (ptId != -1)
        {
            onlineHistory.PtId = ptId;
        }
        if (confirmationType != -1)
        {
            onlineHistory.ConfirmationType = confirmationType;
        }
        return TrackingDataContext.SaveChanges() > 0;
    }

    public bool CheckExistIdList(List<long> idList, int hpId)
    {
        idList = idList.Distinct().ToList();
        var countId = NoTrackingDataContext.OnlineConfirmationHistories.Count(x => idList.Contains(x.ID) && x.HpId == hpId);
        return idList.Count == countId;
    }

    public bool UpdateOQConfirmation(int hpId, int userId, long onlineHistoryId, Dictionary<string, string> onlQuaResFileDict, Dictionary<string, (int confirmationType, string infConsFlg)> onlQuaConfirmationTypeDict)
    {
        if (!onlQuaResFileDict.Any())
        {
            return false;
        }
        var history = TrackingDataContext.OnlineConfirmationHistories.FirstOrDefault(x => x.ID == onlineHistoryId && x.HpId == hpId);
        if (history == null)
        {
            return false;
        }
        bool success = false;
        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(
            () =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    var item = onlQuaResFileDict.First();
                    history.ConfirmationType = onlQuaConfirmationTypeDict.ContainsKey(item.Key) ? onlQuaConfirmationTypeDict[item.Key].confirmationType : 1;
                    history.InfoConsFlg = onlQuaConfirmationTypeDict.ContainsKey(item.Key) ? onlQuaConfirmationTypeDict[item.Key].infConsFlg : "    ";
                    history.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    history.UpdateId = userId;
                    TrackingDataContext.SaveChanges();

                    int sindate = CIUtil.DateTimeToInt(history.OnlineConfirmationDate);
                    var raiinInfsInSameday = TrackingDataContext.RaiinInfs.Where(x => x.HpId == hpId && x.SinDate == sindate && x.PtId == history.PtId).ToList();

                    UpdateConfirmationTypeInRaiinInf(userId, raiinInfsInSameday, history.ConfirmationType);
                    if (!string.IsNullOrEmpty(history.InfoConsFlg))
                    {
                        UpdateInfConsFlgInRaiinInf(userId, raiinInfsInSameday, history.InfoConsFlg);
                    }
                    transaction.Commit();
                    success = true;
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            });
        return success;
    }

    public bool SaveAllOQConfirmation(int hpId, int userId, long ptId, Dictionary<string, string> onlQuaResFileDict, Dictionary<string, (int confirmationType, string infConsFlg)> onlQuaConfirmationTypeDict)
    {
        bool success = false;
        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(
            () =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    List<OnlineConfirmationHistory> historyList = new();
                    foreach (var item in onlQuaResFileDict)
                    {
                        historyList.Add(new OnlineConfirmationHistory()
                        {
                            PtId = ptId,
                            OnlineConfirmationDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.ParseExact(item.Key, "yyyyMMddHHmmss", CultureInfo.InvariantCulture)),
                            ConfirmationType = onlQuaConfirmationTypeDict.ContainsKey(item.Key) ? onlQuaConfirmationTypeDict[item.Key].confirmationType : 1,
                            InfoConsFlg = onlQuaConfirmationTypeDict.ContainsKey(item.Key) ? onlQuaConfirmationTypeDict[item.Key].infConsFlg : "    ",
                            ConfirmationResult = item.Value,
                            CreateDate = CIUtil.GetJapanDateTimeNow(),
                            CreateId = userId,
                            UpdateDate = CIUtil.GetJapanDateTimeNow(),
                            UpdateId = userId,
                            HpId = hpId
                        });
                    }
                    TrackingDataContext.OnlineConfirmationHistories.AddRange(historyList);
                    TrackingDataContext.SaveChanges();

                    var ptIdList = historyList.Select(item => item.PtId).Distinct().ToList();
                    var sinDateList = historyList.Select(item => CIUtil.DateTimeToInt(item.OnlineConfirmationDate)).Distinct().ToList();
                    var raiinInfList = TrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId && sinDateList.Contains(item.SinDate) && ptIdList.Contains(item.PtId)).ToList();
                    foreach (var historyItem in historyList)
                    {
                        int sindate = CIUtil.DateTimeToInt(historyItem.OnlineConfirmationDate);
                        var raiinInfsInSameday = raiinInfList.Where(item => item.SinDate == sindate && item.PtId == historyItem.PtId).ToList();
                        UpdateConfirmationTypeInRaiinInf(userId, raiinInfsInSameday, historyItem.ConfirmationType);
                        if (!string.IsNullOrEmpty(historyItem.InfoConsFlg))
                        {
                            UpdateInfConsFlgInRaiinInf(userId, raiinInfsInSameday, historyItem.InfoConsFlg);
                        }
                    }
                    transaction.Commit();
                    success = true;
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            });
        return success;
    }

    public bool SaveOQConfirmation(int hpId, int userId, long onlineHistoryId, long ptId, string confirmationResult, string onlineConfirmationDateString, int confirmationType, string infConsFlg, int uketukeStatus = 0, bool isUpdateRaiinInf = true)
    {
        bool success = false;
        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(
            () =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    var onlineConfirmationDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.ParseExact(onlineConfirmationDateString, "yyyyMMddHHmmss", CultureInfo.InvariantCulture));
                    if (onlineHistoryId == 0)
                    {
                        TrackingDataContext.OnlineConfirmationHistories.Add(new OnlineConfirmationHistory()
                        {
                            PtId = ptId,
                            OnlineConfirmationDate = onlineConfirmationDate,
                            ConfirmationType = confirmationType,
                            ConfirmationResult = confirmationResult,
                            InfoConsFlg = infConsFlg,
                            UketukeStatus = uketukeStatus,
                            CreateDate = CIUtil.GetJapanDateTimeNow(),
                            CreateId = userId,
                            UpdateDate = CIUtil.GetJapanDateTimeNow(),
                            UpdateId = userId,
                            HpId = hpId
                        });
                    }
                    else
                    {
                        var onlineHistory = TrackingDataContext.OnlineConfirmationHistories.FirstOrDefault(p => p.ID == onlineHistoryId && p.HpId == hpId);
                        if (onlineHistory != null)
                        {
                            if (uketukeStatus > 0)
                            {
                                onlineHistory.UketukeStatus = uketukeStatus;
                            }
                            onlineHistory.PtId = ptId;
                            onlineHistory.ConfirmationType = confirmationType;
                            onlineHistory.ConfirmationResult = confirmationResult;
                            onlineHistory.InfoConsFlg = infConsFlg;
                            onlineHistory.UpdateDate = CIUtil.GetJapanDateTimeNow();
                            onlineHistory.UpdateId = userId;
                        }
                    }
                    TrackingDataContext.SaveChanges();
                    if (isUpdateRaiinInf)
                    {
                        UpdateOnlineInRaiinInfAction(hpId, userId, ptId, onlineConfirmationDate, confirmationType, infConsFlg);
                    }
                    transaction.Commit();
                    success = true;
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            });
        return success;
    }

    public bool UpdateOnlineInRaiinInf(int hpId, int userId, long ptId, DateTime onlineConfirmationDate, int confirmationType, string infConsFlg)
    {
        bool success = false;
        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(
            () =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    UpdateOnlineInRaiinInfAction(hpId, userId, ptId, onlineConfirmationDate, confirmationType, infConsFlg);
                    transaction.Commit();
                    success = true;
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            });
        return success;
    }

    /// <summary>
    /// UpdateOnlineInRaiinInf action without transaction
    /// </summary>
    /// <param name="hpId"></param>
    /// <param name="userId"></param>
    /// <param name="ptId"></param>
    /// <param name="onlineConfirmationDate"></param>
    /// <param name="confirmationType"></param>
    /// <param name="infConsFlg"></param>
    private void UpdateOnlineInRaiinInfAction(int hpId, int userId, long ptId, DateTime onlineConfirmationDate, int confirmationType, string infConsFlg)
    {
        int sindate = CIUtil.DateTimeToInt(onlineConfirmationDate);
        var raiinInfsInSameday = TrackingDataContext.RaiinInfs.Where(x => x.HpId == hpId && x.SinDate == sindate && x.PtId == ptId).ToList();
        UpdateConfirmationTypeInRaiinInf(userId, raiinInfsInSameday, confirmationType);
        if (!string.IsNullOrEmpty(infConsFlg))
        {
            UpdateInfConsFlgInRaiinInf(userId, raiinInfsInSameday, infConsFlg);
        }
    }

    public bool UpdatePtInfOnlineQualify(int hpId, int userId, long ptId, List<PtInfConfirmationModel> resultList)
    {
        if (resultList == null || resultList.Count == 0)
        {
            return false;
        }
        if (!resultList.Any(item => !item.IsReflect))
        {
            return false;
        }

        var ptInf = TrackingDataContext.PtInfs.FirstOrDefault(item => item.HpId == hpId && item.PtId == ptId);
        if (ptInf == null)
        {
            return false;
        }

        foreach (var model in resultList)
        {
            if (model.IsReflect)
            {
                continue;
            }
            switch (model.AttributeName)
            {
                case PtInfOQConst.KANJI_NAME:
                    if (!string.IsNullOrEmpty(model.XmlValue))
                    {
                        ptInf.Name = model.XmlValue;
                    }
                    break;
                case PtInfOQConst.KANA_NAME:
                    if (!string.IsNullOrEmpty(model.XmlValue))
                    {
                        ptInf.KanaName = model.XmlValue;
                    }
                    break;
                case PtInfOQConst.SEX:
                    if (!string.IsNullOrEmpty(model.XmlValue))
                    {
                        ptInf.Sex = model.XmlValue.AsInteger();
                    }
                    break;
                case PtInfOQConst.BIRTHDAY:
                    if (!string.IsNullOrEmpty(model.XmlValue))
                    {
                        ptInf.Birthday = model.XmlValue.AsInteger();
                    }
                    break;
                case PtInfOQConst.HOME_ADDRESS:
                    if (!string.IsNullOrEmpty(model.XmlValue))
                    {
                        ptInf.HomeAddress1 = model.XmlValue;
                    }
                    break;
                case PtInfOQConst.HOME_POST:
                    if (!string.IsNullOrEmpty(model.XmlValue))
                    {
                        string value = model.XmlValue;
                        if (!string.IsNullOrEmpty(value) && value.Contains("-"))
                        {
                            value = value.Replace("-", string.Empty);
                        }
                        ptInf.HomePost = value;
                    }
                    break;
                case PtInfOQConst.SETANUSI:
                    if (!string.IsNullOrEmpty(model.XmlValue))
                    {
                        ptInf.Setanusi = model.XmlValue;
                    }
                    break;
            }
        }
        var isUpdated = resultList.Any(item => !item.IsReflect && !string.IsNullOrEmpty(item.XmlValue));
        if (isUpdated)
        {
            ptInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
            ptInf.UpdateId = userId;
            return TrackingDataContext.SaveChanges() > 0;
        }
        return true;
    }

    public bool UpdateConfirmationTypeInRaiinInf(int userId, List<RaiinInf> raiinInfsInSameday, int confirmationType)
    {
        var unConfirmedRaiinInfs = raiinInfsInSameday.Where(x => x.ConfirmationType == 0);
        var confirmedRaiininfs = raiinInfsInSameday.Where(x => x.ConfirmationType > 0);
        if (!confirmedRaiininfs.Any())
        {
            foreach (var raiinInf in unConfirmedRaiinInfs)
            {
                raiinInf.ConfirmationType = confirmationType;
                raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                raiinInf.UpdateId = userId;
            }
        }
        else
        {
            int minConfirmationType = confirmedRaiininfs.Select(x => x.ConfirmationType).DefaultIfEmpty()?.Min() ?? 0;
            int newConfirmationType = minConfirmationType > confirmationType ? confirmationType : minConfirmationType;
            foreach (var raiinInf in confirmedRaiininfs)
            {
                raiinInf.ConfirmationType = newConfirmationType;
                raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                raiinInf.UpdateId = userId;
            }
            foreach (var raiinInf in unConfirmedRaiinInfs)
            {
                raiinInf.ConfirmationType = newConfirmationType;
                raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                raiinInf.UpdateId = userId;
            }
        }
        return TrackingDataContext.SaveChanges() > 0;
    }

    public List<OnlineConfirmationHistoryModel> GetListOnlineConfirmationHistoryModel(long ptId, int hpId)
    {
        var listOnlineConfirmationHistory = NoTrackingDataContext.OnlineConfirmationHistories.Where(item => item.PtId == ptId && item.HpId == hpId).ToList();
        var result = listOnlineConfirmationHistory.Select(item => ConvertToModel(item))
                                                  .OrderByDescending(item => item.OnlineConfirmationDate)
                                                  .ToList();
        return result;
    }

    public List<OnlineConfirmationHistoryModel> GetListOnlineConfirmationHistoryModel(int userId, Dictionary<string, string> onlQuaResFileDict, Dictionary<string, (int confirmationType, string infConsFlg)> onlQuaConfirmationTypeDict, int hpId)
    {
        var listOnlineConfirmationHistory = new List<OnlineConfirmationHistory>();
        foreach (var item in onlQuaResFileDict)
        {
            listOnlineConfirmationHistory.Add(new OnlineConfirmationHistory()
            {
                PtId = 0,
                OnlineConfirmationDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.ParseExact(item.Key, "yyyyMMddHHmmss", CultureInfo.InvariantCulture)),
                ConfirmationType = onlQuaConfirmationTypeDict.ContainsKey(item.Key) ? onlQuaConfirmationTypeDict[item.Key].confirmationType : 1,
                InfoConsFlg = onlQuaConfirmationTypeDict.ContainsKey(item.Key) ? onlQuaConfirmationTypeDict[item.Key].infConsFlg : "    ",
                ConfirmationResult = item.Value,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId,
                HpId = hpId,
            });
        }
        var result = listOnlineConfirmationHistory.Select(item => ConvertToModel(item))
                                                  .OrderByDescending(item => item.OnlineConfirmationDate)
                                                  .ToList();
        return result;
    }

    public List<OnlineConsentModel> GetOnlineConsentModel(long ptId, int hpId, bool isContainAgreedConsent = false)
    {
        var systemDate = CIUtil.GetJapanDateTimeNow();

        if (isContainAgreedConsent)
        {
            var onlineAgreedConsentList = NoTrackingDataContext.OnlineAgreedConsents.Where(item => item.PtId == ptId
                                                                                   && new List<int>() { 1, 2, 3 }.Contains(item.ConsKbn))
                                                                    .Where(item => item.LimitDate >= systemDate)
                                                                    .GroupBy(item => item.ConsKbn)
                                                                    .Select(group => group.OrderByDescending(x => x.ProcessTime).First())
                                                                    .ToList();

            var hasOnlineAgreedConsent = NoTrackingDataContext.OnlineAgreedConsents.Any(item => item.PtId == ptId && new List<int>() { 1, 2, 3 }.Contains(item.ConsKbn) && item.LimitDate >= systemDate);
            if (hasOnlineAgreedConsent)
            {
                return onlineAgreedConsentList.Where(x => x.ConsFlg == 1).Select(item => new OnlineConsentModel(item.PtId, item.ConsKbn, item.ConsDate, item.LimitDate, true)).ToList() ?? new();
            }
        }

        var onlineConsentList = NoTrackingDataContext.OnlineConsents.Where(item => item.HpId == hpId && item.PtId == ptId
                                                                                   && new List<int>() { 1, 2, 3 }.Contains(item.ConsKbn))
                                                                    .ToList();
        onlineConsentList = onlineConsentList.Where(item => item.LimitDate >= systemDate).ToList();
        return onlineConsentList.Select(item => new OnlineConsentModel(item.PtId, item.ConsKbn, item.ConsDate, item.LimitDate)).ToList();
    }

    public bool UpdateOnlineConsents(int userId, long ptId, List<QCXmlMsgResponse> responseList, int hpId)
    {
        void UpdateOnlineConsent(int consKbn, DateTime consDate, DateTime limitDate, List<OnlineConsent> onlineConsentList)
        {
            if (consKbn == 0) return;
            var onlineConsent = onlineConsentList.FirstOrDefault(x => x.PtId == ptId && x.ConsKbn == consKbn);
            if (onlineConsent != null)
            {
                onlineConsent.ConsDate = consDate;
                onlineConsent.LimitDate = limitDate;
                onlineConsent.UpdateDate = CIUtil.GetJapanDateTimeNow();
                onlineConsent.UpdateId = userId;
            }
            else
            {
                TrackingDataContext.OnlineConsents.Add(new OnlineConsent()
                {
                    PtId = ptId,
                    ConsKbn = consKbn,
                    ConsDate = consDate,
                    LimitDate = limitDate,
                    UpdateDate = CIUtil.GetJapanDateTimeNow(),
                    UpdateId = userId,
                    CreateDate = CIUtil.GetJapanDateTimeNow(),
                    CreateId = userId,
                    HpId = hpId
                });
            }
            TrackingDataContext.SaveChanges();
        }

        bool successed = true;
        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(
            () =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    foreach (var confirmation in from response in responseList
                                                 where response.MessageBody.ResultList != null && response.MessageBody.ResultList.ResultOfQualificationConfirmation?.Any() == true
                                                 from confirmation in response.MessageBody.ResultList.ResultOfQualificationConfirmation
                                                 select confirmation)
                    {
                        var onlineConsentList = TrackingDataContext.OnlineConsents.Where(item => item.HpId == hpId && item.PtId == ptId && new List<int>() { 1, 2, 3, 4 }.Contains(item.ConsKbn)).ToList();
                        if (confirmation.SpecificHealthCheckupsInfoConsFlg.AsInteger() == 1)
                        {
                            int consKbn = 2;
                            DateTime consDate = DateTime.SpecifyKind(CIUtil.StrDateToDate(confirmation.SpecificHealthCheckupsInfoConsTime), DateTimeKind.Utc);
                            DateTime limitDate = DateTime.SpecifyKind(CIUtil.StrDateToDate(confirmation.SpecificHealthCheckupsInfoAvailableTime), DateTimeKind.Utc);
                            UpdateOnlineConsent(consKbn, consDate, limitDate, onlineConsentList);
                        }

                        if (confirmation.PharmacistsInfoConsFlg.AsInteger() == 1)
                        {
                            int consKbn = 1;
                            DateTime consDate = DateTime.SpecifyKind(CIUtil.StrDateToDate(confirmation.PharmacistsInfoConsTime), DateTimeKind.Utc);
                            DateTime limitDate = DateTime.SpecifyKind(CIUtil.StrDateToDate(confirmation.PharmacistsInfoAvailableTime), DateTimeKind.Utc);
                            UpdateOnlineConsent(consKbn, consDate, limitDate, onlineConsentList);
                        }

                        if (confirmation.DiagnosisInfoConsFlg.AsInteger() == 1)
                        {
                            int consKbn = 3;
                            DateTime consDate = DateTime.SpecifyKind(CIUtil.StrDateToDate(confirmation.DiagnosisInfoConsTime), DateTimeKind.Utc);
                            DateTime limitDate = DateTime.SpecifyKind(CIUtil.StrDateToDate(confirmation.DiagnosisInfoAvailableTime), DateTimeKind.Utc);
                            UpdateOnlineConsent(consKbn, consDate, limitDate, onlineConsentList);
                        }

                        if (confirmation.OperationInfoConsFlg.AsInteger() == 1)
                        {
                            int consKbn = 4;
                            DateTime consDate = DateTime.SpecifyKind(CIUtil.StrDateToDate(confirmation.OperationInfoConsTime), DateTimeKind.Utc);
                            DateTime limitDate = DateTime.SpecifyKind(CIUtil.StrDateToDate(confirmation.OperationInfoAvailableTime), DateTimeKind.Utc);
                            UpdateOnlineConsent(consKbn, consDate, limitDate, onlineConsentList);
                        }
                    }
                    transaction.Commit();
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            });
        return successed;
    }


    public List<QualificationInfModel> GetListQualificationInf(int hpId)
    {
        var listOnlineConfirmation = NoTrackingDataContext.OnlineConfirmations
                                        .Where(x => x.HpId == hpId)
                                        .OrderByDescending(u => u.CreateDate)
                                        .Select(x => new QualificationInfModel(
                                                        x.ReceptionNo,
                                                        x.ReceptionDateTime,
                                                        x.YoyakuDate,
                                                        x.SegmentOfResult ?? string.Empty,
                                                        x.ErrorMessage ?? string.Empty
                                            ))
                                        .Take(50)
                                        .ToList();
        return listOnlineConfirmation;
    }

    public bool SaveOnlineConfirmation(int userId, QualificationInfModel qualificationInf, ModelStatus status, int hpId)
    {
        if (status == ModelStatus.Added)
        {
            var onlConfirm = new OnlineConfirmation();
            onlConfirm.ReceptionNo = qualificationInf.ReceptionNo;
            onlConfirm.ReceptionDateTime = CIUtil.SetKindUtc(qualificationInf.ReceptionDateTime);
            onlConfirm.YoyakuDate = qualificationInf.YoyakuDate;
            onlConfirm.SegmentOfResult = qualificationInf.SegmentOfResult;
            onlConfirm.ErrorMessage = qualificationInf.ErrorMessage;
            onlConfirm.CreateDate = CIUtil.GetJapanDateTimeNow();
            onlConfirm.CreateId = userId;
            onlConfirm.UpdateDate = CIUtil.GetJapanDateTimeNow();
            onlConfirm.UpdateId = userId;
            onlConfirm.HpId = hpId;

            TrackingDataContext.OnlineConfirmations.Add(onlConfirm);
            return TrackingDataContext.SaveChanges() > 0;
        }
        else if (status == ModelStatus.Modified)
        {
            var onlConfirm = TrackingDataContext.OnlineConfirmations.FirstOrDefault(x => x.ReceptionNo == qualificationInf.ReceptionNo);

            if (onlConfirm != null)
            {
                onlConfirm.SegmentOfResult = qualificationInf.SegmentOfResult;
                onlConfirm.ErrorMessage = qualificationInf.ErrorMessage;
                onlConfirm.UpdateDate = CIUtil.GetJapanDateTimeNow();
                onlConfirm.UpdateId = userId;

                return TrackingDataContext.SaveChanges() > 0;
            }
        }

        return true;
    }

    public bool UpdateOnlineConfirmation(int userId, QualificationInfModel qualificationInf, int hpId)
    {
        var onlConfirm = TrackingDataContext.OnlineConfirmations.FirstOrDefault(
            x => x.ReceptionNo == qualificationInf.ReceptionNo && 
                 x.HpId == hpId
                 );

        if (onlConfirm is null) return false;

        onlConfirm.SegmentOfResult = qualificationInf.SegmentOfResult;
        onlConfirm.ErrorMessage = qualificationInf.ErrorMessage;
        onlConfirm.ErrorCode = qualificationInf.ErrorCode;

        onlConfirm.UpdateDate = CIUtil.GetJapanDateTimeNow();
        onlConfirm.UpdateId = userId;

        return TrackingDataContext.SaveChanges() > 0;
    }

    public bool InsertListOnlConfirmHistory(int userId, List<OnlineConfirmationHistoryModel> listOnlineConfirmationHistoryModel, int hpId)
    {
        foreach (var item in listOnlineConfirmationHistoryModel)
        {
            TrackingDataContext.Add(new OnlineConfirmationHistory()
            {
                PtId = item.PtId,
                OnlineConfirmationDate = item.OnlineConfirmationDate,
                InfoConsFlg = item.InfoConsFlg,
                ConfirmationType = item.ConfirmationType,
                PrescriptionIssueType = item.PrescriptionIssueType,
                ConfirmationResult = item.ConfirmationResult,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId,
                UketukeStatus = item.UketukeStatus,
                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateId = userId,
                HpId = hpId,
            });
        }

        return TrackingDataContext.SaveChanges() > 0;

    }

    public (bool, List<ReceptionRowModel> receptions) UpdateRaiinInfByResResult(int hpId, int userId, List<ConfirmResultModel> listResResult)
    {
        var raiinInfsChange = new List<RaiinInf>();

        listResResult = listResResult.Where(u => u.PtId > 0).ToList();
        if (listResResult.Count == 0)
        {
            return (true, new());
        }
        foreach (var resResult in listResResult)
        {
            var raiinInfToUpdate = GetRaiinInfToUpdateByPtId(hpId, resResult.PtId, resResult.SinDate);
            if (raiinInfToUpdate.raiinInfs == null)
            {
                continue;
            }

            if (raiinInfToUpdate.referenceNo != resResult.ReferenceNo && resResult.ReferenceNo != 0)
            {
                resResult.ChangeConfirmationStatus(97);
                resResult.ChangeConfirmationResult("照会番号不一致");
            }
            else
            {
                if (resResult.ConfirmationStatus != 4)
                {
                    var ptHokenCheckModel = TrackingDataContext.PtHokenChecks.FirstOrDefault(u => u.HpId == hpId &&
                                                                                                    u.PtID == resResult.PtId &&
                                                                                                    u.HokenId == resResult.HokenId &&
                                                                                                    u.HokenGrp == resResult.HokenGrp &&
                                                                                                    u.CheckDate == CIUtil.IntToDate(resResult.CheckDate) &&
                                                                                                    u.IsDeleted == DeleteStatus.None);
                    if (ptHokenCheckModel != null)
                    {
                        ptHokenCheckModel.CheckCmt = resResult.PtHokenCheckModel.CheckComment;
                        ptHokenCheckModel.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        ptHokenCheckModel.UpdateId = userId;
                    }
                    else
                    {
                        TrackingDataContext.PtHokenChecks.Add(new PtHokenCheck()
                        {
                            HpId = hpId,
                            PtID = resResult.PtHokenCheckModel.PtId,
                            HokenGrp = resResult.PtHokenCheckModel.HokenGrp,
                            HokenId = resResult.PtHokenCheckModel.HokenId,
                            CheckDate = CIUtil.IntToDate(resResult.PtHokenCheckModel.ConfirmDate),
                            CheckId = resResult.PtHokenCheckModel.CheckId,
                            CheckCmt = resResult.PtHokenCheckModel.CheckComment,
                            IsDeleted = 0,
                            CreateDate = CIUtil.GetJapanDateTimeNow(),
                            CreateId = userId,
                            UpdateDate = CIUtil.GetJapanDateTimeNow(),
                            UpdateId = userId,
                        });
                    }
                }
            }

            foreach (var raiinInf in raiinInfToUpdate.raiinInfs)
            {
                raiinInf.ConfirmationResult = GetConfirmationResult(hpId, resResult, raiinInf.SinDate);
                raiinInf.ConfirmationState = resResult.ConfirmationStatus;
                raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                raiinInf.UpdateId = userId;
            }

            foreach (var raiinInf in raiinInfToUpdate.raiinInfs)
            {
                raiinInfsChange.Add(raiinInf);
            }
        }

        var saveChanges = TrackingDataContext.SaveChanges() > 0;
        return (saveChanges, saveChanges ? GetListRaiinInf(raiinInfsChange) : new());
    }

    private List<ReceptionRowModel> GetListRaiinInf(List<RaiinInf> raiinInfs)
    {
        var result = new List<ReceptionRowModel>();
        foreach (var raiinInf in raiinInfs)
        {
            result.AddRange(_receptionRepository.GetList(raiinInf.HpId, raiinInf.SinDate, CommonConstants.InvalidId, raiinInf.PtId, isDeleted: 0));
        }

        return result;
    }

    private (List<RaiinInf>? raiinInfs, long referenceNo) GetRaiinInfToUpdateByPtId(int hpId, long ptId, int sinDate)
    {
        var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(u => u.HpId == hpId &&
                                                                           u.PtId == ptId &&
                                                                           u.IsDelete == DeleteStatus.None &&
                                                                           u.IsTester == 0);
        if (ptInf == null)
        {
            return (null, 0);
        }
        var listRaiinInf = TrackingDataContext.RaiinInfs.Where(u => u.HpId == hpId &&
                                                                   u.PtId == ptId &&
                                                                   u.SinDate == sinDate &&
                                                                   u.Status == RaiinState.Reservation &&
                                                                   u.IsDeleted == DeleteStatus.None).ToList();
        if (listRaiinInf.Count > 0)
        {
            return (listRaiinInf, ptInf.ReferenceNo);
        }
        return (null, 0);
    }

    private string GetConfirmationResult(int hpId, ConfirmResultModel resResult, int sinDate)
    {
        var hokenInfs = _insuranceRepository.FindHokenInfByPtId(hpId, resResult.PtId);
        var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(p => p.HpId == hpId && p.PtId == resResult.PtId && p.IsDelete != 1);
        var matchPtInf = new List<PtInfConfirmationModel>();
        var resultOfQC = new ResultOfQualificationConfirmation()
        {
            NameKana = resResult.NameKana,
            Name = resResult.Name,
            Sex1 = resResult.Sex1,
            Sex2 = resResult.Sex2,
            PostNumber = resResult.PostNumber,
            Birthdate = resResult.Birthday,
            Address = resResult.Address,
            InsuredName = resResult.InsuredName,
            InsurerNumber = resResult.InsurerNumber,
            InsuredCardSymbol = resResult.InsuredCardSymbol,
            InsuredBranchNumber = resResult.InsuredBranchNumber,
            PersonalFamilyClassification = resResult.PersonalFamilyClassification,
            InsuredCertificateIssuanceDate = resResult.InsuredCertificateIssuanceDate,
            InsuredCardValidDate = resResult.InsuredCardValidDate,
            InsuredCardExpirationDate = resResult.InsuredCardExpirationDate,
        };
        var matchHokenInfs = hokenInfs.FindAll(p => PatientInfoConverter.GetHokenConfirmationModels(p, resultOfQC, resResult.Birthday.AsInteger(), sinDate).All(x => x.IsReflect));

        var systemConfigList = _systemConfig.GetList(hpId, new List<int> { 100029 });

        int nameBasicInfoCheck = (int)(systemConfigList.FirstOrDefault(item => item.GrpEdaNo == 1)?.Val ?? 1);
        int kanaNameBasicInfoCheck = (int)(systemConfigList.FirstOrDefault(item => item.GrpEdaNo == 2)?.Val ?? 1);
        int genderBasicInfoCheck = (int)(systemConfigList.FirstOrDefault(item => item.GrpEdaNo == 3)?.Val ?? 1);
        int birthDayBasicInfoCheck = (int)(systemConfigList.FirstOrDefault(item => item.GrpEdaNo == 4)?.Val ?? 1);
        int addressBasicInfoCheck = (int)(systemConfigList.FirstOrDefault(item => item.GrpEdaNo == 5)?.Val ?? 1);
        int postcodeBasicInfoCheck = (int)(systemConfigList.FirstOrDefault(item => item.GrpEdaNo == 6)?.Val ?? 1);
        int seitaiNushiBasicInfoCheck = (int)(systemConfigList.FirstOrDefault(item => item.GrpEdaNo == 7)?.Val ?? 1);

        if (ptInf != null)
        {
            var ptInfModel = ConvertToPatientInfoModel(ptInf);

            matchPtInf = new List<PtInfConfirmationModel>
            {
                new PtInfConfirmationModel(PtInfOQConst.KANA_NAME,
                                           ptInfModel.KanaName,
                                           resultOfQC.NameKana ,
                                           nameBasicInfoCheck,
                                           kanaNameBasicInfoCheck,
                                           genderBasicInfoCheck,
                                           birthDayBasicInfoCheck,
                                           addressBasicInfoCheck,
                                           postcodeBasicInfoCheck,
                                           seitaiNushiBasicInfoCheck),
                new PtInfConfirmationModel(PtInfOQConst.KANJI_NAME,
                                           ptInfModel.Name,
                                           resultOfQC.Name,
                                           nameBasicInfoCheck,
                                           kanaNameBasicInfoCheck,
                                           genderBasicInfoCheck,
                                           birthDayBasicInfoCheck,
                                           addressBasicInfoCheck,
                                           postcodeBasicInfoCheck,
                                           seitaiNushiBasicInfoCheck),
                new PtInfConfirmationModel(PtInfOQConst.SEX,
                                           ptInfModel.Sex.AsString(),
                                           string.IsNullOrEmpty(resultOfQC.Sex2) ? resultOfQC.Sex1: resultOfQC.Sex2,
                                           nameBasicInfoCheck,
                                           kanaNameBasicInfoCheck,
                                           genderBasicInfoCheck,
                                           birthDayBasicInfoCheck,
                                           addressBasicInfoCheck,
                                           postcodeBasicInfoCheck,
                                           seitaiNushiBasicInfoCheck),
                new PtInfConfirmationModel(PtInfOQConst.BIRTHDAY,
                                           ptInfModel.Birthday.AsString(),
                                           resultOfQC.Birthdate.AsString(),
                                           nameBasicInfoCheck,
                                           kanaNameBasicInfoCheck,
                                           genderBasicInfoCheck,
                                           birthDayBasicInfoCheck,
                                           addressBasicInfoCheck,
                                           postcodeBasicInfoCheck,
                                           seitaiNushiBasicInfoCheck),
                new PtInfConfirmationModel(PtInfOQConst.SETANUSI, ptInfModel.Setanusi, resultOfQC.InsuredName, nameBasicInfoCheck, kanaNameBasicInfoCheck, genderBasicInfoCheck, birthDayBasicInfoCheck, addressBasicInfoCheck, postcodeBasicInfoCheck, seitaiNushiBasicInfoCheck),
                new PtInfConfirmationModel(PtInfOQConst.HOME_ADDRESS, ptInfModel.HomeAddress1, resultOfQC.Address, nameBasicInfoCheck, kanaNameBasicInfoCheck, genderBasicInfoCheck, birthDayBasicInfoCheck, addressBasicInfoCheck, postcodeBasicInfoCheck, seitaiNushiBasicInfoCheck),
                new PtInfConfirmationModel(PtInfOQConst.HOME_POST, ptInfModel.HomePost, resultOfQC.PostNumber, nameBasicInfoCheck, kanaNameBasicInfoCheck, genderBasicInfoCheck, birthDayBasicInfoCheck, addressBasicInfoCheck, postcodeBasicInfoCheck, seitaiNushiBasicInfoCheck),
            };
            matchPtInf = matchPtInf.Where(x => x.IsVisible).ToList();
        }
        if (matchPtInf.Any(x => !x.IsReflect) && matchHokenInfs.Count <= 0)
        {
            return "[基本・保険差異あり] " + resResult.ProcessingResultMessage;
        }
        else if (!matchPtInf.Any(x => !x.IsReflect) && matchHokenInfs.Count <= 0)
        {
            return "[保険差異あり] " + resResult.ProcessingResultMessage;
        }
        else if (matchPtInf.Any(x => !x.IsReflect) && matchHokenInfs.Count > 0)
        {
            return "[基本差異あり] " + resResult.ProcessingResultMessage;
        }
        return resResult.ProcessingResultMessage;
    }

    private static PatientInforModel ConvertToPatientInfoModel(PtInf ptInf)
    {
        return new PatientInforModel(
                    ptInf.KanaName ?? string.Empty,
                    ptInf.Name ?? string.Empty,
                    ptInf.Sex,
                    ptInf.Birthday,
                    ptInf.HomePost ?? string.Empty,
                    ptInf.HomeAddress1 ?? string.Empty,
                    ptInf.Setanusi ?? string.Empty
                );
    }

    /// <summary>
    /// Check Exist Online Confirm of Patient by Sindate
    /// </summary>
    /// <param name="ptId"></param>
    /// <param name="sinDate"></param>
    /// <returns>true or false</returns>
    public bool ExistOnlineConsent(long ptId, int sinDate, int hpId)
    {
        var onlConfirms = NoTrackingDataContext.OnlineConfirmationHistories.Where(x => x.HpId == hpId && x.PtId == ptId && x.InfoConsFlg != null && x.InfoConsFlg.Contains("1")).ToList();
        return onlConfirms.Any(p => CIUtil.DateTimeToInt(p.OnlineConfirmationDate) == sinDate);
    }

    public Tuple<bool, List<long>, long, long, int> AddConfDataHisByXml(int hpId, string xmlContent, int userId, int pmhStatus, string pmhResult)
    {
        XElement xml = XElement.Parse(xmlContent);

        string referenceNumber = xml.Descendants(XmlConstant.ReferenceNumber).FirstOrDefault()?.Value;
        string processExecutionTime = xml.Descendants(XmlConstant.ProcessExecutionTime).FirstOrDefault()?.Value;
        string qualificationConfirmationDate = xml.Descendants(XmlConstant.QualificationConfirmationDate).FirstOrDefault()?.Value;
        string prescriptionIssueSelect = xml.Descendants(XmlConstant.PrescriptionIssueSelect).FirstOrDefault()?.Value;
        string pharmacistsInfoConsFlg = xml.Descendants(XmlConstant.PharmacistsInfoConsFlg).FirstOrDefault()?.Value;
        string specificHealthCheckupsInfoConsFlg = xml.Descendants(XmlConstant.SpecificHealthCheckupsInfoConsFlg).FirstOrDefault()?.Value;
        string diagnosisInfoConsFlg = xml.Descendants(XmlConstant.DiagnosisInfoConsFlg).FirstOrDefault()?.Value;
        string operationInfoConsFlg = xml.Descendants(XmlConstant.OperationInfoConsFlg).FirstOrDefault()?.Value;
        var infoConsFlg = ConcatenateIfNotNull(pharmacistsInfoConsFlg, specificHealthCheckupsInfoConsFlg, diagnosisInfoConsFlg, operationInfoConsFlg);
        DateTime onlineConfirmDate = DateTime.ParseExact(processExecutionTime, "yyyyMMddHHmmss", CultureInfo.InvariantCulture);

        var nameOfOtherKana = CIUtil.ToFullsize(xml.Descendants(XmlConstant.NameOfOtherKana).FirstOrDefault()?.Value);
        var nameKana = CIUtil.ToFullsize(xml.Descendants(XmlConstant.NameKana).FirstOrDefault()?.Value);

        string pattern = @"<(NameOfOtherKana|NameKana)>(.*?)</\1>";
        string result = Regex.Replace(xmlContent, pattern, match =>
        {
            string tag = match.Groups[1].Value;
            string fullWidth = tag == "NameOfOtherKana" ? nameOfOtherKana : nameKana;
            return $"<{tag}>{fullWidth}</{tag}>";
        });

        CIUtil.ConvertJapanTimeToUtc(ref onlineConfirmDate);
        PtInf? ptInf = null;
        if (!string.IsNullOrWhiteSpace(referenceNumber))
        {
            ptInf = NoTrackingDataContext.PtInfs
                                         .FirstOrDefault(c => c.HpId == hpId &&
                                                              c.ReferenceNo == Convert.ToInt64(referenceNumber) &&
                                                              c.PtNum > 0 &&
                                                              c.IsDelete == DeleteStatus.None);
        }

        var ptId = ptInf?.PtId ?? 0;

        var raiinInfs = TrackingDataContext.RaiinInfs
            .Where(c => c.HpId == hpId
                        && c.PtId == ptId
                        && c.SinDate == Convert.ToInt32(qualificationConfirmationDate)
                        && c.Status == RaiinState.Reservation
                        && c.IsDeleted == DeleteStatus.None)
            .ToList();

        var pmhLicense = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 100029 && p.GrpEdaNo == 201)?.Val ?? 0;
        if (pmhLicense != 1)
        {
            pmhStatus = 0;
            pmhResult = string.Empty;
        }

        var onlineHistory = new OnlineConfirmationHistoryModel(
            id: 0,
            ptId: ptId,
            onlineConfirmationDate: onlineConfirmDate,
            confirmationType: 1,
            infoConsFlg: infoConsFlg,
            confirmationResult: result,
            prescriptionIssueType: int.Parse(prescriptionIssueSelect ?? "0"),
            uketukeStatus: 0,
            hpid: hpId,
            pmhStatus: pmhStatus,
            pmhResult: pmhResult
        );

        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        List<long> updatedRaiinInfsIds = new List<long>();
        long newRaiinInfsIds = 0;

       return executionStrategy.Execute(
            () =>
                {
                    using var transaction = TrackingDataContext.Database.BeginTransaction();
                    var onlineConfHis = Insert(userId, onlineHistory);

                    if (raiinInfs.Count > 0)
                    {
                        foreach (var raiinInf in raiinInfs)
                        {
                            raiinInf.Status = RaiinState.Confirmation;
                            raiinInf.PrintEpsReference = 0;
                            raiinInf.PrescriptionIssueType = Convert.ToInt32(prescriptionIssueSelect);
                            raiinInf.OnlineConfirmationId = Convert.ToInt32(onlineConfHis.ID);
                            raiinInf.UpdateId = userId;
                            raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();

                            updatedRaiinInfsIds.Add(raiinInf.RaiinNo);
                        }
                    }
                    else if (raiinInfs.Count == 0 && ptInf != null)
                    {
                        var reception = new ReceptionUpsertItem(
                            hpId: hpId,
                            ptId: ptId,
                            sinDate: Convert.ToInt32(qualificationConfirmationDate),
                            raiinNo: 0,
                            oyaRaiinNo: 0,
                            hokenPid: 0,
                            santeiKbn: 0,
                            status: 1,
                            isYoyaku: 0,
                            yoyakuTime: string.Empty,
                            yoyakuId: 0,
                            uketukeSbt: 1,
                            uketukeTime: string.Empty,
                            uketukeId: 0,
                            uketukeNo: 0,
                            sinStartTime: string.Empty,
                            sinEndTime: string.Empty,
                            kaikeiTime: string.Empty,
                            kaikeiId: 0,
                            kaId: 0,
                            tantoId: 0,
                            syosaisinKbn: 0,
                            jikanKbn: 0,
                            comment: string.Empty,
                            treatmentDepartmentId: 0,
                            printEpsReference: 0,
                            prescriptionIssueType: onlineConfHis.PrescriptionIssueType,
                            onlineConfHis.ID.AsInteger()
                        );

                        var dto = new ReceptionSaveDto(
                            reception: reception,
                            receptionComment: null,
                            kubunInfs: new List<RaiinKbnInfDto>(),
                            insurances: new List<InsuranceDto>(),
                            diseases: null
                        );
                        var newRaiinNo = _receptionRepository.InsertRaiinInf(dto, hpId, userId);

                        newRaiinInfsIds =newRaiinNo;
                    }
                    TrackingDataContext.SaveChanges();
                    transaction.Commit();
                    return new Tuple<bool, List<long>, long, long, int>(true, updatedRaiinInfsIds, newRaiinInfsIds, onlineConfHis.ID, Convert.ToInt32(qualificationConfirmationDate));
                });
    }

    public long DeleteConfirmationHis(int hpid, int userId, long onlineHisId)
    {
        var onlineHis = TrackingDataContext.OnlineConfirmationHistories
            .FirstOrDefault(c => c.HpId == hpid && c.ID == onlineHisId && c.UketukeStatus != XmlConstant.IsDeleted);
        if (onlineHis == null)
        {
            return -1;
        }

        onlineHis.UketukeStatus = XmlConstant.IsDeleted;
        TrackingDataContext.SaveChanges();
        return onlineHis.ID;
    }

    public OnlineConfirmationHistory Insert(int userId, OnlineConfirmationHistoryModel onlineRecord)
    {
        var onlineEntity = new OnlineConfirmationHistory
        {
            ID = 0,
            PtId = onlineRecord.PtId,
            OnlineConfirmationDate = onlineRecord.OnlineConfirmationDate.ToUniversalTime(),
            ConfirmationType = onlineRecord.ConfirmationType,
            PrescriptionIssueType = onlineRecord.PrescriptionIssueType,
            ConfirmationResult = onlineRecord.ConfirmationResult,
            UketukeStatus = onlineRecord.UketukeStatus,
            InfoConsFlg = onlineRecord.InfoConsFlg,
            HpId = onlineRecord.Hpid,
            PmhStatus = onlineRecord.PmhStatus,
            PmhResult = onlineRecord.PmhResult,
            CreateDate = CIUtil.GetJapanDateTimeNow().ToUniversalTime(),
            UpdateDate = CIUtil.GetJapanDateTimeNow().ToUniversalTime(),
            CreateId = userId,
            UpdateId = userId
        };

        TrackingDataContext.Add(onlineEntity);
        TrackingDataContext.SaveChanges();
        return onlineEntity;
    }

    public string ConcatenateIfNotNull(params string[] values)
    {
        if (values == null || values.Length == 0 || values.All(v => string.IsNullOrEmpty(v)))
        {
            return string.Empty;
        }

        var result = string.Join("", values.Select(v => string.IsNullOrEmpty(v) ? " " : v));
        return string.IsNullOrEmpty(result) ? string.Empty : result;
    }

    public long SaveOnlineConfirmationHistory(OnlineConfirmationHistoryModel onlineConfirmationHistoryModel, int userId)
    {
        var date = CIUtil.GetJapanDateTimeNow();
        var entity = new OnlineConfirmationHistory()
        {
            PtId = onlineConfirmationHistoryModel.PtId,
            OnlineConfirmationDate = onlineConfirmationHistoryModel.OnlineConfirmationDate,
            ConfirmationType = onlineConfirmationHistoryModel.ConfirmationType,
            ConfirmationResult = onlineConfirmationHistoryModel.ConfirmationResult,
            UketukeStatus = onlineConfirmationHistoryModel.UketukeStatus,
            InfoConsFlg = onlineConfirmationHistoryModel.InfoConsFlg,
            PrescriptionIssueType = onlineConfirmationHistoryModel.PrescriptionIssueType,
            CreateDate = date,
            UpdateDate = date,
            CreateId = userId,
            UpdateId = userId,
            HpId = onlineConfirmationHistoryModel.Hpid,
        };
        TrackingDataContext.OnlineConfirmationHistories.Add(entity);
        TrackingDataContext.SaveChanges();
        return entity.ID;
    }

    #region private function
    private OnlineConfirmationHistoryModel ConvertToModel(OnlineConfirmationHistory entity)
    {
        return new OnlineConfirmationHistoryModel(
                   entity.ID,
                   entity.PtId,
                   entity.OnlineConfirmationDate,
                   entity.ConfirmationType,
                   entity.InfoConsFlg ?? string.Empty,
                   entity.ConfirmationResult ?? string.Empty,
                   entity.PrescriptionIssueType,
                   entity.UketukeStatus,
                   entity.HpId,
                   entity.PmhStatus,
                   entity.PmhResult
               );
    }

    private void UpdateInfConsFlgInRaiinInf(int userId, List<RaiinInf> raiinInfsInSameday, string infConsFlg)
    {
        var unConfirmedRaiinInfs = raiinInfsInSameday.Where(x => string.IsNullOrEmpty(x.InfoConsFlg));
        var confirmedRaiininfs = raiinInfsInSameday.Except(unConfirmedRaiinInfs).ToList();
        if (!confirmedRaiininfs.Any())
        {
            foreach (var raiinInf in unConfirmedRaiinInfs)
            {
                raiinInf.InfoConsFlg = infConsFlg;
                raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                raiinInf.UpdateId = userId;
            }
        }
        else
        {
            void UpdateFlgValue(int flgIdx)
            {
                char flgToChar(int flg)
                {
                    if (flg == 1)
                    {
                        return '1';
                    }
                    else if (flg == 2)
                    {
                        return '2';
                    }
                    return ' ';
                }
                var unCofirmedFlgRaiinInfs = confirmedRaiininfs.Where(x => x.InfoConsFlg?.Length > flgIdx && x.InfoConsFlg[flgIdx] == ' ');
                var confirmedFlgRaiinInfs = confirmedRaiininfs.Where(x => x.InfoConsFlg?.Length > flgIdx && x.InfoConsFlg[flgIdx] != ' ');
                if (!confirmedFlgRaiinInfs.Any())
                {
                    foreach (var raiinInf in unCofirmedFlgRaiinInfs)
                    {
                        raiinInf.InfoConsFlg = ReplaceAt(raiinInf.InfoConsFlg ?? string.Empty, flgIdx, infConsFlg[flgIdx]);
                        raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        raiinInf.UpdateId = userId;
                    }
                }
                else
                {
                    int minFlg = confirmedFlgRaiinInfs.Select(x => x.InfoConsFlg![flgIdx].AsInteger()).DefaultIfEmpty()?.Min() ?? 0;
                    int respondedFlg = infConsFlg[flgIdx] == ' ' ? 0 : infConsFlg![flgIdx].AsInteger();
                    int compareFlg = minFlg > respondedFlg ? respondedFlg : minFlg;
                    int newFlg = respondedFlg == 0 ? minFlg : compareFlg;
                    foreach (var raiinInf in confirmedFlgRaiinInfs)
                    {
                        raiinInf.InfoConsFlg = ReplaceAt(raiinInf.InfoConsFlg ?? string.Empty, flgIdx, flgToChar(newFlg));
                        raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        raiinInf.UpdateId = userId;
                    }
                    foreach (var raiinInf in unCofirmedFlgRaiinInfs)
                    {
                        raiinInf.InfoConsFlg = ReplaceAt(raiinInf.InfoConsFlg ?? string.Empty, flgIdx, flgToChar(newFlg));
                        raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        raiinInf.UpdateId = userId;
                    }
                }
            }

            //Update PharmacistsInfoConsFlg
            UpdateFlgValue(0);
            //Update SpecificHealthCheckupsInfoConsFlg
            UpdateFlgValue(1);
            //Update DiagnosisInfoConsFlg
            UpdateFlgValue(2);
            //Update OperationInfoConsFlg
            UpdateFlgValue(3);

            //Apply computed infoconsflg for the new raiininf which has nullable infoconsFlg value
            string newInfoConsFlg = confirmedRaiininfs.FirstOrDefault()?.InfoConsFlg ?? string.Empty;
            foreach (var raiinInf in unConfirmedRaiinInfs)
            {
                raiinInf.InfoConsFlg = newInfoConsFlg;
                raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                raiinInf.UpdateId = userId;

            }
        }
        TrackingDataContext.SaveChanges();
    }

    private string ReplaceAt(string input, int index, char newChar)
    {
        if (input == null)
        {
            return string.Empty;
        }
        StringBuilder builder = new StringBuilder(input);
        builder[index] = newChar;
        return builder.ToString();
    }
    #endregion

    public void ReleaseResource()
    {
        DisposeDataContext();
        _insuranceRepository.ReleaseResource();
        _receptionRepository.ReleaseResource();
    }

    public List<BatchOnlineCheckModel> GetBatchOnlineChecks(int hpId, int batchConfirmationType)
    {
        var batchOnlineCheck = NoTrackingDataContext.OnlineConfirmations
                            .Where(x => x.HpId == hpId && (batchConfirmationType == 0 || x.BatchConfirmationType == batchConfirmationType))
                            .OrderByDescending(x => x.ReceptionDateTime)
                            .Take(50)
                            .ToList();

        var result = batchOnlineCheck.Select(x => new BatchOnlineCheckModel
        {
            ReceptionNo = x.ReceptionNo,
            ReceptionDateTime = x.ReceptionDateTime,
            YoyakuDate = x.YoyakuDate,
            SegmentOfResult = x.SegmentOfResult,
            ErrorMessage = x.ErrorMessage,
            ConfirmationType = x.ConfirmationType,
            ExaminationTo = x.ExaminationTo,
            SinYm = x.SinYm,
            ConsentFrom = x.ConsentFrom,
            ConsentTo = x.ConsentTo,
            ExaminationFrom = x.ExaminationFrom,
            BatchConfirmationType = x.BatchConfirmationType,
            ProcessTime = x.ProcessTime
        }).ToList();

        return result;
    }


    public List<PatientInforModel> GetPatientHomeVisit(int hpId, int ptNum)
    {
        var query = NoTrackingDataContext.PtInfs
            .Where(x => x.HpId == hpId && 
                        (ptNum == 0 || x.PtNum == ptNum)
                        && x.PtNum > 0
                        && x.IsDelete == DeleteStatus.None
                        && x.HoumonAgreed == 1)
            .OrderBy(x => x.PtNum)
            .Take(50)
            .Select(item => new PatientInforModel(item.PtId, item.PtNum, item.KanaName, item.Name, item.Birthday))
            .ToList();

        return query;
    }


    public bool ProcessOQSXML(int hpId, int userId, OnlineConfirmationModel onlineConfirmationModel)
    {
        var date = CIUtil.GetJapanDateTimeNow();

        OnlineConfirmation onlineConfirmation = new OnlineConfirmation();
        onlineConfirmation.ReceptionDateTime = DateTime.SpecifyKind(onlineConfirmationModel.ReceptionDateTime, DateTimeKind.Local).ToUniversalTime();
        onlineConfirmation.ReceptionNo = onlineConfirmationModel.ReceptionNo;
        onlineConfirmation.ProcessTime = DateTime.SpecifyKind(onlineConfirmationModel.ProcessTime.Value, DateTimeKind.Local).ToUniversalTime();
        onlineConfirmation.CreateId = userId;
        onlineConfirmation.CreateDate = date;
        onlineConfirmation.UpdateId = userId;
        onlineConfirmation.UpdateDate = date;
        onlineConfirmation.SegmentOfResult = "0";
        onlineConfirmation.YoyakuDate = onlineConfirmationModel.YoyakuDate;
        onlineConfirmation.SinYm = onlineConfirmationModel.SinYm;
        onlineConfirmation.ConsentFrom = onlineConfirmationModel.ConsentFrom;
        onlineConfirmation.ConsentTo = onlineConfirmationModel.ConsentTo;
        onlineConfirmation.ExaminationFrom = onlineConfirmationModel.ExaminationFrom;
        onlineConfirmation.ExaminationTo = onlineConfirmationModel.ExaminationTo;
        onlineConfirmation.BatchConfirmationType = onlineConfirmationModel.BatchConfirmationType;
        onlineConfirmation.HpId = hpId;
        TrackingDataContext.OnlineConfirmations.Add(onlineConfirmation);
        return TrackingDataContext.SaveChanges() > 0;
    }
    public bool CancelPatientHomeVisit(int hpId, long ptId, int userId)
    {
        var patientInfo = TrackingDataContext.PtInfs.FirstOrDefault(x => x.PtId == ptId && x.HpId == hpId);

        if (patientInfo is null)
            return false;
        patientInfo.HoumonAgreed = 0;

        patientInfo.UpdateDate = CIUtil.GetJapanDateTimeNow();
        patientInfo.UpdateId = userId;
        return TrackingDataContext.SaveChanges() > 0;
    }

    public void SaveOnlineConfirmationComposite(int userId, int hpId, List<OnlineConfirmationCompositeModel> onlineConfirmationComposite)
    {
        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(() =>
        {
            using var transaction = TrackingDataContext.Database.BeginTransaction();
            try
            {
                foreach (var composite in onlineConfirmationComposite)
                {
                    if (composite.OnlineConfirmationHistory != null)
                    {
                        var currentDate = CIUtil.GetJapanDateTimeNow();
                        var onlineConfirmationHistory = new OnlineConfirmationHistory
                        {
                            ID = 0,
                            PtId = composite.OnlineConfirmationHistory.PtId,
                            OnlineConfirmationDate = currentDate,
                            ConfirmationType = composite.OnlineConfirmationHistory.ConfirmationType,
                            ConfirmationResult = composite.OnlineConfirmationHistory.ConfirmationResult,
                            UketukeStatus = composite.OnlineConfirmationHistory.UketukeStatus,
                            InfoConsFlg = composite.OnlineConfirmationHistory.InfoConsFlg,
                            CreateDate = currentDate,
                            UpdateDate = currentDate,
                            CreateId = userId,
                            UpdateId = userId,
                            HpId = hpId
                        };

                        var onlineHistoryEntry = TrackingDataContext.OnlineConfirmationHistories.Add(onlineConfirmationHistory);
                        TrackingDataContext.SaveChanges();

                        var onlineDetails = composite.OnlineConfirmationDetails.Select(
                                x => new OnlineConfirmationDetail
                                {
                                    ReceptionNo = x.ReceptionNo ?? string.Empty,
                                    PtId = x.PtId,
                                    ConfirmationResult = x.ConfirmationResult,
                                    Message = x.Message,
                                    Status = x.Status,
                                    OnlineConfirmationId = onlineHistoryEntry.Entity.ID,
                                    CreateDate = currentDate,
                                    UpdateDate = currentDate,
                                    CreateId = userId,
                                    UpdateId = userId,
                                    ConfirmationState = x.ConfirmationState
                                })
                            .ToList();

                        TrackingDataContext.OnlineConfirmationDetails.AddRange(onlineDetails);

                        if (composite.AgreedConsents is { Count: > 0 })
                        {
                            var agreedConsents = composite.AgreedConsents.Select(x => new OnlineAgreedConsent
                            {
                                PtId = x.PtId,
                                HpId = hpId,
                                ProcessTime = x.ProcessTime,
                                ConsType = x.ConsType,
                                ConsKbn = x.ConsKbn,
                                ConsFlg = x.ConsFlg,
                                ConsDate = x.ConsDate,
                                LimitDate = x.LimitDate,
                                CreateDate = CIUtil.GetJapanDateTimeNow(),
                                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                                CreateId = userId,
                                UpdateId = userId
                            });

                            TrackingDataContext.OnlineAgreedConsents.AddRange(agreedConsents);
                        }

                        if (composite.AgreedPrescriptions is { Count: > 0 })
                        {
                            var agreedPrescriptions = composite.AgreedPrescriptions.Select(x => new OnlineAgreedPrescription
                            {
                                HpId = hpId,
                                PtId = x.PtId,
                                ConsType = x.ConsType,
                                ConsDate = x.ConsDate,
                                LimitDate = x.LimitDate,
                                CreateId = userId,
                                UpdateId = userId,
                                PrescriptionIssueType = x.PrescriptionIssueType,
                                CreateDate = CIUtil.GetJapanDateTimeNow(),
                                UpdateDate = CIUtil.GetJapanDateTimeNow()
                            });

                            TrackingDataContext.OnlineAgreedPrescriptions.AddRange(agreedPrescriptions);
                        }

                        if (composite.ConfirmDateModel != null)
                        {
                            UpdateHokenCheck(composite.ConfirmDateModel, hpId, onlineHistoryEntry.Entity.ID.AsInteger());
                        }
                    }
                    else
                    {
                        var currentDate = CIUtil.GetJapanDateTimeNow();
                        var onlineDetails = composite.OnlineConfirmationDetails.Select(
                                x => new OnlineConfirmationDetail
                                {
                                    ReceptionNo = x.ReceptionNo ?? string.Empty,
                                    PtId = x.PtId,
                                    ConfirmationResult = x.ConfirmationResult,
                                    Message = x.Message,
                                    Status = x.Status,
                                    OnlineConfirmationId = 0,
                                    CreateDate = currentDate,
                                    UpdateDate = currentDate,
                                    CreateId = userId,
                                    UpdateId = userId,
                                    ConfirmationState = x.ConfirmationState
                                })
                            .ToList();

                        TrackingDataContext.OnlineConfirmationDetails.AddRange(onlineDetails);
                    }
                }

                TrackingDataContext.SaveChanges();
                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        });
    }

    public void UpdateHokenCheck(ConfirmDateModel model, int hpId, int onlineConfirmationId)
    {
        var userId = model.CheckId;
        var checkDate = DateTime.SpecifyKind(CIUtil.IntToDate(model.ConfirmDate), DateTimeKind.Utc);
        var hokenChecks = TrackingDataContext.PtHokenChecks.Where(u => 
            u.HpId == hpId &&
            u.PtID == model.PtId &&
            u.HokenId == model.HokenId &&
            u.HokenGrp == model.HokenGrp &&
            u.IsDeleted == DeleteStatus.None)
            .ToList();

        var hokenCheck = hokenChecks.FirstOrDefault(u => u.CheckDate.ToString("yyyyMMdd") == model.ConfirmDate.ToString());
        if (hokenCheck != null)
        {
            hokenCheck.CheckDate = hokenCheck.CheckDate.SetKindUtc();
            hokenCheck.CreateDate = hokenCheck.CreateDate.SetKindUtc();
            hokenCheck.UpdateDate = CIUtil.GetJapanDateTimeNow();
            hokenCheck.UpdateId = userId;
            hokenCheck.CheckId = userId;
            hokenCheck.OnlineConfirmationId = onlineConfirmationId;
            TrackingDataContext.PtHokenChecks.Update(hokenCheck);
        }
        else
        {
            TrackingDataContext.PtHokenChecks.Add(new PtHokenCheck
            {
                HpId = hpId,
                PtID = model.PtId,
                HokenGrp = model.HokenGrp,
                HokenId = model.HokenId,
                CheckDate = checkDate,
                CheckId = userId,
                CheckCmt = model.CheckComment,
                IsDeleted = 0,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId,
                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateId = userId,
                OnlineConfirmationId = onlineConfirmationId
            });
        }

        TrackingDataContext.SaveChanges();
    }

    public (bool, long) ViewResultConfirm(int hpId, long ptId, long onlineDetailId, int userId, int confirmType, string xmlValue, bool isRegiterPatient)
    {
        var newOnlineConf = new OnlineConfirmationHistory();
        bool IsSuccess = true;
        var onlineDetail = TrackingDataContext.OnlineConfirmationDetails.FirstOrDefault(c => c.Id == onlineDetailId);
        if (onlineDetail == null) { return (false, newOnlineConf.ID); }

        var ptInf = TrackingDataContext.PtInfs.FirstOrDefault(c => c.HpId == hpId && c.PtId == ptId );
        if (ptInf == null) { return (false, newOnlineConf.ID); }

        onlineDetail.Status = 1;

        if (onlineDetail.PtId == 0)
        {
            onlineDetail.PtId = ptId;
        }

        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
        executionStrategy.Execute(() =>
        {
            using var transaction = TrackingDataContext.Database.BeginTransaction();
            if (!isRegiterPatient)
            {
                var infoConsFlg = string.Empty;
                if (confirmType is 3 or 4)
                {
                    XElement xml = XElement.Parse(xmlValue);
                    string referenceNumber = xml.Descendants(XmlConstant.ReferenceNumber).FirstOrDefault()?.Value;
                    string processExecutionTime = xml.Descendants(XmlConstant.ProcessExecutionTime).FirstOrDefault()?.Value;
                    string qualificationConfirmationDate = xml.Descendants(XmlConstant.QualificationConfirmationDate).FirstOrDefault()?.Value;
                    string prescriptionIssueSelect = xml.Descendants(XmlConstant.PrescriptionIssueSelect).FirstOrDefault()?.Value;
                    string pharmacistsInfoConsFlg = xml.Descendants(XmlConstant.PharmacistsInfoConsFlg).FirstOrDefault()?.Value;
                    string specificHealthCheckupsInfoConsFlg = xml.Descendants(XmlConstant.SpecificHealthCheckupsInfoConsFlg).FirstOrDefault()?.Value;
                    string diagnosisInfoConsFlg = xml.Descendants(XmlConstant.DiagnosisInfoConsFlg).FirstOrDefault()?.Value;
                    string operationInfoConsFlg = xml.Descendants(XmlConstant.OperationInfoConsFlg).FirstOrDefault()?.Value;
                    infoConsFlg = ConcatenateIfNotNull(pharmacistsInfoConsFlg, specificHealthCheckupsInfoConsFlg, diagnosisInfoConsFlg, operationInfoConsFlg);
                }
               
                var onlineEntity = new OnlineConfirmationHistoryModel(0, ptId, CIUtil.GetJapanDateTimeNow(), 2, infoConsFlg, xmlValue, 0, 2, hpId);
                newOnlineConf = Insert(userId, onlineEntity);
            }
            if (confirmType == 3 && !isRegiterPatient)
            {
                ptInf.HoumonAgreed = 1;
            }

            if (confirmType is 3 or 4 && !isRegiterPatient)
            {
                XElement xml = XElement.Parse(xmlValue);
                var xmlObject = new XmlSerializer(typeof(QCXmlMsgResponse)).Deserialize(new StringReader(xmlValue)) as QCXmlMsgResponse;
                if (xmlObject == null)
                {
                    IsSuccess = false;
                    return;
                }
                string processExecutionTime = xml.Descendants(XmlConstant.ProcessExecutionTime).FirstOrDefault()?.Value;
                string qualificationAvailableTime = xml.Descendants(XmlConstant.QualificationAvailableTime).FirstOrDefault()?.Value;
                string qualificationConsTime = xml.Descendants(XmlConstant.QualificationConsTime).FirstOrDefault()?.Value;
                string prescriptionIssueSelect = xml.Descendants(XmlConstant.PrescriptionIssueSelect).FirstOrDefault()?.Value;

                if (!string.IsNullOrEmpty(processExecutionTime) &&
                    DateTime.TryParseExact(processExecutionTime, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime onlineConfirmDate))
                {
                    CIUtil.ConvertJapanTimeToUtc(ref onlineConfirmDate);
                    InsertOnlineAgreedConsent(hpId, ptId, xmlObject.MessageBody.ResultList.ResultOfQualificationConfirmation.First(), onlineConfirmDate.ToUniversalTime(), userId, confirmType);
                   var insertOnlineAgreedPrescriptionRes = InsertOnlineAgreedPrescription(hpId, ptId, qualificationConsTime, qualificationAvailableTime, prescriptionIssueSelect, userId, confirmType);
                    if (!insertOnlineAgreedPrescriptionRes)
                    {
                        IsSuccess = false;
                        return;
                    }
                }
            }

            TrackingDataContext.SaveChanges();
            transaction.Commit();
        });
        return (IsSuccess, newOnlineConf.ID);
    }

    public void InsertOnlineAgreedConsent(int hpId,long patientId, ResultOfQualificationConfirmation result, DateTime processTime, int userId, int confirmType)
    {
        var agreedConsents = new List<OnlineAgreedConsent>
        {
            new OnlineAgreedConsent
            {
                HpId = hpId,
                PtId = patientId,
                ProcessTime = processTime,
                ConsType = confirmType == 4 ? 1 : 0, 
                ConsKbn = 1, 
                ConsFlg = (result.PharmacistsInfoConsFlg).AsInteger(),
                ConsDate = ConvertToDateTime(result.PharmacistsInfoConsTime),
                LimitDate = ConvertToDateTime(result.PharmacistsInfoAvailableTime),
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId,
                UpdateDate = CIUtil.GetJapanDateTimeNow()
            },
            new OnlineAgreedConsent
            {
                HpId = hpId,
                PtId = patientId,
                ProcessTime = processTime,
                ConsType = confirmType == 4 ? 1 : 0,
                ConsKbn = 2,
                ConsFlg = (result.SpecificHealthCheckupsInfoConsFlg).AsInteger(),
                ConsDate = ConvertToDateTime(result.SpecificHealthCheckupsInfoConsTime),
                LimitDate = ConvertToDateTime(result.SpecificHealthCheckupsInfoAvailableTime),
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId,
                UpdateDate = CIUtil.GetJapanDateTimeNow()
            },
            new OnlineAgreedConsent
            {
                HpId = hpId,
                PtId = patientId,
                ProcessTime = processTime,
                ConsType = confirmType == 4 ? 1 : 0,
                ConsKbn = 3,
                ConsFlg = (result.DiagnosisInfoConsFlg).AsInteger(),
                ConsDate = ConvertToDateTime(result.DiagnosisInfoConsTime),
                LimitDate = ConvertToDateTime(result.DiagnosisInfoAvailableTime),
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId,
                UpdateDate = CIUtil.GetJapanDateTimeNow()
            },
            new OnlineAgreedConsent
            {
                HpId = hpId,
                PtId = patientId,
                ProcessTime = processTime,
                ConsType = confirmType == 4 ? 1 : 0,
                ConsKbn = 4, 
                ConsFlg = (result.OperationInfoConsFlg).AsInteger(),
                ConsDate = ConvertToDateTime(result.OperationInfoConsTime),
                LimitDate = ConvertToDateTime(result.OperationInfoAvailableTime),
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId,
                UpdateDate = CIUtil.GetJapanDateTimeNow()
            }
        };

        agreedConsents = agreedConsents.Where(x => x.ConsFlg != 0).ToList();
        if (agreedConsents.Any())
        {
            TrackingDataContext.OnlineAgreedConsents.AddRange(agreedConsents);
            TrackingDataContext.SaveChanges();
        }
    }

    public bool InsertOnlineAgreedPrescription(
    int hpId,
    long ptId,
    string qualificationConsTime,
    string qualificationAvailableTime,
    string prescriptionIssueSelect,
    int userId,
    int confirmType)
    {
        if (!DateTime.TryParseExact(qualificationConsTime, "yyyyMMddHHmmss",
                CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime consDate))
        {
            return false;
        }

        if (!DateTime.TryParseExact(qualificationAvailableTime, "yyyyMMddHHmmss",
                CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime limitDate))
        {
            return false;
        }

        if (!int.TryParse(prescriptionIssueSelect, out int prescriptionIssueType))
        {
            prescriptionIssueType = 2; 
        }

        var entity = new OnlineAgreedPrescription
        {
            HpId = hpId,
            PtId = ptId,
            ConsType = confirmType == 4 ? 1 : 0, 
            ConsDate = consDate.ToUniversalTime(),
            LimitDate = limitDate.ToUniversalTime(),
            PrescriptionIssueType = prescriptionIssueType,
            CreateDate = CIUtil.GetJapanDateTimeNow(),
            CreateId = userId,
            UpdateDate = CIUtil.GetJapanDateTimeNow(),
            UpdateId = userId,
        };

        TrackingDataContext.OnlineAgreedPrescriptions.Add(entity);
        TrackingDataContext.SaveChanges();

        return true;
    }

    private DateTime ConvertToDateTime(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return default(DateTime);

        if (DateTime.TryParseExact(value, "yyyyMMddHHmmss",
            CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
        {
            return date.ToUniversalTime();
        }

        return default(DateTime);
    }
    public List<OnlineConfirmationDetailDto> GetListViewResult(int hpId, string receptionNo, bool? isConfirmState, bool? isStatus)
    {
        var onlineConfirmationDetails = NoTrackingDataContext.OnlineConfirmationDetails.Where(e => e.ReceptionNo == receptionNo);
        if (isConfirmState == true) onlineConfirmationDetails = onlineConfirmationDetails.Where(e => e.ConfirmationState != 1);
        if (isStatus == true) onlineConfirmationDetails = onlineConfirmationDetails.Where(e => e.Status == 0);

        var query = (from detail in onlineConfirmationDetails
                     orderby detail.PtId ascending
                     select new OnlineConfirmationDetailDto
                     (
                         detail.Id,
                         detail.ReceptionNo,
                         detail.PtId,
                         detail.ConfirmationResult,
                         detail.ConfirmationState,
                         detail.Message,
                         detail.Status,
                         detail.OnlineConfirmationId
                     )).ToList();

        var ptIds = query.Select(e => e.PtId).Distinct().ToList();
        var ptInfs = NoTrackingDataContext.PtInfs.Where(e => e.HpId == hpId && e.IsDelete == DeleteTypes.None && ptIds.Contains(e.PtId)).Select(e => new { e.PtId, e.PtNum }).ToList();

        foreach (var item in query)
        {
            var ptInf = ptInfs.FirstOrDefault(e => e.PtId == item.PtId);
            if (ptInf != null) item.SetPtNum(ptInf.PtNum);
        }
        return query;
    }

    public List<OnlineConfirmationHistoryModel> GetListOnlineConfirmationHistoryModelById(int hpId, long onlineConfirmationId)
    {
        var listOnlineConfirmationHistory = NoTrackingDataContext.OnlineConfirmationHistories.Where(item => item.HpId == hpId && item.ID == onlineConfirmationId).ToList();
        var result = listOnlineConfirmationHistory.Select(item => ConvertToModel(item))
                                                  .OrderByDescending(item => item.OnlineConfirmationDate)
                                                  .ToList();
        return result;
    }

    public OnlineConfirmationDetailDto GetOnlineConfirmationDetailById(long id)
    {
        var onlineConfirmationDetail = NoTrackingDataContext.OnlineConfirmationDetails
            .Where(e => e.Id == id)
            .Select(e => new OnlineConfirmationDetailDto(
                         e.Id,
                         e.ReceptionNo,
                         e.PtId,
                         e.ConfirmationResult,
                         e.ConfirmationState,
                         e.Message,
                         e.Status,
                         e.OnlineConfirmationId))
            .FirstOrDefault();

        if (onlineConfirmationDetail != null)
        {
            var cfType = NoTrackingDataContext.OnlineConfirmations.FirstOrDefault(e => e.ReceptionNo == onlineConfirmationDetail.ReceptionNo);
            onlineConfirmationDetail.SetBatchCOnfirmationType(cfType?.BatchConfirmationType ?? 0);
        }
        return onlineConfirmationDetail ?? new();
    }

    public bool IsViewDrugInformation(long ptId)
    {
        return TrackingDataContext.OnlineConsents.Any(x => x.PtId == ptId && x.ConsKbn == 1 && x.ConsDate <= CIUtil.GetJapanDateTimeNow() && x.LimitDate >= CIUtil.GetJapanDateTimeNow());
    }
}
