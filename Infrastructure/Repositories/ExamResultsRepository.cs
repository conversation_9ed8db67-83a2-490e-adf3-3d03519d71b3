using Domain.Models.ExamResults;
using Domain.Models.SpecialNote.PatientInfo;
using Helper.Common;
using Helper.Redis;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using System.Globalization;
using System.Text.RegularExpressions;
using Domain.Models.ExamResults;
using Helper.Common;
using Helper.Constants;
using Domain.Enum;

namespace Infrastructure.Repositories
{
    public class ExamResultsRepository : RepositoryBase, IExamResultsRepository
    {
        private readonly string key;
        private readonly IDatabase _cache;
        private readonly IConfiguration _configuration;

        public ExamResultsRepository(ITenantProvider tenantProvider, IConfiguration configuration) : base(
            tenantProvider)
        {
            key = GetDomainKey();
            _configuration = configuration;
            GetRedis();
            _cache = RedisConnectorHelper.Connection.GetDatabase();
        }

        public void GetRedis()
        {
            string connection =
                string.Concat(_configuration["Redis:RedisHost"], ":", _configuration["Redis:RedisPort"]);
            if (RedisConnectorHelper.RedisHost != connection)
            {
                RedisConnectorHelper.RedisHost = connection;
            }
        }


        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        public ExamResultsDto GetExamResults(int hpId, long ptId, long startDate, long endDate, string? keyword, bool timeSequence, List<string>? kensaItemCds, string centerCd = "")
        {
            List<ExamResultsModel> examResultsModels = new();
            string kensaItemCd = "";
            string kensaName = "";

            if (startDate == 0 && endDate == 0 && !timeSequence)
            {
                endDate = 99999999;
            }
            else if (startDate != 0 && endDate == 0 && !timeSequence)
            {
                endDate = 99999999;
            }

            if (timeSequence)
            {
                if (startDate == 0 && endDate != 0)
                {
                    DateTime date2 = DateTime.ParseExact(endDate.ToString(), "yyyyMMdd", null);
                    DateTime date1 = date2.AddYears(-1);
                    startDate = int.Parse(date1.ToString("yyyyMMdd"));
                }
                else if (startDate != 0 && endDate == 0)
                {
                    DateTime date1 = DateTime.ParseExact(startDate.ToString(), "yyyyMMdd", null);
                    DateTime date2 = date1.AddYears(+1);
                    endDate = int.Parse(date2.ToString("yyyyMMdd"));
                }
            }

            if (Regex.IsMatch(keyword, @"^[0-9]"))
            {
                kensaItemCd = keyword;
            }
            else
            {
                kensaName = keyword;
            }

            string kanaKeyword = CIUtil.ToHalfsize(kensaName);

            if (!WanaKana.IsKana(kanaKeyword) && WanaKana.IsRomaji(kanaKeyword))
            {
                if (WanaKana.IsRomaji(kanaKeyword))
                    kanaKeyword = keyword;
            }

            string sBigKeyword = kanaKeyword.ToUpper()
                .Replace("ｧ", "ｱ")
                .Replace("ｨ", "ｲ")
                .Replace("ｩ", "ｳ")
                .Replace("ｪ", "ｴ")
                .Replace("ｫ", "ｵ")
                .Replace("ｬ", "ﾔ")
                .Replace("ｭ", "ﾕ")
                .Replace("ｮ", "ﾖ")
                .Replace("ｯ", "ﾂ");

            var kensaInfs = NoTrackingDataContext.KensaInfs.Where(x => x.HpId == hpId &&
                                                           x.PtId == ptId &&
                                                           x.IsDeleted == DeleteTypes.None &&
                                                           x.IraiDate >= startDate &&
                                                           x.IraiDate <= endDate &&
                                                           (x.InoutKbn == 0 || x.InoutKbn == 1) &&
                                                           (string.IsNullOrEmpty(centerCd) || x.CenterCd == centerCd))
                                                .ToList();

            var allKensaInfDetails = NoTrackingDataContext.KensaInfDetails
                .Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None && x.KensaItemCd != null && (!kensaItemCds.Any() || kensaItemCds.Contains(x.KensaItemCd))).ToList();

            var query = (from kensaInf in kensaInfs
                         join kensaInfDetail in allKensaInfDetails on kensaInf.IraiCd equals kensaInfDetail.IraiCd
                         select new
                         {
                             kensaInf.IraiDate,
                             kensaInf.KensaTime,
                             CenterCd = kensaInf.CenterCd ?? string.Empty,
                             KensaInf = kensaInf,
                             KensaInfDetail = kensaInfDetail,
                         })
                .GroupBy(item => new { item.IraiDate, item.KensaTime, item.CenterCd })
                .Select(group => new
                {
                    IraiDate = group.Key.IraiDate,
                    KensaTime = group.Key.KensaTime,
                    CenterCd = group.Key.CenterCd,
                    KensaInfs = group.Select(x => x.KensaInf).Distinct().ToList(),
                    KensaInfDetails = group.Select(x => x.KensaInfDetail).ToList()
                })
                .ToList();

            var allCommonCenterKensaMst = NoTrackingDataContext.CommonCenterKensaMst.ToList();
            var allCommonKensaCenterMst = NoTrackingDataContext.CommonKensaCenterMst.ToList();
            var allCommonCenterStdMst = NoTrackingDataContext.CommonCenterStdMst.ToList();

            var allKensaMst = NoTrackingDataContext.KensaMsts.Where(x => x.HpId == hpId && !x.KensaItemCd.StartsWith("V"))
                                                             .OrderByDescending(x => x.CreateDate)
                                                             .ThenBy(mst => mst.SortNo)
                                                             .ToList();

            var relevantKensaMst = allKensaMst.Where(x =>
                (!kensaItemCds.Any() || kensaItemCds.Contains(x.KensaItemCd))
            ).ToList();

            var relevantCommonCenterKensaMst = allCommonCenterKensaMst.Where(x =>
                (!kensaItemCds.Any() || kensaItemCds.Contains(x.KensaItemCd)) &&
                (string.IsNullOrEmpty(centerCd) || x.CenterCd == centerCd)
            ).ToList();
            foreach (var kensaMst in relevantKensaMst)
            {
                var matchingQueryItems = query.Where(item =>
                    item.CenterCd == CommonConstants.InHospitalCenterCd &&
                    item.KensaInfDetails.Any(detail => detail.KensaItemCd == kensaMst.KensaItemCd)
                ).ToList();

                if (matchingQueryItems.Any())
                {
                    foreach (var item in matchingQueryItems)
                    {
                        var firstKensaInf = item.KensaInfs.First();
                        List<KensaInfDetailModel> kensaInfDetailModels = new();
                        string dspCenterName = timeSequence ? "院内" : "院内検査";
                        string kensName = kensaMst.KensaName ?? string.Empty;
                        string kensaKana = kensaMst.KensaKana ?? string.Empty;
                        string maleStd = kensaMst.MaleStd ?? string.Empty;
                        string femaleStd = kensaMst.FemaleStd ?? string.Empty;
                        string unit = kensaMst.Unit ?? string.Empty;

                        var relevantDetails = item.KensaInfDetails.Where(detail => detail.KensaItemCd == kensaMst.KensaItemCd).ToList();

                        foreach (var detail in relevantDetails)
                        {
                            kensaInfDetailModels.Add(new KensaInfDetailModel(
                                detail.HpId, detail.PtId, detail.IraiCd, detail.SeqNo, detail.IraiDate,
                                detail.RaiinNo, detail.KensaItemCd ?? string.Empty, detail.ResultVal ?? string.Empty,
                                detail.ResultType ?? string.Empty, detail.AbnormalKbn ?? string.Empty,
                                detail.IsDeleted, detail.CmtCd1 ?? string.Empty, detail.CmtCd2 ?? string.Empty,
                                detail.UpdateDate, unit, kensName, 0, kensaKana, maleStd, femaleStd));
                        }

                        if (!string.IsNullOrEmpty(kensaName))
                        {
                            kensaInfDetailModels = kensaInfDetailModels.Where(x =>
                                (!string.IsNullOrEmpty(x.KensaKana) && x.KensaKana.ToUpper()
                                     .Replace("ｧ", "ｱ").Replace("ｨ", "ｲ").Replace("ｩ", "ｳ")
                                     .Replace("ｪ", "ｴ").Replace("ｫ", "ｵ").Replace("ｬ", "ﾔ")
                                     .Replace("ｭ", "ﾕ").Replace("ｮ", "ﾖ").Replace("ｯ", "ﾂ")
                                     .StartsWith(sBigKeyword)) ||
                                (!string.IsNullOrEmpty(x.KensaName) && x.KensaName.Contains(kensaName))
                            ).ToList();
                        }

                        if (!string.IsNullOrEmpty(kensaItemCd))
                        {
                            kensaInfDetailModels = kensaInfDetailModels
                                .Where(x => x.KensaItemCd.StartsWith(kensaItemCd)).ToList();
                        }

                        if (kensaInfDetailModels.Any())
                        {
                            var examResultsModel = new ExamResultsModel(
                                dspCenterName,
                                item.KensaTime,
                                kensaInfDetailModels.OrderBy(x => x.KensaName).ToList(),
                                item.IraiDate,
                                firstKensaInf.InoutKbn,
                                firstKensaInf.SikyuKbn,
                                firstKensaInf.TosekiKbn,
                                item.CenterCd);

                            examResultsModels.Add(examResultsModel);
                        }
                    }
                }
                else
                {
                    List<KensaInfDetailModel> kensaInfDetailModels = new();
                    var defaultDetailModel = new KensaInfDetailModel(
                        hpId, ptId, 0, 0, 0, 0,
                        kensaMst.KensaItemCd ?? string.Empty, string.Empty, string.Empty,
                        string.Empty, DeleteTypes.None, string.Empty, string.Empty,
                        DateTime.MinValue,
                        kensaMst.Unit ?? string.Empty,
                        kensaMst.KensaName ?? string.Empty,
                        0,
                        kensaMst.KensaKana ?? string.Empty,
                        kensaMst.MaleStd ?? string.Empty,
                        kensaMst.FemaleStd ?? string.Empty);

                    kensaInfDetailModels.Add(defaultDetailModel);

                    var examResultsModel = new ExamResultsModel(
                        timeSequence ? "院内" : "院内検査",
                        "",
                        kensaInfDetailModels,
                        0,
                        (sbyte)InouKbnEnums.InHospital,
                        0,
                        0,
                        CommonConstants.InHospitalCenterCd);
                    examResultsModels.Add(examResultsModel);
                }
            }

            var centerCdGroups = relevantCommonCenterKensaMst.GroupBy(x => x.CenterCd).ToList();

            foreach (var centerGroup in centerCdGroups)
            {
                var currentCenterCd = centerGroup.Key;

                foreach (var commonMst in centerGroup)
                {
                    var matchingQueryItems = query.Where(item =>
                        item.CenterCd == currentCenterCd &&
                        item.KensaInfDetails.Any(detail => detail.KensaItemCd == commonMst.KensaItemCd)
                    ).ToList();

                    if (matchingQueryItems.Any())
                    {
                        foreach (var item in matchingQueryItems)
                        {
                            var firstKensaInf = item.KensaInfs.First();
                            List<KensaInfDetailModel> kensaInfDetailModels = new();
                            string dspCenterName = string.Empty;
                            string kensName = commonMst.KensaName ?? string.Empty;
                            string kensaKana = commonMst.KensaKana ?? string.Empty;
                            string unit = commonMst.Unit ?? string.Empty;

                            string maleStd = string.Empty;
                            string femaleStd = string.Empty;

                            var relevantDetails = item.KensaInfDetails.Where(detail => detail.KensaItemCd == commonMst.KensaItemCd).ToList();

                            foreach (var detail in relevantDetails)
                            {
                                var commonCenterStdMst = allCommonCenterStdMst.FirstOrDefault(x => x.CenterCd == currentCenterCd && x.KensaItemCd == detail.KensaItemCd);
                                if (commonCenterStdMst != null)
                                {
                                    maleStd = commonCenterStdMst.MealStd ?? string.Empty;
                                    femaleStd = commonCenterStdMst.FemelStd ?? string.Empty;
                                }

                                var commonKensaCenterMst = allCommonKensaCenterMst.FirstOrDefault(x => x.CenterCd == currentCenterCd);
                                if (commonKensaCenterMst != null)
                                {
                                    dspCenterName = commonKensaCenterMst.DspCenterName ?? string.Empty;
                                }

                                kensaInfDetailModels.Add(new KensaInfDetailModel(
                                    detail.HpId, detail.PtId, detail.IraiCd, detail.SeqNo, detail.IraiDate,
                                    detail.RaiinNo, detail.KensaItemCd ?? string.Empty, detail.ResultVal ?? string.Empty,
                                    detail.ResultType ?? string.Empty, detail.AbnormalKbn ?? string.Empty,
                                    detail.IsDeleted, detail.CmtCd1 ?? string.Empty, detail.CmtCd2 ?? string.Empty,
                                    detail.UpdateDate, unit, kensName, 0, kensaKana, maleStd, femaleStd));
                            }

                            if (!string.IsNullOrEmpty(kensaName))
                            {
                                kensaInfDetailModels = kensaInfDetailModels.Where(x =>
                                    (!string.IsNullOrEmpty(x.KensaKana) && x.KensaKana.ToUpper()
                                         .Replace("ｧ", "ｱ").Replace("ｨ", "ｲ").Replace("ｩ", "ｳ")
                                         .Replace("ｪ", "ｴ").Replace("ｫ", "ｵ").Replace("ｬ", "ﾔ")
                                         .Replace("ｭ", "ﾕ").Replace("ｮ", "ﾖ").Replace("ｯ", "ﾂ")
                                         .StartsWith(sBigKeyword)) ||
                                    (!string.IsNullOrEmpty(x.KensaName) && x.KensaName.Contains(kensaName))
                                ).ToList();
                            }

                            if (!string.IsNullOrEmpty(kensaItemCd))
                            {
                                kensaInfDetailModels = kensaInfDetailModels
                                    .Where(x => x.KensaItemCd.StartsWith(kensaItemCd)).ToList();
                            }

                            if (kensaInfDetailModels.Any())
                            {
                                var examResultsModel = new ExamResultsModel(
                                    dspCenterName,
                                    item.KensaTime,
                                    kensaInfDetailModels.OrderBy(x => x.KensaName).ToList(),
                                    item.IraiDate,
                                    firstKensaInf.InoutKbn,
                                    firstKensaInf.SikyuKbn,
                                    firstKensaInf.TosekiKbn,
                                    item.CenterCd);

                                examResultsModels.Add(examResultsModel);
                            }
                        }
                    }
                    else
                    {
                        List<KensaInfDetailModel> kensaInfDetailModels = new();

                        var commonKensaCenterMst = allCommonKensaCenterMst.FirstOrDefault(x => x.CenterCd == currentCenterCd);
                        string dspCenterName = commonKensaCenterMst?.DspCenterName ?? string.Empty;

                        string maleStd = string.Empty;
                        string femaleStd = string.Empty;
                        var commonCenterStdMst = allCommonCenterStdMst.FirstOrDefault(x => x.CenterCd == currentCenterCd && x.KensaItemCd == commonMst.KensaItemCd);
                        if (commonCenterStdMst != null)
                        {
                            maleStd = commonCenterStdMst.MealStd ?? string.Empty;
                            femaleStd = commonCenterStdMst.FemelStd ?? string.Empty;
                        }

                        var defaultDetailModel = new KensaInfDetailModel(
                            hpId, ptId, 0, 0, 0, 0,
                            commonMst.KensaItemCd ?? string.Empty, string.Empty, string.Empty,
                            string.Empty, DeleteTypes.None, string.Empty, string.Empty,
                            DateTime.MinValue,
                            commonMst.Unit ?? string.Empty,
                            commonMst.KensaName ?? string.Empty,
                            0,
                            commonMst.KensaKana ?? string.Empty,
                            maleStd,
                            femaleStd);

                        kensaInfDetailModels.Add(defaultDetailModel);

                        var examResultsModel = new ExamResultsModel(
                            dspCenterName,
                            "",
                            kensaInfDetailModels,
                            0,
                            (sbyte)InouKbnEnums.OutHospital,
                            0,
                            0,
                            currentCenterCd);
                        examResultsModels.Add(examResultsModel);
                    }
                }
            }
            examResultsModels = examResultsModels
    .GroupBy(x => new { x.IraiDate, x.KensaTime, x.CenterCd })
    .Select(g =>
        new ExamResultsModel(
            g.First().DspCenterName,
            g.Key.KensaTime,
            g.SelectMany(x => x.KensaInfDetailModels).OrderBy(x => x.KensaName).ToList(),
            g.Key.IraiDate,
            g.First().InoutKbn,
            g.First().SikyuKbn,
            g.First().TosekiKbn,
            g.Key.CenterCd))
    .ToList();

            if (!timeSequence)
            {
                examResultsModels = examResultsModels
                    .Where(x => x.IraiDate != 0)
                    .OrderByDescending(item => long.Parse(item.IraiDate.ToString() + item.KensaTime))
                    .ToList();
            }
            else
            {
                examResultsModels = examResultsModels
                    .OrderBy(item => long.Parse(item.IraiDate.ToString() + item.KensaTime))
                    .ToList();
            }

            if (!timeSequence)
            {
                examResultsModels = examResultsModels
                    .Where(x => x.IraiDate != 0)
                    .OrderByDescending(item => long.Parse(item.IraiDate.ToString() + item.KensaTime))
                    .ToList();
            }
            else
            {
                examResultsModels = examResultsModels
                    .OrderBy(item => long.Parse(item.IraiDate.ToString() + item.KensaTime))
                    .ToList();
            }

            return new ExamResultsDto(examResultsModels, GetAverageDays(examResultsModels), GetAverageMonths(examResultsModels, startDate, endDate));
        }

        private Dictionary<long, Dictionary<string, double>> GetAverageDays(List<ExamResultsModel> examResultsModels)
        {
            var result = new Dictionary<long, Dictionary<string, double>>();

            var kensaWithCenter = examResultsModels
                .SelectMany(exam => exam.KensaInfDetailModels.Select(kensa => new
                {
                    exam.CenterCd,
                    Kensa = kensa
                }))
                .Where(x => double.TryParse(x.Kensa.ResultVal, out _))
                .OrderBy(x => x.Kensa.IraiDate)
                .ToList();

            var averageDays = kensaWithCenter
                .GroupBy(x => new { x.Kensa.KensaItemCd, x.Kensa.IraiDate, x.CenterCd })
                .Select(g => new
                {
                    Day = g.Key.IraiDate,
                    KensaItemCdWithCenter = $"{g.Key.KensaItemCd}-{g.Key.CenterCd}",
                    AverageResultVal = g.Average(k => double.Parse(k.Kensa.ResultVal))
                })
                .GroupBy(x => x.Day);

            foreach (var averages in averageDays)
            {
                var averageDay = new Dictionary<string, double>();
                foreach (var item in averages)
                {
                    averageDay.Add(item.KensaItemCdWithCenter, Math.Round(item.AverageResultVal, 2, MidpointRounding.AwayFromZero));
                }
                result.Add(averages.Key, averageDay);
            }

            return result;
        }

        private Dictionary<string, Dictionary<string, double>> GetAverageMonths(List<ExamResultsModel> examResultsModels, long? startDate, long? endDate)
        {
            var result = new Dictionary<string, Dictionary<string, double>>();

            var filteredExams = examResultsModels
                .Where(item => item.IraiDate >= startDate && item.IraiDate <= endDate)
                .ToList();

            var kensaWithCenter = filteredExams
                .SelectMany(exam => exam.KensaInfDetailModels.Select(kensa => new
                {
                    exam.CenterCd,
                    Kensa = kensa
                }))
                .Where(x => double.TryParse(x.Kensa.ResultVal, out _))
                .ToList();

            var monthlyAverages = kensaWithCenter
                .GroupBy(x =>
                {
                    var date = DateTime.ParseExact(x.Kensa.IraiDate.ToString(), "yyyyMMdd", CultureInfo.InvariantCulture);
                    var month = date.ToString("yyyyMM");
                    return new { Month = month, x.Kensa.KensaItemCd, x.CenterCd };
                })
                .Select(g => new
                {
                    Month = g.Key.Month,
                    KensaItemCdWithCenter = $"{g.Key.KensaItemCd}-{g.Key.CenterCd}",
                    AverageResultVal = g.Average(x => double.Parse(x.Kensa.ResultVal))
                })
                .GroupBy(x => x.Month)
                .OrderBy(x => x.Key);

            foreach (var monthlyAverage in monthlyAverages)
            {
                var averageMonth = new Dictionary<string, double>();
                foreach (var item in monthlyAverage)
                {
                    averageMonth.Add(item.KensaItemCdWithCenter, Math.Round(item.AverageResultVal, 2, MidpointRounding.AwayFromZero));
                }
                result.Add(monthlyAverage.Key, averageMonth);
            }

            return result;
        }

    }
}