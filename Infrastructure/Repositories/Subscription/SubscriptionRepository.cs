﻿using Domain.Models.Subscription;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using static System.Net.Mime.MediaTypeNames;

namespace Infrastructure.Repositories.Subscription
{
    public class SubscriptionRepository : RepositoryBase, ISubscriptionRepository
    {
        public SubscriptionRepository(ITenantProvider tenantProvider) : base(tenantProvider)
        {
        }

        public void DeleteSubscription(string functionCode)
        {
            var deleteSql = $"delete from subscription where function_code = @p0";
            TrackingDataContext.Database.ExecuteSqlRaw(deleteSql, functionCode);
        }

        public bool InsertSubscription(string functionCode, string text)
        {
            var insertSql = $"insert into subscription (function_code, text) values(@p0, @p1)";
            return TrackingDataContext.Database.ExecuteSqlRaw(insertSql, functionCode, text) > 0;
        }
    }
}
