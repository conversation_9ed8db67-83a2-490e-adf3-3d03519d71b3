﻿using CommonCheckers.OrderRealtimeChecker.DB;
using Domain.Constant;
using Domain.Enum;
using Domain.Models.Accounting;
using Domain.Models.Ka;
using Domain.Models.MstItem;
using Domain.Models.OrdInfs;
using Domain.Models.PatientInfor;
using Domain.Models.TodayOdr;
using Emr.Report.OutDrug.Model;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Mapping;
using Infrastructure.Interfaces;
using Reporting.CommonMasters.Config;
using Reporting.OutDrug.Constants;
using Reporting.OutDrug.DB;
using Reporting.OutDrug.Model;
using Reporting.OutDrug.Service;
using Reporting.OutDrug.Utils;
using System.Text;
using UseCase.Eps.GetOutDrugCsvData;
using static Domain.Enum.EpsEnum;

namespace Interactor.Eps;

public class GetOutDrugCsvDataInteractor : IGetOutDrugCsvDataInputPort
{
    private readonly IPatientInforRepository _patientInforRepository;
    private readonly ICoOutDrugFinder _finder;
    private readonly IKaRepository _kaRepository;
    private readonly IAccountingRepository _accountingRepository;
    private readonly ISystemConfig _systemConfig;
    private readonly IMasterFinder _masterFinder;

    public GetOutDrugCsvDataInteractor(
            IPatientInforRepository patientInforRepository,
            ITenantProvider tenantProvider,
            ITodayOdrRepository todayOdrRepository,
            ICoOutDrugFinder finder,
            IKaRepository kaRepository,
            IOutDrugCoReportService outDrugCoReportService,
            IAccountingRepository accountingRepository,
            IMstItemRepository mstItemRepository,
            IMasterFinder masterFinder,
            ISystemConfig systemConfig
        )
    {
        _patientInforRepository = patientInforRepository;
        _kaRepository = kaRepository;
        _finder = finder;
        _accountingRepository = accountingRepository;
        _masterFinder = masterFinder;
        _systemConfig = systemConfig;
    }

    public GetOutDrugCsvDataOutputData Handle(GetOutDrugCsvDataInputData inputData)
    {
        try
        {
            var validateResult = ValidateInputData(inputData);
            if (validateResult != GetOutDrugCsvDataStatus.ValidateSuccess)
            {
                return new GetOutDrugCsvDataOutputData(validateResult);
            }
            var csvData = GetCSVData(inputData);
            if (string.IsNullOrEmpty(csvData)) return new GetOutDrugCsvDataOutputData(GetOutDrugCsvDataStatus.Successed, string.Empty, string.Empty);

            var prescriptionDocument = EpsUtil.BytesToBase64String(EpsUtil.Utf8StringToBytes(csvData));

            return new GetOutDrugCsvDataOutputData(GetOutDrugCsvDataStatus.Successed, csvData, prescriptionDocument);
        }
        finally
        {
            _patientInforRepository.ReleaseResource();
            _kaRepository.ReleaseResource();
            _finder.ReleaseResource();
            _accountingRepository.ReleaseResource();
            _masterFinder.ReleaseResource();
            _systemConfig.ReleaseResource();
        }
        return new GetOutDrugCsvDataOutputData(GetOutDrugCsvDataStatus.Failed);
    }

    private GetOutDrugCsvDataStatus ValidateInputData(GetOutDrugCsvDataInputData inputData)
    {
        if (!IsValidPtId(inputData))
            return GetOutDrugCsvDataStatus.InvalidUserId;
        if (!IsValidSinDate(inputData))
            return GetOutDrugCsvDataStatus.InvalidSindate;
        if (!IsValidRaiinNo(inputData))
            return GetOutDrugCsvDataStatus.InvalidRaiinNo;
        if (!Enum.IsDefined(typeof(EpsEnum.EpsCsvType), inputData.FileType))
            return GetOutDrugCsvDataStatus.InvalidFileType;
        if (!Enum.IsDefined(typeof(RefileCount), inputData.RefileCount))
            return GetOutDrugCsvDataStatus.InvalidRefileCount;
        if (inputData.FileType == (sbyte)EpsEnum.EpsCsvType.Unfinished && !inputData.TodayOdrInfs.Any())
            return GetOutDrugCsvDataStatus.NoOutputOrders;

        return GetOutDrugCsvDataStatus.ValidateSuccess;
    }
    private bool IsValidPtId(GetOutDrugCsvDataInputData inputData)
    {
        return inputData.PtId > ValidateConstant.INVALID_NO &&
               _patientInforRepository.CheckExistIdList(inputData.HpId, new List<long> { inputData.PtId });
    }

    private bool IsValidSinDate(GetOutDrugCsvDataInputData inputData)
    {
        return inputData.SinDate.ToString().Length == ValidateConstant.DATE_LENGTH;
    }

    private bool IsValidRaiinNo(GetOutDrugCsvDataInputData inputData)
    {
        return inputData.RaiinNo > ValidateConstant.INVALID_NO;
    }

    private string GetCSVData(GetOutDrugCsvDataInputData inputData)
    {
        // 戻り値
        string retData = "";

        inputData.RefileCount = inputData.RefileCount < 1 ? 1 : inputData.RefileCount;

        #region 対象データの取得

        // 医療機関情報取得
        CoHpInfModel hpInf = _finder.FindHpInf(inputData.HpId, inputData.SinDate);
        // 患者情報
        CoPtInfModel ptInf = _finder.FindPtInf(inputData.HpId, inputData.PtId, inputData.SinDate);
        List<KaMstModel> kaMsts = _kaRepository.GetList(inputData.HpId, (sbyte)Delete.None);
        // 来院情報取得
        CoRaiinInfModel raiinInf = _finder.FindRaiinInf(inputData.HpId, inputData.PtId, inputData.SinDate, inputData.RaiinNo);

        // 重複投薬等チェック情報
        List<CoEpsChk> epsChks = _finder.FindEPSChecks(inputData.HpId, inputData.PtId, inputData.RaiinNo);

        List<CoOdrInfModel> odrInfs;
        List<CoOdrInfDetailModel> odrInfDtls;

        var TodayOdrInfs = inputData.TodayOdrInfs;

        if (TodayOdrInfs != null)
        {
            // カルテ作成画面のオーダー情報を院外処方箋用にコンバート
            (odrInfs, odrInfDtls) = ConvertTodayOdrToOutDrugOdr(TodayOdrInfs, inputData.HpId, inputData.PtId, inputData.SinDate);
            // 処方に絞り込む
            odrInfs = odrInfs.FindAll(o => o.InoutKbn == (sbyte)OrderInfConst.InoutKbn.OutHospital && OrderInfConst.TodayOdrKouiKbn.Contains(o.OdrKouiKbn));
            odrInfDtls = odrInfDtls.FindAll(o => !(o.ItemCd.StartsWith("8") && o.ItemCd.Length == 9));
        }
        else
        {
            odrInfs = _finder.FindOdrInfData(inputData.HpId, inputData.PtId, inputData.SinDate, inputData.RaiinNo);
            odrInfDtls = _finder.FindOdrInfDetailData(inputData.HpId, inputData.PtId, inputData.SinDate, inputData.RaiinNo);
        }

        // 処方箋使用期間
        int rxLimit = 0;
        if (odrInfDtls.Any(o => o.ItemCd == ItemCdConst.Con_RxLimit && o.Suryo > 0))
        {
            rxLimit = (int)odrInfDtls.Where(o => o.ItemCd == ItemCdConst.Con_RxLimit).Select(o => o.Suryo).FirstOrDefault();
        }

        List<CoOdrInfModel> filteredOdrInfs = new List<CoOdrInfModel>();
        List<CoOdrInfDetailModel> filteredOdrInfDtls = new List<CoOdrInfDetailModel>();

        // 健保だけを対象とする(自費算定分も除く)
        filteredOdrInfs = odrInfs.FindAll(p => p.HokenSyu == HokenSyu.Kenpo && p.SanteiKbn != 2);
        filteredOdrInfDtls = odrInfDtls.FindAll(p => p.HokenSyu == HokenSyu.Kenpo && p.SanteiKbn != 2);

        // オーダーデータがなければデータ出力しない
        if (filteredOdrInfs.Any() == false)
        {
            return retData;
        }

        #region リフィル回数による絞り込み
        (int count, List<(long rpno, long rpedano)> rp) refill = (0, null);

        if (inputData.FileType != (sbyte)EpsEnum.EpsCsvType.Unfinished)
        {
            // 確定前処方箋情報以外はリフィルの回数によって処方箋情報を分ける

            List<(int count, List<(long rpno, long rpedano)> rp)> refills = new List<(int, List<(long, long)>)>();

            foreach (CoOdrInfModel odr in filteredOdrInfs)
            {
                List<CoOdrInfDetailModel> dtlrefill =
                    filteredOdrInfDtls.FindAll(p =>
                        p.RpNo == odr.RpNo &&
                        p.RpEdaNo == odr.RpEdaNo &&
                        p.ItemCd == ItemCdConst.Con_Refill);
                if (dtlrefill != null && dtlrefill.Any() && dtlrefill.First().Suryo > 1)
                {
                    // リフィル2回以上
                    if (refills.Any(p => p.count == dtlrefill.First().Suryo) == false)
                    {
                        refills.Add(((int)dtlrefill.First().Suryo, new List<(long rpno, long rpedano)>()));
                    }

                    refills.Find(p => p.count == (int)dtlrefill.First().Suryo).rp.Add((odr.RpNo, odr.RpEdaNo));
                }
                else
                {
                    // リフィルなし（リフィル1回はリフィルなしと同じ）
                    if (refills.Any(p => p.count == 1) == false)
                    {
                        refills.Add((1, new List<(long rpno, long rpedano)>()));
                    }

                    refills.Find(p => p.count == 1).rp.Add((odr.RpNo, odr.RpEdaNo));
                }
            }

            // 残薬確認・情報提供のチェック（取得したodrInfと紐づかないので別にチェック）
            foreach (CoOdrInfDetailModel dtl in filteredOdrInfDtls.FindAll(p => new string[] { ItemCdConst.ZanGigi, ItemCdConst.ZanTeiKyo }.Contains(p.ItemCd)))
            {
                List<CoOdrInfDetailModel> dtlrefill =
                   filteredOdrInfDtls.FindAll(p =>
                        p.RpNo == dtl.RpNo &&
                        p.RpEdaNo == dtl.RpEdaNo &&
                        p.ItemCd == ItemCdConst.Con_Refill);
                if (dtlrefill != null && dtlrefill.Any() && dtlrefill.First().Suryo > 1)
                {
                    // リフィル2回以上
                    if (refills.Any(p => p.count == dtlrefill.First().Suryo) == false)
                    {
                        refills.Add(((int)dtlrefill.First().Suryo, new List<(long rpno, long rpedano)>()));
                    }

                    refills.Find(p => p.count == (int)dtlrefill.First().Suryo).rp.Add((dtl.RpNo, dtl.RpEdaNo));
                }
                else
                {
                    // リフィルなし（リフィル1回はリフィルなしと同じ）
                    if (refills.Any(p => p.count == 1) == false)
                    {
                        refills.Add((1, new List<(long rpno, long rpedano)>()));
                    }

                    refills.Find(p => p.count == 1).rp.Add((dtl.RpNo, dtl.RpEdaNo));
                }
            }
            refill = refills.Where(p => p.count == inputData.RefileCount).FirstOrDefault();

            if (refill.rp == null)
            {
                // 指定されたリフィル回数のデータがなければ出力しない
                return retData;
            }

            bool _isRefillRp(long Arpno, long Arpedano)
            {
                bool ret = false;

                foreach ((long rpno, long rpedano) in refill.rp)
                {
                    if (Arpno == rpno && Arpedano == rpedano)
                    {
                        ret = true;
                        break;
                    }
                }

                return ret;
            }

            filteredOdrInfs = filteredOdrInfs.FindAll(p => _isRefillRp(p.RpNo, p.RpEdaNo));

            if (filteredOdrInfs.Any(p => !(new int[] { 100, 101 }.Contains(p.OdrKouiKbn))) == false)
            {
                // 処方コメント、備考しかない場合は出力しない
                return retData;
            }

            filteredOdrInfDtls = filteredOdrInfDtls.FindAll(p => _isRefillRp(p.RpNo, p.RpEdaNo));
        }
        #endregion

        #endregion

        // 処方箋に印字する主保険
        int mainHokenId = 0;

        #region 処方で使用した主保険を１つにまとめる ※同じ日に有効な主保険が複数あるのはあり得ない
        // 公費単独は除く
        List<int> mainhokenIds = filteredOdrInfs.Where(p => p.IsKohiTandoku == false).GroupBy(p => p.HokenId).Select(p => p.Key).ToList();

        // 優先順位: 今月オン資で確認した保険→有効保険→保険ID順
        int onlineConfirmedHokenId = _finder.GetOnlineConfirmedHokenId(inputData.HpId, inputData.PtId, inputData.SinDate);
        mainHokenId = filteredOdrInfs.Where(p => p.IsKohiTandoku == false && p.HokenId == onlineConfirmedHokenId)
            .Select(p => p.HokenId)
            .FirstOrDefault();

        if (mainHokenId <= 0)
        {
            // 有効保険
            mainHokenId = filteredOdrInfs.Where(p => p.IsKohiTandoku == false && p.HokenStartDate <= inputData.SinDate && inputData.SinDate <= p.HokenEndDate)
                .Select(p => p.HokenId)
                .FirstOrDefault();
        }
        if (mainHokenId <= 0)
        {
            // 最初の保険
            mainHokenId = filteredOdrInfs.Where(p => p.IsKohiTandoku == false)
            .Select(p => p.HokenId)
            .FirstOrDefault();
        }
        #endregion

        // PT_HOKEN
        CoPtHokenInfModel ptHoken = _finder.FindPtHoken(inputData.HpId, inputData.PtId, mainHokenId, inputData.SinDate);

        // この患者が当該診療で使用しているすべての公費
        List<CoPtKohiModel> ptKohis = new List<CoPtKohiModel>();
        // 処方箋の公費欄に印字する公費
        List<CoPtKohiModel> filteredPtKohis = new List<CoPtKohiModel>();

        #region オーダーの公費使用状況を設定
        // 保険IDの種類（自費算定を除いて、ODR_INFのHOKEN_IDをグループ化）
        List<int> hokenIds = filteredOdrInfs.GroupBy(p => p.HokenId).Select(p => p.Key).ToList();
        // KohiIDの種類
        HashSet<int> kohiIds = new HashSet<int>();

        // 保険IDごとのループ
        foreach (int hokenId in hokenIds)
        {
            // 保険PIDの種類（保険IDを持つ組み合わせIDを探す）
            var groupbyHokenPids = filteredOdrInfs.Where(p => p.HokenId == hokenId).GroupBy(p => p.HokenPid).ToList();

            List<int> hokenPids = new List<int>();

            foreach (var groupbyHokenPid in groupbyHokenPids)
            {
                hokenPids.Add(groupbyHokenPid.Key);
            }

            var pidOdrInfs = filteredOdrInfs.FindAll(p => hokenPids.Contains(p.HokenPid));

            // 公費の種類を調べ、オーダーに公費使用状況を設定する
            foreach (CoOdrInfModel odrInf in pidOdrInfs)
            {
                for (int j = 1; j <= 4; j++)
                {
                    if (odrInf.KohiId(j) > 0) kohiIds.Add(odrInf.KohiId(j));
                }
            }
        }

        if (kohiIds.Any())
        {
            ptKohis = _finder.FindPtKohi(inputData.HpId, inputData.PtId, inputData.SinDate, kohiIds);
            // priority順に並べ替え（特殊公費は最後）
            // 公費（5.生保、6.分点、7.一般) で、負担者番号または特殊番号があるものに絞り込み
            // 他に処方箋未記載の公費があれば、ここでフィルタする
            filteredPtKohis =
                ptKohis.FindAll(p =>
                    new int[] { 5, 6, 7 }.Contains(p.HokenSbtKbn) &&
                    (p.FutansyaNo != "" || p.TokusyuNo != "")
                    )
                .OrderBy(p => p.IsSpecial).ThenBy(p => p.SortKey)
                .ToList();

            //公費IDごとに記録箇所をセット
            int j = 1;
            foreach (var filteredPtKohi in filteredPtKohis)
            {
                filteredPtKohi.RecKbn = EpsKohiRecKbn.Biko;

                if (filteredPtKohi.IsSpecial == 0)
                {
                    //第１～３公費
                    switch (j)
                    {
                        case 1: filteredPtKohi.RecKbn = EpsKohiRecKbn.Kohi1; break;
                        case 2: filteredPtKohi.RecKbn = EpsKohiRecKbn.Kohi2; break;
                        case 3: filteredPtKohi.RecKbn = EpsKohiRecKbn.Kohi3; break;
                        default: break;
                    }

                    j++;
                }
                else if (!filteredPtKohis.Exists(f => f.RecKbn == EpsKohiRecKbn.KohiSp))
                {
                    //特殊公費
                    filteredPtKohi.RecKbn = EpsKohiRecKbn.KohiSp;
                }
            }

            // HOKEN_PIDに対する公費使用状況のマスタ
            List<(int pid, int kohi1, int kohi2, int kohi3, int kohiSp)>
                futans = new List<(int pid, int kohi1, int kohi2, int kohi3, int kohiSp)>();

            foreach (CoOdrInfModel odrInf in filteredOdrInfs)
            {
                if (futans.Any(p => p.pid == odrInf.HokenPid))
                {
                    // PIDがマスタに存在する場合
                    (int pid, int kohi1, int kohi2, int kohi3, int kohiSp) futan =
                        futans.Find(p => p.pid == odrInf.HokenPid);

                    odrInf.Kohi1Futan = futan.kohi1;
                    odrInf.Kohi2Futan = futan.kohi2;
                    odrInf.Kohi3Futan = futan.kohi3;
                    odrInf.KohiSpFutan = futan.kohiSp;
                }
                else
                {
                    // PIDがマスタに存在しない場合
                    if (odrInf.PtHokenPattern.Kohi1Id > 0)
                    {
                        int[] kohis = new int[3];
                        for (int k = 1; k <= 4; k++)
                        {
                            int odrKohiId = odrInf.KohiId(k);

                            int l = 0;
                            foreach (var filteredPtKohi in filteredPtKohis)
                            {
                                if (filteredPtKohi.HokenId == odrKohiId)
                                {
                                    int ftnKbn = 0;
                                    if (new int[] { HokenSbtKbn.Seiho, HokenSbtKbn.Bunten, HokenSbtKbn.Ippan }.Contains(filteredPtKohis[l].HokenMst.HokenSbtKbn))
                                    {
                                        // 分点公費の場合は2を設定
                                        ftnKbn = 2;
                                    }
                                    else
                                    {
                                        ftnKbn = 1;
                                    }

                                    switch (filteredPtKohi.RecKbn)
                                    {
                                        case EpsKohiRecKbn.Kohi1: odrInf.Kohi1Futan = ftnKbn; break;
                                        case EpsKohiRecKbn.Kohi2: odrInf.Kohi2Futan = ftnKbn; break;
                                        case EpsKohiRecKbn.Kohi3: odrInf.Kohi3Futan = ftnKbn; break;
                                        case EpsKohiRecKbn.KohiSp: odrInf.KohiSpFutan = ftnKbn; break;
                                        default: break;
                                    }
                                }
                            }
                        }

                        futans.Add((odrInf.HokenPid, odrInf.Kohi1Futan, odrInf.Kohi2Futan, odrInf.Kohi3Futan, odrInf.KohiSpFutan));
                    }
                }
            }
        }
        #endregion

        // ソート
        filteredOdrInfs =
            filteredOdrInfs
                .OrderBy(p => p.KohiSortKey)
                .ThenBy(p => p.SortNo)
                .ThenBy(p => p.OdrKouiKbn)
                .ThenBy(p => p.RpNo)
                .ThenBy(p => p.RpEdaNo)
                .ToList();

        // 処方箋のバージョン
        string version = EpsCsvVersion.SJ1;
        #region CSVデータ作成
        CoOutDrugEpsCsvData epsCsvData = new CoOutDrugEpsCsvData(inputData.HpId, inputData.FileType, version, inputData.SinDate, hpInf, raiinInf, ptInf, ptHoken, filteredPtKohis, kaMsts, refill.count, rxLimit, _systemConfig);

        if (inputData.FileType != (sbyte)EpsEnum.EpsCsvType.Unfinished)
        {
            //確定前処方箋情報以外の場合
            #region 麻薬
            epsCsvData.CSV060 = null;
            if (filteredOdrInfDtls.Any(p => p.MadokuKbn == 1))
            {
                // 麻薬
                epsCsvData.CSV060 = new CoOutDrugCSV060(version, raiinInf.MayakuLicenseNo, ptInf.HomeAddress1 + ptInf.HomeAddress2, ptInf.Tel1 ?? ptInf.Tel2);
            }
            #endregion

            #region 残薬
            epsCsvData.CSV062 = null;
            if (filteredOdrInfDtls.Any(p => p.ItemCd == @"@ZANGIGI"))
            {
                // 疑義照会
                epsCsvData.CSV062 = new CoOutDrugCSV062(version, 1);
            }
            else if (filteredOdrInfDtls.Any(p => p.ItemCd == @"@ZANTEIKYO"))
            {
                // 情報提供
                epsCsvData.CSV062 = new CoOutDrugCSV062(version, 2);
            }
            #endregion

            #region 備考
            epsCsvData.CSV081 = new CoOutDrugCSV081(version);

            List<string> bikos = new List<string>();
            string biko = "";

            #region 処方箋備考欄
            foreach (CoOdrInfModel odrInf in filteredOdrInfs)
            {
                List<CoOdrInfDetailModel> BikoOdrInfDtls =
                    odrInfDtls.FindAll(p =>
                        p.RpNo == odrInf.RpNo && p.RpEdaNo == odrInf.RpEdaNo && p.BikoComment == 1 && p.ItemCd != ItemCdConst.Con_Refill);

                if (BikoOdrInfDtls.Any() == false) continue;

                foreach (CoOdrInfDetailModel odrDtl in BikoOdrInfDtls)
                {
                    bikos.Add(odrDtl.ItemName);
                }
            }
            #endregion

            #region 公費
            int kohiIndex = 4;
            foreach (var filteredPtKohi in filteredPtKohis)
            {
                if (filteredPtKohi.RecKbn == EpsKohiRecKbn.Biko)
                {
                    bikos.Add(
                    $"公費{CIUtil.ToWide(kohiIndex++.ToString())}　" +
                    $"負担者番号：{filteredPtKohi.EpsFutansyaNo}　" +
                    $"受給者番号：{filteredPtKohi.EpsJyukyusyaNo}"
                    );
                }
            }
            #endregion

            #region ニコチン
            if (filteredOdrInfDtls.Any(p => ItemCdConst.nicotineruls.Contains(p.ItemCd)))
            {
                biko = OutDrugUtil.AppendStr(biko, "ニコチン依存症管理料の算定に伴う処方");
            }
            #endregion

            #region 地域包括
            if (_systemConfig.SyohosenChiikiHoukatu(inputData.HpId) == 1)
            {
                #region sub method
                void _addBikoSanteiOdrItem(List<string> itemCds, string kigo)
                {
                    bool santei = false;
                    santei = _finder.CheckSanteiTerm(inputData.HpId, inputData.PtId, inputData.SinDate / 100 * 100 + 1, inputData.SinDate, itemCds);
                    if (santei == false)
                    {
                        santei = _finder.CheckOdrTerm(inputData.HpId, inputData.PtId, inputData.SinDate, inputData.SinDate, itemCds);
                    }

                    if (santei)
                    {
                        biko = OutDrugUtil.AppendStr(biko, kigo);
                    }
                }
                void _addBikoOdrItem(List<string> itemCds, string kigo)
                {
                    bool santei = false;

                    santei = _finder.CheckOdrTerm(inputData.HpId, inputData.PtId, inputData.SinDate, inputData.SinDate, itemCds);

                    if (santei)
                    {
                        biko = OutDrugUtil.AppendStr(biko, kigo);
                    }
                }
                #endregion

                _addBikoSanteiOdrItem(new List<string> { ItemCdConst.IgakuTiikiHoukatu1 }, "地域包括診療料１算定");
                _addBikoSanteiOdrItem(new List<string> { ItemCdConst.IgakuTiikiHoukatu2 }, "地域包括診療料２算定");
                _addBikoSanteiOdrItem(new List<string> { ItemCdConst.IgakuNintiTiikiHoukatu1 }, "認知症地域包括診療料１算定");
                _addBikoSanteiOdrItem(new List<string> { ItemCdConst.IgakuNintiTiikiHoukatu2 }, "認知症地域包括診療料２算定");

                _addBikoOdrItem(new List<string> { ItemCdConst.SaisinTiikiHoukatu1 }, "地域包括診療料加算１算定");
                _addBikoOdrItem(new List<string> { ItemCdConst.SaisinTiikiHoukatu2 }, "地域包括診療料加算２算定");
                _addBikoOdrItem(new List<string> { ItemCdConst.SaisinNintiTiikiHoukatu1 }, "認知症地域包括診療料加算１算定");
                _addBikoOdrItem(new List<string> { ItemCdConst.SaisinNintiTiikiHoukatu2 }, "認知症地域包括診療料加算２算定");
            }
            #endregion

            #region 情報通信
            if (_finder.CheckOdrRaiin(inputData.HpId, inputData.PtId, inputData.RaiinNo, new List<string> { ItemCdConst.Con_Jouhou }))
            {
                biko = OutDrugUtil.AppendStr(biko, "情報通信");
            }
            #endregion

            #region 高齢者
            if (ptHoken != null && (CIUtil.AgeChk(ptInf.Birthday, inputData.SinDate, 70) || ptHoken.IsKouki) && ptHoken.HokenSbtKbn == HokenSbtKbn.Hoken)
            {
                int hokenRate = GetHokenRate(ptInf, inputData.SinDate, ptHoken.Rate, ptHoken.HokenSbtKbn, ptHoken.KogakuKbn, ptHoken.Houbetu);

                if (hokenRate >= 30)
                {
                    biko = OutDrugUtil.AppendStr(biko, "（高７）");
                }
                else
                {
                    if (ptHoken.IsKouki && inputData.SinDate >= KaiseiDate.d20221001)
                    {
                        if (ptHoken.KogakuKbn == 41)
                        {
                            biko = OutDrugUtil.AppendStr(biko, "（高８）");
                        }
                        else
                        {
                            biko = OutDrugUtil.AppendStr(biko, "（高９）");
                        }
                    }
                    else
                    {
                        biko = OutDrugUtil.AppendStr(biko, "（高一）");
                    }
                }
            }
            #endregion

            #region 未就学
            if (CIUtil.IsStudent(ptInf.Birthday, inputData.SinDate) == false)
            {
                biko = OutDrugUtil.AppendStr(biko, "（６歳就学前）");
            }
            #endregion

            #region マル長
            int marucyo = _finder.ExistMarucyo(inputData.HpId, inputData.PtId, inputData.SinDate, mainHokenId);

            if ((ptKohis.Any(p => p.HokenSbtKbn == 2 && p.HokenEdaNo == 0)) || (marucyo == 1))
            {
                biko = OutDrugUtil.AppendStr(biko, "（長）");
            }
            else if ((ptKohis.Any(p => p.HokenSbtKbn == 2 && p.HokenEdaNo == 1)) || (marucyo == 2))
            {
                biko = OutDrugUtil.AppendStr(biko, "（長２）");
            }
            #endregion

            #region 都道府県別処理
            string prefBiko = epsCsvData.CSV081.GetPrefBiko(hpInf.PrefNo, ptKohis);
            biko = OutDrugUtil.AppendStr(biko, prefBiko);
            #endregion

            if (biko != "")
            {
                bikos.Add(biko);
            }

            // 備考レコード生成（1レコード150byteまで）
            foreach (string bikoLine in bikos)
            {
                string line = bikoLine;
                while (line != "")
                {
                    string tmp = line;
                    tmp = OutDrugUtil.GetUtf8ByteSubString(line, 150);

                    epsCsvData.CSV081.Add(tmp);
                    line = line.Substring(tmp.Length);
                }
            }

            #endregion

            #region 提供診療情報
            epsCsvData.CSV301 = null;
            if (epsChks.Any(e => e.CheckResult == 1))
            {
                epsCsvData.CSV301 = new CoOutDrugCSV301(version);
                // 重複投薬等チェックの処方箋全体に対するコメント
                epsCsvData.CSV301.Add("", "重複投薬等チェック結果を確認済みです。");
            }
            #endregion
        }
        epsCsvData.CSVRps = new List<CoOutDrugCSVRp>();

        int rpNo = 1;
        foreach (CoOdrInfModel odrInf in filteredOdrInfs.FindAll(p => p.OdrKouiKbn != 100 && p.OdrKouiKbn != 101))
        {
            // Detail絞り込み
            filteredOdrInfDtls = odrInfDtls.FindAll(p => p.RpNo == odrInf.RpNo && p.RpEdaNo == odrInf.RpEdaNo && p.BikoComment == 0);

            // Detailがない場合は次へ
            if (filteredOdrInfDtls.Any() == false) continue;

            // CSVデータ初期化
            CoOutDrugCSV101 CSV101 = null;

            // 剤型区分
            int zaikeiKbn = GetZaikeiKbn(odrInf.OdrKouiKbn);
            // 総調剤数量
            int totalSuryo = 1;
            // 調剤数量
            int cyozaiSuryo = 1;

            totalSuryo = odrInf.DaysCnt;
            cyozaiSuryo = odrInf.DaysCnt;

            CSV101 = new CoOutDrugCSV101(version, rpNo, zaikeiKbn, totalSuryo);

            filteredOdrInfDtls = odrInfDtls.FindAll(p => p.RpNo == odrInf.RpNo && p.RpEdaNo == odrInf.RpEdaNo && p.BikoComment == 0);

            List<int> yohoRowNos = new List<int> { };

            // 用法
            CoOutDrugCSV111 CSV111 = new CoOutDrugCSV111(version, rpNo, "　", YohoCdConst.Dummy);

            // 用法補足
            CoOutDrugCSV181 CSV181 = new CoOutDrugCSV181(version, rpNo);

            double[] fukuyos = new double[5];
            bool isYoho = true;

            foreach (CoOdrInfDetailModel odrDtl in filteredOdrInfDtls)
            {
                if (odrDtl.ItemCd == ItemCdConst.ChusyaJikocyu)
                {
                    // 「自己注射」手技項目は印字しない
                    continue;
                }
                else if (odrDtl.ItemCd == ItemCdConst.Con_Refill)
                {
                    // リフィル処方は印字しない
                    continue;
                }
                else if ((odrDtl.MasterSbt == "Y" && odrDtl.YohoKbn == 0) || odrDtl.IsTokuzai)
                {
                    // 薬とそのコメントは後で薬品情報グループに記録する
                    isYoho = false;
                    continue;
                }
                else if (odrDtl.YohoKbn == 1)
                {
                    // 用法
                    CSV111.YohoName = odrDtl.YohoName;
                    CSV111.YohoCd = odrDtl.YohoCd;
                    CSV111.OneDayKaisu = GetOneDayKaisu(odrDtl.YohoName);

                    // 用法補足の自動発生
                    if (!string.IsNullOrEmpty(odrDtl.ItemCd.Trim()))
                    {
                        var yohoHosokus = _finder.FindYohoHosoku(inputData.HpId, odrDtl.ItemCd, odrDtl.SinDate);
                        foreach (var yohoHosoku in yohoHosokus)
                        {
                            CSV181.Add(yohoHosoku.YohoHosokuKbn, yohoHosoku.Hosoku);

                            if (CSV111.OneDayKaisu <= 0)
                            {
                                CSV111.OneDayKaisu = GetOneDayKaisu(yohoHosoku.Hosoku);
                            }
                        }
                    }

                    if (odrDtl.IsFukinto)
                    {
                        // 不均等は薬品情報グループで記録するので情報をセットしておく
                        double[] fukuyoTimings = {odrDtl.TenMst.FukuyoRise,
                                    odrDtl.TenMst.FukuyoMorning,
                                    odrDtl.TenMst.FukuyoDaytime,
                                    odrDtl.TenMst.FukuyoNight,
                                    odrDtl.TenMst.FukuyoSleep};

                        int j = 0;
                        for (int i = 0; i < fukuyoTimings.Length; i++)
                        {
                            if (fukuyoTimings[i] > 0)
                            {
                                fukuyos[j++] = fukuyoTimings[i];
                            }
                        }
                    }

                    yohoRowNos.Add(odrDtl.RowNo);
                    isYoho = true;
                }
                else if ((odrDtl.YohoKbn == 2) || isYoho)
                {
                    // 補助用法、コメント
                    string comment = CIUtil.ToWide(odrDtl.ItemName);

                    int yohoHosokuKbn = 0;
                    if (odrDtl.YohoKbn == 2) yohoHosokuKbn = odrDtl.YohoHosokuKbn;
                    CSV181.Add(yohoHosokuKbn, comment);

                    if (CSV111.OneDayKaisu <= 0)
                    {
                        CSV111.OneDayKaisu = GetOneDayKaisu(comment);
                    }

                    yohoRowNos.Add(odrDtl.RowNo);
                    isYoho = true;
                }
            }

            // 薬品情報グループ

            // 薬剤情報
            List<CoOutDrugCSVDrug> CSVDrugs = new List<CoOutDrugCSVDrug>();
            CoOutDrugCSV201 CSV201 = null;
            CoOutDrugCSV211 CSV211 = null;
            CoOutDrugCSV221 CSV221 = null;
            CoOutDrugCSV231 CSV231 = null;
            CoOutDrugCSV241 CSV241 = null;
            CoOutDrugCSV281 CSV281 = null;

            int seqNo = 0;

            foreach (CoOdrInfDetailModel odrDtl in filteredOdrInfDtls)
            {

                if (odrDtl.ItemCd == ItemCdConst.ChusyaJikocyu)
                {
                    // 「自己注射」手技項目は印字しない
                    continue;
                }
                else if (odrDtl.ItemCd == ItemCdConst.Con_Refill)
                {
                    // リフィル処方は印字しない
                    continue;
                }
                else if (yohoRowNos.Contains(odrDtl.RowNo))
                {
                    // 用法は記録済み
                    continue;
                }
                else if ((odrDtl.MasterSbt == "Y" && odrDtl.YohoKbn == 0) || odrDtl.IsTokuzai)
                {
                    // 薬剤または特材
                    if (odrDtl.MasterSbt == "Y" && odrDtl.YohoKbn == 0
                        && (!odrDtl.SanteiItemCd.StartsWith("6") || odrDtl.SanteiItemCd.Length != 9 || odrDtl.SanteiItemCd == DrugCdConst.DrugDummy))
                    {
                        // 保険適用外の医薬品は印字しない
                        continue;
                    }
                    else if (odrDtl.IsTokuzai
                        && (!odrDtl.SanteiItemCd.StartsWith("7") || odrDtl.SanteiItemCd.Length != 9 || odrDtl.SanteiItemCd == DrugCdConst.ZairyoDummy))
                    {
                        // 保険適用外の医療材料は印字しない
                        continue;
                    }

                    if (CSV201 != null)
                    {
                        CSVDrugs.Add(new CoOutDrugCSVDrug(CSV201, CSV211, CSV221, CSV231, CSV241, CSV281));
                    }

                    // 初期化
                    seqNo++;
                    CSV201 = null;
                    CSV211 = null;
                    CSV221 = null;
                    CSV231 = null;
                    CSV241 = null;
                    CSV281 = new CoOutDrugCSV281(version, rpNo, seqNo);

                    int infKbn = 0;
                    int cdSbt = 0;
                    string itemCd = "";
                    string drugName = "";
                    double yoryo = 0;
                    int rikika = 0;
                    string unitName = "";

                    // 提供診療情報レコード
                    if (epsCsvData.CSV301 != null)
                    {
                        List<string> chkCmts = new List<string>();
                        chkCmts = epsChks.Where(e =>
                          (
                          (e.TargetPharmaceuticalCodeType == DrugCdSbt.RezedenCd.ToString()
                          && e.TargetPharmaceuticalCode == odrDtl.SanteiItemCd)
                          || (e.TargetPharmaceuticalCodeType == DrugCdSbt.IpnNameCd.ToString()
                          && e.TargetPharmaceuticalCode == odrDtl.IpnCd + "ZZZ")
                          )
                          && e.Comment != null)
                          .Select(e => e.Comment)
                          .ToList();

                        foreach (string chkCmt in chkCmts)
                        {
                            // 重複投薬等チェックの各薬品に対するコメント
                            epsCsvData.CSV301.Add(odrDtl.SyohoKbn == 3 ? odrDtl.IpnName : odrDtl.ItemName, chkCmt);
                        }
                    }

                    // 公費負担
                    if (epsCsvData.IsHeiyo)
                    {
                        CSV231 = new CoOutDrugCSV231(
                            version,
                            rpNo, seqNo,
                            odrInf.Kohi1Futan > 0 ? 1 : 0,
                            odrInf.Kohi2Futan > 0 ? 1 : 0,
                            odrInf.Kohi3Futan > 0 ? 1 : 0,
                            odrInf.KohiSpFutan > 0 ? 1 : 0);
                    }

                    if (odrDtl.MasterSbt == "Y" && odrDtl.YohoKbn == 0)
                    {
                        // 薬剤
                        infKbn = DrugInfKbn.Drug;

                        if (odrDtl.SyohoKbn != 3)
                        {
                            //一般名処方以外
                            cdSbt = DrugCdSbt.RezedenCd;
                            itemCd = odrDtl.SanteiItemCd;
                            drugName = odrDtl.ReceName;
                        }
                        else
                        {
                            //一般名処方
                            cdSbt = DrugCdSbt.IpnNameCd;
                            itemCd = odrDtl.IpnCd + "ZZZ";
                            drugName = odrDtl.IpnName;

                            // 2025/5/12より一般名処方マスタにない一般名も登録可能になった
                            if (CIUtil.DateTimeToInt(DateTime.Now) < 20250512)
                            {
                                if (string.IsNullOrEmpty(odrDtl.IpnName) || !_finder.ExistsIpnKasanMst(odrDtl.IpnCd, inputData.SinDate)
                                || _finder.IsExcludedIpnName(odrDtl.IpnCd, inputData.SinDate))
                                {
                                    //一般名処方マスタにない一般名はオーダーした薬を記録する（2024/12～ダミーコード使用禁止対応）
                                    cdSbt = DrugCdSbt.RezedenCd;
                                    itemCd = odrDtl.SanteiItemCd;
                                    drugName = odrDtl.ReceName;
                                    //一般名処方マスタにない一般名はダミーコードを使用する
                                    //cdSbt = DrugCdSbt.RezedenCd;
                                    //itemCd = DrugCdConst.DrugDummy;

                                    //if (string.IsNullOrEmpty(odrDtl.IpnName))
                                    //{
                                    //    drugName = string.IsNullOrEmpty(odrDtl.ReceName) ? odrDtl.ItemName : odrDtl.ReceName;
                                    //    drugName = $"一般名登録なし（{drugName}）";
                                    //}
                                }
                            }
                        }

                        yoryo = odrDtl.Suryo;
                        rikika = 1;

                        if (_systemConfig.SyohosenTani(inputData.HpId) == 1)
                        {
                            // レセ単位で出力する場合
                            if (odrDtl.TermVal > 0)
                            {
                                // 単位を変換
                                yoryo = odrDtl.Suryo * odrDtl.TermVal;
                            }

                            unitName = odrDtl.TenMst.ReceUnitName;
                        }
                        else
                        {
                            unitName = odrDtl.UnitName;

                            if (odrDtl.TermVal != 0 && odrDtl.TermVal != 1)
                            {
                                // 単位変換レコード
                                CSV211 = new CoOutDrugCSV211(version, rpNo, seqNo, odrDtl.TermVal);

                                // 力価単位？
                                CoDosageDrugModel dosageDrug = _finder.GetDosageDrugModel(inputData.HpId, odrDtl.TenMst?.YjCd ?? "");
                                if (dosageDrug != null)
                                {
                                    if (CIUtil.ToWide(unitName) == CIUtil.ToWide(dosageDrug.RikikaUnit))
                                    {
                                        // 力価
                                        rikika = 2;
                                    }
                                }
                            }
                        }



                        CSV201 = new CoOutDrugCSV201(version, rpNo, seqNo, infKbn, cdSbt, itemCd, drugName, yoryo, rikika, unitName);

                        bool isSingleDosage = _finder.IsSingleDosageUnit(inputData.HpId, unitName);

                        if (isSingleDosage)
                        {
                            // 1回量剤形登録あり
                            if (fukuyos.Any(f => f > 0))
                            {
                                // 不均等レコード
                                CSV221 = new CoOutDrugCSV221(version, rpNo, seqNo, fukuyos, yoryo);
                            }
                            else if (CSV111.OneDayKaisu > 0)
                            {
                                // １日○回
                                // １回服用量レコード
                                CSV241 = new CoOutDrugCSV241(version, rpNo, seqNo, yoryo, CSV111.OneDayKaisu);
                            }
                        }

                        // 一般名処方、後発品変更可否チェック
                        int yakuhinHosokuKbn = 0;
                        string yakuhinHosokuInf = "";
                        int yakuhinHosokuKbn2 = 0;
                        string yakuhinHosokuInf2 = ""; ;

                        if (odrDtl.SyohoKbn == 1)
                        {
                            yakuhinHosokuKbn = 3;
                            yakuhinHosokuInf = "後発品変更不可";
                        }
                        else if (new int[] { 2, 3 }.Contains(odrDtl.SyohoKbn))
                        {
                            if (odrDtl.SyohoLimitKbn == 1)
                            {
                                yakuhinHosokuKbn = 4;
                                // 2024/10/1を機に「型」を「形」に変更
                                yakuhinHosokuInf = inputData.SinDate < KaiseiDate.d20241001 ? "剤型変更不可" : "剤形変更不可";

                            }
                            else if (odrDtl.SyohoLimitKbn == 2)
                            {
                                yakuhinHosokuKbn = 5;
                                yakuhinHosokuInf = "含量規格変更不可";

                            }
                            else if (odrDtl.SyohoLimitKbn == 3)
                            {
                                if (inputData.SinDate < KaiseiDate.d20241001)
                                {
                                    yakuhinHosokuKbn = 4;
                                    yakuhinHosokuInf = "剤型変更不可";
                                    yakuhinHosokuKbn2 = 5;
                                    yakuhinHosokuInf2 = "含量規格変更不可";
                                }
                                else
                                {
                                    // 2024/10/1を機に1レコードにまとめ、「型」を「形」に変更
                                    yakuhinHosokuKbn = 6;
                                    yakuhinHosokuInf = "剤形変更不可及び含量規格変更不可";
                                }
                            }
                        }
                        if (odrDtl.SyohoKbn == 4 &&  inputData.SinDate >= KaiseiDate.d20241001)
                        {
                            yakuhinHosokuKbn = 8;
                            yakuhinHosokuInf = "先発医薬品患者希望";
                        }

                        if (yakuhinHosokuKbn > 0)
                        {
                            CSV281.Add(yakuhinHosokuKbn, yakuhinHosokuInf);
                        }
                        if (yakuhinHosokuKbn2 > 0)
                        {
                            CSV281.Add(yakuhinHosokuKbn2, yakuhinHosokuInf2);
                        }

                    }
                    else if (odrDtl.IsTokuzai)
                    {
                        // 特材
                        infKbn = DrugInfKbn.Zairyo;
                        cdSbt = DrugCdSbt.RezedenCd;

                        itemCd = odrDtl.SanteiItemCd;
                        if (odrDtl.ItemCd != odrDtl.SanteiItemCd)
                        {
                            var santeiItem = _masterFinder.FindTenMst(inputData.HpId, odrDtl.SanteiItemCd, inputData.SinDate);
                            drugName = santeiItem?.ReceName;
                            //医療材料において、商品名を明記する場合には、「 薬品補足レコード」の「薬品補足情報」に記載する。
                            CSV281.Add(0, "商品名:" + odrDtl.ItemName);
                        }
                        else
                        {
                            drugName = odrDtl.ReceName;
                        }
                        yoryo = odrDtl.Suryo;
                        rikika = 1;
                        unitName = odrDtl.UnitName;
                        CSV201 = new CoOutDrugCSV201(version, rpNo, seqNo, infKbn, cdSbt, itemCd, drugName, yoryo, rikika, unitName);
                    }

                }
                else
                {
                    // 薬に対するコメント
                    string comment = CIUtil.ToWide(odrDtl.ItemName);
                    // 薬品補足レコード追加
                    CSV281.Add(0, comment);
                }
            }

            if (CSV201 != null)
            {
                CSVDrugs.Add(new CoOutDrugCSVDrug(CSV201, CSV211, CSV221, CSV231, CSV241, CSV281));
            }

            if (CSVDrugs.Count > 0)
            {
                epsCsvData.CSVRps.Add(new CoOutDrugCSVRp(CSV101, CSV111, CSV181, CSVDrugs));
                rpNo++;
            }

            #endregion
        }

        if (epsCsvData.CSVRps.Count > 0)
        {
            retData = epsCsvData.CSVData;
            Encoding enc = Encoding.GetEncoding("UTF-8");
            string encData = Convert.ToBase64String(enc.GetBytes(retData));
            // CSVのデータをログに出力

            string CSVLogOutputDirectory = $@"{AppDomain.CurrentDomain.BaseDirectory}\Logs\{DateTime.Now.ToString("yyyyMMdd")}";
            if (CIUtil.IsDirectoryExisting(CSVLogOutputDirectory) == false)
            {
                // ログファイル出力先が存在しない場合は作成
                Directory.CreateDirectory(CSVLogOutputDirectory);
            }
            if (CIUtil.IsDirectoryExisting(CSVLogOutputDirectory))
            {
                string outPath = $@"{CSVLogOutputDirectory}\outdrug_eps{inputData.FileType}_{DateTime.Now.ToString("yyyyMMdd_HHmmssfff")}_{inputData.PtId}_{inputData.SinDate}_{inputData.RaiinNo}.CSV";
                File.WriteAllText(outPath, retData, Encoding.GetEncoding("UTF-8"));
            }
        }

        return retData;
    }

    /// <summary>
    /// 今回オーダー情報を院外処方箋用のオーダー情報に変換
    /// </summary>
    /// <param name="todayOdrInfs">今回オーダー情報</param>
    /// <returns>院外処方箋用のオーダー情報</returns>
    private (List<CoOdrInfModel>, List<CoOdrInfDetailModel>) ConvertTodayOdrToOutDrugOdr(List<OrdInfModel> todayOdrInfs, int hpId, long ptId, int sinDate)
    {
        List<CoOdrInfModel> retOdrInf = new List<CoOdrInfModel> { };
        List<CoOdrInfDetailModel> retOdrInfDetail = new List<CoOdrInfDetailModel> { };

        foreach (var todayOdrInf in todayOdrInfs)
        {
            var rowNo = 1;
            var hokenPattern = _accountingRepository.FindPtHokenPatternById(hpId, ptId, sinDate, todayOdrInf.HokenPid, 0, false);
            CoOdrInfModel odrInf = new CoOdrInfModel(Mapper.Map(todayOdrInf, new OdrInf()), Mapper.Map(hokenPattern, new PtHokenPattern()));
            retOdrInf.Add(odrInf);

            foreach (var OdrInfDetailModel in todayOdrInf.OrdInfDetails)
            {
                OdrInfDetailModel.RowNo = rowNo++;
                if (OdrInfDetailModel != null)
                {
                    var tenMst = _masterFinder.FindTenMst(hpId, OdrInfDetailModel.ItemCd, sinDate);
                    if (tenMst != null && tenMst?.TenMst != null && !string.IsNullOrEmpty(tenMst?.TenMst.ItemCd))
                    {
                        var yohoMst = _finder.FindYohoMst(hpId, tenMst.TenMst.YohoCd ?? string.Empty, sinDate);
                        CoOdrInfDetailModel odrInfDetail = new CoOdrInfDetailModel(
                            Mapper.Map(OdrInfDetailModel, new OdrInfDetail()),
                            Mapper.Map(todayOdrInf, new OdrInf()),
                            tenMst?.TenMst,
                            Mapper.Map(hokenPattern, new PtHokenPattern()),
                            yohoMst?.YohoMst);
                        retOdrInfDetail.Add(odrInfDetail);
                    }
                }
            }
        }

        return (retOdrInf, retOdrInfDetail);
    }

    /// <summary>
    /// 主保険負担率計算
    /// </summary>
    /// <param name="ptInf">患者情報</param> 
    /// <param name="sinDate">診療日</param> 
    /// <param name="futanRate">負担率</param>
    /// <param name="hokenSbtKbn">保険種別区分</param>
    /// <param name="kogakuKbn">高額療養費区分</param>
    /// <param name="honkeKbn">本人家族区分</param>
    /// <param name="houbetu">法別番号</param>
    /// <param name="receSbt">レセプト種別</param>
    /// <returns></returns>
    private int GetHokenRate(CoPtInfModel ptInf, int sinDate, int futanRate, int hokenSbtKbn, int kogakuKbn, string houbetu)
    {
        int wrkRate = futanRate;

        switch (hokenSbtKbn)
        {
            case 0:
                //主保険なし
                break;
            case 1:
                //主保険
                if (ptInf.IsPreSchool())
                {
                    //６歳未満未就学児
                    wrkRate = 20;
                }
                else if (ptInf.IsElder() && houbetu != "39")
                {
                    wrkRate =
                        ptInf.IsElder20per() ? wrkRate = 20 :  //前期高齢
                        ptInf.IsElderExpat() ? wrkRate = 20 :  //75歳以上海外居住者
                        wrkRate = 10;
                }

                if (ptInf.IsElder() || houbetu == "39")
                {
                    if ((kogakuKbn == 3 && sinDate < KaiseiDate.d20180801) ||
                        (new int[] { 26, 27, 28 }.Contains(kogakuKbn) && sinDate >= KaiseiDate.d20180801))
                    {
                        //後期７割 or 高齢７割
                        wrkRate = 30;
                    }
                    else if (houbetu == "39" && kogakuKbn == 41 &&
                        sinDate >= KaiseiDate.d20221001)
                    {
                        //後期８割
                        wrkRate = 20;
                    }
                }
                break;
            default:
                break;
        }

        return wrkRate;
    }


    /// <summary>
    /// 剤型区分の取得
    /// </summary>
    /// <param name="odrKouiKbn">オーダー行為区分</param>
    /// <returns>剤型区分</returns>
    private int GetZaikeiKbn(int odrKouiKbn)
    {
        int ret = 0;
        if (odrKouiKbn == 21)
        {
            // 内服
            ret = 1;
        }
        else if (odrKouiKbn == 22)
        {
            // 頓服
            ret = 2;
        }
        else if (odrKouiKbn == 23)
        {
            // 外用
            ret = 3;
        }
        else if (odrKouiKbn == 28 || (odrKouiKbn >= 30 && odrKouiKbn <= 39))
        {
            // 注射
            ret = 5;
        }
        else
        {
            // その他
            ret = 9;
        }

        return ret;
    }

    /// <summary>
    /// １日回数の取得
    /// </summary>
    /// <param name="name">用法名称</param>
    /// <returns>１日回数</returns>
    private int GetOneDayKaisu(string name)
    {
        int ret = 0;

        const string conOneDay = "１日";
        const string conKai = "回";

        name = CIUtil.ToWide(name);
        int posOneDay = name.IndexOf(conOneDay);
        if (posOneDay >= 0)
        {
            int startPoint = posOneDay + conOneDay.Length;
            int endPoint = name.IndexOf(conKai, startPoint);

            if (endPoint > startPoint)
            {
                int length = endPoint - startPoint;
                string kaisu = name.Substring(startPoint, length);
                kaisu = CIUtil.ToNarrow(kaisu);
                ret = CIUtil.StrToIntDef(kaisu, 0);
            }

        }

        return ret;
    }
}