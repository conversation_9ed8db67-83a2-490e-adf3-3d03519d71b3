﻿using Helper.Exceptions;
using Interactor.CommonChecker.CommonMedicalCheck;
using UseCase.CommonChecker;

namespace Interactor.CommonChecker
{
    public class CommonCheckerInteractor : IGetOrderCheckerInputPort
    {
        private readonly ICommonMedicalCheck _commonMedicalCheck;

        public CommonCheckerInteractor(ICommonMedicalCheck commonMedicalCheck)
        {
            _commonMedicalCheck = commonMedicalCheck;
        }

        public GetOrderCheckerOutputData Handle(GetOrderCheckerInputData inputData)
        {
            try
            {
                var checkedResult = _commonMedicalCheck.CheckListOrder(inputData.HpId, inputData.PtId, inputData.SinDay, inputData.CurrentListOdr, inputData.ListCheckingOrder, inputData.SpecialNoteItem, inputData.PtDiseaseModels, inputData.FamilyItems, inputData.IsDataOfDb, inputData.RealTimeCheckerCondition);

                if (checkedResult == null || checkedResult.Count == 0)
                {
                    return new GetOrderCheckerOutputData(new(), string.Empty, string.Empty, string.Empty, string.Empty, GetOrderCheckerStatus.Successed);
                }
                else
                {
                    var result = _commonMedicalCheck.GetErrorDetails(inputData.HpId, inputData.PtId, inputData.SinDay, checkedResult, inputData.SpecialNoteItem);
                    return new GetOrderCheckerOutputData(result.errors ?? new(), result.weightInfo, result.weightDateInfo, result.heightInfo, result.heightDateInfo, GetOrderCheckerStatus.Error);
                }
            }
            catch (Exception ex)
            {
                string name = GetType().Namespace ?? string.Empty;
                throw new InteractorCustomException(name, ex);
            }
            finally
            {
                _commonMedicalCheck.ReleaseResource();
            }
        }
    }
}
