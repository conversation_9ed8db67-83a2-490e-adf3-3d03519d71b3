﻿using Domain.Models.Insurance;
using Domain.Models.Online;
using Domain.Models.Online.OQSmuhvq02res;
using Domain.Models.PatientInfor;
using Helper.Common;
using Helper.Constants;
using Helper.Extension;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using UseCase.Online.ProcessXMLOQS;

namespace Interactor.Online;

public class ProcessXmlOQSmuhvq02resInteractor : IProcessXmlOQSmuhvq02resInputPort
{
    private readonly IOnlineRepository _onlineRepository;
    private readonly IPatientInforRepository _patientInforRepository;
    private readonly IInsuranceRepository _insuranceRepository;

    public ProcessXmlOQSmuhvq02resInteractor(IOnlineRepository onlineRepository,
        IPatientInforRepository patientInforRepository,
        IInsuranceRepository insuranceRepository)
    {
        _onlineRepository = onlineRepository;
        _patientInforRepository = patientInforRepository;
        _insuranceRepository = insuranceRepository;
    }

    public ProcessXmlOQSmuhvq02resOutputData Handle(ProcessXmlOQSmuhvq02resInputData inputData)
    {
        var responseStatus = ProcessXMLStatus.Successed;
        var hpId = inputData.HpId;
        var receptionNo = inputData.ReceptionNo;
        var xml = XmlExtensions.Deserialize<OQSmuhvq02Response>(inputData.XmlString);
        if (xml == null || xml.MessageHeader.ReceptionNumber != receptionNo)
        {
            return new(ProcessXMLStatus.InvalidReceptionNo);
        }

        var onlConfirmModel = new QualificationInfModel(xml.MessageHeader.ReceptionNumber,
            xml.MessageHeader.SegmentOfResult,
            xml.MessageHeader.ErrorMessage,
            xml.MessageHeader.ErrorCode
        );

        var processExecutionTime = CIUtil.DateTimeToInt(ParseDateTime(xml.MessageHeader.ProcessExecutionTime, setKindUtc: false));

        var bulkConfirmUnits = xml.MessageBody.BulkConfirmUnits;
        switch (xml.MessageHeader.SegmentOfResult)
        {
            case XmlConstant.SegmentOfResultStatus.Processing:
            case XmlConstant.SegmentOfResultStatus.AbnormalCompletion:
                var result = _onlineRepository.UpdateOnlineConfirmation(
                    inputData.UserId,
                    onlConfirmModel,
                    hpId
                ) ? ProcessXMLStatus.Successed : ProcessXMLStatus.Failed;

                return new(result);
            case XmlConstant.SegmentOfResultStatus.NormalCompletion:
                List<PatientInforModel> patientInfModels = new();
                List<OnlineConfirmationCompositeModel> compositeModels = new();
                if (bulkConfirmUnits == null || bulkConfirmUnits.Length == 0) return new(ProcessXMLStatus.EmptyBulkConfirmUnit);
                foreach (var confirmUnit in bulkConfirmUnits)
                {
                    var refNo = confirmUnit.ReferenceNumber;
                    var resultConfirms = confirmUnit.ResultList.ResultOfQualificationConfirmations?.ToList();
                    var compositeModel = new OnlineConfirmationCompositeModel();
                    if (string.IsNullOrEmpty(refNo) || refNo.AsLong() == 0 || resultConfirms == null || resultConfirms.Count == 0)
                    {
                        if (resultConfirms?.Count > 0)
                        {
                            resultConfirms.ForEach(x =>
                            {
                                compositeModel.OnlineConfirmationHistory = null;
                                compositeModel.OnlineConfirmationDetails.Add(new(
                                    receptionNo: receptionNo,
                                    message: string.Empty,
                                    confirmationResult: TransformXml(xml, confirmUnit, x),
                                    ptId: 0,
                                    status: 0
                                ));
                            });
                        }
                        else
                        {
                            compositeModel.OnlineConfirmationHistory = null;
                            compositeModel.OnlineConfirmationDetails.Add(new(
                                receptionNo: receptionNo,
                                message: string.Empty,
                                confirmationResult: TransformXml(xml, confirmUnit),
                                ptId: 0,
                                status: 0
                            ));
                        }

                        compositeModels.Add(compositeModel);

                        continue;
                    }

                    var firstResultConfirm = resultConfirms[0];
                    var sex2 = firstResultConfirm.Sex2;
                    var sex = !string.IsNullOrWhiteSpace(sex2) ? sex2 : firstResultConfirm.Sex1;
                    var ptInfo = _patientInforRepository.GetPtInfByRefNo(hpId, refNo.AsLong());
                    var processTime = ParseDateTime(xml.MessageHeader.ProcessExecutionTime, "yyyyMMddHHmmss", false);
                    CIUtil.ConvertJapanTimeToUtc(ref processTime);
                    var isMatchedInf = ptInfo.Sex.ToString() == sex && ptInfo.Birthday.ToString() == firstResultConfirm.Birthdate && ptInfo.PtId > 0;

                    compositeModel.OnlineConfirmationHistory = new(
                        id: 0,
                        ptId: ptInfo.PtId,
                        confirmationType: 2,
                        infoConsFlg: GetConsFlag(resultConfirms),
                        confirmationResult: TransformXml(xml, confirmUnit),
                        prescriptionIssueType: 0,
                        uketukeStatus: 0,
                        onlineConfirmationDate: CIUtil.GetJapanDateTimeNow(),
                        hpid: hpId
                    );

                    if (isMatchedInf)
                    {
                        patientInfModels.Add(ptInfo);
                        var consDate = ParseDateTime(confirmUnit.QualificationConsTime, "yyyyMMddHHmmss", false);
                        var limitDate = ParseDateTime(confirmUnit.QualificationAvailableTime, "yyyyMMddHHmmss", false);
                        CIUtil.ConvertJapanTimeToUtc(ref consDate);
                        CIUtil.ConvertJapanTimeToUtc(ref limitDate);
                        compositeModel.AgreedPrescriptions.Add(new(
                            ptId: ptInfo.PtId,
                            consType: 0,
                            consDate: consDate.ToUniversalTime(),
                            limitDate: limitDate.ToUniversalTime(),
                            prescriptionIssueType: confirmUnit.PrescriptionIssueSelect.AsInteger()
                        ));
                    }

                    foreach (var resultConfirmation in resultConfirms)
                    {
                        var xmlTransformed = TransformXml(xml, confirmUnit, resultConfirmation);
                        if (isMatchedInf)
                        {
                            var message = GetCombinedInfoDifferenceMessage(ptInfo, resultConfirmation, inputData.SinDate, processExecutionTime);
                            compositeModel.OnlineConfirmationDetails.Add(new(
                                receptionNo: receptionNo,
                                message: message,
                                confirmationResult: xmlTransformed,
                                ptId: ptInfo.PtId,
                                status: 0
                            ));

                            compositeModel.AgreedConsents.AddRange(GetOnlineAgreedConsents(
                                ptInfo.PtId,
                                processTime.ToUniversalTime(),
                                resultConfirmation)
                            );
                        }
                        else
                        {
                            compositeModel.OnlineConfirmationHistory = null;
                            compositeModel.OnlineConfirmationDetails.Add(new(
                                receptionNo: receptionNo,
                                message: string.Empty,
                                confirmationResult: xmlTransformed,
                                ptId: 0,
                                status: 0
                            ));
                        }
                    }

                    compositeModels.Add(compositeModel);
                }

                _onlineRepository.UpdateOnlineConfirmation(
                    inputData.UserId,
                    onlConfirmModel,
                    hpId
                );

                if (compositeModels.Count > 0)
                {
                    _onlineRepository.SaveOnlineConfirmationComposite(inputData.UserId, hpId, compositeModels);
                }

                if (patientInfModels.Count > 0)
                {
                    _patientInforRepository.BulkUpdateHoumonAgreed(patientInfModels, hpId, houmonAgreed: 1);
                }

                break;
            default:
                responseStatus = ProcessXMLStatus.Failed;
                break;
        }

        return new(responseStatus);
    }

    private static bool HasBasicInfoDifference(PatientInforModel ptInfo, ResultOfQualificationConfirmation confirmResult)
    {
        var sex2 = confirmResult.Sex2;
        var sex = !string.IsNullOrWhiteSpace(sex2) ? sex2 : confirmResult.Sex1;
        return !(CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(ptInfo.Name)) == CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(confirmResult.Name))
                 && CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(ptInfo.KanaName)) == CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(confirmResult.NameKana))
                 && $"{ptInfo.Birthday}" == $"{confirmResult.Birthdate?.Trim()}"
                 && $"{ptInfo.Sex}" == $"{sex?.Trim()}"
                 && $"{ptInfo.HomePost.Trim()}" == $"{Regex.Replace(confirmResult.PostNumber?.Trim() ?? "", "[-‐‑‒–—―−]", "")}"
                 && $"{ptInfo.HomeAddress1?.Trim()}{ptInfo.HomeAddress2?.Trim()}" == $"{confirmResult.Address?.Trim()}"
                 && $"{ptInfo.Setanusi?.Trim()}" == $"{confirmResult.InsuredName?.Trim()}");
    }

    private bool HasInsuranceInfoDifference(PatientInforModel ptInfo, ResultOfQualificationConfirmation confirmResult, int sinDate, int processExecutionTime)
    {
        var currentPtBirthDay = CIUtil.DateTimeToInt(ParseDateTime(confirmResult.Birthdate, DateTimeFormat.yyyyMMdd, false));
        if (confirmResult.InsuredCardClassification != "A1")
        {
            var isMaruchoDifference = false;
            var diseasesCertificateInfos = confirmResult.SpecificDiseasesCertificateList?.SpecificDiseasesCertificateInfos;
            if (diseasesCertificateInfos?.Length > 0)
            {
                var diseasesCertificateInfo = diseasesCertificateInfos[0];
                var validStartDate = diseasesCertificateInfo.SpecificDiseasesValidStartDate;
                var validEndDate = diseasesCertificateInfo.SpecificDiseasesValidEndDate;
                var specificDiseasesSelfPay = diseasesCertificateInfo.SpecificDiseasesSelfPay;
                isMaruchoDifference = _insuranceRepository.HasMaruchoDifference(
                    ptId: ptInfo.PtId,
                    hpId: ptInfo.HpId,
                    prefNo: 0,
                    hokenNo: 102,
                    hokenEdaNo: specificDiseasesSelfPay == "10000" ? 0 : 1,
                    startDate: string.IsNullOrEmpty(validStartDate) ? null : validStartDate.AsInteger(),
                    endDate: string.IsNullOrEmpty(validEndDate) ? null : validEndDate.AsInteger()
                );
            }

            var currentPtAge = CIUtil.SDateToAge(currentPtBirthDay, processExecutionTime);

            var insuredCardExpirationDate = confirmResult.InsuredCardExpirationDate;
            int? endDate = !string.IsNullOrEmpty(insuredCardExpirationDate) ? insuredCardExpirationDate.AsInteger() : null;
            var isApplicable = confirmResult.InsuredCardClassification.AsInteger() == 5;
            var certificateRelatedInfo = confirmResult.LimitApplicationCertificateRelatedInfo;
            var flag = certificateRelatedInfo?.LimitApplicationCertificateClassificationFlag;
            var personalFamilyClassification = confirmResult.PersonalFamilyClassification?.Trim();
            string? kogakuValue = null;
            if (!string.IsNullOrEmpty(flag))
            {
                var kogakuKbn = !string.IsNullOrEmpty(flag) ? XmlExtensions.MapLimitApplicationCertificateFlag(flag, currentPtAge) : null;
                kogakuValue = _insuranceRepository.GetKogakuValue(
                   currentPtAge,
                   confirmResult.InsurerNumber?.Trim() ?? string.Empty,
                   sinDate,
                   kogakuKbn
               );
            }


            var hokensyaNo = confirmResult.InsurerNumber?.Trim();
            if (string.IsNullOrEmpty(hokensyaNo)) return false;

            return _insuranceRepository.GetHokenInf(
                hpId: ptInfo.HpId,
                ptId: ptInfo.PtId,
                hokensyaNo: confirmResult.InsurerNumber?.Trim() ?? string.Empty,
                kigo: confirmResult.InsuredCardSymbol?.Trim() ?? string.Empty,
                bango: confirmResult.InsuredIdentificationNumber?.Trim() ?? string.Empty,
                edaNo: confirmResult.InsuredBranchNumber?.Trim() ?? string.Empty,
                honkeKbn: !string.IsNullOrEmpty(personalFamilyClassification) ? personalFamilyClassification.AsInteger() : null,
                kofuDate: confirmResult.InsuredCertificateIssuanceDate.AsInteger(),
                startDate: confirmResult.InsuredCardValidDate.AsInteger(),
                endDate: endDate,
                age: currentPtAge,
                sinDate: sinDate,
                kogakuValue: kogakuValue,
                isApplicable
            ) == null || isMaruchoDifference;
        }

        var medicalTicketInfos = confirmResult.PublicExpenseResultList?.MedicalTicketInfos;
        var medicalTicketInfo = medicalTicketInfos?.FirstOrDefault(x => x.DesignatedMedicalInstitutionFlag == "0") ?? medicalTicketInfos?[0];
        var medicalTicketValidDate = medicalTicketInfo?.MedicalTicketValidDate.AsInteger();
        var medicalTicketExpirationDate = medicalTicketInfo?.MedicalTicketExpirationDate.AsInteger();
        var selfPayAmount = medicalTicketInfo?.SelfPayAmount.AsInteger();

        return _insuranceRepository.HasKohiInfoDifference(
            hpId: ptInfo.HpId,
            ptId: ptInfo.PtId,
            futansyaNo: confirmResult.InsurerNumber,
            jyukyusyaNo: confirmResult.InsuredIdentificationNumber,
            startDate: medicalTicketValidDate,
            endDate: medicalTicketExpirationDate,
            selfPayAmount: selfPayAmount,
            birthDay: currentPtBirthDay
        ).IsDifference;
    }

    public string GetCombinedInfoDifferenceMessage(PatientInforModel ptInfo, ResultOfQualificationConfirmation confirmResult, int sinDate, int processExecutionTime)
    {
        var basicDiff = HasBasicInfoDifference(ptInfo, confirmResult);
        var insuranceDiff = HasInsuranceInfoDifference(ptInfo, confirmResult, sinDate, processExecutionTime);

        return basicDiff switch
        {
            true when insuranceDiff => "[基本・保険差異あり]",
            true => "[基本差異あり]",
            _ => insuranceDiff ? "[保険差異あり]" : string.Empty
        };
    }

    private static string GetConsFlag(List<ResultOfQualificationConfirmation> confirmations)
    {
        if (confirmations.Count == 0) return string.Empty;

        const int flagLength = 4;
        var finalFlags = new char[flagLength];

        // Initialize with max possible values
        for (var i = 0; i < flagLength; i++)
        {
            finalFlags[i] = ' ';
        }

        foreach (var confirmation in confirmations)
        {
            string[] currentFlags =
            {
                Get(confirmation.PharmacistsInfoConsFlg),
                Get(confirmation.SpecificHealthCheckupsInfoConsFlg),
                Get(confirmation.DiagnosisInfoConsFlg),
                Get(confirmation.OperationInfoConsFlg)
            };

            for (var i = 0; i < flagLength; i++)
            {
                if (finalFlags[i] == ' ') // If current is space, replace it
                {
                    finalFlags[i] = currentFlags[i][0];
                }
                else if (currentFlags[i] != " ") // Otherwise, take the smaller value
                {
                    finalFlags[i] = (char)Math.Min(finalFlags[i], currentFlags[i][0]);
                }
            }
        }

        return new string(finalFlags);

        static string Get(string? flag)
        {
            return flag switch
            {
                "1" => "1",
                "0" or "2" => "2",
                _ => " "
            };
        }
    }

    private static List<OnlineAgreedConsentModel> GetOnlineAgreedConsents(long ptId, DateTime processTime, ResultOfQualificationConfirmation confirmation)
    {
        List<OnlineAgreedConsentModel> consentModels = new();
        for (var i = 1; i <= 4; i++)
        {
            var consFlg = 0;
            var consDate = DateTime.MinValue;
            var limitDate = DateTime.MinValue;
            switch (i)
            {
                case 1:
                    limitDate = ParseDateTime(confirmation.PharmacistsInfoAvailableTime, "yyyyMMddHHmmss", false);
                    consDate = ParseDateTime(confirmation.PharmacistsInfoConsTime, "yyyyMMddHHmmss", false);
                    consFlg = confirmation.PharmacistsInfoConsFlg.AsInteger();

                    if (!string.IsNullOrEmpty(confirmation.PharmacistsInfoAvailableTime))
                    {
                        CIUtil.ConvertJapanTimeToUtc(ref limitDate);
                    }

                    if (!string.IsNullOrEmpty(confirmation.PharmacistsInfoConsTime))
                    {
                        CIUtil.ConvertJapanTimeToUtc(ref consDate);
                    }
                    break;
                case 2:
                    limitDate = ParseDateTime(confirmation.SpecificHealthCheckupsInfoAvailableTime, "yyyyMMddHHmmss", false);
                    consDate = ParseDateTime(confirmation.SpecificHealthCheckupsInfoConsTime, "yyyyMMddHHmmss", false);
                    consFlg = confirmation.SpecificHealthCheckupsInfoConsFlg.AsInteger();

                    if (!string.IsNullOrEmpty(confirmation.SpecificHealthCheckupsInfoAvailableTime))
                    {
                        CIUtil.ConvertJapanTimeToUtc(ref limitDate);
                    }

                    if (!string.IsNullOrEmpty(confirmation.SpecificHealthCheckupsInfoConsTime))
                    {
                        CIUtil.ConvertJapanTimeToUtc(ref consDate);
                    }
                    break;
                case 3:
                    limitDate = ParseDateTime(confirmation.DiagnosisInfoAvailableTime, "yyyyMMddHHmmss", false);
                    consDate = ParseDateTime(confirmation.DiagnosisInfoConsTime, "yyyyMMddHHmmss", false);
                    consFlg = confirmation.DiagnosisInfoConsFlg.AsInteger();
                    if (!string.IsNullOrEmpty(confirmation.DiagnosisInfoAvailableTime))
                    {
                        CIUtil.ConvertJapanTimeToUtc(ref limitDate);
                    }

                    if (!string.IsNullOrEmpty(confirmation.DiagnosisInfoConsTime))
                    {
                        CIUtil.ConvertJapanTimeToUtc(ref consDate);
                    }
                    break;
                case 4:
                    limitDate = ParseDateTime(confirmation.OperationInfoAvailableTime, "yyyyMMddHHmmss", false);
                    consDate = ParseDateTime(confirmation.OperationInfoConsTime, "yyyyMMddHHmmss", false);
                    consFlg = confirmation.OperationInfoConsFlg.AsInteger();
                    if (!string.IsNullOrEmpty(confirmation.OperationInfoAvailableTime))
                    {
                        CIUtil.ConvertJapanTimeToUtc(ref limitDate);
                    }

                    if (!string.IsNullOrEmpty(confirmation.OperationInfoConsTime))
                    {
                        CIUtil.ConvertJapanTimeToUtc(ref consDate);
                    }
                    break;
            }

            consentModels.Add(new OnlineAgreedConsentModel(
                ptId: ptId,
                processTime: processTime,
                consType: 0,
                consKbn: i,
                consFlg: consFlg,
                consDate: DateTime.SpecifyKind(consDate, DateTimeKind.Local).ToUniversalTime(),
                limitDate: DateTime.SpecifyKind(limitDate, DateTimeKind.Local).ToUniversalTime()
            ));
        }

        return consentModels;
    }

    private static DateTime ParseDateTime(string dateTime, string format = "yyyyMMddHHmmss", bool setKindUtc = true)
    {
        var result = CIUtil.StrDateToDate(dateTime, format);
        return setKindUtc ? result.SetKindUtc() : result;
    }

    private static string TransformXml(OQSmuhvq02Response xml, BulkConfirmUnit confirmUnit, ResultOfQualificationConfirmation? confirmation = null)
    {
        var messageHeader = XmlExtensions.SerializeToXElement(xml.MessageHeader);
        var messageBody = XmlExtensions.SerializeToXElement(xml.MessageBody);
        messageBody.Elements("BulkConfirmUnit").Remove();

        var resultList = XmlExtensions.SerializeToXElement(confirmUnit, "ResultList");
        resultList.Element("ResultList")?.Remove();
        resultList.Element("ReferenceNumber")?.Remove();
        messageBody.Add(resultList.Elements());
        resultList.Elements().Remove();

        if (confirmation == null)
        {
            if (confirmUnit.ResultList.ResultOfQualificationConfirmations.Length > 0)
                foreach (var resultConfirmation in confirmUnit.ResultList.ResultOfQualificationConfirmations)
                {
                    var xElementConfirmation = XmlExtensions.SerializeToXElement(resultConfirmation);
                    if (!string.IsNullOrEmpty(confirmUnit.ReferenceNumber))
                        xElementConfirmation.Add(new XElement("ReferenceNumber", confirmUnit.ReferenceNumber));

                    resultList.Add(xElementConfirmation);
                }
        }
        else
        {
            var xElementConfirmation = XmlExtensions.SerializeToXElement(confirmation);
            if (!string.IsNullOrEmpty(confirmUnit.ReferenceNumber))
            {
                xElementConfirmation.Add(new XElement("ReferenceNumber", confirmUnit.ReferenceNumber));
            }

            resultList.Add(xElementConfirmation);
        }

        messageBody.Add(resultList);
        var newDoc = new XDocument(
            new XDeclaration("1.0", "UTF-8", "no"),
            new XElement("XmlMsg", messageHeader, messageBody)
        );

        return newDoc.ToXmlString();
    }
}
