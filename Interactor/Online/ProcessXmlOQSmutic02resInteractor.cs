﻿using Domain.Models.Insurance;
using Domain.Models.Online;
using Domain.Models.Online.OQSmutic02res;
using Domain.Models.PatientInfor;
using Helper.Common;
using Helper.Constants;
using Helper.Extension;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using UseCase.Online.ProcessXMLOQS;

namespace Interactor.Online;

public class ProcessXmlOQSmutic02resInteractor : IProcessXmlOQSmutic02resInputPort
{
    private readonly IOnlineRepository _onlineRepository;
    private readonly IPatientInforRepository _patientInforRepository;
    private readonly IInsuranceRepository _insuranceRepository;

    public ProcessXmlOQSmutic02resInteractor(IOnlineRepository onlineRepository,
        IPatientInforRepository patientInforRepository,
        IInsuranceRepository insuranceRepository)
    {
        _onlineRepository = onlineRepository;
        _patientInforRepository = patientInforRepository;
        _insuranceRepository = insuranceRepository;
    }

    public ProcessXmlOQSmutic02resOutputData Handle(ProcessXmlOQSmutic02resInputData inputData)
    {
        var responseStatus = ProcessXMLStatus.Successed;
        var hpId = inputData.HpId;
        var receptionNo = inputData.ReceptionNo;
        var xml = XmlExtensions.Deserialize<OQSmutic02Response>(inputData.XmlString);
        if (xml == null || xml.MessageHeader.ReceptionNumber != receptionNo)
        {
            return new(ProcessXMLStatus.InvalidReceptionNo);
        }

        var onlConfirmModel = new QualificationInfModel(xml.MessageHeader.ReceptionNumber,
            xml.MessageHeader.SegmentOfResult,
            xml.MessageHeader.ErrorMessage,
            xml.MessageHeader.ErrorCode
        );

        var bulkConfirmUnits = xml.MessageBody.BulkConfirmUnits;
        switch (xml.MessageHeader.SegmentOfResult)
        {
            case XmlConstant.SegmentOfResultStatus.Processing:
            case XmlConstant.SegmentOfResultStatus.AbnormalCompletion:
                var result = _onlineRepository.UpdateOnlineConfirmation(
                    inputData.UserId,
                    onlConfirmModel,
                    hpId
                ) ? ProcessXMLStatus.Successed : ProcessXMLStatus.Failed;

                return new(result);
            case XmlConstant.SegmentOfResultStatus.NormalCompletion:
                List<OnlineConfirmationCompositeModel> compositeModels = new();
                if (bulkConfirmUnits == null || bulkConfirmUnits.Length == 0) return new(ProcessXMLStatus.EmptyBulkConfirmUnit);
                foreach (var confirmUnit in bulkConfirmUnits)
                {
                    var refNo = confirmUnit.ReferenceNumber;
                    var confirmResult = confirmUnit.ResultOfQualificationConfirmation;
                    var xmlTransformed = TransformXml(xml, confirmUnit, confirmResult);
                    if (string.IsNullOrEmpty(refNo) || refNo.AsLong() == 0 || confirmResult == null)
                    {
                        compositeModels.Add(new()
                        {
                            OnlineConfirmationHistory = null,
                            OnlineConfirmationDetails = new()
                            {
                                new (
                                    receptionNo: receptionNo,
                                    message: string.Empty,
                                    confirmationResult: xmlTransformed,
                                    ptId: 0,
                                    status: 0
                                )
                            }
                        });

                        continue;
                    }

                    var sex2 = confirmResult.Sex2;
                    var sex = !string.IsNullOrWhiteSpace(sex2) ? sex2 : confirmResult.Sex1;
                    var ptInfo = _patientInforRepository.GetPtInfByRefNo(hpId, refNo.AsLong());
                    if (ptInfo.Sex.ToString() == sex && ptInfo.Birthday.ToString() == confirmResult.Birthdate && ptInfo.PtId > 0)
                    {
                        var message = GetCombinedInfoDifferenceMessage(ptInfo, confirmResult);
                        compositeModels.Add(new()
                        {
                            OnlineConfirmationHistory = new OnlineConfirmationHistoryModel(
                                id: 0,
                                ptId: ptInfo.PtId,
                                confirmationType: 2,
                                infoConsFlg: string.Empty,
                                confirmationResult: xmlTransformed,
                                prescriptionIssueType: 0,
                                uketukeStatus: 0,
                                onlineConfirmationDate: CIUtil.GetJapanDateTimeNow(),
                                hpid: hpId
                            ),
                            OnlineConfirmationDetails = new()
                            {
                                new (
                                    receptionNo: receptionNo,
                                    message: message,
                                    confirmationResult: xmlTransformed,
                                    ptId: ptInfo.PtId,
                                    status: 0
                                    )
                            }
                        });
                    }
                    else
                    {
                        compositeModels.Add(new()
                        {
                            OnlineConfirmationHistory = null,
                            OnlineConfirmationDetails = new()
                            {
                                new (
                                    receptionNo: receptionNo,
                                    message: string.Empty,
                                    confirmationResult: xmlTransformed,
                                    ptId: 0,
                                    status: 0
                                )
                            }
                        });
                    }
                }

                _onlineRepository.UpdateOnlineConfirmation(
                    inputData.UserId,
                    onlConfirmModel,
                    hpId
                );

                if (compositeModels.Count > 0)
                {
                    _onlineRepository.SaveOnlineConfirmationComposite(inputData.UserId, hpId, compositeModels);
                }

                break;
            default:
                responseStatus = ProcessXMLStatus.Failed;
                break;
        }

        return new(responseStatus);
    }

    private static bool HasBasicInfoDifference(PatientInforModel? ptInfo, ResultOfQualificationConfirmation confirmResult)
    {
        if (ptInfo == null) return true;

        var sex2 = confirmResult.Sex2;
        var sex = !string.IsNullOrWhiteSpace(sex2) ? sex2 : confirmResult.Sex1;
        return !(CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(ptInfo.Name)) == CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(confirmResult.Name))
                 && CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(ptInfo.KanaName)) == CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(confirmResult.NameKana))
                 && $"{ptInfo.Birthday}" == $"{confirmResult.Birthdate?.Trim()}"
                 && $"{ptInfo.Sex}" == $"{sex?.Trim()}"
                 && $"{ptInfo.HomePost.Trim()}" == $"{Regex.Replace(confirmResult.PostNumber?.Trim() ?? "", "[-‐‑‒–—―−]", "")}"
                 && $"{ptInfo.HomeAddress1?.Trim()}{ptInfo.HomeAddress2?.Trim()}" == $"{confirmResult.Address?.Trim()}");
    }

    private bool HasInsuranceInfoDifference(PatientInforModel? ptInfo, ResultOfQualificationConfirmation confirmResult)
    {
        if (ptInfo == null) return true;

        var currentPtBirthDay = CIUtil.DateTimeToInt(ParseDateTime(confirmResult.Birthdate, DateTimeFormat.yyyyMMdd, false));
        var medicalTicketInfos = confirmResult.MedicalTicketInfos;
        var medicalTicketInfo = medicalTicketInfos?.FirstOrDefault(x => x.DesignatedMedicalInstitutionFlag == "0") ?? medicalTicketInfos?[0];
        var medicalTicketValidDate = medicalTicketInfo?.MedicalTicketValidDate.AsInteger();
        var medicalTicketExpirationDate = medicalTicketInfo?.MedicalTicketExpirationDate.AsInteger();
        var selfPayAmount = medicalTicketInfo?.SelfPayAmount;
        return _insuranceRepository.HasKohiInfoDifference(
            hpId: ptInfo.HpId,
            ptId: ptInfo.PtId,
            futansyaNo: confirmResult.PublicExpenseNumber?.Trim() ?? string.Empty,
            jyukyusyaNo: confirmResult.BeneficiaryNumber?.Trim() ?? string.Empty,
            startDate: medicalTicketValidDate,
            endDate: medicalTicketExpirationDate,
            selfPayAmount: !string.IsNullOrEmpty(selfPayAmount) ? selfPayAmount.AsInteger() : null,
            birthDay: currentPtBirthDay
            ).IsDifference;
    }

    private static DateTime ParseDateTime(string dateTime, string format = "yyyyMMddHHmmss", bool setKindUtc = true)
    {
        var result = CIUtil.StrDateToDate(dateTime, format);
        return setKindUtc ? result.SetKindUtc() : result;
    }

    public string GetCombinedInfoDifferenceMessage(PatientInforModel? ptInfo, ResultOfQualificationConfirmation confirmResult)
    {
        var basicDiff = HasBasicInfoDifference(ptInfo, confirmResult);
        var insuranceDiff = HasInsuranceInfoDifference(ptInfo, confirmResult);

        return basicDiff switch
        {
            true when insuranceDiff => "[基本・保険差異あり]",
            true => "[基本差異あり]",
            _ => insuranceDiff ? "[保険差異あり]" : string.Empty
        };
    }

    private static string TransformXml(OQSmutic02Response xml, BulkConfirmUnit confirmUnit, ResultOfQualificationConfirmation? confirmation)
    {
        var messageHeader = XmlExtensions.SerializeToXElement(xml.MessageHeader);
        var messageBody = XmlExtensions.SerializeToXElement(xml.MessageBody);
        messageBody.Elements("BulkConfirmUnit").Remove();

        var bulkConfirmUnit = XmlExtensions.SerializeToXElement(confirmUnit, "ResultList");
        bulkConfirmUnit.Element("ReferenceNumber")?.Remove();
        bulkConfirmUnit.Elements("ResultOfQualificationConfirmation")?.Remove();
        messageBody.Add(bulkConfirmUnit.Elements());
        bulkConfirmUnit.Elements().Remove();

        if (confirmation != null)
        {
            var xElementConfirmation = XmlExtensions.SerializeToXElement(confirmation);

            if (!string.IsNullOrEmpty(confirmUnit.ReferenceNumber))
            {
                xElementConfirmation.Add(new XElement("ReferenceNumber", confirmUnit.ReferenceNumber));
            }

            bulkConfirmUnit.Add(xElementConfirmation);
        }

        messageBody.Add(bulkConfirmUnit);
        var newDoc = new XDocument(
            new XDeclaration("1.0", "UTF-8", "no"),
            new XElement("XmlMsg", messageHeader, messageBody)
        );

        return newDoc.ToXmlString();
    }
}
