﻿using System.Text.RegularExpressions;
using Domain.Models.Insurance;
using Domain.Models.Online;
using Domain.Models.Online.OQSmuquc02res;
using Domain.Models.PatientInfor;
using Helper.Common;
using Helper.Constants;
using Helper.Extension;
using System.Xml.Linq;
using UseCase.Online.ProcessXMLOQS;

namespace Interactor.Online;

public class ProcessXmlOQSmuquc02resInteractor : IProcessXmlOQSmuquc02resInputPort
{
    private readonly IOnlineRepository _onlineRepository;
    private readonly IPatientInforRepository _patientInforRepository;
    private readonly IInsuranceRepository _insuranceRepository;

    public ProcessXmlOQSmuquc02resInteractor(IOnlineRepository onlineRepository,
        IPatientInforRepository patientInforRepository,
        IInsuranceRepository insuranceRepository)
    {
        _onlineRepository = onlineRepository;
        _patientInforRepository = patientInforRepository;
        _insuranceRepository = insuranceRepository;
    }

    public ProcessXmlOQSmuquc02resOutputData Handle(ProcessXmlOQSmuquc02resInputData inputData)
    {
        var responseStatus = ProcessXMLStatus.Successed;
        var hpId = inputData.HpId;
        var receptionNo = inputData.ReceptionNo;
        var xml = XmlExtensions.Deserialize<OQSmuquc02Response>(inputData.XmlString);
        if (xml == null || xml.MessageHeader.ReceptionNumber != receptionNo)
        {
            return new(ProcessXMLStatus.InvalidReceptionNo);
        }

        var onlConfirmModel = new QualificationInfModel(xml.MessageHeader.ReceptionNumber,
            xml.MessageHeader.SegmentOfResult,
            xml.MessageHeader.ErrorMessage,
            xml.MessageHeader.ErrorCode
        );

        var bulkConfirmUnits = xml.MessageBody.BulkConfirmUnits;
        var qualificationConfirmationDate = xml.MessageHeader.QualificationConfirmationDate;
        switch (xml.MessageHeader.SegmentOfResult)
        {
            case XmlConstant.SegmentOfResultStatus.Processing:
            case XmlConstant.SegmentOfResultStatus.AbnormalCompletion:
                var result = _onlineRepository.UpdateOnlineConfirmation(
                    inputData.UserId,
                    onlConfirmModel,
                    hpId
                    ) ? ProcessXMLStatus.Successed : ProcessXMLStatus.Failed;

                return new(result);
            case XmlConstant.SegmentOfResultStatus.NormalCompletion:
                List<OnlineConfirmationCompositeModel> compositeModels = new();
                if (bulkConfirmUnits == null || bulkConfirmUnits.Length == 0) return new(ProcessXMLStatus.EmptyBulkConfirmUnit);

                foreach (var confirmUnit in bulkConfirmUnits)
                {
                    var refNo = confirmUnit.ReferenceNumber;
                    var qualificationValidity = confirmUnit.QualificationValidity.AsInteger();
                    var confirmResult = confirmUnit.ResultOfQualificationConfirmation;
                    var xmlTransformed = TransformXml(xml, confirmUnit, confirmResult);
                    var processingResultStatus = confirmUnit.ProcessingResultStatus.AsInteger();
                    var confirmationState = processingResultStatus == 2 ? ConfirmationStateConstant.VerificationError : qualificationValidity;
                    var errorMessage = processingResultStatus == 2 ? confirmUnit.ProcessingResultMessage : string.Empty;
                    if (string.IsNullOrEmpty(refNo) || refNo.AsLong() == 0 || confirmResult == null)
                    {
                        compositeModels.Add(new()
                        {
                            OnlineConfirmationHistory = null,
                            OnlineConfirmationDetails = new()
                            {
                                new (
                                    receptionNo: receptionNo,
                                    message: errorMessage,
                                    confirmationResult: xmlTransformed,
                                    ptId: 0,
                                    status: 0,
                                    confirmationState: confirmationState
                                )
                            }
                        });

                        continue;
                    }
                    
                    var sex2 = confirmResult.Sex2;
                    var sex = !string.IsNullOrWhiteSpace(sex2) ? sex2 : confirmResult.Sex1;
                    var ptInfo = _patientInforRepository.GetPtInfByRefNo(hpId, refNo.AsLong());
                    if (ptInfo.Sex.ToString() == sex && ptInfo.Birthday.ToString() == confirmResult.Birthdate && ptInfo.PtId > 0)
                    {
                        var message = GetCombinedInfoDifferenceMessage(ptInfo, confirmResult, inputData.SinDate);
                        var compositeModel = new OnlineConfirmationCompositeModel
                        {
                            OnlineConfirmationHistory = new OnlineConfirmationHistoryModel(
                                id: 0,
                                ptId: ptInfo.PtId,
                                confirmationType: 2,
                                infoConsFlg: string.Empty,
                                confirmationResult: xmlTransformed,
                                prescriptionIssueType: 0,
                                uketukeStatus: 0,
                                onlineConfirmationDate: CIUtil.GetJapanDateTimeNow(),
                                hpid: hpId
                            ),
                            OnlineConfirmationDetails = new()
                            {
                                new(
                                    receptionNo: receptionNo,
                                    message: processingResultStatus == 2 ? confirmUnit.ProcessingResultMessage : message.Message,
                                    confirmationResult: xmlTransformed,
                                    ptId: ptInfo.PtId,
                                    status: 0,
                                    confirmationState: confirmationState
                                )
                            }
                        };

                        if (message.Hoken.hokenId != null)
                        {
                            if (qualificationValidity != 4)
                            {
                                compositeModel.ConfirmDateModel = new(
                                    ptId: ptInfo.PtId,
                                    hokenGrp: message.Hoken.isHoken ? 1 : 2,
                                    hokenId: message.Hoken.hokenId.Value,
                                    confirmDate: CIUtil.IntToDate(qualificationConfirmationDate.AsInteger()),
                                    checkId: inputData.UserId,
                                    checkComment: string.Empty);
                            }
                        }

                        compositeModels.Add(compositeModel);
                    }
                    else
                    {
                        compositeModels.Add(new()
                        {
                            OnlineConfirmationHistory = null,
                            OnlineConfirmationDetails = new()
                            {
                                new (
                                    receptionNo: receptionNo,
                                    message: errorMessage,
                                    confirmationResult: xmlTransformed,
                                    ptId: 0,
                                    status: 0,
                                    confirmationState: confirmationState
                                )
                            }
                        });
                    }
                }

                _onlineRepository.UpdateOnlineConfirmation(
                    inputData.UserId,
                    onlConfirmModel,
                    hpId
                );

                if (compositeModels.Count > 0)
                {
                    _onlineRepository.SaveOnlineConfirmationComposite(inputData.UserId, hpId, compositeModels);
                }

                break;
            default:
                responseStatus = ProcessXMLStatus.Failed;
                break;
        }

        return new(responseStatus);
    }

    private static bool HasBasicInfoDifference(PatientInforModel? ptInfo, ResultOfQualificationConfirmation confirmResult)
    {
        if (ptInfo == null) return true;

        var sex2 = confirmResult.Sex2;
        var sex = !string.IsNullOrWhiteSpace(sex2) ? sex2 : confirmResult.Sex1;
        return !(CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(ptInfo.Name)) == CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(confirmResult.Name))
                 && CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(ptInfo.KanaName)) == CIUtil.RemoveAllSpaces(CIUtil.ToFullsize(confirmResult.NameKana))
                 && $"{ptInfo.Birthday}" == $"{confirmResult.Birthdate?.Trim()}"
                 && $"{ptInfo.Sex}" == $"{sex?.Trim()}"
                 && $"{ptInfo.HomePost.Trim()}" == $"{Regex.Replace(confirmResult.PostNumber?.Trim() ?? "", "[-‐‑‒–—―−]", "")}"
                 && $"{ptInfo.HomeAddress1?.Trim()}{ptInfo.HomeAddress2?.Trim()}" == $"{confirmResult.Address?.Trim()}"
                 && $"{ptInfo.Setanusi?.Trim()}" == $"{confirmResult.InsuredName?.Trim()}");
    }

    private (int? HokenId, bool IsHoken) GetHokenId(PatientInforModel? ptInfo, ResultOfQualificationConfirmation confirmResult, int sinDate)
    {
        if (ptInfo == null) return (null, false);
        var insuredCardExpirationDate = confirmResult.InsuredCardExpirationDate;
        int? endDate = !string.IsNullOrEmpty(insuredCardExpirationDate) ? insuredCardExpirationDate.AsInteger() : null;
        var personalFamilyClassification = confirmResult.PersonalFamilyClassification?.Trim();
        if (confirmResult.InsuredCardClassification != "A1")
        {
            var hokensyaNo = confirmResult.InsurerNumber?.Trim();
            if (string.IsNullOrEmpty(hokensyaNo)) return (null, false);

            var isApplicable = confirmResult.InsuredCardClassification == "05";
            var hokenInf = _insuranceRepository.GetHokenInf(
                hpId: ptInfo.HpId,
                ptId: ptInfo.PtId,
                hokensyaNo: confirmResult.InsurerNumber?.Trim() ?? string.Empty,
                kigo: confirmResult.InsuredCardSymbol?.Trim() ?? string.Empty,
                bango: confirmResult.InsuredIdentificationNumber?.Trim() ?? string.Empty,
                edaNo: confirmResult.InsuredBranchNumber?.Trim(),
                honkeKbn: !string.IsNullOrEmpty(personalFamilyClassification) ? personalFamilyClassification.AsInteger() : null,
                kofuDate: confirmResult.InsuredCertificateIssuanceDate.AsInteger(),
                startDate: confirmResult.InsuredCardValidDate.AsInteger(),
                endDate: endDate,
                age: 0,
                sinDate: sinDate,
                kogakuValue: null,
                isApplicable
            );

            return (hokenInf?.HokenId, true);
        }

        var currentPtBirthDay = CIUtil.DateTimeToInt(ParseDateTime(confirmResult.Birthdate, DateTimeFormat.yyyyMMdd, false));
        var kohi = _insuranceRepository.HasKohiInfoDifference(
            hpId: ptInfo.HpId,
            ptId: ptInfo.PtId,
            futansyaNo: confirmResult.InsurerNumber?.Trim() ?? string.Empty,
            jyukyusyaNo: confirmResult.InsuredIdentificationNumber?.Trim() ?? string.Empty,
            startDate: null,
            endDate: null,
            selfPayAmount: null,
            birthDay: currentPtBirthDay
        );

        return (kohi.KohiInf?.HokenId, false);
    }

    public ((int? hokenId, bool isHoken) Hoken, string Message) GetCombinedInfoDifferenceMessage(PatientInforModel? ptInfo, ResultOfQualificationConfirmation confirmResult, int sinDate)
    {
        var basicDiff = HasBasicInfoDifference(ptInfo, confirmResult);
        var hoken = GetHokenId(ptInfo, confirmResult, sinDate);
        var insuranceDiff = hoken.HokenId == null;

        return basicDiff switch
        {
            true when insuranceDiff => (hoken, "[基本・保険差異あり]"),
            true => (hoken, "[基本差異あり]"),
            _ => insuranceDiff ? (hoken, "[保険差異あり]") : (hoken, string.Empty)
        };
    }

    private static DateTime ParseDateTime(string dateTime, string format = "yyyyMMddHHmmss", bool setKindUtc = true)
    {
        var result = CIUtil.StrDateToDate(dateTime, format);
        return setKindUtc ? result.SetKindUtc() : result;
    }

    private static string TransformXml(OQSmuquc02Response xml, BulkConfirmUnit confirmUnit, ResultOfQualificationConfirmation? confirmation)
    {
        var messageHeader = XmlExtensions.SerializeToXElement(xml.MessageHeader);
        var messageBody = XmlExtensions.SerializeToXElement(xml.MessageBody);
        messageBody.Elements("BulkConfirmUnit").Remove();

        var bulkConfirmUnit = XmlExtensions.SerializeToXElement(confirmUnit, "ResultList");
        bulkConfirmUnit.Element("ReferenceNumber")?.Remove();
        bulkConfirmUnit.Elements("ResultOfQualificationConfirmation")?.Remove();
        messageBody.Add(bulkConfirmUnit.Elements());
        bulkConfirmUnit.Elements().Remove();
        
        if (confirmation != null)
        {
            var xElementConfirmation = XmlExtensions.SerializeToXElement(confirmation);

            if (!string.IsNullOrEmpty(confirmUnit.ReferenceNumber))
            {
                xElementConfirmation.Add(new XElement("ReferenceNumber", confirmUnit.ReferenceNumber));
            }

            bulkConfirmUnit.Add(xElementConfirmation);
        }

        messageBody.Add(bulkConfirmUnit);
        var newDoc = new XDocument(
            new XDeclaration("1.0", "UTF-8", "no"),
            new XElement("XmlMsg", messageHeader, messageBody)
        );

        return newDoc.ToXmlString();
    }
}
