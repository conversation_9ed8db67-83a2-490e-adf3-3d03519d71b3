﻿using Domain.Models.AccountDue;
using Domain.Models.HpInf;
using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using Domain.Models.User;
using EventProcessor.Interfaces;
using EventProcessor.Model;
using Helper.Constants;
using Helper.Enum;
using Infrastructure.Interfaces;
using Infrastructure.Logger;
using Microsoft.AspNetCore.Mvc;
using UseCase.AccountDue.SaveAccountDueList;

namespace Interactor.AccountDue;

public class SaveAccountDueListInteractor : ISaveAccountDueListInputPort
{
    private readonly IAccountDueRepository _accountDueRepository;
    private readonly IUserRepository _userRepository;
    private readonly IHpInfRepository _hpInfRepository;
    private readonly IPatientInforRepository _patientInforRepository;
    private readonly IEventProcessorService _eventProcessorService;
    private readonly IReceptionRepository _receptionRepository;
    private readonly ILoggingHandler? _loggingHandler;

    public SaveAccountDueListInteractor(IAccountDueRepository accountDueRepository, IUserRepository userRepository, IHpInfRepository hpInfRepository, IPatientInforRepository patientInforRepository, IEventProcessorService eventProcessorService, IReceptionRepository receptionRepository, ITenantProvider tenantProvider)
    {
        _accountDueRepository = accountDueRepository;
        _userRepository = userRepository;
        _hpInfRepository = hpInfRepository;
        _patientInforRepository = patientInforRepository;
        _eventProcessorService = eventProcessorService;
        _receptionRepository = receptionRepository;
        var dbContextOptions = tenantProvider.CreateNewTrackingAdminDbContextOption();
        if (dbContextOptions != null)
        {
            _loggingHandler = new LoggingHandler(dbContextOptions, tenantProvider);
        }
    }

    public SaveAccountDueListOutputData Handle(SaveAccountDueListInputData inputData)
    {
        try
        {
            var validateResult = ValidateInputData(inputData);
            if (validateResult != SaveAccountDueListStatus.ValidateSuccess)
            {
                return new SaveAccountDueListOutputData(validateResult);
            }
            var listAccountDueModel = ConvertToListAccountDueModel(inputData.SyunoNyukinInputItems.Where(item => item.IsUpdated).ToList());
            var listRaiinNo = listAccountDueModel.Select(item => item.RaiinNo).ToList();
            var listSyunoSeikyuDB = _accountDueRepository.GetListSyunoSeikyuModel(inputData.HpId, listRaiinNo);
            var listSyunoNyukinDB = _accountDueRepository.GetListSyunoNyukinModel(inputData.HpId, listRaiinNo);
            var dictRaiinInfDB = _accountDueRepository.GetDictRaiinInfModel(inputData.HpId, listRaiinNo);
            var paymentMethod = _accountDueRepository.GetPaymentMethod(inputData.HpId);


            List<ArgumentModel> listTraiLogModels = new();

            if (!listAccountDueModel.Any())
            {
                return new SaveAccountDueListOutputData(SaveAccountDueListStatus.NoItemChange);
            }

            var validateOnlinePaymentResult = ValidateOnlinePayment(listAccountDueModel, dictRaiinInfDB, listSyunoNyukinDB);
            if (validateOnlinePaymentResult != SaveAccountDueListStatus.ValidateSuccess)
            {
                return new SaveAccountDueListOutputData(validateOnlinePaymentResult);
            }

            var listSeqNos = listAccountDueModel.Select(item => item.SeqNo).ToList();
            foreach (var accountDue in listAccountDueModel)
            {
                if (!paymentMethod.ContainsKey(accountDue.PaymentMethodCd))
                {
                    return new SaveAccountDueListOutputData(SaveAccountDueListStatus.InvalidPaymentMethodCd);
                }

                // FCO連携で、免除が選択されていた場合エラー
                if (accountDue.NyukinKbn == (int)NyukinKbnEnums.NoBilling && accountDue.PaymentMethodCd == (int)PaymentMethodCdEnum.FcoPayment)
                {
                    return new SaveAccountDueListOutputData(SaveAccountDueListStatus.NotAllowedBulkPaymentMethodSelectFCO);
                }

                var validateInvalidNyukinKbnResult = ValidateInvalidNyukinKbn(accountDue, listSeqNos, listSyunoSeikyuDB, listSyunoNyukinDB, listAccountDueModel);
                if (validateInvalidNyukinKbnResult != SaveAccountDueListStatus.ValidateSuccess)
                {
                    return new SaveAccountDueListOutputData(validateInvalidNyukinKbnResult);
                }
                listTraiLogModels = CreateListAuditTrailLogModel(inputData, accountDue, listSyunoSeikyuDB, listTraiLogModels);
            }
            var result = _accountDueRepository.SaveAccountDueList(
                                                    inputData.HpId,
                                                    inputData.PtId,
                                                    inputData.UserId,
                                                    inputData.SinDate,
                                                    listAccountDueModel,
                                                    dictRaiinInfDB,
                                                    inputData.KaikeiTime
                                                );

            if (result.Any())
            {
                foreach (var accountDue in result)
                {
                    if (accountDue.ErrorCode != null)
                    {
                        return new SaveAccountDueListOutputData(SaveAccountDueListStatus.InvalidOnlinePayment, accountDue.ErrorCode, accountDue.UserMessage);
                    }
                }
                _eventProcessorService.DoEvent(listTraiLogModels, inputData.HpId);
                var receptionInfos = _receptionRepository.GetRecptionList(inputData.HpId, inputData.SinDate, ptId: inputData.PtId);
                var sameVisitList = _receptionRepository.GetListSameVisit(inputData.HpId, inputData.PtId, inputData.SinDate);
                return new SaveAccountDueListOutputData(SaveAccountDueListStatus.Successed, result, receptionInfos, sameVisitList);
            }
        }
        catch (Exception ex)
        {
            if (_loggingHandler != null)
            {
                _loggingHandler.WriteLogExceptionAsync(ex);
            }
        }
        finally
        {
            _accountDueRepository.ReleaseResource();
            _userRepository.ReleaseResource();
            _patientInforRepository.ReleaseResource();
            _hpInfRepository.ReleaseResource();
            if (_loggingHandler != null)
            {
                _loggingHandler.Dispose();
            }
        }
        return new SaveAccountDueListOutputData(SaveAccountDueListStatus.Failed);
    }

    private SaveAccountDueListStatus ValidateInvalidNyukinKbn(AccountDueModel accountDue, List<long> listSeqNos, List<SyunoSeikyuModel> listSyunoSeikyuDB, List<SyunoNyukinModel> listSyunoNyukinDB, List<AccountDueModel> listAccountDueModel)
    {
        // 取消しの場合チェック不要
        if(accountDue.IsDelete){
            return SaveAccountDueListStatus.ValidateSuccess;
        }

        var accountDueByRaiinNo = listAccountDueModel.Where(item => item.RaiinNo == accountDue.RaiinNo && !item.IsDelete);
        var seikyuAdjustFutanListInput = accountDueByRaiinNo.Select(item => item.SeikyuAdjustFutan).Distinct().ToList();
        var seikyuGakuListInput = accountDueByRaiinNo.Select(item => item.SeikyuGaku).Distinct().ToList();
        if (seikyuAdjustFutanListInput.Count != 1)
        {
            return SaveAccountDueListStatus.InvalidSeikyuAdjustFutan;
        }
        if (seikyuGakuListInput.Count != 1)
        {
            return SaveAccountDueListStatus.InvalidSeikyuGaku;
        }
   
        var syunoNyuikinDB = listSyunoNyukinDB.FirstOrDefault(item => item.SeqNo == accountDue.SeqNo && item.RaiinNo == accountDue.RaiinNo && item.SinDate == accountDue.SeikyuSinDate);
        if (syunoNyuikinDB != null) {
            // 支払い方法：FCO連携以外からFCO連携への変更はNG
            if (accountDue.PaymentMethodCd == (int)PaymentMethodCdEnum.FcoPayment && syunoNyuikinDB.PaymentMethodCd != (int)PaymentMethodCdEnum.FcoPayment)
            {
                return SaveAccountDueListStatus.NotAllowedPaymentMethodChangeToFCO;
            }
        }

        //特定の来院の更新予定の入金額合計
        int sumNyukinGakuInput = accountDueByRaiinNo.Sum(item => item.NyukinGaku);
        //特定の来院の更新予定の調整額合計
        int sumAdjustFutanInput = accountDueByRaiinNo.Sum(item => item.AdjustFutan);
        //特定の来院の請求情報(DB)
        var syunoSeikyuRaiins = listSyunoSeikyuDB.Where(item => item.RaiinNo == accountDue.RaiinNo).ToList();
        //更新予定以外のDBにある入金額の合計
        int nyukinGakuDB = listSyunoNyukinDB.Where(item => !listSeqNos.Contains(item.SeqNo) && item.RaiinNo == accountDue.RaiinNo).Sum(item => item.NyukinGaku);
        //更新予定以外のDBにある調整額合計
        int adjustFutanDB = listSyunoNyukinDB.Where(item => !listSeqNos.Contains(item.SeqNo) && item.RaiinNo == accountDue.RaiinNo).Sum(item => item.AdjustFutan);
        //特定の来院の請求額(DB)
        int seikyuGakuDB = syunoSeikyuRaiins.FirstOrDefault()?.SeikyuGaku ?? 0;
        //特定の来院の新規請求額(DB)
        int newSeikyuGakuDB = syunoSeikyuRaiins.FirstOrDefault()?.NewSeikyuGaku ?? 0;
        //請求額の入力
        int seikyuGakuInput = seikyuGakuListInput.FirstOrDefault();

        //DBにある新規請求額と入力の請求額が等しい
        if (seikyuGakuInput != seikyuGakuDB && newSeikyuGakuDB == seikyuGakuInput)
        {
            seikyuGakuDB = seikyuGakuInput;
        }

        //請求額 - 入金額　- 調整額 -  特定の来院の更新予定の調整額合計 - 特定の来院の更新予定の入金額合計
        int unPaid = seikyuGakuDB - nyukinGakuDB - adjustFutanDB - sumAdjustFutanInput - sumNyukinGakuInput;
        if (accountDue.NyukinKbn == (int)NyukinKbnEnums.PartiallySettled && unPaid == 0)
        {
            return SaveAccountDueListStatus.InvalidNyukinKbn;
        }
        else if (accountDue.NyukinKbn == (int)NyukinKbnEnums.NoBilling
                && (unPaid != (seikyuGakuDB - nyukinGakuDB - adjustFutanDB)
                    || listSyunoNyukinDB.Count(item => item.RaiinNo == accountDue.RaiinNo) - accountDueByRaiinNo.Count() > 1
                    || sumNyukinGakuInput != 0
                    || sumAdjustFutanInput != 0))
        {
            return SaveAccountDueListStatus.InvalidNyukinKbn;
        }
        else if (accountDue.NyukinKbn == (int)NyukinKbnEnums.FullySettled && (unPaid != 0))
        {
            return SaveAccountDueListStatus.InvalidNyukinKbn;
        }
        return SaveAccountDueListStatus.ValidateSuccess;
    }

    private SaveAccountDueListStatus ValidateOnlinePayment(List<AccountDueModel> listAccountDueModel, Dictionary<long, RaiinInfModel> dictRaiinInfDB, List<SyunoNyukinModel> listSyunoNyukinDB)
    {
        Dictionary<long, List<AccountDueModel>> dictRaiinAccountDue = listAccountDueModel
          .GroupBy(accountDue => accountDue.RaiinNo)
          .ToDictionary(g => g.Key, g => g.ToList());

        foreach (var entry in dictRaiinAccountDue)
        {
            if (dictRaiinInfDB.TryGetValue(entry.Key, out var raiinInfModel))
            {
                //オンライン診療
                if (raiinInfModel.IsOnlineTreatment())
                {
                    //分割入金禁止(リクエストとDBの入金件数が2件以上はNG)
                    var nyukinDbCount = listSyunoNyukinDB.Where(item => item.RaiinNo == entry.Key).Count();
                    var nyukinInputCount = entry.Value.Where(item => item.SeqNo == 0).Count();
                    var sumNyukinCount = nyukinDbCount + nyukinInputCount;
                    var validPayTypes = new HashSet<int>
                    {
                        (int)PaymentMethodCdEnum.Credit,
                        (int)PaymentMethodCdEnum.ElectronicMoney,
                        (int)PaymentMethodCdEnum.Cash,
                        (int)PaymentMethodCdEnum.Online
                    };

                    if (sumNyukinCount > 1)
                    {
                        return SaveAccountDueListStatus.NotAllowedPartialNyukin;
                    }

                    foreach (var accountDue in entry.Value)
                    {
                        if (accountDue.SeqNo != 0)
                        {
                            var syunoNyukin = listSyunoNyukinDB.Where(item => item.RaiinNo == entry.Key && item.SeqNo == accountDue.SeqNo).FirstOrDefault();
                            if (syunoNyukin != null)
                            {
                                //支払い方法変更禁止
                                if (accountDue.PaymentMethodCd != syunoNyukin.PaymentMethodCd)
                                {
                                    return SaveAccountDueListStatus.NotAllowedPaymentMethodChange;
                                }
                                //入金日変更禁止
                                if (accountDue.NyukinDate != syunoNyukin.NyukinDate)
                                {
                                    return SaveAccountDueListStatus.NotAllowedNyukinDateChange;
                                }
                            }
                        } else {
                            // 免除またはオンライン診療で許可された支払い方法
                            bool isValidPayType = validPayTypes.Contains(accountDue.PaymentMethodCd) || accountDue.NyukinKbn == (int)NyukinKbnEnums.NoBilling;
                            if (!isValidPayType)
                            {
                                return SaveAccountDueListStatus.InvalidPaymentMethodCd;
                            }
                        }
                    }
                } else {
                    foreach (var accountDue in entry.Value)
                    {
                        //オンライン診療以外でのオンライン決済禁止
                        if (accountDue.PaymentMethodCd == (int)PaymentMethodCdEnum.Online)
                        {
                            return SaveAccountDueListStatus.InvalidPaymentMethodCd;
                        }
                    }
                }
            }
            else
            {
                return SaveAccountDueListStatus.InvalidRaiinNo;
            }
        }
        return SaveAccountDueListStatus.ValidateSuccess; ;
    }

    private List<ArgumentModel> CreateListAuditTrailLogModel(SaveAccountDueListInputData inputData, AccountDueModel accountDue, List<SyunoSeikyuModel> listSyunoNyukinDB, List<ArgumentModel> listTraiLogModels)
    {
        if (listSyunoNyukinDB.Any(item => accountDue.RaiinNo == item.RaiinNo && accountDue.NyukinKbn != item.NyukinKbn))
        {
            int tempStatus = accountDue.NyukinKbn == 0 ? RaiinState.AmountConfirmed : RaiinState.Paid;
            var eventCd = string.Empty;
            var hosoku = string.Empty;

            if (tempStatus != accountDue.RaiinInfStatus)
            {
                if (tempStatus == RaiinState.AmountConfirmed)
                {
                    eventCd = EventCode.UpdateToWaiting;
                }
                else
                {
                    eventCd = EventCode.UpdateToSettled;
                }

                listTraiLogModels.Add(new ArgumentModel(
                        inputData.HpId,
                        inputData.UserId,
                        eventCd,
                        accountDue.PtId,
                        inputData.SinDate,
                        accountDue.RaiinNo,
                        hosoku
                    ));
            }
        }

        return listTraiLogModels;
    }

    public SaveAccountDueListStatus ValidateInputData(SaveAccountDueListInputData inputData)
    {
        if (inputData.HpId <= 0 || !_hpInfRepository.CheckHpId(inputData.HpId))
        {
            return SaveAccountDueListStatus.InvalidHpId;
        }
        else if (inputData.UserId <= 0 || !_userRepository.CheckExistedUserId(inputData.HpId, inputData.UserId))
        {
            return SaveAccountDueListStatus.InvalidUserId;
        }
        else if (inputData.PtId <= 0 || !_patientInforRepository.CheckExistIdList(inputData.HpId, new List<long> { inputData.PtId }))
        {
            return SaveAccountDueListStatus.InvalidPtId;
        }
        else if (inputData.SinDate.ToString().Length != 8)
        {
            return SaveAccountDueListStatus.InvalidSindate;
        }
        else if (inputData.SyunoNyukinInputItems.Any(item => item.NyukinKbn < 0))
        {
            return SaveAccountDueListStatus.InvalidNyukinKbn;
        }
        else if (inputData.SyunoNyukinInputItems.Any(item => item.RaiinNo <= 0))
        {
            return SaveAccountDueListStatus.InvalidRaiinNo;
        }
        else if (inputData.SyunoNyukinInputItems.Any(item => item.SortNo < 0))
        {
            return SaveAccountDueListStatus.InvalidSortNo;
        }
        else if (inputData.SyunoNyukinInputItems.Any(item => item.PaymentMethodCd < 0))
        {
            return SaveAccountDueListStatus.InvalidPaymentMethodCd;
        }
        else if (inputData.SyunoNyukinInputItems.Any(item => item.NyukinDate < 0))
        {
            return SaveAccountDueListStatus.InvalidNyukinDate;
        }
        else if (inputData.SyunoNyukinInputItems.Any(item => item.NyukinCmt.Length > 100))
        {
            return SaveAccountDueListStatus.NyukinCmtMaxLength100;
        }
        else if (inputData.SyunoNyukinInputItems.Any(item => item.SeikyuTensu < 0))
        {
            return SaveAccountDueListStatus.InvalidSeikyuTensu;
        }
        else if (inputData.SyunoNyukinInputItems.Any(item => item.SeqNo < 0))
        {
            return SaveAccountDueListStatus.InvalidSeqNo;
        }
        // validate same value
        var updatedItem = inputData.SyunoNyukinInputItems;
        var countNyukinKbn = updatedItem.Select(item => new
        {
            item.RaiinNo,
            item.NyukinKbn
        }).Distinct().Count();
        var countRaiinNo = updatedItem.Select(item => item.RaiinNo).Distinct().Count();
        if (countRaiinNo != countNyukinKbn)
        {
            return SaveAccountDueListStatus.InvalidNyukinKbn;
        }
        var countSeikyuGaku = updatedItem.Select(item => new
        {
            item.RaiinNo,
            item.SeikyuGaku
        }).Distinct().Count();
        if (countRaiinNo != countSeikyuGaku)
        {
            return SaveAccountDueListStatus.InvalidSeikyuGaku;
        }
        var countSeikyuAdjustFutan = updatedItem.Select(item => new
        {
            item.RaiinNo,
            item.SeikyuAdjustFutan
        }).Distinct().Count();
        if (countRaiinNo != countSeikyuAdjustFutan)
        {
            return SaveAccountDueListStatus.InvalidSeikyuAdjustFutan;
        }
        return SaveAccountDueListStatus.ValidateSuccess;
    }

    private List<AccountDueModel> ConvertToListAccountDueModel(List<SyunoNyukinInputItem> syunoNyukinInputItems)
    {
        var accountDueModels = syunoNyukinInputItems
                                        .Select(item => new AccountDueModel(
                                            item.NyukinKbn,
                                            item.SortNo,
                                            item.RaiinNo,
                                            item.AdjustFutan,
                                            item.NyukinGaku,
                                            item.PaymentMethodCd,
                                            item.NyukinDate,
                                            item.UketukeSbt,
                                            item.NyukinCmt,
                                            item.SeikyuGaku,
                                            item.SeikyuTensu,
                                            item.SeikyuDetail,
                                            item.SeqNo,
                                            item.RaiinInfStatus,
                                            item.SeikyuAdjustFutan,
                                            item.SeikyuSinDate,
                                            item.IsDelete
                                        )).ToList();
        return accountDueModels;
    }
}
