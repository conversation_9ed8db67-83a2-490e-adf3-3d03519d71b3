﻿using Domain.Models.AccountDue;
using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using Helper.Constants;
using Helper.Enum;
using UseCase.AccountDue.GetAccountDueList;

namespace Interactor.AccountDue;

public class GetAccountDueListInteractor : IGetAccountDueListInputPort
{
    private readonly IAccountDueRepository _accountDueRepository;
    private readonly IReceptionRepository _receptionRepository;
    private readonly IPatientInforRepository _patientInforRepository;

    public GetAccountDueListInteractor(IAccountDueRepository accountDueRepository, IReceptionRepository receptionRepository, IPatientInforRepository patientInforRepository)
    {
        _accountDueRepository = accountDueRepository;
        _receptionRepository = receptionRepository;
        _patientInforRepository = patientInforRepository;
    }

    public GetAccountDueListOutputData Handle(GetAccountDueListInputData inputData)
    {
        try
        {
            if (inputData.HpId <= 0)
            {
                return new GetAccountDueListOutputData(GetAccountDueListStatus.InvalidHpId);
            }
            else if (!_patientInforRepository.CheckExistIdList(inputData.HpId, new List<long>() { inputData.PtId }))
            {
                return new GetAccountDueListOutputData(GetAccountDueListStatus.InvalidPtId);
            }
            else if (inputData.SinDate.ToString().Length != 8)
            {
                return new GetAccountDueListOutputData(GetAccountDueListStatus.InvalidSindate);
            }
            var uketsukeSbt = _accountDueRepository.GetUketsukeSbt(inputData.HpId);
            var paymentMethod = _accountDueRepository.GetPaymentMethod(inputData.HpId);
            var listAccountDues = _accountDueRepository.GetAccountDueList(inputData.HpId, inputData.PtId, inputData.SinDate, inputData.IsUnpaidChecked);
            var hokenPatternList = _receptionRepository.GetList(inputData.HpId, inputData.SinDate, -1, inputData.PtId, true);
            var listRaiinNo = listAccountDues.Select(x => x.RaiinNo).ToList();
            var dictRaiinInfDB = _accountDueRepository.GetDictRaiinInfModel(inputData.HpId, listRaiinNo);

            // Calculate Unpaid
            AccountDueModel tempModel = new();
            foreach (var model in listAccountDues)
            {
                var hokenPatternName = string.Empty;
                var unPaid = 0;
                var isSeikyuRow = true;
                var hokenPattern = hokenPatternList.FirstOrDefault(p => p.HokenPid == model.HokenPid && p.SinDate == model.SeikyuSinDate);
                var isOnlineTreatment = dictRaiinInfDB.TryGetValue(model.RaiinNo, out var raiinInfModel) && raiinInfModel.IsOnlineTreatment();
                var reserveDetailId = 0;

                if (isOnlineTreatment && raiinInfModel?.ReserveDetail != null)
                {
                    reserveDetailId = raiinInfModel.ReserveDetail.ReserveDetailId;
                }

                if (hokenPattern != null)
                {
                    var x = hokenPattern.SinDate;
                    hokenPattern.SetSinDate(model.SeikyuSinDate);
                    hokenPatternName = hokenPattern.HokenPatternName;
                }

                var isFcoWaiting = model.RaiinInfStatus == RaiinState.FcoWaiting;  
                var isFcoPayment = model.PaymentMethodCd == (int)PaymentMethodCdEnum.FcoPayment;  

                // 入金区分が「未収あり」、または「未設定」だがFCO精算待ちではない場合、またはFCO精算待ちでFCO支払いの場合  
                if (model.NyukinKbn == 2 || (model.NyukinKbn == 0 && !isFcoWaiting) || (isFcoPayment && isFcoWaiting))
                {
                    tempModel = model;
                    model.UpdateAccountDueListModel(unPaid, hokenPatternName, isSeikyuRow, isOnlineTreatment, reserveDetailId);
                    continue;
                }
                if (tempModel.RaiinNo == model.RaiinNo)
                {
                    unPaid = tempModel.UnPaid - model.NyukinGaku - model.AdjustFutan;
                    isSeikyuRow = false;
                }
                else
                {
                    unPaid = model.SeikyuGaku - model.NyukinGaku - model.AdjustFutan;
                }

                model.UpdateAccountDueListModel(unPaid, hokenPatternName, isSeikyuRow, isOnlineTreatment, reserveDetailId);
                tempModel = model;
            }

            var result = new AccountDueListModel(listAccountDues, paymentMethod, uketsukeSbt);
            return new GetAccountDueListOutputData(result, GetAccountDueListStatus.Successed);
        }
        finally
        {
            _accountDueRepository.ReleaseResource();
            _receptionRepository.ReleaseResource();
            _patientInforRepository.ReleaseResource();
        }
    }
}
