﻿using Domain.Models.Diseases;
using Domain.Models.MedicalExamination;
using Domain.Models.MstItem;
using Domain.Models.Online;
using Domain.Models.OrdInfDetails;
using Domain.Models.OrdInfs;
using Domain.Models.Reception;
using Domain.Models.SystemConf;
using Domain.Models.TodayOdr;
using Helper.Constants;
using Interactor.CalculateService;
using UseCase.MedicalExamination.GetCheckedOrder;
using static Helper.Constants.OrderInfConst;

namespace Interactor.MedicalExamination
{
    public class GetCheckedOrderInteractor : IGetCheckedOrderInputPort
    {
        private readonly IMedicalExaminationRepository _medicalExaminationRepository;
        private readonly IMedicalExaminationRepository _medicalExaminationRepository1;
        private readonly IMedicalExaminationRepository _medicalExaminationRepository2;
        private readonly IMedicalExaminationRepository _medicalExaminationRepository3;
        private readonly IMedicalExaminationRepository _medicalExaminationRepository4;
        private readonly IMedicalExaminationRepository _medicalExaminationRepository5;
        private readonly IMedicalExaminationRepository _medicalExaminationRepository6;
        private readonly IMedicalExaminationRepository _medicalExaminationRepository7;
        private readonly IMedicalExaminationRepository _medicalExaminationRepository8;
        private readonly IMedicalExaminationRepository _medicalExaminationRepository9;
        private readonly IReceptionRepository _receptionRepository;
        private readonly ICalculateService _calculateRepository;
        private readonly ITodayOdrRepository _todayOdrRepository;
        private readonly IMstItemRepository _mstItemRepository;
        private readonly IOnlineRepository _onlineRepository;
        private readonly ISystemConfRepository _systemConfRepository;

        public GetCheckedOrderInteractor(
            IMedicalExaminationRepository medicalExaminationRepository,
            IMedicalExaminationRepository medicalExaminationRepository1,
            IMedicalExaminationRepository medicalExaminationRepository2,
            IMedicalExaminationRepository medicalExaminationRepository3,
            IMedicalExaminationRepository medicalExaminationRepository4,
            IMedicalExaminationRepository medicalExaminationRepository5,
            IMedicalExaminationRepository medicalExaminationRepository6,
            IMedicalExaminationRepository medicalExaminationRepository7,
            IMedicalExaminationRepository medicalExaminationRepository8,
            IMedicalExaminationRepository medicalExaminationRepository9,
            IReceptionRepository receptionRepository, ICalculateService calculateRepository,
            ITodayOdrRepository todayOdrRepository, IMstItemRepository mstItemRepository,
            IOnlineRepository onlineRepository, ISystemConfRepository systemConfRepository)
        {
            _medicalExaminationRepository = medicalExaminationRepository;
            _medicalExaminationRepository1 = medicalExaminationRepository1;
            _medicalExaminationRepository2 = medicalExaminationRepository2;
            _medicalExaminationRepository3 = medicalExaminationRepository3;
            _medicalExaminationRepository4 = medicalExaminationRepository4;
            _medicalExaminationRepository5 = medicalExaminationRepository5;
            _medicalExaminationRepository6 = medicalExaminationRepository6;
            _medicalExaminationRepository7 = medicalExaminationRepository7;
            _medicalExaminationRepository8 = medicalExaminationRepository8;
            _medicalExaminationRepository9 = medicalExaminationRepository9;
            _receptionRepository = receptionRepository;
            _calculateRepository = calculateRepository;
            _todayOdrRepository = todayOdrRepository;
            _mstItemRepository = mstItemRepository;
            _onlineRepository = onlineRepository;
            _systemConfRepository = systemConfRepository;

        }

        public GetCheckedOrderOutputData Handle(GetCheckedOrderInputData inputData)
        {
            try
            {
                if (inputData.HpId <= 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidHpId, new());
                }
                if (inputData.UserId <= 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidUserId, new());
                }
                if (inputData.SinDate <= 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidSinDate, new());
                }
                if (inputData.HokenId <= 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidHokenId, new());
                }
                if (inputData.PtId <= 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidPtId, new());
                }
                if (inputData.IBirthDay <= 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidIBirthDay, new());
                }
                if (inputData.RaiinNo <= 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidRaiinNo, new());
                }
                if (inputData.SyosaisinKbn < 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidSyosaisinKbn, new());
                }
                if (inputData.OyaRaiinNo <= 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidOyaRaiinNo, new());
                }
                if (inputData.TantoId <= 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidTantoId, new());
                }
                //if (inputData.HokenPid <= 0)
                //{
                //    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidHokenPid, new());
                //}

                if (inputData.PrimaryDoctor < 0)
                {
                    return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidPrimaryDoctor, new());
                }

                var ordInfs = inputData.OdrInfItems.Select(o => new OrdInfModel(
                        inputData.HpId,
                        o.RaiinNo,
                        o.RpNo,
                        o.RpEdaNo,
                        o.PtId,
                        o.SinDate,
                        o.HokenPid,
                        o.OdrKouiKbn,
                        string.Empty,
                        o.InoutKbn,
                        o.SikyuKbn,
                        o.SyohoSbt,
                        o.SanteiKbn,
                        0,
                        o.DaysCnt,
                        o.SortNo,
                        o.IsDeleted,
                        0,
                        o.DetailInfoList.Select(od => new OrdInfDetailModel(
                                inputData.HpId,
                                od.RaiinNo,
                                od.RpNo,
                                od.RpEdaNo,
                                od.RowNo,
                                od.PtId,
                                od.SinDate,
                                od.SinKouiKbn,
                                od.ItemCd,
                                od.ItemName,
                                od.Suryo,
                                od.UnitName,
                                0,
                                od.TermVal,
                                0,
                                od.SyohoKbn,
                                0,
                                od.DrugKbn,
                                od.YohoKbn,
                                od.Kokuji1,
                                od.Kokuji2,
                                od.IsNodspRece,
                        od.IpnCd,
                                string.Empty,
                                0,
                                DateTime.MinValue,
                                0,
                                string.Empty,
                                string.Empty,
                                string.Empty,
                                string.Empty,
                                od.CmtOpt,
                                string.Empty,
                                0,
                                string.Empty,
                                o?.InoutKbn ?? 0,
                                0,
                                true,
                                0,
                                0,
                                0,
                                0,
                                0,
                                0,
                                0,
                                0,
                                "",
                                new List<YohoSetMstModel>(),
                                0,
                                0,
                                "",
                                "",
                                "",
                                ""
                            )).ToList(),
                        DateTime.MinValue,
                        0,
                        "",
                        DateTime.MinValue,
                        0,
                        "",
                        string.Empty,
                        string.Empty
                    )).ToList();

                var diseases = inputData.DiseaseItems.Select(i => new PtDiseaseModel(
                        i.SikkanKbn,
                        i.HokenPid,
                        i.StartDate,
                        i.TenkiKbn,
                        i.TenkiDate,
                        i.SyubyoKbn,
                        i.NanoNanByoCd,
                        i.Icd1022013,
                        i.Icd1012013,
                        i.ByomeiCd
                    )).ToList();

                var checkedOrderModelList = new List<CheckedOrderModel>();
                var allOdrInfDetail = new List<OrdInfDetailModel>();
                foreach (var odr in ordInfs)
                {
                    allOdrInfDetail.AddRange(odr.OrdInfDetails);
                }
                if (inputData.SyosaisinKbn != SyosaiConst.Jihi && inputData.EnabledSanteiCheck)
                {
                    var checkRaiinInf = _receptionRepository.CheckRaiinInf(inputData.HpId, inputData.RaiinNo);

                    if (checkRaiinInf == false)
                    {
                        return new GetCheckedOrderOutputData(GetCheckedOrderStatus.InvalidRaiinNo);
                    }

                    var raiinTask = Task.Run(() => _receptionRepository.Get(inputData.HpId, inputData.RaiinNo));
                    var systemConfTask = Task.Run(() => _systemConfRepository.GetAllSystemConfig(inputData.HpId));
                    Task.WhenAll(raiinTask, systemConfTask).Wait();
                    var raiinInf = raiinTask.Result;
                    var listSystemConf = systemConfTask.Result;
                    bool isJouhou = allOdrInfDetail.Any(d => d.ItemCd == ItemCdConst.Con_Jouhou);
                    List<CheckedOrderModel> checkingOrders = _medicalExaminationRepository.IgakuTokusitu(inputData.HpId, inputData.RaiinNo, inputData.SinDate, inputData.HokenId, inputData.SyosaisinKbn, diseases, allOdrInfDetail, isJouhou, listSystemConf);
                    checkingOrders = _medicalExaminationRepository.IgakuTokusituIsChecked(inputData.HpId, inputData.SinDate, inputData.SyosaisinKbn, checkingOrders, allOdrInfDetail, listSystemConf);
                    checkedOrderModelList.AddRange(checkingOrders);

                    var tasks = new List<Task<List<CheckedOrderModel>>>
                    {
                        Task.Run(() => _medicalExaminationRepository.SihifuToku1(
                            inputData.HpId, inputData.PtId, inputData.SinDate, inputData.HokenId, inputData.SyosaisinKbn,
                            inputData.RaiinNo, inputData.OyaRaiinNo, diseases, allOdrInfDetail, isJouhou, listSystemConf)),

                        Task.Run(() => _medicalExaminationRepository1.SihifuToku2(
                            inputData.HpId, inputData.PtId, inputData.SinDate, inputData.HokenId, inputData.IBirthDay,
                            inputData.RaiinNo, inputData.SyosaisinKbn, inputData.OyaRaiinNo, diseases, allOdrInfDetail,
                            ordInfs.Select(x => x.OdrKouiKbn).ToList(), isJouhou, listSystemConf)),

                        Task.Run(() => _medicalExaminationRepository2.IgakuTenkan(
                            inputData.HpId, inputData.SinDate, inputData.HokenId, inputData.SyosaisinKbn,
                            diseases, allOdrInfDetail, isJouhou)),

                        Task.Run(() => _medicalExaminationRepository3.IgakuNanbyo(
                            inputData.HpId, inputData.SinDate, inputData.HokenId, inputData.SyosaisinKbn,
                            diseases, allOdrInfDetail, isJouhou)),
                    };


                    Task.WhenAll(tasks).Wait();
                    foreach (var task in tasks)
                    {
                        checkedOrderModelList.AddRange(task.Result);
                    }
                    checkedOrderModelList = _medicalExaminationRepository.InitPriorityCheckDetail(checkedOrderModelList);
                    var tasks2 = new List<Task<List<CheckedOrderModel>>>()
                    {
                        Task.Run(() => _medicalExaminationRepository1.TouyakuTokusyoSyoho(
                            inputData.HpId, inputData.SinDate, inputData.HokenId, diseases, allOdrInfDetail, ordInfs, listSystemConf)
                            .Where(c => c.CheckingType > 0).ToList()),

                        Task.Run(() => _medicalExaminationRepository2.ChikiHokatu(
                            inputData.HpId, inputData.PtId, inputData.UserId, inputData.SinDate, inputData.PrimaryDoctor,
                            inputData.TantoId, allOdrInfDetail, inputData.SyosaisinKbn)),

                        Task.Run(() => _medicalExaminationRepository3.YakkuZai(
                            inputData.HpId, inputData.PtId, inputData.SinDate, inputData.IBirthDay,
                            allOdrInfDetail, ordInfs)),

                        Task.Run(() => _medicalExaminationRepository4.SiIkuji(
                            inputData.HpId, inputData.SinDate, inputData.IBirthDay,
                            allOdrInfDetail, isJouhou, inputData.SyosaisinKbn)),

                        Task.Run(() => _medicalExaminationRepository5.TrialIryoJyohoKibanCalculation(
                            inputData.HpId, inputData.PtId, inputData.SinDate, inputData.RaiinNo, allOdrInfDetail)),

                        Task.Run(() => _medicalExaminationRepository6.IryoDX(
                            inputData.HpId, inputData.PtId, inputData.SinDate, allOdrInfDetail, inputData.SyosaisinKbn)),

                        Task.Run(() => _medicalExaminationRepository7.TrialGairaiZaitakuRiha(
                            inputData.HpId, inputData.PtId, inputData.SinDate, allOdrInfDetail)),

                        Task.Run(() => _medicalExaminationRepository8.TrialIryoJyohoSyutoku(
                            inputData.HpId, inputData.PtId, inputData.SinDate, allOdrInfDetail, ordInfs)),

                        Task.Run(() => _medicalExaminationRepository9.IryoJyohoSyutoku202412(
                            inputData.HpId, inputData.PtId, inputData.SinDate, allOdrInfDetail, ordInfs))
                    };
                    foreach (var task in tasks2)
                    {
                        checkedOrderModelList.AddRange(task.Result);
                    }
                    var odrItems = ordInfs.Select(o => new OdrInfItem(
                            inputData.HpId,
                            o.PtId,
                            o.SinDate,
                            o.RaiinNo,
                            o.RpNo,
                            o.RpEdaNo,
                            o.HokenPid,
                            o.OdrKouiKbn,
                            o.InoutKbn,
                            o.SikyuKbn,
                            o.SyohoSbt,
                            o.SanteiKbn,
                            o.DaysCnt,
                            o.SortNo,
                            o.IsDeleted,
                            o.OrdInfDetails.Select(od =>
                                    new OdrInfDetailItem(
                                            inputData.HpId,
                                            od.PtId,
                                            od.SinDate,
                                            od.RaiinNo,
                                            od.RpNo,
                                            od.RpEdaNo,
                                            od.RowNo,
                                            od.SinKouiKbn,
                                            od.ItemCd,
                                            od.Suryo,
                                            od.UnitName,
                                            od.TermVal,
                                            od.SyohoKbn,
                                            od.DrugKbn,
                                            od.YohoKbn,
                                            od.Kokuji1,
                                            od.Kokuji2,
                                            od.IsNodspRece,
                                            od.IpnCd,
                                            od.IpnName,
                                            od.CmtOpt,
                                            od.ItemName,
                                            od.IsDummy
                                        )
                                ).ToList()
                        )).ToList();


                    long maxRpNoOnDB = _todayOdrRepository.GetMaxRpNo(inputData.HpId, inputData.PtId, inputData.RaiinNo, inputData.SinDate);
                    long maxRpNo = Math.Max(maxRpNoOnDB, 1);
                    var itemCdCheckOrders = checkedOrderModelList.Select(c => c.ItemCd).ToList();
                    var tenMstModels = _mstItemRepository.GetTenMstInfoList(inputData.HpId, itemCdCheckOrders, inputData.SinDate);
                    foreach (var itemCd in itemCdCheckOrders)
                    {
                        odrItems.Add(CreateIkaTodayOdrInfModel(inputData.HpId, inputData.PtId, inputData.SinDate, inputData.RaiinNo, inputData.HokenPid, itemCd, maxRpNo, tenMstModels));

                        // 追加した項目のDummyフラグをセット
                        foreach (var detail in odrItems.Last().DetailInfoList)
                        {
                            detail.IsDummy = true;
                        }

                        maxRpNo++;
                    }
                    var requestRaiinInf = new ReceptionItem(raiinInf);
                    var runTraialCalculateRequest = new RunTraialCalculateRequest(
                            inputData.HpId,
                            inputData.PtId,
                            inputData.SinDate,
                            inputData.RaiinNo,
                            odrItems,
                            requestRaiinInf,
                            false
                        );

                    var runTrialCalculate = _calculateRepository.RunTrialCalculate(runTraialCalculateRequest);

                    var itemCds = runTrialCalculate.SinMeiList.Select(x => x.ItemCd).Distinct().ToList();
                    _medicalExaminationRepository.GairaiZaitakuRiha(inputData.HpId, inputData.PtId, inputData.SinDate, ref checkedOrderModelList, itemCds);

                    checkedOrderModelList = checkedOrderModelList.Where(c => itemCds.Contains(c.ItemCd)).ToList();
                    IryoJyohoKibanCalculation(ref checkedOrderModelList, allOdrInfDetail, itemCds, inputData.HpId, inputData.PtId, inputData.SinDate);
                    _medicalExaminationRepository.IryoJyohoSyutoku(inputData.HpId, inputData.PtId, inputData.SinDate, ref checkedOrderModelList, itemCds, ordInfs, allOdrInfDetail);
                    checkedOrderModelList.AddRange(_medicalExaminationRepository.Zanyaku(inputData.HpId, inputData.SinDate, allOdrInfDetail, ordInfs));
                }

                if (checkedOrderModelList.Any(c => c.ItemCd == ItemCdConst.YakuzaiJohoTeiyo)
                    && !checkedOrderModelList.Any(c => c.ItemCd == ItemCdConst.YakuzaiJoho))
                {
                    checkedOrderModelList.RemoveAll(c => c.ItemCd == ItemCdConst.YakuzaiJohoTeiyo);
                }

                var listItemCd = checkedOrderModelList.Select(p => p.ItemCd).Distinct().ToList();
                var existedItemCds = _mstItemRepository.GetExistedTenMstItemCds(inputData.HpId, listItemCd, inputData.SinDate);
                checkedOrderModelList.RemoveAll(p => !existedItemCds.Contains(p.ItemCd));
                //checkedOrderModelList.RemoveAll(p => !_mstItemRepository.ExistedTenMstItem(inputData.HpId, p.ItemCd, inputData.SinDate));

                return new GetCheckedOrderOutputData(GetCheckedOrderStatus.Successed, checkedOrderModelList);
            }
            finally
            {
                _medicalExaminationRepository.ReleaseResource();
                _medicalExaminationRepository1.ReleaseResource();
                _medicalExaminationRepository2.ReleaseResource();
                _medicalExaminationRepository3.ReleaseResource();
                _medicalExaminationRepository4.ReleaseResource();
                _medicalExaminationRepository5.ReleaseResource();
                _medicalExaminationRepository6.ReleaseResource();
                _medicalExaminationRepository7.ReleaseResource();
                _medicalExaminationRepository8.ReleaseResource();
                _medicalExaminationRepository9.ReleaseResource();
                _receptionRepository.ReleaseResource();
                _calculateRepository.ReleaseSource();
                _todayOdrRepository.ReleaseResource();
                _mstItemRepository.ReleaseResource();
                _onlineRepository.ReleaseResource();
            }
        }

        private OdrInfItem CreateIkaTodayOdrInfModel(int hpId, long ptId, int sinDate, long raiinNo, int hokenPid, string itemCd, long maxRpNo, List<TenItemModel> tenItemModels)
        {
            var tenMst = tenItemModels.FirstOrDefault(i => i.ItemCd == itemCd);
            List<OdrInfDetailItem> odrInfDetails = new List<OdrInfDetailItem>();

            OdrInfDetailItem detail = new OdrInfDetailItem(
                hpId,
                ptId,
                sinDate,
                raiinNo,
                maxRpNo + 1,
                1,
                1,
                tenMst!.SinKouiKbn,
                itemCd,
                0,
                string.Empty,
                0,
                0,
                0,
                0,
                String.Empty,
                String.Empty,
                0,
                String.Empty,
                String.Empty,
                String.Empty,
                String.Empty,
                false
            );

            odrInfDetails.Add(detail);


            OdrInfItem odrInf = new OdrInfItem(
                    hpId,
                    ptId,
                    sinDate,
                    raiinNo,
                    maxRpNo + 1,
                    1,
                    hokenPid,
                    tenMst.SinKouiKbn,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    odrInfDetails
                );


            return odrInf;
        }

        private void IryoJyohoKibanCalculation(ref List<CheckedOrderModel> checkingOrderModelList, List<OrdInfDetailModel> allOdrInfDetail, List<string> itemSantei, int hpId, long ptId, int sinDate)
        {
            // 2024/05/31までは医療情報・システム基盤整備体制充実加算の算定支援
            if (sinDate >= KaiseiDate.d20240601)
            {
                return;
            }

            //初診
            bool existOnlineConsent = _onlineRepository.ExistOnlineConsent(ptId, sinDate, hpId);
            bool isExistFirstVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && x.Suryo == 1);
            bool isExistReturnVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && x.Suryo == 3);
            if (isExistFirstVisit)
            {
                bool isExistSyosinIryoJyohoKiban1 = itemSantei.Any(x => x == ItemCdConst.SyosinIryoJyohoKiban1)
                                                    && checkingOrderModelList.Any(x => x.ItemCd == ItemCdConst.SyosinIryoJyohoKiban1);
                if (isExistSyosinIryoJyohoKiban1)
                {
                    var checkingOrderModelSyosinIryoJyohoKiban1 = checkingOrderModelList.First(x => x.ItemCd == ItemCdConst.SyosinIryoJyohoKiban1);
                    checkingOrderModelSyosinIryoJyohoKiban1.ChangeSantei(!existOnlineConsent);
                    int index = checkingOrderModelList.IndexOf(checkingOrderModelSyosinIryoJyohoKiban1);

                    var syosinIryoJyohoKiban2TenMstModel = _mstItemRepository.GetTenMstInfo(hpId, ItemCdConst.SyosinIryoJyohoKiban2, sinDate);
                    if (syosinIryoJyohoKiban2TenMstModel != null)
                    {
                        var checkingOrderModelSyosinIryoJyohoKiban2 = new CheckedOrderModel(
                            CheckingType.MissingCalculate,
                            existOnlineConsent,
                            FormatSanteiMessage(syosinIryoJyohoKiban2TenMstModel.Name),
                            syosinIryoJyohoKiban2TenMstModel.ItemCd,
                            syosinIryoJyohoKiban2TenMstModel.SinKouiKbn,
                            syosinIryoJyohoKiban2TenMstModel.Name,
                            0
                            );

                        if (index + 1 == checkingOrderModelList.Count)
                        {
                            checkingOrderModelList.Add(checkingOrderModelSyosinIryoJyohoKiban2);
                        }
                        else
                        {
                            checkingOrderModelList.Insert(index + 1, checkingOrderModelSyosinIryoJyohoKiban2);
                        }
                    }
                }

                bool isExistMedicalDevelopmentSystemEnhanceAdd1 = itemSantei.Any(x => x == ItemCdConst.IgakuIryoJyohoKiban1)
                                                                  && checkingOrderModelList.Any(x => x.ItemCd == ItemCdConst.IgakuIryoJyohoKiban1);
                if (isExistMedicalDevelopmentSystemEnhanceAdd1)
                {
                    var checkingOrderModelMedicalDevelopmentSystemEnhanceAdd1 = checkingOrderModelList.First(x => x.ItemCd == ItemCdConst.IgakuIryoJyohoKiban1);
                    checkingOrderModelMedicalDevelopmentSystemEnhanceAdd1.ChangeSantei(!existOnlineConsent);
                    int index = checkingOrderModelList.IndexOf(checkingOrderModelMedicalDevelopmentSystemEnhanceAdd1);

                    var medicalDevelopmentSystemEnhanceAdd2TenMstModel = _mstItemRepository.GetTenMstInfo(hpId, ItemCdConst.IgakuIryoJyohoKiban2, sinDate);
                    if (medicalDevelopmentSystemEnhanceAdd2TenMstModel != null)
                    {
                        var checkingOrderModelMedicalDevelopmentSystemEnhanceAdd2 = new CheckedOrderModel(
                                                                                                        CheckingType.MissingCalculate,
                                                                                                        existOnlineConsent,
                                                                                                        FormatSanteiMessage(medicalDevelopmentSystemEnhanceAdd2TenMstModel.Name),
                                                                                                        medicalDevelopmentSystemEnhanceAdd2TenMstModel.ItemCd,
                                                                                                        medicalDevelopmentSystemEnhanceAdd2TenMstModel.SinKouiKbn,
                                                                                                        medicalDevelopmentSystemEnhanceAdd2TenMstModel.Name,
                                                                                                        0
                                                                                                         );
                        if (index + 1 == checkingOrderModelList.Count)
                        {
                            checkingOrderModelList.Add(checkingOrderModelMedicalDevelopmentSystemEnhanceAdd2);
                        }
                        else
                        {
                            checkingOrderModelList.Insert(index + 1, checkingOrderModelMedicalDevelopmentSystemEnhanceAdd2);
                        }
                    }
                }
            }
            else if (isExistReturnVisit)
            {
                bool isExistSaisinIryoJyohoKiban3 = itemSantei.Any(x => x == ItemCdConst.SaisinIryoJyohoKiban3)
                                                    && checkingOrderModelList.Any(x => x.ItemCd == ItemCdConst.SaisinIryoJyohoKiban3);
                if (isExistSaisinIryoJyohoKiban3)
                {
                    var checkingOrderModelSaisinIryoJyohoKiban3 = checkingOrderModelList.First(x => x.ItemCd == ItemCdConst.SaisinIryoJyohoKiban3);
                    checkingOrderModelSaisinIryoJyohoKiban3.ChangeSantei(!existOnlineConsent);
                }

                bool isExistReturnVisitDevelopmentSystemEnhanceAdd3 = itemSantei.Any(x => x == ItemCdConst.IgakuIryoJyohoKiban3)
                                                                    && checkingOrderModelList.Any(x => x.ItemCd == ItemCdConst.IgakuIryoJyohoKiban3);
                if (isExistReturnVisitDevelopmentSystemEnhanceAdd3)
                {
                    var checkingOrderModelReturnVisitDevelopmentSystemEnhanceAdd3 = checkingOrderModelList.First(x => x.ItemCd == ItemCdConst.IgakuIryoJyohoKiban3);
                    checkingOrderModelReturnVisitDevelopmentSystemEnhanceAdd3.ChangeSantei(!existOnlineConsent);
                }
            }
        }

        private string FormatSanteiMessage(string santeiItemName)
        {
            return $"\"{santeiItemName}\"を算定できる可能性があります。";

        }
    }
}
