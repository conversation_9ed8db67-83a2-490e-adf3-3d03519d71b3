﻿using Domain.Models.Ka;
using Domain.Models.PatientInfor;
using Domain.Models.PtCmtInf;
using Domain.Models.PtMemo;
using Domain.Models.RaiinCmtInf;
using Domain.Models.RaiinKubunMst;
using Domain.Models.RaiinStatusMst;
using Domain.Models.Reception;
using Domain.Models.UketukeSbtMst;
using Domain.Models.User;
using Helper.Constants;
using Interactor.CalculateService;
using System.Collections.Generic;
using System.Linq;
using UseCase.Accounting.Recaculate;
using UseCase.Reception.Update;
using UseCase.Reception.UpdateStaticCell;

namespace Interactor.Reception;

public class UpdateReceptionStaticCellInteractor : IUpdateReceptionStaticCellInputPort
{
    private readonly IReceptionRepository _receptionRepository;
    private readonly IRaiinCmtInfRepository _raiinCmtInfRepository;
    private readonly IUserRepository _userRepository;
    private readonly IUketukeSbtMstRepository _uketukeSbtMstRepository;
    private readonly IKaRepository _kaMstRepository;
    private readonly IPtCmtInfRepository _ptCmtInfRepository;
    private readonly ICalculateService _calculateRepository;
    private readonly IPatientInforRepository _patientInforRepository;
    private readonly IRaiinKubunMstRepository _raiinKubunMstRepository;
    private readonly IPtMemoRepository _ptMemoRepository;
    private readonly IRaiinStatusMstRepository _raiinStatusMstRepository;

    public UpdateReceptionStaticCellInteractor(IReceptionRepository receptionRepository,
        IRaiinCmtInfRepository raiinCmtInfRepository,
        IUserRepository userRepository,
        IUketukeSbtMstRepository uketukeSbtMstRepository,
        IKaRepository kaMstRepository,
        IPtCmtInfRepository ptCmtInfRepository,
        ICalculateService calculateRepository,
        IPatientInforRepository patientInforRepository
,
        IRaiinKubunMstRepository raiinKubunMstRepository,
        IPtMemoRepository ptMemoRepository,
        IRaiinStatusMstRepository raiinStatusMstRepository)
    {
        _receptionRepository = receptionRepository;
        _raiinCmtInfRepository = raiinCmtInfRepository;
        _userRepository = userRepository;
        _uketukeSbtMstRepository = uketukeSbtMstRepository;
        _kaMstRepository = kaMstRepository;
        _ptCmtInfRepository = ptCmtInfRepository;
        _calculateRepository = calculateRepository;
        _patientInforRepository = patientInforRepository;
        _raiinKubunMstRepository = raiinKubunMstRepository;
        _ptMemoRepository = ptMemoRepository;
        _raiinStatusMstRepository = raiinStatusMstRepository;
    }

    public UpdateReceptionStaticCellOutputData Handle(UpdateReceptionStaticCellInputData input)
    {
        if (input.HpId <= 0)
        {
            return new UpdateReceptionStaticCellOutputData(UpdateReceptionStaticCellStatus.InvalidHpId);
        }
        if (input.SinDate <= 0)
        {
            return new UpdateReceptionStaticCellOutputData(UpdateReceptionStaticCellStatus.InvalidSinDate);
        }
        if (input.RaiinNo <= 0)
        {
            return new UpdateReceptionStaticCellOutputData(UpdateReceptionStaticCellStatus.InvalidRaiinNo);
        }
        if (input.PtId <= 0)
        {
            return new UpdateReceptionStaticCellOutputData(UpdateReceptionStaticCellStatus.InvalidPtId);
        }

        try
        {
            var result = UpdateStaticCell(input);
            var status = result.status;
            List<ReceptionForViewDto> receptionInfos = new();
            //List<ReceptionRowModel> receptionInfos = new();

            List<SameVisitModel> sameVisitList = new();
            PatientInforModel patientInforModel = new();
            List <RaiinStatusCountListDto> raiinStatusCountList = new();
            var statusGe5 = new List<int> { 5, 6 };
            var statusLt5 = new List<int> { 0, 1, 2, 3, 4 };

            if(result.typeValidStatus == 1 && input.CellName.ToLower() == "status" && status == UpdateReceptionStaticCellStatus.InvalidStatus)
            {
                return new UpdateReceptionStaticCellOutputData(status, receptionInfos, sameVisitList, patientInforModel, raiinStatusCountList);
            }    

            if (status == UpdateReceptionStaticCellStatus.RaiinInfUpdated && input.CellName.ToLower() == "status" && statusGe5.Contains(result.oldStatus) && statusLt5.Contains(int.Parse(input.CellValue)))
            {
                Task.Run(() =>
                {
                    _calculateRepository.RunCalculate(new RecaculationInputDto(input.HpId, input.PtId, input.SinDate, 0, ""));
                });
            }
            if (status == UpdateReceptionStaticCellStatus.RaiinInfUpdated || status == UpdateReceptionStaticCellStatus.RaiinCmtUpdated || status == UpdateReceptionStaticCellStatus.LabelUpdated || status == UpdateReceptionStaticCellStatus.PatientCmtUpdated)
            {
                receptionInfos = _receptionRepository.GetRecptionList(input.HpId, input.SinDate, input.RaiinNo);
                raiinStatusCountList = _raiinStatusMstRepository.GetRaiinStatusCountList(input.HpId, input.SinDate);
            }
            if (status == UpdateReceptionStaticCellStatus.PatientCmtUpdated)
            {
                patientInforModel = _patientInforRepository.GetById(input.HpId, input.PtId, input.SinDate, 0) ?? new();
            }
            return new UpdateReceptionStaticCellOutputData(status, receptionInfos, sameVisitList, patientInforModel, raiinStatusCountList);
        }
        catch(Exception ex)
        {
                Console.WriteLine(ex.InnerException);
                throw;
        }
        finally
        {
            _receptionRepository.ReleaseResource();
            _raiinCmtInfRepository.ReleaseResource();
            _userRepository.ReleaseResource();
            _uketukeSbtMstRepository.ReleaseResource();
            _kaMstRepository.ReleaseResource();
            _ptCmtInfRepository.ReleaseResource();
            _patientInforRepository.ReleaseResource();
            _ptMemoRepository.ReleaseResource();
        }
    }

    private (UpdateReceptionStaticCellStatus status, int oldStatus, int typeValidStatus) UpdateStaticCell(UpdateReceptionStaticCellInputData input)
    {
        string pascalCaseCellName = FirstCharToUpper(input.CellName);
        switch (pascalCaseCellName)
        {
            // Update RaiinInf
            case nameof(ReceptionForViewDto.Status):
                var result = _receptionRepository.UpdateStatus(input.HpId, input.RaiinNo, int.Parse(input.CellValue), input.UserId, input.PtId);
                var success = result.resultSave;
                return (GetRaiinInfUpdateStatus(success, result.typeInvalid), result.oldStatus, result.typeInvalid);
            case nameof(ReceptionForViewDto.TreatmentDepartmentId):
                success = _receptionRepository.UpdateTreatmentDepartment(input.HpId, input.RaiinNo, int.Parse(input.CellValue), input.UserId);
                return (GetRaiinInfUpdateStatus(success), -1, -1);
            case nameof(ReceptionForViewDto.TantoId):
                var tanto = _userRepository.GetByUserId(input.HpId, int.Parse(input.CellValue));
                if (tanto is null)
                {
                    return (UpdateReceptionStaticCellStatus.InvalidTantoId, -1, -1);
                }
                success = _receptionRepository.UpdateTantoId(input.HpId, input.RaiinNo, tanto.UserId, input.UserId);
                return (GetRaiinInfUpdateStatus(success), -1, -1);
            case nameof(ReceptionForViewDto.MonshinStatus):
                success = _receptionRepository.UpdateMonshinStatus(input.HpId, input.RaiinNo, int.Parse(input.CellValue), input.UserId);
                return (GetRaiinInfUpdateStatus(success), -1, -1);
            // Update or insert RaiinCmtInf
            case nameof(ReceptionForViewDto.RaiinMemo):
                _raiinCmtInfRepository.Upsert(input.HpId, input.PtId, input.SinDate, input.RaiinNo, CmtKbns.Comment, input.CellValue, input.UserId);
                return (UpdateReceptionStaticCellStatus.RaiinCmtUpdated, -1, -1);
            // Update or insert PtMemo
            case nameof(ReceptionForViewDto.PtMemo):
                _ptMemoRepository.Upsert(input.HpId, input.PtId, input.CellValue, input.UserId);
                return (UpdateReceptionStaticCellStatus.PatientCmtUpdated, -1, -1);
            // insert RaiinKbnInf
            case nameof(ReceptionForViewDto.Labels):
                _raiinKubunMstRepository.SaveRaiinKbnInfs(input.HpId, input.PtId, input.SinDate, input.RaiinNo, input.UserId, input.GrpIds);
                return (UpdateReceptionStaticCellStatus.LabelUpdated, -1, -1);
            default:
                return (UpdateReceptionStaticCellStatus.InvalidCellName, -1, -1);
        }

        UpdateReceptionStaticCellStatus GetRaiinInfUpdateStatus(bool success, int typeValidStatus = 0)
        {
            if (typeValidStatus == 1) return UpdateReceptionStaticCellStatus.InvalidStatus;
            return success ? UpdateReceptionStaticCellStatus.RaiinInfUpdated : UpdateReceptionStaticCellStatus.RaiinInfNotFound;
        }
    }

    private string FirstCharToUpper(string s)
    {
        if (string.IsNullOrEmpty(s))
        {
            return string.Empty;
        }
        return char.ToUpper(s[0]) + s.Substring(1);
    }
}
