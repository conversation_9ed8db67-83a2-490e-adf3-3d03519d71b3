﻿using Domain.Models.Reception;
using UseCase.Reception.SaveCombineBill;

namespace Interactor.Reception
{
    public class SaveCombineSplitBillInteractor : ISaveCombineBillInputPort
    {
        private readonly IReceptionRepository _receptionRepository;
        public SaveCombineSplitBillInteractor(IReceptionRepository receptionRepository)
        {
            _receptionRepository = receptionRepository;
        }
        public SaveCombineBillOutputData Handle(SaveCombineBillInputData inputData)
        {
            try
            {
                var result = _receptionRepository.SaveCombineBill(inputData.CombineBills, inputData.UserId, inputData.IsSplited, inputData.RaiinNo);
                return new SaveCombineBillOutputData((SaveCombineBillStatus)result);
            }
            finally
            {
                _receptionRepository.ReleaseResource();
            }
        }
    }
}
