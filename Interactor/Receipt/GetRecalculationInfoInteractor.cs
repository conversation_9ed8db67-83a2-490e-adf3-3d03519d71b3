﻿using System.Text.Json;
using Domain.Models.Recalculation;
using Helper.Redis;
using StackExchange.Redis;
using UseCase.ReceiptCheck.GetRecalculationInfo;

namespace Interactor.Receipt
{
    public class GetRecalculationInfoInteractor : IGetRecalculationInfoInputPort
    {
        private readonly IDatabase _cache;

        public GetRecalculationInfoInteractor()
        {
            _cache = RedisConnectorHelper.Connection.GetDatabase();
        }

        public GetRecalculationInfoOutputData Handle(GetRecalculationInfoInputData inputData)
        {
            var filnaley = $"Recalculation{inputData.HpId.ToString()}";
            try
            {
                if (_cache.KeyExists(filnaley))
                {
                    var cachedValue = _cache.StringGet(filnaley).ToString();

                    var inputOld = JsonSerializer.Deserialize<RecalculationMessage>(cachedValue);
                    if (inputOld != null &&
                        inputOld.HpId == inputData.HpId &&
                        inputOld.UserId == inputData.UserId)
                    {
                        return new GetRecalculationInfoOutputData(inputOld, GetRecalculationInfoStatus.Successed);
                    }
                }
                return new GetRecalculationInfoOutputData(new(), GetRecalculationInfoStatus.NotFound);
            }
            catch
            {
                return new GetRecalculationInfoOutputData(new(), GetRecalculationInfoStatus.Failed);
            }


        }
    }
}
