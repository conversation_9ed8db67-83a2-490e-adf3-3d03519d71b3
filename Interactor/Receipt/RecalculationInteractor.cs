﻿using System.Text.Json;
using Domain.Models.AuditLog;
using Domain.Models.Recalculation;
using Domain.Models.Receipt;
using Domain.Models.Receipt.Recalculation;
using Helper.Constants;
using Helper.Exceptions;
using Helper.Messaging;
using Helper.Messaging.Data;
using Helper.Redis;
using Infrastructure.Interfaces;
using Interactor.CalculateService;
using UseCase.MedicalExamination.Calculate;
using UseCase.Receipt.CancelProccessCaculation;
using UseCase.Receipt.Recalculation;

namespace Interactor.Receipt;

public class RecalculationInteractor : IRecalculationInputPort
{
    private readonly IReceiptRepository _receiptRepository;
    private readonly ICalculateService _calculateService;
    private readonly ICommonReceRecalculation _commonReceRecalculation;
    private readonly IAuditLogRepository _auditLogRepository;
    private IMessenger? _messenger;
    private readonly StackExchange.Redis.IDatabase _cache;
    private readonly IUserInfoService _userInfoService;


    bool isStopCalc = false;

    public RecalculationInteractor(IReceiptRepository receiptRepository, ICommonReceRecalculation commonReceRecalculation, ICalculateService calculateRepository, IAuditLogRepository auditLogRepository, IUserInfoService userInfoService)
    {
        _receiptRepository = receiptRepository;
        _commonReceRecalculation = commonReceRecalculation;
        _calculateService = calculateRepository;
        _auditLogRepository = auditLogRepository;
        _cache = RedisConnectorHelper.Connection.GetDatabase();
        _userInfoService = userInfoService;
    }

    public RecalculationOutputData Handle(RecalculationInputData inputData)
    {
        _messenger = inputData.Messenger;
        var finalKey = $"Recalculation{inputData.HpId.ToString()}";
        var PtIdList = inputData.PtIdList.Select(pt => pt.PtId).ToList();
        if (_cache.KeyExists(finalKey))
        {
            var stringValue = _cache.StringGet(finalKey).ToString();
            RecalculationMessage inputOld = JsonSerializer.Deserialize<RecalculationMessage>(stringValue)!;
            var userName = _userInfoService.GetNameById(inputOld.UserId);
            string inputStr = inputOld.SinYm.ToString();
            string year = inputStr.Substring(0, 4);
            string month = inputStr.Substring(4, 2);
            SendMessager(new RecalculationStatus(false, CalculateStatusConstant.Invalid, 0, 0, $"{year}年{month}月の再計算が{userName}さんにより実行中です。再計算の同時実行は出来ません。", string.Empty));
            return new RecalculationOutputData(false);
        }
        try
        {
            string json = JsonSerializer.Serialize(new RecalculationMessage
            {
                HpId = inputData.HpId,
                UserId = inputData.UserId,
                IsRecalculationCheckBox = inputData.IsRecalculationCheckBox,
                IsReceiptAggregationCheckBox = inputData.IsReceiptAggregationCheckBox,
                IsCheckErrorCheckBox = inputData.IsCheckErrorCheckBox,
                SinYm = inputData.SinYm,
                PtIdList = inputData.PtIdList,
                IsOperator = inputData.IsOperator,
                OperatorName = inputData.OperatorName,
                ProccessKey = inputData.ProccessKey
            });
            _cache.StringSet(finalKey, json);
            bool success = true;
            // run Recalculation
            if (!isStopCalc && inputData.IsRecalculationCheckBox)
            {
                success = RunCalculateMonth(inputData.HpId, inputData.UserId, inputData.SinYm, PtIdList, inputData.UniqueKey, inputData.CancellationToken);

                // Check next step
                while (true)
                {
                    if (CheckAllowNextStep())
                    {
                        break;
                    }
                    if (inputData.CancellationToken.IsCancellationRequested || !_cache.KeyExists(finalKey))
                    {
                        return new RecalculationOutputData(false);
                    }
                }
            }

            // Check is stop progerss
            var statusCallBack = _messenger!.SendAsync(new StopCalcStatus());
            isStopCalc = statusCallBack.Result.Result;
            if (isStopCalc || inputData.CancellationToken.IsCancellationRequested || !_cache.KeyExists(finalKey))
            {
                success = false;
            }
            // run Receipt Aggregation
            if (success && !isStopCalc && inputData.IsReceiptAggregationCheckBox)
            {
                success = ReceFutanCalculateMain(inputData.HpId, inputData.SinYm, PtIdList, inputData.UniqueKey, inputData.CancellationToken);

                // Check next step
                while (true)
                {
                    if (CheckAllowNextStep())
                    {
                        break;
                    }
                    if (inputData.CancellationToken.IsCancellationRequested || !_cache.KeyExists(finalKey))
                    {
                        return new RecalculationOutputData(false);
                    }
                }
            }

            // Check is stop progerss
            statusCallBack = _messenger!.SendAsync(new StopCalcStatus());
            isStopCalc = statusCallBack.Result.Result;
            if (isStopCalc || inputData.CancellationToken.IsCancellationRequested || !_cache.KeyExists(finalKey))
            {
                success = false;
            }

            // check error in month
            List<ReceRecalculationModel> receRecalculationList = new();
            if (success && !isStopCalc && (inputData.IsCheckErrorCheckBox || inputData.IsReceiptAggregationCheckBox))
            {
                receRecalculationList = _receiptRepository.GetReceRecalculationList(inputData.HpId, inputData.SinYm, PtIdList);
                int allCheckCount = receRecalculationList.Count;

                var resultCheckError = _commonReceRecalculation.CheckErrorInMonth(inputData.HpId, PtIdList, inputData.SinYm, inputData.UserId, receRecalculationList, allCheckCount, _messenger, RunRecalculationStatus.RunCalculationInMonth, isReceiptAggregationCheckBox: inputData.IsReceiptAggregationCheckBox, inputData.IsCheckErrorCheckBox, inputData.CancellationToken, inputData.UniqueKey);
                success = resultCheckError.Success;
            }

            // resetStatus
            if (success)
            {
                if (inputData.IsCheckErrorCheckBox)
                {
                    _receiptRepository.ResetStatusAfterCheckErr(inputData.HpId, inputData.UserId, inputData.SinYm, receRecalculationList);
                }
                else if (inputData.IsRecalculationCheckBox)
                {
                    _receiptRepository.ResetStatusAfterReCalc(inputData.HpId, PtIdList, inputData.SinYm);
                }
            }

            if (!inputData.IsCheckErrorCheckBox && !inputData.IsReceiptAggregationCheckBox && !inputData.IsRecalculationCheckBox)
            {
                SendMessager(new RecalculationStatus(true, CalculateStatusConstant.None, 0, 0, string.Empty, string.Empty));
            }
            AddAuditLog(inputData.HpId, inputData.UserId, inputData.SinYm, inputData.IsRecalculationCheckBox, inputData.IsReceiptAggregationCheckBox, inputData.IsCheckErrorCheckBox, inputData.PtIdList.Any(), inputData.IsOperator, inputData.OperatorName);
            return new RecalculationOutputData(success);
        }
        catch (Exception ex)
        {
            string name = GetType().Namespace ?? string.Empty;
            throw new InteractorCustomException(name, ex);
        }
        finally
        {
            _commonReceRecalculation.ReleaseResource();
            _receiptRepository.ReleaseResource();
            _auditLogRepository.ReleaseResource();
            _calculateService.ReleaseSource();
            _messenger!.Send(new CancelProccessStatus(inputData.HpId, inputData.UserId, (int)ProccessStatus.ProccessSuccess));
        }
    }

    private bool RunCalculateMonth(int hpId, int userId, int seikyuYm, List<long> ptInfList, string uniqueKey, CancellationToken cancellationToken)
    {
        SendMessager(new RecalculationStatus(false, CalculateStatusConstant.RecalculationCheckBox, 0, 0, "StartCalculateMonth", string.Empty));
        var statusCallBack = _messenger!.SendAsync(new StopCalcStatus());
        isStopCalc = statusCallBack.Result.Result;
        if (isStopCalc)
        {
            return false;
        }
        _calculateService.RunCalculateMonth(new CalculateMonthRequest()
        {
            HpId = hpId,
            PtIds = ptInfList,
            SeikyuYm = seikyuYm,
            PreFix = userId.ToString(),
            UniqueKey = uniqueKey
        }, cancellationToken, _messenger).Wait();
        return true;
    }

    private bool ReceFutanCalculateMain(int hpId, int seikyuYm, List<long> ptInfList, string uniqueKey, CancellationToken cancellationToken)
    {
        SendMessager(new RecalculationStatus(false, CalculateStatusConstant.ReceiptAggregationCheckBox, 0, 0, "StartFutanCalculateMain", string.Empty));
        var statusCallBack = _messenger!.SendAsync(new StopCalcStatus());
        isStopCalc = statusCallBack.Result.Result;
        if (isStopCalc)
        {
            return false;
        }
        _calculateService.ReceFutanCalculateMain(new ReceCalculateRequest(hpId, ptInfList, seikyuYm, uniqueKey), cancellationToken, _messenger).Wait();
        return true;
    }

    private void SendMessager(RecalculationStatus status)
    {
        _messenger!.Send(status);
    }

    private bool CheckAllowNextStep()
    {
        var allowNextStep = _messenger!.SendAsync(new AllowNextStepStatus());
        return allowNextStep.Result.Result;
    }

    private void AddAuditLog(int hpId, int userId, int sinDate, bool recalculation, bool receiptAggregation, bool isCheckError, bool isSpecifiedPt, int isOperator, string operatorName)
    {
        var hosoku = string.Format("CALC:{0},SUMRECE:{1},CHECK:{2},PT:{3}",
                                                   recalculation ? 1 : 0,
                                                   receiptAggregation ? 1 : 0,
                                                   isCheckError ? 1 : 0,
                                                   isSpecifiedPt ? 1 : 0);
        var arg = new ArgumentModel(
                        EventCode.Recalculation,
                        0,
                        sinDate,
                        0,
                        0,
                        0,
                        0,
                        0,
        hosoku
        );

        _auditLogRepository.AddAuditTrailLog(userId, isOperator, operatorName, arg, hpId);
    }
}