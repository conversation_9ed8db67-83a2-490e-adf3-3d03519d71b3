﻿using System.Text.Json;
using Domain.Models.Recalculation;
using Helper.Messaging;
using Helper.Redis;
using StackExchange.Redis;
using UseCase.Receipt.CancelProccessCaculation;

namespace Interactor.Receipt
{
    public class CancelProccessCalculationInteractor : ICancelProccessCalculationInputPort
    {
        private readonly IDatabase _cache;
        private IMessenger? _messenger;

        public CancelProccessCalculationInteractor()
        {
            _cache = RedisConnectorHelper.Connection.GetDatabase();
        }

        public CancelProccessCalculationOutputData Handle(CancelProccessCalculationInputData inputData)
        {
            var finalKey = $"Recalculation{inputData.HpId.ToString()}";
            if (!_cache.KeyExists(finalKey))
            {
                return new CancelProccessCalculationOutputData(CancelProccessCalculationStatus.NotFound);
            }
            var cachedValue = _cache.StringGet(finalKey).ToString();

            RecalculationMessage? inputOld;

            try
            {
                inputOld = JsonSerializer.Deserialize<RecalculationMessage>(cachedValue);
            }
            catch (JsonException)
            {
                return new CancelProccessCalculationOutputData(CancelProccessCalculationStatus.Failed);
            }

            if (inputOld != null &&
                inputOld.HpId == inputData.HpId &&
                inputOld.UserId == inputData.UserId)
            {
                _cache.KeyDelete(finalKey);
                return new CancelProccessCalculationOutputData(CancelProccessCalculationStatus.Successed, inputOld);
            }

            return new CancelProccessCalculationOutputData(CancelProccessCalculationStatus.Failed);
        }
    }
}
