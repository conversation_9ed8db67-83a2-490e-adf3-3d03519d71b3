﻿using Domain.Models.Receipt;
using Domain.Models.Receipt.ReceiptListAdvancedSearch;
using Helper.Enum;
using UseCase.Receipt.ReceiptListAdvancedSearch;

namespace Interactor.Receipt;

public class ReceiptListAdvancedSearchInteractor : IReceiptListAdvancedSearchInputPort
{
    private readonly IReceiptRepository _receiptRepository;

    public ReceiptListAdvancedSearchInteractor(IReceiptRepository receiptRepository)
    {
        _receiptRepository = receiptRepository;
    }

    public ReceiptListAdvancedSearchOutputData Handle(ReceiptListAdvancedSearchInputData inputData)
    {
        try
        {
            var result = _receiptRepository.GetReceiptList(inputData.HpId, inputData.SeikyuYm, ConvertToInputAdvancedSearch(inputData));
            var TotalCount = result?.Count ?? 0;
            var DisplayTensu = result?.Sum(item => item.Tensu) ?? 0;

            // カーソル・ページネーション
            result = PaginateByCursor(result,  ConvertToInputAdvancedSearch(inputData));
            return new ReceiptListAdvancedSearchOutputData(result, TotalCount, DisplayTensu, ReceiptListAdvancedSearchStatus.Successed);
        }
        finally
        {
            _receiptRepository.ReleaseResource();
        }
    }

    public ReceiptListAdvancedSearchInput ConvertToInputAdvancedSearch(ReceiptListAdvancedSearchInputData inputData)
    {
        var itemList = inputData.ItemList.Select(item => new ItemSearchModel(
                                                                                item.ItemCd,
                                                                                item.InputName,
                                                                                item.RangeSeach,
                                                                                item.Amount,
                                                                                item.OrderStatus,
                                                                                item.IsComment
                                                                            ))
                                                                            .ToList();

        var byomeiCdList = inputData.ByomeiCdList.Select(item => new SearchByoMstModel(
                                                                                            item.ByomeiCd,
                                                                                            item.InputName,
                                                                                            item.IsComment
                                                                                       ))
                                                                                       .ToList();

        return new ReceiptListAdvancedSearchInput(
                                                    inputData.IsAdvanceSearch,
                                                    inputData.Tokki,
                                                    inputData.HokenSbts,
                                                    inputData.IsAll,
                                                    inputData.IsNoSetting,
                                                    inputData.IsSystemSave,
                                                    inputData.IsSave1,
                                                    inputData.IsSave2,
                                                    inputData.IsSave3,
                                                    inputData.IsTempSave,
                                                    inputData.IsDone,
                                                    inputData.ReceSbtCenter,
                                                    inputData.ReceSbtRight,
                                                    inputData.HokenHoubetu,
                                                    inputData.Kohi1Houbetu,
                                                    inputData.Kohi2Houbetu,
                                                    inputData.Kohi3Houbetu,
                                                    inputData.Kohi4Houbetu,
                                                    inputData.IsIncludeSingle,
                                                    inputData.HokensyaNoFrom,
                                                    inputData.HokensyaNoTo,
                                                    inputData.HokensyaNoFromLong,
                                                    inputData.HokensyaNoToLong,
                                                    inputData.PtId,
                                                    inputData.PtIdFrom,
                                                    inputData.PtIdTo,
                                                    (PtIdSearchOptionEnum)inputData.PtSearchOption,
                                                    inputData.TensuFrom,
                                                    inputData.TensuTo,
                                                    inputData.LastRaiinDateFrom,
                                                    inputData.LastRaiinDateTo,
                                                    inputData.BirthDayFrom,
                                                    inputData.BirthDayTo,
                                                    itemList,
                                                    (QuerySearchEnum)inputData.ItemQuery,
                                                    inputData.IsOnlySuspectedDisease,
                                                    (QuerySearchEnum)inputData.ByomeiQuery,
                                                    byomeiCdList,
                                                    inputData.IsFutanIncludeSingle,
                                                    inputData.FutansyaNoFromLong,
                                                    inputData.FutansyaNoToLong,
                                                    inputData.KaId,
                                                    inputData.DoctorId,
                                                    inputData.Name,
                                                    inputData.IsTestPatientSearch,
                                                    inputData.IsNotDisplayPrinted,
                                                    inputData.GroupSearchModels,
                                                    inputData.SeikyuKbnAll,
                                                    inputData.SeikyuKbnDenshi,
                                                    inputData.SeikyuKbnPaper
                                                )
        {
            FilterType = inputData.FilterType,
            SortKey = inputData.SortKey,
            SortOrder = inputData.SortOrder,
            Limit = inputData.Limit,
            CursorPtId = inputData.CursorPtId,
            CursorSinYm = inputData.CursorSinYm,
            CursorHokenId = inputData.CursorHokenId
        };
    }

    private List<ReceiptListModel>  PaginateByCursor(List<ReceiptListModel>  searchdata, ReceiptListAdvancedSearchInput searchModel){
        // キー指定でポジション取得
        int startIndex = 0;
        if (searchModel.CursorPtId.HasValue && searchModel.CursorSinYm.HasValue && searchModel.CursorHokenId.HasValue)
        {
            long cursorPtId = searchModel.CursorPtId.Value;
            int cursorSinYm = searchModel.CursorSinYm.Value;
            int cursorHokenId = searchModel.CursorHokenId.Value;

            startIndex = searchdata.FindIndex(x =>
                x.PtId == cursorPtId &&
                x.SinYm == cursorSinYm &&
                x.HokenId == cursorHokenId
            );

            if (startIndex < 0) startIndex = 0; // 見つからなければ先頭から
            else startIndex++; // カーソル値の「次」から取得
        }
        // 3. 指定件数だけ取得(1000件をデフォルト値とする)
        int limit = searchModel.Limit ?? 1000;
        var result = searchdata.Skip(startIndex).Take(limit).ToList();

        return result;
    }
}
