using Helper.Exceptions;
using Infrastructure.Interfaces;
using Infrastructure.Options;
using Microsoft.Extensions.Options;
using UseCase.AgentSetting.GetAgentDownloadLink;

namespace Interactor.AgentSetting
{
    public class GetAgentDownloadLinkInteractor : IGetAgentDownloadLinkInputPort
    {
        private readonly IAmazonS3Service _amazonS3Service;
        private readonly AmazonS3Options _options;

        public GetAgentDownloadLinkInteractor(IAmazonS3Service amazonS3Service, IOptions<AmazonS3Options> optionsAccessor)
        {
            _amazonS3Service = amazonS3Service;
            _options = optionsAccessor.Value;
        }

        public GetAgentDownloadLinkOutputData Handle(GetAgentDownloadLinkInputData input)
        {
            try
            {
                if (!IsValidDeviceType(input.DeviceType))
                {
                    return CreateErrorResponse(GetAgentDownloadLinkStatus.InvalidDeviceType);
                }

                if (!IsSupportedDeviceType(input.DeviceType))
                {
                    return CreateErrorResponse(GetAgentDownloadLinkStatus.NotImplemented);
                }

                var fileExtension = GetFileExtension(input.DeviceType);
                var environment = ExtractEnvironmentFromBucket();

                if (string.IsNullOrEmpty(environment))
                {
                    return CreateErrorResponse(GetAgentDownloadLinkStatus.NotImplemented);
                }

                var fileName = GenerateFileName(environment, fileExtension, input.DeviceType);
                var s3Key = $"SmartKarteAgent/{fileName}";

                var presignedUrl = _amazonS3Service.GetPreSignedUrlCommons(s3Key);

                if (string.IsNullOrEmpty(presignedUrl))
                {
                    throw new Exception("Failed to get presigned URL from S3 service");
                }

                return new GetAgentDownloadLinkOutputData(presignedUrl, fileName, GetAgentDownloadLinkStatus.Success);
            }
            catch (Exception ex)
            {
                string name = GetType().Namespace ?? string.Empty;
                throw new InteractorCustomException(name, ex);
            }
        }

        private bool IsValidDeviceType(string deviceType)
        {
            return !string.IsNullOrEmpty(deviceType) &&
                   (deviceType.ToLower() == "win" || deviceType.ToLower() == "mac");
        }

        private bool IsSupportedDeviceType(string deviceType)
        {
            return deviceType.ToLower() == "win" || deviceType.ToLower() == "mac";
        }

        private string GetFileExtension(string deviceType)
        {
            return deviceType.ToLower() == "win" ? "exe" : "pkg";
        }

        private string ExtractEnvironmentFromBucket()
        {
            var bucketName = _options.BucketName;

            if (bucketName.Contains("dev2"))
                return "Dev2";

            if (bucketName.Contains("stg2"))
                return "Stg2";

            // TODO: Production environment not supported yet
            // if (bucketName.Contains("prd"))
            //     return "Prd";

            return "";
        }

        private string GenerateFileName(string environment, string fileExtension, string deviceType)
        {
            var bucketName = _options.BucketName;
            var shouldIncludeEnvironment = bucketName.Contains("dev2") || bucketName.Contains("stg2");

            if (!shouldIncludeEnvironment)
            {
                return $"SmartKarteAgent.{fileExtension}";
            }

            var separator = deviceType.ToLower() == "mac" ? "-" : "_";
            return $"SmartKarteAgent{separator}{environment}.{fileExtension}";
        }

        private GetAgentDownloadLinkOutputData CreateErrorResponse(GetAgentDownloadLinkStatus status)
        {
            return new GetAgentDownloadLinkOutputData(string.Empty, string.Empty, status);
        }
    }
}
