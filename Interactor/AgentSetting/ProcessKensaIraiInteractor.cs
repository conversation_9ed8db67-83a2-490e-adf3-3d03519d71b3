﻿using Domain.Models.AgentSetting;
using Domain.Models.KensaCenterPartnership;
using Domain.Models.KensaIrai;
using Domain.Models.KensaIrai.GetIraiFileDataDummy;
using Domain.Models.Renkei;
using Domain.Models.RenkeiOutputData;
using Domain.Models.SystemConf;
using Entity.Tenant;
using Helper.Common;
using Reporting.Kensalrai.DB;
using Reporting.Kensalrai.Service;
using System.Text.RegularExpressions;
using UseCase.AgentSetting.GetIraiFileDataDummy;
using KensaCenterMstModel = Domain.Models.KensaIrai.KensaCenterMstModel;
using KensaInfDetailModel = Reporting.Kensalrai.Model.KensaInfDetailModel;
using KensaInfModel = Reporting.Kensalrai.Model.KensaInfModel;
using KensaIraiDetailModel = Reporting.Kensalrai.Model.KensaIraiDetailModel;
using KensaIraiModel = Reporting.Kensalrai.Model.KensaIraiModel;

namespace Interactor.AgentSetting
{
    public class ProcessKensaIraiInteractor : IProcessKensaIraiInputPort
    {
        private readonly IAgentSettingRepository _agentSettingRepository;
        private readonly IKensaIraiRepository _kensaIraiRepository;
        private readonly ISystemConfRepository _systemConfRepository;
        private readonly IKensaIraiCoReportService _kensaIraiCoReportService;
        private readonly ICoKensaIraiFinder _coKensaIraiFinder;
        private readonly IRenkeiRepository _renkeiRepository;
        private readonly IRenkeiOutputDataRepository _renkeiOutputDataRepository;
        private readonly IKensaCenterPartnershipRepository _kensaCenterPartnershipRepository;

        public ProcessKensaIraiInteractor(IAgentSettingRepository agentSettingRepository, IKensaIraiRepository kensaIraiRepository, ISystemConfRepository systemConfRepository, IKensaIraiCoReportService kensaIraiCoReportService, ICoKensaIraiFinder coKensaIraiFinder, IRenkeiRepository renkeiRepository, IRenkeiOutputDataRepository renkeiOutputDataRepository, IKensaCenterPartnershipRepository kensaCenterPartnershipRepository)
        {
            _agentSettingRepository = agentSettingRepository;
            _kensaIraiRepository = kensaIraiRepository;
            _systemConfRepository = systemConfRepository;
            _kensaIraiCoReportService = kensaIraiCoReportService;
            _coKensaIraiFinder = coKensaIraiFinder;
            _renkeiRepository = renkeiRepository;
            _renkeiOutputDataRepository = renkeiOutputDataRepository;
            _kensaCenterPartnershipRepository = kensaCenterPartnershipRepository;
        }

        public ProcessKensaIraiOuputData Handle(ProcessKensaIraiInputData inputData)
        {
            try
            {
                var iraiFiles = new List<IraiFileModel>();
                Execute(inputData.HpId, inputData.PtId, inputData.SinDate, inputData.RaiinNo, inputData.UserId, inputData.EventCd ?? string.Empty, inputData.MachineName ?? string.Empty, ref iraiFiles);
                if(iraiFiles.Count > 0) 
                    return new ProcessKensaIraiOuputData(iraiFiles, ProcessKensaIraiStatus.Success);
                return new ProcessKensaIraiOuputData(new(), ProcessKensaIraiStatus.NoData);
            }
            catch
            {
                return new ProcessKensaIraiOuputData(new(), ProcessKensaIraiStatus.Fail);
            }
            finally
            {
                _agentSettingRepository.ReleaseResource();
                _kensaIraiRepository.ReleaseResource();
                _systemConfRepository.ReleaseResource();
                _coKensaIraiFinder.ReleaseResource();
                _kensaCenterPartnershipRepository.ReleaseResource();
            }
        }

        /// <summary>
        /// オーダー時検査依頼連携　実行
        /// </summary>
        /// <param name="ptId">患者ID</param>
        /// <param name="sinDate">診療日</param>
        /// <param name="raiinNo">来院番号</param>
        public void Execute(int hpId, long ptId, int sinDate, long raiinNo, int userId, string eventCd, string machineName, ref List<IraiFileModel> iraiFileModels)
        {
            try
            {
                int iraiDate;
                string kensaTime = string.Empty;
                /// <summary>
                /// オーダー情報（院外検査のみ）
                /// </summary>
                List<OdrInfModel>? odrInfModels = null;
                /// <summary>
                /// オーダー詳細情報（院外検査のみ）
                /// </summary>
                List<OdrInfDetailModel>? odrInfDetailModels = null;
                /// <summary>
                /// 検査依頼情報
                /// </summary>
                List<KensaInfModel>? kensaInfModels = null;
                /// <summary>
                /// 検査依頼詳細情報
                /// </summary>
                List<KensaInfDetailModel>? kensaInfDetailModels = null;
                /// <summary>
                /// 患者情報
                /// </summary>
                PtInfModel? ptInfModel = null;
                /// <summary>
                /// 来院情報
                /// </summary>
                RaiinInfModel? raiinInfModel = null;
                /// <summary>
                /// ファイル出力先
                /// </summary>
                List<OutputPathModel> listOutputPath = new();

                // 初期処理（設定の取得、および、チェック）
                if (Init(hpId, sinDate, eventCd, machineName, ref listOutputPath, out iraiDate, out kensaTime))
                {
                    // データ取得
                    GetData(
                        hpId: hpId,
                        ptId: ptId,
                        ptInf: ref ptInfModel,
                        raiinInfModel: ref raiinInfModel,
                        odrInfModels: ref odrInfModels,
                        odrInfDetailModels: ref odrInfDetailModels,
                        kensaInfModels: ref kensaInfModels,
                        kensaInfDetailModels: ref kensaInfDetailModels,
                        listOutputPath: listOutputPath,
                        sinDate: sinDate,
                        raiinNo: raiinNo
                    );

                    // 取得したデータをチェック
                    if (ptInfModel == null)
                    {
                        return;
                    }

                    if (odrInfModels == null || odrInfModels.Any() == false || odrInfDetailModels == null || odrInfDetailModels.Any() == false)
                    {
                        return;
                    }

                    // 依頼データを作成する
                    MakeIraiData(
                        hpId: hpId,
                        ptId: ptId,
                        iraiDate: iraiDate,
                        raiinNo: raiinNo,
                        userId: userId,
                        sinDate: sinDate,
                        machineName: machineName,
                        kensaTime: kensaTime,
                        kensaInfDetailModels: kensaInfDetailModels,
                        odrInfModels: odrInfModels,
                        odrInfDetailModels: odrInfDetailModels,
                        kensaInfModels: kensaInfModels,
                        ptInfModel: ptInfModel,
                        raiinInfModel: raiinInfModel,
                        listOutputPath: listOutputPath,
                        iraiFiles: ref iraiFileModels);
                }
            }
            catch (Exception e)
            {
                return;
            }

        }

        /// <summary>
        /// 初期処理（設定の取得、およびチェック）
        /// </summary>
        bool Init(int hpId, int sinDate, string eventCd, string machineName, ref List<OutputPathModel> listOutputPath, out int iraiDate, out string kensaTime)
        {
            var now = DateTime.Now;
            iraiDate = CIUtil.DateTimeToInt(now);
            kensaTime = now.ToString("HH:mm");

            var listRenkei = _renkeiRepository.GetRenkeiModels(hpId, machineName, eventCd).Where(e => e.RenkeiId == 1000).ToList();
            int systemDate = CIUtil.DateTimeToInt(DateTime.Now);
            for(int i = 0; i < listRenkei.Count; i++)
            {
                if (listRenkei[i].Param.Contains("/onlydojitu") && sinDate != systemDate)
                    listRenkei.Remove(listRenkei[i]);
                var lastPathDuplicate = listRenkei.LastOrDefault(e => e.Path == listRenkei[i].Path && e.ConfSeqNo != listRenkei[i].ConfSeqNo);
                if (listRenkei.IndexOf(lastPathDuplicate) >= i) listRenkei.Remove(listRenkei[i]);
            }

            if (!listRenkei.Any()) return false;

            string patternCenterCd = @"/center='(.+?)'";
            foreach (var item in listRenkei)
            {
                Match match = Regex.Match(item.Param, patternCenterCd);
                item.IsMatchRegex = match.Success;
                if (item.IsMatchRegex)
                {
                    var outputPath = new OutputPathModel(match.Groups[1].Value, item.Path);
                    listOutputPath.Add(outputPath);
                }
            }

            if(listOutputPath.Any())
            {
                var listCenterCdExist = _coKensaIraiFinder.GetListCenterCd(hpId, sinDate, listOutputPath.Select(e => e.CenterCd).ToList());
                if (!listCenterCdExist.Any()) return false;
                foreach(var item in listOutputPath.Where(e => listCenterCdExist.Contains(e.CenterCd)).ToList())
                {
                    item.IsExist = true;
                }
            }

            if (listRenkei.Any(e => !e.IsMatchRegex))
            {
                var listCenterCdPartnership = _kensaCenterPartnershipRepository.GetKensaCenterPartnershipBySinDate(hpId, sinDate);
                if (listCenterCdPartnership.Any() && listCenterCdPartnership.Count == 1)
                {
                    foreach(var renkei in listRenkei.Where(e => !e.IsMatchRegex).ToList())
                    {
                        var outputPath = new OutputPathModel(listCenterCdPartnership.FirstOrDefault().CenterCd, renkei.Path, true);
                        listOutputPath.Add(outputPath);
                    }
                }
                else
                {
                    return false;
                }
            }
            listOutputPath = listOutputPath.Where(e => e.IsExist).ToList();
            return true;
        }

        /// <summary>
        /// データ取得（患者情報、来院情報、オーダー情報、検査情報）
        /// </summary>
        private void GetData(
            int hpId,
            long ptId,
            ref PtInfModel? ptInf,
            ref RaiinInfModel? raiinInfModel,
            ref List<OdrInfModel>? odrInfModels,
            ref List<OdrInfDetailModel>? odrInfDetailModels,
            ref List<KensaInfModel>? kensaInfModels,
            ref List<KensaInfDetailModel>? kensaInfDetailModels,
            List<OutputPathModel> listOutputPath,
            int sinDate, long raiinNo)
        {
            var listCenterCd = listOutputPath.Select(e => e.CenterCd).ToList();
            // 患者情報取得
            ptInf = _kensaIraiRepository.GetPtInf(hpId, ptId, sinDate);

            // 来院情報取得
            raiinInfModel = _kensaIraiRepository.GetRaiinInf(hpId, ptId, sinDate, raiinNo);

            // オーダー情報取得
            odrInfModels = _kensaIraiRepository.GetIngaiKensaOdrInf(hpId, ptId, sinDate, raiinNo);
            odrInfDetailModels = _kensaIraiRepository.GetIngaiKensaOdrInfDetail(hpId, ptId, sinDate, raiinNo, listCenterCd);

            // 既存の検査情報があれば取得
            kensaInfModels = _coKensaIraiFinder.GetKensaInf(hpId, ptId, raiinNo, listCenterCd);
            kensaInfDetailModels = _coKensaIraiFinder.GetKensaInfDetail(hpId, ptId, raiinNo, listCenterCd);
        }

        /// <summary>
        /// 依頼データを作成する
        /// </summary>
        private void MakeIraiData(
            int hpId,
            long ptId,
            int iraiDate,
            long raiinNo,
            int userId,
            int sinDate,
            string? machineName,
            string kensaTime,
            //List<KensaCenterMstModel> kensaCenterMst,
            List<KensaInfDetailModel> kensaInfDetailModels,
            List<OdrInfModel> odrInfModels,
            List<OdrInfDetailModel> odrInfDetailModels,
            List<KensaInfModel> kensaInfModels,
            PtInfModel ptInfModel,
            RaiinInfModel raiinInfModel,
            List<OutputPathModel> listOutputPath,
            ref List<IraiFileModel> iraiFiles
            )
        {
            long keyNo = 0;
            var listOdrInfDetail = new List<OdrInfDetail>();
            var japanDate = CIUtil.GetJapanDateTimeNow();
            // 透析区分別に検査情報を生成する
            for (int toseki = 0; toseki <= 2; toseki++)
            {
                // 依頼キーを取得（-1: エラー、0: 新規追加、>0: 既存の依頼コード）
                long iraiCd = GetIraiCd(toseki, kensaInfModels);

                if (iraiCd >= 0)
                {
                    if (kensaInfDetailModels.Any(p => string.IsNullOrEmpty(p.ResultVal) == false))
                    {
                        // 既に検査結果が入力されている検査があった場合はエラー
                        //Log.WriteLogError(_module, this, conFncName, new Exception($"結果取り込み済みの項目が存在するため、依頼ファイルを作成できません iraicd:{iraiCd}"));
                        return;
                    }
                    else
                    {
                        if (odrInfModels.Any(p => p.TosekiKbn == toseki))
                        {
                            int firstOdrId = odrInfModels.Find(p => p.TosekiKbn == toseki).CreateId;

                            // 至急区分を取得する
                            int sikyu = GetSikyuKbn(toseki, odrInfModels);

                            // 透析区分をキーに、対象となるオーダー詳細を取得する
                            List<OdrInfDetailModel> targetOdrDtl = GetTargetOdrInfDetail(toseki, odrInfModels, odrInfDetailModels);

                            if (targetOdrDtl.Any())
                            {
                                // 依頼詳細レコード削除
                                foreach (KensaInfDetailModel delKensaDtl in kensaInfDetailModels.FindAll(p => p.IraiCd == iraiCd))
                                {
                                    delKensaDtl.IsDeleted = 1;
                                }

                                #region 検査情報レコード 追加 or キー設定
                                keyNo++;

                                if (iraiCd == 0)
                                {
                                    foreach(var item in listOutputPath)
                                    {
                                        KensaInfModel addkensaInf = new KensaInfModel(new KensaInf());

                                        addkensaInf.IsAddNew = true;
                                        addkensaInf.HpId = hpId;
                                        addkensaInf.PtId = ptId;
                                        addkensaInf.IraiDate = iraiDate;
                                        addkensaInf.RaiinNo = raiinNo;
                                        addkensaInf.InoutKbn = 1;
                                        addkensaInf.Status = 0;
                                        addkensaInf.TosekiKbn = toseki;
                                        addkensaInf.SikyuKbn = sikyu;
                                        addkensaInf.ResultCheck = 0;
                                        addkensaInf.CenterCd = item.CenterCd;
                                        addkensaInf.KensaTime = kensaTime;
                                        // iraiCd = 0なら追加
                                        addkensaInf.KeyNo = keyNo;
                                        kensaInfModels.Add(addkensaInf);
                                    }    
                                }
                                else
                                {
                                    // 既存のレコードを使用する場合は、キーだけ設定
                                    var kensaInf = kensaInfModels.Find(p => p.IraiCd == iraiCd);
                                    kensaInf.KeyNo = keyNo;
                                    kensaInf.TosekiKbn = toseki;
                                    kensaInf.SikyuKbn = sikyu;

                                    kensaInf.IsUpdate = true;
                                }
                                #endregion

                                #region 検査情報詳細レコード　追加

                                // 詳細レコード連番
                                int seqNo = 0;

                                foreach (OdrInfDetailModel odrDtl in targetOdrDtl)
                                {
                                    KensaInfDetailModel addKensaDtl = new KensaInfDetailModel(new KensaInfDetail());

                                    addKensaDtl.IsAddNew = true;
                                    addKensaDtl.HpId = hpId;
                                    addKensaDtl.PtId = ptId;
                                    addKensaDtl.IraiDate = iraiDate;
                                    addKensaDtl.RaiinNo = raiinNo;
                                    seqNo++;
                                    addKensaDtl.SeqNo = seqNo;
                                    addKensaDtl.KensaItemCd = odrDtl.KensaItemCd;
                                    addKensaDtl.commonCenterKensaMst = odrDtl.CommonCenterKensaMst;

                                    addKensaDtl.KeyNo = keyNo;

                                    if (iraiCd > 0)
                                    {
                                        // 既存の依頼コードを使用する場合は、セットする
                                        addKensaDtl.IraiCd = iraiCd;
                                    }

                                    var odrInfDtl = odrDtl.OdrInfDetail;
                                    odrInfDtl.JissiKbn = 1;
                                    odrInfDtl.JissiDate = japanDate;
                                    odrInfDtl.JissiId = userId;
                                    odrInfDtl.JissiMachine = machineName;
                                    odrInfDtl.ReqCd = iraiCd.ToString();

                                    kensaInfDetailModels.Add(addKensaDtl);
                                    listOdrInfDetail.Add(odrInfDtl);
                                }
                                #endregion
                            }
                        }
                    }
                }
            }
            var listOdrInfDetailDst = listOdrInfDetail.DistinctBy(o => new { o.HpId, o.RaiinNo, o.RpNo, o.RpEdaNo, o.RowNo, o.Edition }).ToList();
            // DBに反映する
            _coKensaIraiFinder.SaveKensaInf(hpId, userId, kensaInfModels, kensaInfDetailModels, listOdrInfDetailDst);

            // 依頼ファイルを作成する
            iraiFiles = SaveIraiFile(
                hpId: hpId,
                sinDate: sinDate,
                userId: userId,
                kensaInfModels: kensaInfModels,
                kensaInfDetailModels: kensaInfDetailModels,
                ptInfModel: ptInfModel,
                raiinInfModel: raiinInfModel,
                listOutputPath: listOutputPath
                );

        }

        /// <summary>
        /// 依頼ファイルを保存する
        /// </summary>
        List<IraiFileModel> SaveIraiFile(
            int hpId,
            int sinDate,
            int userId,
            List<KensaInfModel> kensaInfModels,
            List<KensaInfDetailModel> kensaInfDetailModels,
            PtInfModel ptInfModel,
            RaiinInfModel raiinInfModel,
            List<OutputPathModel> listOutputPath
            )
        {
            var iraiFiles = new List<IraiFileModel>();
            var odrkensaIraiKaCodeParam = _systemConfRepository.GetSettingParams(100019, 8, hpId);
            // KeyNo > 0 ・・・ 依頼対象の検査情報レコード
            foreach (KensaInfModel kensaInf in kensaInfModels.FindAll(p => p.KeyNo > 0))
            {
                int seqNo = 0;

                // detail生成
                List<KensaIraiDetailModel> addKensaIraiDtls = new List<KensaIraiDetailModel>();
                foreach (KensaInfDetailModel kensaDtl in kensaInfDetailModels.FindAll(p => p.KeyNo == kensaInf.KeyNo))
                {
                    addKensaIraiDtls.Add(new KensaIraiDetailModel(true, 0, 0, 0, seqNo, kensaDtl.commonCenterKensaMst, kensaDtl.CenterCd));
                }

                var isAddKensaIraiDtls = addKensaIraiDtls.Any();
                if (isAddKensaIraiDtls)
                {
                    var listContenFile = new List<ContentFileModel>();
                    foreach (var outputPath in listOutputPath.Where(e => e.CenterCd == kensaInf.CenterCd).ToList())
                    {
                        // 検査依頼データ生成
                        KensaIraiModel addKensaIrai =
                            new KensaIraiModel(
                                kensaInf.IraiCd, kensaInf.TosekiKbn, kensaInf.SikyuKbn, ptInfModel.PtInf, raiinInfModel.RaiinInf, addKensaIraiDtls.Where(e => e.CenterCd == outputPath.CenterCd).ToList());
                        addKensaIrai.UpdateTime = kensaInf.UpdateDate.ToString("HHmm");
                        addKensaIrai.TantoKanaName = raiinInfModel.TantoKanaName;

                        // ファイルの内容を取得
                        List<string> output = _kensaIraiCoReportService.GetIraiFileData(outputPath.CenterCd, new List<KensaIraiModel> { addKensaIrai }, 0);
                        outputPath.Path = ReplaceParam(sinDate, outputPath.Path, kensaInf.IraiCd, ptInfModel);
                        bool outputFile = false;
                        var dataCompare = string.Join("", output);
                        int renkeiType = 1;
                        var renkeiOutputData = _renkeiOutputDataRepository.GetRenkeiOutputDataModel(hpId, renkeiType, ptInfModel.PtInf.PtId, sinDate, raiinInfModel.RaiinInf.RaiinNo, outputPath.CenterCd);
                        if (!dataCompare.Equals(renkeiOutputData.Data))
                        {
                            _renkeiOutputDataRepository.SaveRenkeiOutputData(hpId, renkeiType, ptInfModel.PtInf.PtId, sinDate, raiinInfModel.RaiinInf.RaiinNo, outputPath.CenterCd, dataCompare, userId);
                            outputFile = true;
                        }

                        var contentFile = new ContentFileModel(outputPath.Path, output, outputFile);
                        listContenFile.Add(contentFile);
                    }

                    iraiFiles.Add(new IraiFileModel(listContenFile));
                }
            }

            return iraiFiles;
        }

        /// <summary>
        /// 依頼コードを取得する
        /// 透析区分をキーに検査情報を検索
        /// 0件の場合・・・新たに検査情報を作成
        /// 1件の場合・・・既存の依頼コードを流用する
        /// 2件以上の場合・・・エラーとする
        /// </summary>
        /// <param name="toseki"></param>
        /// <returns>
        /// -1: 既存データ複数あり
        ///  0: 既存データなし（新規追加）
        /// >0: 既存の依頼コード
        /// </returns>
        private long GetIraiCd(int toseki, List<KensaInfModel> kensaInfModels)
        {
            long iraiCd = -1;

            if (kensaInfModels.Any(p => p.TosekiKbn == toseki) == false)
            {
                // 条件に合う検査依頼情報がない場合は、新規追加する
                iraiCd = 0;
            }
            else if (kensaInfModels.Count(p => p.TosekiKbn == toseki) == 1)
            {
                // 条件に合う検査依頼情報が1件の場合は、依頼コードをそのまま使用する
                iraiCd = kensaInfModels.Find(p => p.TosekiKbn == toseki).IraiCd;
            }
            else
            {
                // 条件に合う検査依頼情報が複数の場合は、エラーとする
                List<string> iraiCds =
                    kensaInfModels.Where(p => p.TosekiKbn == toseki).Select(p => p.IraiCd.ToString()).ToList();
            }

            return iraiCd;
        }

        /// <summary>
        /// 至急区分を取得する
        /// 透析区分が一致するオーダー情報の中に、1つでも至急区分=1のレコードがあれば1を返す
        /// </summary>
        /// <param name="toseki">透析区分</param>
        /// <returns>1-至急あり</returns>
        private int GetSikyuKbn(int toseki, List<OdrInfModel> odrInfModels)
        {
            int ret = 0;

            if (odrInfModels.Any(p => p.TosekiKbn == toseki && p.SikyuKbn == 1))
            {
                ret = 1;
            }

            return ret;
        }

        /// <summary>
        /// 透析区分が一致するオーダー詳細を取得する
        /// </summary>
        /// <param name="toseki"></param>
        /// <returns></returns>
        private List<OdrInfDetailModel> GetTargetOdrInfDetail(int toseki, List<OdrInfModel> odrInfModels, List<OdrInfDetailModel> odrInfDetailModels)
        {
            List<OdrInfDetailModel> results = new List<OdrInfDetailModel>();

            foreach (OdrInfModel odrInf in odrInfModels.FindAll(p => p.TosekiKbn == toseki))
            {
                foreach (OdrInfDetailModel odrDtl in odrInfDetailModels.FindAll(p => p.RpNo == odrInf.RpNo && p.RpEdaNo == odrInf.RpEdaNo))
                {
                    results.Add(odrDtl);
                }
            }

            return results;
        }

        /// <summary>
        /// パラメータ置換処理
        /// </summary>
        /// <param name="param">置換したいパラメータ</param>
        /// <param name="iraiKey">依頼キー</param>
        /// <param name="ptInf">患者情報</param>
        /// <returns></returns>
        private string ReplaceParam(int sinDate, string param, long iraiKey, PtInfModel ptInf)
        {
            #region local method
            // param置換処理
            void Replace(string oldValue, string newValue)
            {
                param = param.Replace($"\"{oldValue}\"", newValue);
            }

            // 指定のbaseStrで始まるパラメータがparamに存在する場合、その値を返す
            string GetFieldName(string baseStr)
            {
                int posStart = 0;
                int posEnd = 0;
                string repStr = "";
                posStart = param.IndexOf($"\"{baseStr}");
                posEnd = param.IndexOf("\"", posStart + 1);
                if (posStart >= 0 && posEnd >= 0)
                {
                    repStr = param.Substring(posStart, posEnd - posStart + 1);
                    repStr = repStr.Replace("\"", "");
                }

                return repStr;
            }

            // パラメータ名に長さ等を含むパラメータを取得し、パラメータ名とそこに含まれる数値を返す
            (string repStr, int len) GetLengthFieldData(string baseStr)
            {
                string repStr = GetFieldName(baseStr);
                int len = 0;
                if (string.IsNullOrEmpty(repStr) == false)
                {
                    len = CIUtil.StrToIntDef(repStr.Substring($"{baseStr}".Length, repStr.Length - $"{baseStr}".Length), 0);
                }

                return (repStr, len);
            }

            #endregion
            string rep;
            int length;

            // 患者番号
            Replace("patientid", $"{ptInf.PtNum}");

            (rep, length) = GetLengthFieldData("patientid_z");
            if (string.IsNullOrEmpty(rep) == false)
            {
                if (length == 0)
                {
                    length = 9;
                }
                Replace(rep, ptInf.PtNum.ToString().PadLeft(length, '0'));
            }

            // 依頼コード
            (rep, length) = GetLengthFieldData("iraikey_z");
            Replace(rep, iraiKey.ToString().PadLeft(length, '0'));

            // システム日付
            /// <summary>
            /// 時間系パラメータ置換用
            /// </summary>
            List<(string, string)> ReplaceTimeParamString =
                new List<(string, string)> {
                ("sysdate", "yyyy/MM/dd HH:mm:ss"),
                ("sysdate_2", "yyyyMMddHHmmss"),
                ("sysdate_3", "yyyyMMddHHmm"),
                ("sysdate_4", "yyyyMMdd-HHmmzzz"),
                ("sysdate_hh", "HH"),
                ("sysdate_nn", "mm"),
                ("sysdate_ss", "ss"),
                ("sysdate_zzz", "fff")
                    };

            DateTime dt = DateTime.Now;

            foreach ((string oldVal, string newVal) in ReplaceTimeParamString)
            {
                Replace(oldVal, dt.ToString(newVal));
            }
            Replace("sysdate_yyyy", dt.ToString("yyyy"));
            Replace("sysdate_yy", dt.ToString("yyyy").Substring(2, 2));
            Replace("sysdate_mm", dt.ToString("MM"));
            Replace("sysdate_dd", dt.ToString("dd"));

            // カレンダー日付
            Replace("calendate", $"{sinDate}");

            return param;
        }
    }
}
