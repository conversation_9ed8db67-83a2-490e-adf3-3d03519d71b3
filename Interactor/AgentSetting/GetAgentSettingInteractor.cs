﻿using Domain.Models.AgentSetting;
using Infrastructure.Interfaces;
using UseCase.AgentSetting.GetAgentSetting;

namespace Interactor.AgentSetting
{
    public class GetAgentSettingInteractor : IGetAgentSettingInputPort
    {
        private readonly IAgentSettingRepository _agentSettingRepository;
        private readonly IAmazonS3Service _amazonS3Service;
        public GetAgentSettingInteractor(IAgentSettingRepository agentSettingRepository, IAmazonS3Service amazonS3Service)
        {
            _agentSettingRepository = agentSettingRepository;
            _amazonS3Service = amazonS3Service;
        }

        public GetAgentSettingOutputData Handle(GetAgentSettingInputData input)
        {
            try
            {
                AgentSettingModel data = new AgentSettingModel();
                if (input.IncludeSetting)
                {
                    data = _agentSettingRepository.GetAgentSetting(input.HpId, input.Host);
                    if (data == null || !Int32.TryParse(data?.Port, out int port)) return new GetAgentSettingOutputData(new AgentSettingModel(), GetAgentSettingStatus.Nodata);
                }
                if (input.IncludeVersion)
                {
                    var keyVersion = "SmartKarteAgent/SmartKarteAgent_Version.txt";
                    data.LinkVersion = _amazonS3Service.GetPreSignedUrlCommons(keyVersion);
                    if (!string.IsNullOrEmpty(input.FileNameApp))
                    {
                        var keyApp = $"SmartKarteAgent/{input.FileNameApp}";
                        data.LinkApp = _amazonS3Service.GetPreSignedUrlCommons(keyApp);
                    }

                }

                return new GetAgentSettingOutputData(data, GetAgentSettingStatus.Success);
            }
            finally
            {
                _agentSettingRepository.ReleaseResource();
            }
        }
    }
}
