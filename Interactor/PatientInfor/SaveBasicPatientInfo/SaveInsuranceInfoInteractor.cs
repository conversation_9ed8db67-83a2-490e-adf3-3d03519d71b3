﻿using Domain.Models.Insurance;
using Domain.Models.Online;
using Domain.Models.Online.QualificationConfirmation;
using Domain.Models.PatientInfor;
using Domain.Models.SystemConf;
using Helper.Common;
using Infrastructure.Logger;
using Microsoft.AspNetCore.Mvc;
using System.Globalization;
using System.Xml.Serialization;
using UseCase.PatientInfor.Save;
using UseCase.PatientInfor.SaveBasicInfo.SaveInsuranceInfo;

namespace Interactor.PatientInfor.SaveBasicPatientInfo
{
    public class SaveInsuranceInfoInteractor : ISaveInsuranceInfoInputPort
    {
        private readonly IPatientInforRepository _patientInforRepository;
        private readonly ISystemConfRepository _systemConfRepository;
        private readonly ILoggingHandler? _loggingHandler;
        private readonly IInsuranceRepository _insuranceRepository;

        public SaveInsuranceInfoInteractor(IPatientInforRepository patientInforRepository, ISystemConfRepository systemConfRepository, IInsuranceRepository insuranceRepository)
        {
            _patientInforRepository = patientInforRepository;
            _systemConfRepository = systemConfRepository;
            //_loggingHandler = loggingHandler;
            _insuranceRepository = insuranceRepository;
        }
        public SaveInsuranceInfoOutputData Handle(SaveInsuranceInfoInputData inputData)
        {
            try
            {
                PatientInforModel patientInforModel = new();
                var model = new OnlineConfirmationHistoryModel(0, inputData.PtId, DateTime.MinValue, inputData.OnlineConfirmationHistory.ConfirmationType, inputData.OnlineConfirmationHistory.InfoConsFlg, inputData.OnlineConfirmationHistory.ConfirmationResult, inputData.OnlineConfirmationHistory.PrescriptionIssueType, inputData.OnlineConfirmationHistory.UketukeStatus, inputData.HpId);
                if(inputData.IsConfirmOnline)
                {
                    var xmlObject = new XmlSerializer(typeof(QCXmlMsgResponse)).Deserialize(new StringReader(inputData.OnlineConfirmationHistory.ConfirmationResult)) as QCXmlMsgResponse;
                    if (xmlObject != null)
                    {
                        DateTime confirmDateInsert;
                        var onlineConfirmationDate = xmlObject.MessageHeader.ProcessExecutionTime;
                        var isConvert = DateTime.TryParseExact(onlineConfirmationDate, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out confirmDateInsert);
                        if (!isConvert) return new SaveInsuranceInfoOutputData(new List<SavePatientInfoValidationResult>(), SavePatientInfoStatus.Failed, 0, 0, 0, patientInforModel);

                        CIUtil.ConvertJapanTimeToUtc(ref confirmDateInsert);
                        confirmDateInsert = TimeZoneInfo.ConvertTimeToUtc(confirmDateInsert);
                        model = new OnlineConfirmationHistoryModel(0, inputData.PtId, confirmDateInsert, inputData.OnlineConfirmationHistory.ConfirmationType, inputData.OnlineConfirmationHistory.InfoConsFlg, inputData.OnlineConfirmationHistory.ConfirmationResult, inputData.OnlineConfirmationHistory.PrescriptionIssueType, inputData.OnlineConfirmationHistory.UketukeStatus, inputData.HpId);
                    }
                }    
                
                var result = _patientInforRepository.SaveInsuranceInfo(
                    inputData.HpId,
                    inputData.PtId,
                    inputData.HokenInf,
                    inputData.UserId,
                    inputData.SinDate,
                    inputData.PatientInfo,
                    inputData.PtKyuseiModel,
                    model,
                    inputData.IsConfirmOnline,
                    inputData.EndDateModel);

                if (result.resultSave)
                {
                    patientInforModel = _patientInforRepository.GetById(inputData.HpId, result.ptId, 0, 0) ?? new();
                    return new SaveInsuranceInfoOutputData(new List<SavePatientInfoValidationResult>(), SavePatientInfoStatus.Successful, result.ptId, result.hokenId, result.onlineConfirmHistoryId, patientInforModel);
                }
                else
                    return new SaveInsuranceInfoOutputData(new List<SavePatientInfoValidationResult>(), SavePatientInfoStatus.Failed, 0, result.hokenId, 0, patientInforModel);
            }
            catch (Exception ex)
            {
                if (_loggingHandler != null)
                {
                    _loggingHandler.WriteLogExceptionAsync(ex);
                }
                throw;
            }
            finally
            {
                _patientInforRepository.ReleaseResource();
                _systemConfRepository.ReleaseResource();
                if (_loggingHandler != null)
                {
                    _loggingHandler.Dispose();
                }
                _insuranceRepository.DeleteKeyInsuranceList(inputData.HpId, inputData.PtId);
                _insuranceRepository.ReleaseResource();
            }

        }
    }
}
